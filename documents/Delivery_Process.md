# Delivery Process

## Branching Strategy
GitHub Flow (https://githubflow.github.io/) is the branching strategy employed in BRAD, in contradiction with the usual GitFlow approach followed in other projects.
The decision to migrate to GitHub Flow was made for several reasons:
* Enhanced continuous integration.
* Less merge conflicts.
* Faster and easier releases.

![Branching Strategy Diagram](assets/branching_strategy.png)

## Delivery Process

With this approach there are only 3 types of branches, master, hotfix and feature branches. As seen in the diagram, developers should create short lived feature branches whenever working on a given feature. Then, once development is finished, create a pull request towards master. If the pull request is approved by another developer it can be merged to master and later tested by the QA. If the QA approves the feature, master can be released.

Regarding hotfixes, tags should be branched out and deployed, without forgetting to later merge the hotfix changes to master.

![Delivery Process](assets/delivery_process.svg)