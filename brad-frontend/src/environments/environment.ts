import {CsFinEnvironment} from "@jumia-cs-fin/common";

export const environment: CsFinEnvironment = {
  beBaseUrl: 'http://localhost:8080',
  feBaseUrl: 'http://localhost:4200',
  production: false,
  acl: {
    feBaseUrl: 'https://acl-staging.jumia.services',
    beBaseUrl: 'https://api-acl-staging.jumia.services',
    loginPath: '/login',
    loginResponsePath: 'login/response',
    appCode: 'BRAD'
  }
};

export const languageConfigs = {
  defaultLanguage: 'en',
  languages: ['en']
};


