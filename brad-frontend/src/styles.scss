@import 'node_modules/@angular/material/theming';
@import "node_modules/@jumia-cs-fin/common/assets/styles/general";
@import 'node_modules/@jumia-cs-fin/common/assets/styles/variables';


.search-bar {
  #apply-search-btn {
    color:white
  }
}

.error-snackbar {
  background-color: #EE6666 !important;
  color: white !important;
  font-weight: bold;
  text-align: center;
}

@include mat-core();

$mat-brad-fin-blue: (
  50 : #bce8f5,
  100 : #99dcef,
  200 : #7ad2ec,
  300 : #5ec1de,
  400 : #43b6d7,
  500 : #17b0e0, //buttons
  600 : #35bee7,
  700 : #19b4e1,
  800 : #12b0de,
  900 : #00A7D8,
  A100 : #FFFFFF,
  A200 : #00A7D8,
  A400 : #00A7D8,
  A700 : #00A7D8,
  contrast: (
    50 : #000000,
    100 : #000000,
    200 : #000000,
    300 : #000000,
    400 : #000000,
    500 : #FFFFFF,
    600 : #FFFFFF,
    700 : #FFFFFF,
    800 : #FFFFFF,
    900 : #FFFFFF,
    A100 : #000000,
    A200 : #000000,
    A400 : #000000,
    A700 : #000000,
  )
);

:root {
  --primary-color: #20c4fc; //nav: icon, font and lower right corner of user rectangle
  --darker-color: #20c4fc; //nav: top left corner of user rectangle
  --lighter-color: #d8ecfc; // nav + table selected menu/row.
  --extra-light-color: #b3b3b3; //Header popup input fonts
  --table-color: #FAFAFA;
  --table-even-row-color: #FFFFFF;
  --table-odd-row-color: #fafafb;
  --primary-text-color: #1A2950;
  --accent-text-color: #344775;

  --mdc-checkbox-selected-checkmark-color: #fff;
  --mdc-checkbox-selected-focus-icon-color: #00a7d8;
  --mdc-checkbox-selected-hover-icon-color: #00a7d8;
  --mdc-checkbox-selected-icon-color: #17b0e0;
  --mdc-checkbox-selected-pressed-icon-color: #00a7d8;
  --mdc-checkbox-unselected-focus-icon-color: #212121;
  --mdc-checkbox-unselected-hover-icon-color: #212121;
  --mdc-checkbox-unselected-icon-color: #17b0e0;
  --mdc-checkbox-indeterminate-icon-color: var(--primary-text-color);
  --mat-tab-header-active-label-text-color: var(--primary-text-color)!important;;
}

$cs-fin-app-primary: mat-palette($mat-brad-fin-blue);
$cs-fin-app-accent: mat-palette($mat-brad-fin-blue, A200, A100, A400);
$cs-fin-app-warn: mat-palette($mat-cs-fin-red);

$cs-fin-app-theme: mat-light-theme(
    (
      color: (
        primary: $cs-fin-app-primary,
        accent: $cs-fin-app-accent,
        warn: $cs-fin-app-warn,
      ),
    )
);

@include angular-material-theme($cs-fin-app-theme);
