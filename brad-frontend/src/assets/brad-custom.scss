::ng-deep .custom-dialog-container {
  border: 2px solid var(--primary-color);
  border-radius: 4px;
}

.dialog-header {
  width: 100%;
  padding: 10px;
  border-bottom: 1px solid #e0e0e0;
}

.dialog-container {
  width: 60vh;
  height: 800px;
  flex-direction: column;
  flex-grow: 1;
  justify-content: space-between;
  border: 2px solid var(--primary-color);
}

.dialog-content{
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  justify-content: space-between;
}

.error-snackbar {
  background-color: #EE6666 !important;
  color: white !important;
  font-weight: bold;
  text-align: center;
}

.change-header {
  box-shadow: none;
  .mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__leading,
  .mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__trailing {
    opacity: 0;
  }

  .mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__notch {
    border: none;
  }
  mat-form-field {

    height: 18px;
    font-size: 1px;
    margin-bottom: 16px;

    .mat-mdc-text-field-wrapper,
    .mat-mdc-select,
    .mat-mdc-select-value,
    .mat-mdc-select-trigger {
      height: 18px;
    }
    .mat-mdc-text-field-wrapper.mdc-text-field--outlined .mat-mdc-form-field-infix {
      padding: 0;
    }

    .mat-mdc-select-value {
      font-size: 14px;
    }

  }

}
