{"CONFIRMATION_DIALOG": {"ACTIONS": {"ARE_YOU_SURE": "Are you sure?", "WOULD_YOU_LIKE_TO_PROCEED": "Would you like to proceed?", "REDIRECT_TROUBLESHOOT": "This account has statements in Error. Please correct them in the Troubleshooting menu."}, "BUTTONS": {"NO": "No", "YES": "Yes", "CANCEL": "Cancel", "DELETE": "Delete", "DISCARD": "Discard", "REDIRECT": "Redirect to Troubleshooting", "UPLOAD_BEFORE_ERRORS": "Upload Before Errors"}, "MESSAGES": {"ABOUT_TO_LOG_OFF": "You are about to log off.", "ABOUT_TO_DELETE_ACCOUNT": "You are about to delete a Account.", "ABOUT_TO_DELETE_CONTACT": "You are about to delete a contact.", "ABOUT_TO_DELETE_USER": "You are about to delete a user.", "ABOUT_TO_DELETE_DOCUMENT": "You are about to delete a document.", "ABOUT_TO_REPLACE_FILE": "You are about to replace a file.", "ABOUT_TO_CLOSE_ACCOUNT": "You are about to close a Account.", "ABOUT_TO_RECONCILE": "You are about to reconcile.", "ABOUT_TO_REJECT": "You are about to reject a reconciliation.", "ABOUT_TO_ACCEPT": "You are about to accept a reconciliation.", "ABOUT_TO_RETRY_STATEMENT": "You are about to retry uploading this statement and its transactions.", "ABOUT_TO_ACKNOWLEDGE_API_LOG_FAILURE": "You are about to acknowledge an API log failure (This is irreversible!)", "ABOUT_TO_DISCARD_STATEMENT": "You are about to discard a statement.", "ABOUT_TO_UPLOAD_WITH_STATEMENTS_IN_ERROR": "There are statements in error.", "ABOUT_TO_DELETE_THRESHOLD": "You are about to delete a threshold.", "ABOUT_TO_SCAN_STATEMENT_FILES": "You are about to scan the statement files.", "ABOUT_TO_REPROCESS_STATEMENT_FILES": "You are about to process all statement files in status NEW.", "ABOUT_TO_DELETE_STATEMENT": "You are about to permanently discard a statement."}}, "GENERAL": {"DETAILS": {"LOADING": "Loading...", "SEARCH_BAR": "Search...", "ERRORS": {"UNABLE_TO_FIND_DETAILS": "Unable to find details, please report this issue", "UNABLE_TO_FETCH_DATA": "Unable to fetch data, please report this issue"}}, "BUTTONS": {"LABELS": {"CANCEL": "Cancel", "CREATE": "New", "SAVE": "Save", "DOWNLOAD": "Export", "UPLOAD": "Upload", "SEARCH": "Search", "APPLY": "Apply", "CLEAR": "Clear", "RESTORE": "Rest<PERSON>", "RETRY": "Retry", "SCAN": "<PERSON><PERSON>", "REPROCESS": "Reprocess", "ACCOUNT_DETAILS": "Account Details", "DOCUMENTS": "Documents", "CONTACTS": "Contacts", "USERS": "Users"}, "TOOLTIPS": {"BACK": "Back", "CLEAR_FILTERS": "Clear Filters", "DELETE": "Delete", "DETAILS": "Details", "EDIT": "Edit", "RELOAD": "Reload", "DOWNLOAD": "Download", "CLOSE": "Close", "HISTORY": "History", "RUN": "Run", "RUN_ALL_DB": "Sync All Databases", "RUN_SOME_DB": "Select Databases to Sync", "PAUSE": "Pause", "RESUME": "Resume"}}, "FIELDS": {"ACTIONS": "Actions", "ID": "ID", "CREATED_AT": "Created At", "CREATED_BY": "Created By", "UPDATED_AT": "Updated At", "UPDATED_BY": "Updated By"}, "TABLE": {"NO_RESULTS_FOUND": "No results found."}, "FILTERS": {"TITLE": "Filters", "SEARCH": "Search", "NO_MATCHING_RESULTS_FOUND": "No matching results found.", "LOADING": "Loading...", "ACTIONS": {"CLEAR_SELECTION": "Clear Selection", "CLEAR": "Clear", "APPLY": "Apply"}}}, "LOGIN_RESPONSE": {"LOADING": "Loading..."}, "NAVIGATION": {"SIDENAV": {"LOGOUT": "Logout", "DIALOGS": {"LOGOUT": {"TITLE": "Logout", "DESCRIPTION": "Are you sure you want to <strong>LOGOUT</strong>?", "ACTIONS": {"CANCEL": "Cancel", "CONFIRM": "Logout"}}}}, "GROUPS": {"MANAGEMENT": "Manage", "RECONCILIATION": "Reconcile"}, "MENUS": {"ACCOUNTS": "Accounts", "SCHEDULER": "Scheduler", "FINREC": "FinRec", "BALE": "<PERSON><PERSON>", "THRESHOLDS": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RECONCILE": "Reconcile", "LIST": "List", "RECONCILIATION_AUDIT": "Audit", "STATEMENT": "Statement"}}, "NOTIFICATIONS": {"CLOSE": "Close", "ERROR": {"USER_DOESNT_HAVE_PERMISSION_ON_BRAD": "User {{username}} doesn't have the permission {{permissionCode}} on the application BRAD"}, "HTTP_ERROR": {"401": "Unauthorized", "403": "Forbidden", "409": "Conflict", "500": "Server error", "0": "Unable to complete request.", "-1": "Connection refused", "UNKNOWN": "Unknown"}, "NO_ERROR_MESSAGE_FOUND": "No error message found"}, "PAGE_NOT_FOUND": {"NOT_FOUND": "We looked everywhere — even under the couch — but we couldn't find that page. 🕵️‍♂️"}, "DASHBOARD": {"GENERAL": {"NO_RESULTS_FOUND": "No results found!"}, "DATATABLE": {"COUNTRY": "Country", "LEGAL_ENTITY": "Legal Entity", "BANK_ACCOUNT": "Bank Account", "TRANSACTION_DESCRIPTION": "Transaction Description", "VALUE": "Value", "CURRENCY": "<PERSON><PERSON><PERSON><PERSON>", "DATE": "Date", "VIEW_MORE": "View More"}}, "ACCOUNTS": {"CREATE": {"TITLE": "Create Account"}, "EDIT": {"TITLE": "Edit Account"}, "TITLE": "Accounts", "BUTTONS": {"LABELS": {"CREATE": "New Account", "RECONCILE": "Reconcile"}}, "DIALOG": {"TITLE": "Account Details", "INFO": "Account Info"}, "DETAILS": {"TITLE": "Accounts Details", "SEARCH_BAR": "Search for Accounts...", "FILTERS": "Filters", "HISTORY": "History", "ERRORS": {"UNABLE_TO_FIND_HISTORY": "It appears this Account has no history, strange... please report this error", "UNABLE_TO_FIND_DETAILS": "It appears this Account has no details, strange... please report this error"}, "WARNINGS": {"REQUIRES_ATTENTION": "Issues pending resolution"}, "TABS": {"LABELS": {"ACCOUNT_STATEMENTS": "Statements", "ACCOUNT_DETAILS": "Account Details", "ACCOUNT_HISTORY": "History", "ACCOUNT_CONTACTS": "Contacts", "ACCOUNT_DOCUMENTS": "Documents", "ACCOUNT_USERS": "Users", "ACCOUNT_GRID_TRANSACTION": "Transactions", "ACCOUNT_GRID_STATEMENT": "Statements"}}, "FIELDS": {"BALANCE": "Balance (BAC)", "BALANCE_IN_USD": "Balance (USD)", "BALANCE_IN_LCY": "Balance (LCY)", "LAST_TRANSACTION_DATE": "Last Transaction Date", "LAST_STATEMENT_DATE": "Last Statement Date"}}, "FIELDS": {"ID": "ID", "COMPANY_ID": "Company ID", "COUNTRY": "Country", "NAV_REFERENCE": "Nav Reference", "BALANCE": "Balance", "BENEFICIARY_NAME": "Beneficiary Name", "BENEFICIARY_ADDRESS": "Beneficiary Address", "IBAN": "IBAN", "ACCOUNT_NUMBER": "Account Nº", "NET_CHANGE": "Net Change", "ACCOUNT_ID": "Account ID", "ACCOUNT_NAME": "Account Name", "ACCOUNT_TYPE": "Account Type", "ENTITY": "Entity", "SWIFT_CODE": "Swift Code", "ACCOUNT_ROUTING_CODE": "Account Routing Code", "SORT_CODE": "Sort Code", "BRANCH_CODE": "Branch Code", "RIB": "RIB", "CURRENCY": "Account <PERSON><PERSON><PERSON><PERSON>", "FILTER_CURRENCY": "<PERSON><PERSON><PERSON><PERSON>", "TYPE": "Type", "SUB_TYPE": "Sub Type", "STATUS": "Status", "PARTNER": "Partner", "PHONE_NUMBER": "Phone Number", "CREATED_AT": "Created At", "CREATED_BY": "Created By", "UPDATED_AT": "Updated At", "UPDATED_BY": "Updated By", "AT_DATE": "At Date", "DATE_RANGE": "Date Range", "LAST_STATEMENT_DATE": "Last Statement Date", "STATEMENT_SOURCE": "Statement Source", "STATEMENT_PERIODICITY": "Statement Periodicity", "PERIODICITY": "Periodicity", "BALANCE_IN_USD": "Balance (USD)", "BALANCE_IN_LCY": "Balance (LCY)", "LAST_TRANSACTION_DATE": "Last Transaction Date", "LAST_PROCESSED_STATEMENT_DATE": "Last Processed Statement Date", "ISIN": "ISIN", "CONTRACT_ID": "Contract ID", "AMOUNT_DEPOSITED": "Amount Deposited", "MATURITY_DATE": "Maturity Date", "NOMINAL_AMOUNT": "Nominal Amount", "COUPON_PAYMENT_PERIODICITY": "Coupon Payment Periodicity", "COUPON_RATE": "Coupon Rate", "INTEREST": "Interest", "INVESTMENT_ID": "Investment ID"}, "LIST": {"TITLE": "Accounts"}, "NOTIFICATIONS": {"CREATE_ACCOUNT_SUCCESS": "Successfully created Account {{accountNumber}}.", "DELETE_ACCOUNT_SUCCESS": "Successfully deleted Account {{accountNumber}}.", "DELETE_DOCUMENT_SUCCESS": "Successfully deleted document {{document}}.", "DISCARD_STATEMENT_SUCCESS": "Statement Discarded Successfully.", "RETRY_STATEMENT_SUCCESS": "We are retrying the statement. We'll be done in a minute!", "UPDATE_ACCOUNT_SUCCESS": "Successfully updated Account {{accountNumber}}.", "CLOSE_ACCOUNT_SUCCESS": "Successfully closed Account {{accountNumber}}.", "DOWNLOAD_ACCOUNTS_SUCCESS": "Successfully downloaded Accounts.", "DOWNLOAD_CONTACTS_SUCCESS": "Successfully downloaded contacts.", "DOWNLOAD_USERS_SUCCESS": "Successfully downloaded users."}, "WRITE": {"ERRORS": {"COMPANY_ID_IS_REQUIRED": "Company ID is required", "COUNTRY_IS_REQUIRED": "Country is required", "NAV_REFERENCE_IS_REQUIRED": "Nav Reference is required", "BENEFICIARY_NAME_IS_REQUIRED": "Beneficiary Name is required", "BENEFICIARY_ADDRESS_IS_REQUIRED": "Beneficiary Address is required", "IBAN_IS_REQUIRED": "IBAN is required", "ACCOUNT_NUMBER_IS_REQUIRED": "Account Nº is required", "ACCOUNT_ID_IS_REQUIRED": "Account ID is required", "ACCOUNT_NAME_IS_REQUIRED": "Account Name is required", "SWIFT_CODE_IS_REQUIRED": "Swift Code is required", "ACCOUNT_ROUTING_CODE_IS_REQUIRED": "Account Routing Code is required", "SORT_CODE_IS_REQUIRED": "Sort Code is required", "BRANCH_CODE_IS_REQUIRED": "Branch Code is required", "CURRENCY_IS_REQUIRED": "Currency is required", "RIB_IS_REQUIRED": "RIB is required", "STATUS_IS_REQUIRED": "Status is required", "TYPE_IS_REQUIRED": "Type is required", "SUB_TYPE_IS_REQUIRED": "Sub Type is required", "STATEMENT_SOURCE_IS_REQUIRED": "Statement Source is required", "STATEMENT_PERIODICITY_IS_REQUIRED": "Statement periodicity is required", "COUPON_PAYMENT_PERIODICITY_IS_REQUIRED": "Coupon Payment Periodicity is required", "PARTNER_IS_REQUIRED": "Partner is required", "PHONE_NUMBER_IS_REQUIRED": "Phone Number is required", "AMOUNT_DEPOSITED_IS_REQUIRED": "Amount Deposited is required", "MATURITY_DATE_IS_REQUIRED": "Maturity Date is required", "INTEREST_IS_REQUIRED": "Interest is required", "CONTRACT_ID_IS_REQUIRED": "Contract ID is required", "NOMINAL_AMOUNT_IS_REQUIRED": "Nominal Amount is required", "COUPON_RATE_IS_REQUIRED": "Coupon Rate is required", "ISIN_IS_REQUIRED": "ISIN is required"}, "OPERATION_TYPES": {"CREATE": "Create Account", "EDIT": "Edit Account"}}, "SELECTOR": {"TITLE": "Please select a Account", "LABELS": {"PREVIOUS_ACCOUNT": "Select Previous Account", "TROUBLESHOOT": "Troubleshoot", "RECONCILIATION": "Reconciliation"}}}, "ACCOUNTS_INFO": {"TITLE": "Account Information"}, "CONTACTS": {"CREATE": {"TITLE": "Create Contact"}, "EDIT": {"TITLE": "Edit Contact"}, "TITLE": "Contacts", "BUTTONS": {"LABELS": {"CREATE": "New Contact", "EDIT": "Edit Contact", "DELETE": "Delete Contact", "HISTORY": "See History"}}, "DETAILS": {"TITLE": "Contact Details", "SEARCH_BAR": "Search for Contacts...", "FILTERS": "Filters"}, "DIALOG": {"TITLE": "Contact Details", "INFO": "Contact Info"}, "FIELDS": {"ID": "ID", "CONTACT_TYPE": "Contact Type", "OTHER_CONTACT_TYPE": "Other Contact Type", "NAME": "Name", "EMAIL": "Email", "MOBILE_PHONE_NUMBER": "Mobile Phone Number", "ACCOUNT": "Account ID", "CREATED_AT": "Created At", "CREATED_BY": "Created By", "UPDATED_AT": "Updated At", "UPDATED_BY": "Updated By"}, "NOTIFICATIONS": {"CREATE_CONTACT_SUCCESS": "Successfully created contact {{name}}.", "DELETE_CONTACT_SUCCESS": "Successfully deleted contact {{name}}.", "UPDATE_CONTACT_SUCCESS": "Successfully updated contact {{name}}."}, "WRITE": {"ERRORS": {"CONTACT_TYPE_IS_REQUIRED": "Contact Type is required", "OTHER_CONTACT_TYPE_IS_REQUIRED": "Other Contact Type is required", "NAME_IS_REQUIRED": "Name is required", "EMAIL_IS_REQUIRED": "Email is required", "INVALID_EMAIL_ADDRESS": "Invalid email address!", "MOBILE_PHONE_NUMBER_IS_REQUIRED": "Mobile Phone Number is required!", "INVALID_MOBILE_PHONE_NUMBER": "Invalid mobile Phone number!", "ACCOUNT_ID_IS_REQUIRED": "Account ID is required"}, "OPERATION_TYPES": {"CREATE": "Create Contact", "EDIT": "Edit Contact"}}}, "DOCUMENTS": {"CREATE": {"TITLE": "Create Document"}, "EDIT": {"TITLE": "Edit Document"}, "TITLE": "Documents", "BUTTONS": {"LABELS": {"CREATE": "New Document", "EDIT": "Edit Document", "DELETE": "Delete Document", "HISTORY": "See History"}}, "DETAILS": {"TITLE": "Document Details", "SEARCH_BAR": "Search for Documents...", "FILTERS": "Filters"}, "DIALOG": {"TITLE": "Document Details", "INFO": "Document Info", "PREVIEW": "Document Preview"}, "FIELDS": {"ID": "ID", "DOCUMENT_TYPE": "Document Type", "NAME": "Name", "DESCRIPTION": "Description", "URL": "URL", "ACCOUNT": "Account ID", "CREATED_AT": "Created At", "CREATED_BY": "Created By", "UPDATED_AT": "Updated At", "UPDATED_BY": "Updated By", "ATTACHMENT": "Attachment"}, "NOTIFICATIONS": {"CREATE_DOCUMENT_SUCCESS": "Successfully created document {{name}}.", "DELETE_DOCUMENT_SUCCESS": "Successfully deleted document {{name}}.", "UPDATE_DOCUMENT_SUCCESS": "Successfully updated document {{name}}."}, "WRITE": {"ERRORS": {"DOCUMENT_TYPE_IS_REQUIRED": "Document Type is required", "NAME_IS_REQUIRED": "Name is required", "DESCRIPTION_IS_REQUIRED": "Description is required", "URL_IS_REQUIRED": "URL is required", "ACCOUNT_ID_IS_REQUIRED": "Account ID is required"}, "OPERATION_TYPES": {"CREATE": "Create Document", "EDIT": "Edit Document"}}}, "USERS": {"CREATE": {"TITLE": "Create User"}, "EDIT": {"TITLE": "Edit User"}, "TITLE": "Users", "BUTTONS": {"LABELS": {"CREATE": "New User", "EDIT": "Edit User", "DELETE": "Delete User", "HISTORY": "See History"}}, "DETAILS": {"TITLE": "User Details", "SEARCH_BAR": "Search for Users...", "FILTERS": "Filters"}, "DIALOG": {"TITLE": "User Details", "INFO": "User Info"}, "FIELDS": {"ID": "ID", "USER_NAME": "User Name", "EMAIL": "Email", "ACCOUNT": "Account ID", "CREATED_AT": "Created At", "CREATED_BY": "Created By", "UPDATED_AT": "Updated At", "UPDATED_BY": "Updated By", "HR_ROLE": "HR Role", "PERMISSION_TYPE": "Permission Type", "MOBILE_PHONE_NUMBER": "Mobile Phone Number", "STATUS": "Status"}, "NOTIFICATIONS": {"CREATE_USER_SUCCESS": "Successfully created user {{name}}.", "DELETE_USER_SUCCESS": "Successfully deleted user {{name}}.", "UPDATE_USER_SUCCESS": "Successfully updated user {{name}}."}, "WRITE": {"ERRORS": {"USER_NAME_IS_REQUIRED": "User Name is required", "EMAIL_IS_REQUIRED": "Email is required", "ACCOUNT_ID_IS_REQUIRED": "Account ID is required", "HR_ROLE_IS_REQUIRED": "HR Role is required", "PERMISSION_TYPE_IS_REQUIRED": "Permission Type is required"}, "OPERATION_TYPES": {"CREATE": "Create User", "EDIT": "Edit User"}}}, "SCHEDULER": {"TITLE": "Scheduler", "BUTTONS": {"LABELS": {"CREATE": "New Schedule", "UPDATE_CRON_DESCRIPTION": "Update Cron Description"}}, "DETAILS": {"TITLE": "Schedule Details", "SEARCH_BAR": "Search for schedules...", "FILTERS": "Filters", "HISTORY": "History", "ERRORS": {"UNABLE_TO_FIND_HISTORY": "It appears this schedule has no history, strange... please report this error"}}, "FIELDS": {"CRON_STRING": "Cron String", "JOB_NAME": "Job Name", "CRON_DESCRIPTION": "Cron Description", "TIMEZONE": "Timezone", "LAST_RUN": "Last Run (from schedule)", "NEXT_RUN": "Next Run", "STATE": "State"}, "LIST": {"TITLE": "Schedules"}, "NOTIFICATIONS": {"UPDATE_SCHEDULE_SUCCESS": "Successfully updated job {{jobName}}."}, "WRITE": {"ERRORS": {"CRON_STRING_IS_REQUIRED": "A Cron String is required", "JOB_NAME_IS_REQUIRED": "A Job Name is required"}, "OPERATION_TYPES": {"CREATE": "Create Schedule", "EDIT": "Edit Schedule"}}, "LABELS": {"NEVER": "Never"}}, "STATEMENTS": {"TITLE": "Statement", "CREATE": {"TITLE": "Create Statement"}, "DETAILS": {"TITLE": "Statement Details", "SEARCH_BAR": "Search for Statement...", "FILTERS": "Filters"}, "NOTIFICATIONS": {"MESSAGES": {"FILE_ERROR": "File Error", "CREATE_STATEMENT_SUCCESS": "Successfully created statement {{statementId}}.", "DOWNLOAD_STATEMENTS_SUCCESS": "Successfully downloaded statements."}}, "FIELDS": {"ID": "ID", "ACCOUNT_ID": "Account ID", "CURRENCY": "<PERSON><PERSON><PERSON><PERSON>", "STATEMENT_NUMBER": "Statement Number", "PREVIOUS_STATEMENT_NUMBER": "Previous Statement Number", "INITIAL_DATE": "Initial Date", "FINAL_DATE": "Final Date", "INITIAL_DIRECTION": "Initial Direction", "FINAL_DIRECTION": "Final Direction", "INITIAL_AMOUNT": "Initial Balance", "FINAL_AMOUNT": "Final Balance", "STATUS": "Status", "STATUS_DESCRIPTION": "Status Description", "DESCRIPTION": "Description", "NAV_REFERENCE": "Nav Reference", "CREATED_AT": "Created At", "CREATED_BY": "Created By", "UPDATED_AT": "Updated At", "UPDATED_BY": "Updated By"}, "WRITE": {"ERRORS": {"ACCOUNT_ID_IS_REQUIRED": "Account ID is required", "CURRENCY_IS_REQUIRED": "Currency is required", "STATEMENT_NUMBER_IS_REQUIRED": "Statement Number is required", "PREVIOUS_STATEMENT_NUMBER_IS_REQUIRED": "Previous Statement Number is required", "START_DATE_IS_REQUIRED": "Initial Date is required", "END_DATE_IS_REQUIRED": "Final Date is required", "INITIAL_BALANCE_IS_REQUIRED": "Initial Amount is required", "FINAL_BALANCE_IS_REQUIRED": "Final Amount is required", "STATUS_IS_REQUIRED": "Status is required", "STATUS_DESCRIPTION_IS_REQUIRED": "Status Description is required", "DESCRIPTION_IS_REQUIRED": "Description is required"}, "OPERATION_TYPES": {"CREATE": "Create Statement", "RETRY": "Retry Statement"}, "WARNING": {"TITLE": "WARNING!", "MESSAGE": "You are about to create a gap between the last statement and the one currently being uploaded", "LAST_CLOSING_DATE": "Last statement closing date: {{lastStatementClosingDate}}", "INITIAL_DATE": "This statement initial date: {{initialStatementDate}}"}}}, "TRANSACTIONS": {"TITLE": "Transaction", "DETAILS": {"TITLE": "Transaction Details", "SEARCH_BAR": "Search for Transaction...", "FILTERS": "Filters"}, "FIELDS": {"ID": "ID", "TYPE": "Type", "ACCOUNT_ID": "Account ID", "CURRENCY": "<PERSON>ur", "VALUE_DATE": "Value Date", "PARTITION_KEY": "Partition Key", "TRANSACTION_DATE": "Transaction Date", "STATEMENT_DATE": "Statement Date", "DIRECTION": "Sign", "AMOUNT": "Amount", "REFERENCE": "Reference", "DESCRIPTION": "Description", "STATEMENT_ID": "Statement ID", "REMITTANCE_INFORMATION": "Remittance Information", "ORDERING_PARTY_NAME": "Ordering Party Name", "RECONCILE_STATUS": "Status", "CREATED_AT": "Created At", "CREATED_BY": "Created By", "UPDATED_AT": "Updated At", "UPDATED_BY": "Updated By"}, "NOTIFICATIONS": {"DOWNLOAD_TRANSACTIONS_SUCCESS": "Successfully downloaded transactions."}}, "FINREC": {"TITLE": "Finrec", "DETAILS": {"TITLE": "Finrec Details", "SEARCH_BAR": "Search for Finrec...", "FILTERS": "Filters", "HISTORY": "History", "ERRORS": {"UNABLE_TO_FIND_HISTORY": "It appears this finrec has no history, strange... please report this error"}, "TABS": {"LABELS": {"FINREC_DETAILS": "Details"}}}, "FIELDS": {"ID": "ID", "TYPE": "Type", "ACCOUNT_CODE": "Account Code", "CUR": "<PERSON><PERSON><PERSON><PERSON>", "ACCOUNT": "Account", "STATEMENT_DATE": "Statement Date", "TRANSACTION_DATE": "Transaction Date", "VALUE_DATE": "Value Date", "FLOW_CODE": "Flow Code", "DIRECTION": "Direction", "AMOUNT": "Amount", "REFERENCE": "Reference", "DESCRIPTION": "Description", "STATEMENT_ID": "Statement ID", "FILE_NAME": "File Name", "SK_AUD_INSERT": "SK AUD Insert", "SK_AUD_UPDATE": "SK AUD Update", "TIMESTAMP_LAST_UPDATE": "Timestamp Last Update", "IS_RECONCILED": "Is Reconciled"}}, "BALE": {"TITLE": "<PERSON><PERSON>", "DETAILS": {"TITLE": "<PERSON><PERSON>", "SEARCH_BAR": "Search for <PERSON><PERSON>...", "FILTERS": "Filters", "HISTORY": "History", "ERRORS": {"UNABLE_TO_FIND_HISTORY": "It appears this bale has no history, strange... please report this error"}, "TABS": {"LABELS": {"BALE_DETAILS": "Details"}}}, "FIELDS": {"ID": "ID", "ID_COMPANY": "Company ID", "ACCOUNT": "Account", "ENTRY_NO": "Entry No", "DOCUMENT_NO": "Doc Nº", "DOCUMENT_TYPE": "Document Type", "POSTING_DATE": "Posting Date", "ACCOUNT_POSTING_GROUP": "BA Posting Group", "DESCRIPTION": "Description", "SOURCE_CODE": "Source Code", "REASON_CODE": "Reason Code", "BUS_LINE": "Bus Line", "DEPARTMENT": "Department", "DIRECTION": "Direction", "AMOUNT": "Amount", "REMAINING_AMOUNT": "Remaining Amount", "TRANSACTION_CURRENCY": "<PERSON>ur", "AMOUNT_LCY": "Amount LCY", "BALANCE_ACCOUNT_TYPE": "Balance Account Type", "IS_OPEN": "Is Open", "IS_REVERSED": "<PERSON>ersed", "POSTED_BY": "Posted By", "EXTERNAL_DOCUMENT_NO": "Ext. Doc Nº", "BALE_TIMESTAMP": "<PERSON>le Timestamp", "ACCOUNT_TIMESTAMP": "Account Timestamp", "RECONCILE_STATUS": "Status"}}, "RECONCILIATION": {"RECONCILE_TITLE": "Reconcile Account", "LIST_TITLE": "Reconciliations", "TRANSACTIONS_TITLE": "Transactions", "BALES_TITLE": "Account Ledger Entries", "FILTERS": {"TITLE": "Filter", "ALL_TITLE": "Both Tables", "TRANSACTION_TITLE": "Transactions", "BALE_TITLE": "Account Ledger Entries"}, "DETAILS": {"TITLE": "Details", "FINREC": "Finrec", "BALE": "<PERSON><PERSON>", "SEARCH_BAR_GENERIC": "Search for ", "SEARCH_BAR": "Search for Reconciliations...", "FILTERS": "Filters", "HISTORY": "History", "ERRORS": {"UNABLE_TO_FIND_HISTORY": "It appears this reconciliation has no history, strange... please report this error"}, "TABS": {"LABELS": {"RECONCILIATION_DETAILS": "Details", "FINREC_DETAILS": "Finrec", "BALE_DETAILS": "<PERSON><PERSON>"}}}, "BUTTONS": {"LABELS": {"RECONCILE": "RECONCILE", "APPROVE": "APPROVE", "UNMATCH": "UNMATCH", "CLEAR_SELECTION": "Clear Selection", "CLEAR_ALL": "Clear All", "SWITCH_ACCOUNT": "Switch Account", "SWITCH_TO_BALE_HEADER": "<PERSON><PERSON>", "SWITCH_TO_TRANSACTION_HEADER": "Transactions", "SWITCH_TO_ALL_HEADER": "All", "REMAINING_AMOUNT": "Remaining Amount"}}, "FIELDS": {"ID": "Rec. ID", "ACCOUNT": "Account", "STATUS": "Status", "CREATOR": "Creator", "CREATION_DATE": "Creation Date", "REVIEWER": "Reviewer", "REVIEW_DATE": "Review Date", "AMOUNT_TRANSACTION": "Transactions Amount", "AMOUNT_BALE": "<PERSON><PERSON>", "AMOUNT_THRESHOLD": "<PERSON><PERSON><PERSON><PERSON>", "FINREC_IDS": "Finrec IDs", "BALE_IDS": "Bale IDs"}, "NOTIFICATIONS": {"RECONCILE_SUCCESS": "Successfully reconciled.", "APPROVE_SUCCESS": "Successfully approved reconciliation.", "UNMATCH_SUCCESS": "Successfully unmatched reconciliation."}}, "UPLOAD": {"TITLE": "Add Upload Request", "ACTIONS": {"ADD_UPLOAD_REQUEST": "Add Upload Request", "MASS_APPROVE_REJECT_AGENT_PAYMENT_PROVIDER": "Mass Approve or Reject", "MASS_UPLOAD": "Mass Upload", "EXPORT": "Export", "MASS_MARK_AS_PAID_REQUEST": " <PERSON> <PERSON> As Paid Request ", "DOWNLOAD_TEMPLATE": "Download Template", "BROWSE_FILES": "Browse Files", "UPLOADING": "Uploading", "UPLOAD_REQUEST": "Upload Request", "CANCEL": "Cancel"}, "NOTIFICATIONS": {"MESSAGES": {"DESKTOPS": "Drag and drop or choose your file here. (Only Images and PDF files are accepted)", "ERROR": "Only images and PDFs files are accepted. Please try again.", "SUCCESS": "Your file has been uploaded successfully."}}}, "API_LOG": {"TITLE": "Api Log", "DIALOG": {"TITLE": "Api Log Details", "INFO": "Api Log Info"}, "DETAILS": {"TITLE": "Api Log Details", "SEARCH_BAR": "Search for Api Logs...", "FILTERS": "Filters", "ERRORS": {"UNABLE_TO_FIND_DETAILS": "It appears this log has no details, strange... please report this error"}, "TABS": {"LABELS": {"API_LOG_DETAILS": "Details", "API_LOG_REQUEST": "Request", "API_LOG_RESPONSE": "Response"}}}, "FIELDS": {"ID": "ID", "LOG_TYPE": "Log Type", "REQUEST": "Request", "RESPONSE": "Response", "RELATED_ENTITY_ID": "Related Entities", "LOG_STATUS": "Log Status", "LOG_ERROR": "Log <PERSON>r", "CREATED_AT": "Created At", "CREATED_BY": "Created By", "UPDATED_AT": "Updated At", "UPDATED_BY": "Updated By"}, "TYPES": {"BANK_STATEMENT_CREATION": "Statement Creation", "SFTP_BANK_STATEMENT_IMPORT": "SFTP Statement Import", "API_BANK_STATEMENT_FETCH": "API Statement Fetch"}, "LIST": {"TITLE": "Api Logs"}, "ACTIONS": {"RETRY": "Retry", "ACKNOWLEDGE": "Acknowledge Failure"}, "NOTIFICATIONS": {"MESSAGES": {"ACKNOWLEDGE_SUCCESS": "Successfully acknowledged failure for API log with id {{apiLogId}}."}}}, "TROUBLESHOOTING": {"TITLE": "Troubleshooting", "DETAILS": {"TITLE": "Transaction List"}, "FIELDS": {"ACTIONS": "ACTIONS", "PREVIOUS_STATEMENT_NUMBER": "PREVIOUS STATEMENT NUMBER", "TROUBLESHOOTING": "Troubleshooting", "ID": "ID", "ACCOUNT_ID": "ACCOUNT ID", "CURRENCY": "CURRENCY", "STATEMENT_NUMBER": "STATEMENT NUMBER", "INITIAL_DATE": "INITIAL DATE", "FINAL_DATE": "FINAL DATE", "INITIAL_DIRECTION": "INITIAL DIRECTION", "FINAL_DIRECTION": "FINAL_DIRECTION", "INITIAL_AMOUNT": "INITIAL BALANCE", "FINAL_AMOUNT": "FINAL BALANCE", "STATUS_DESCRIPTION": "STATUS DESCRIPTION"}, "ISSUES": {"SYNC_ISSUE": "Out of sync", "VALIDATION_ISSUE": "Failed validation", "MISSING_MANUAL_UPLOAD": "Missing manual upload"}, "BUTTONS": {"LABELS": {"UPLOAD_BEFORE_FIRST": "Upload Before First", "UPLOAD_HERE": "Upload Here", "UPLOAD_AFTER_LAST": "Upload After Last", "RETRY": "RETRY", "ACCOUNT_DETAILS": "Account Details"}}, "NOTIFICATIONS": {"SUCCESSFULLY_DISCARDED_STATEMENT": "Successfully discarded statement: {{statementNumber}}"}}, "EXPORT_LOGS": {"TITLE": "Export Logs", "DETAILS": {"TITLE": "Export Log Details", "SEARCH_BAR": "Search for Export Logs...", "FILTERS": "Filters", "TABS": {"LABELS": {"EXECUTION_LOG_DETAILS": "Details", "EXECUTION_LOG_ERRORS": "Errors"}}}, "FIELDS": {"ID": "ID", "RECORDS_AMOUNT": "Records Count", "STATUS": "Status", "TYPE": "Type", "EXECUTION_TIME": "Execution Time", "COUNTRIES": "Countries", "FILE": "File"}, "STATUS": {"PENDING": "Pending", "RUNNING": "Running", "COMPLETED": "Completed", "FAILED": "Failed"}, "TYPE": {"BANK_ACCOUNTS": "Bank Accounts"}}, "EXECUTION_LOGS": {"TITLE": "Execution Logs", "DIALOG": {"TITLE": "Execution Log Details", "INFO": "Execution Log Info"}, "DETAILS": {"TITLE": "Execution Log Details", "SEARCH_BAR": "Search for Execution Logs...", "FILTERS": "Filters", "TABS": {"LABELS": {"EXECUTION_LOG_DETAILS": "Details", "EXECUTION_LOG_ERRORS": "Errors"}}}, "FIELDS": {"ID": "ID", "STATUS": "Status", "TYPE": "Type", "RECORDS_AMOUNT": "Records Amount", "EXECUTION_START_TIME": "Execution Start Time", "EXECUTION_END_TIME": "Execution End Time", "APPLIED_FILTERS": "Applied Filters", "QUERY": "Query", "ENTITY": "Entity"}, "LIST": {"TITLE": "Execution Logs"}}, "BALE_VIEW_ENTITY": {"TITLE": "Bale View Entities", "FIELDS": {"ID": "ID", "DATABASE_NAME": "Database Name", "VIEW_NAME": "View Name", "CREATED_AT": "Created At", "CREATED_BY": "Created By", "UPDATED_AT": "Updated At", "UPDATED_BY": "Updated By"}, "BUTTONS": {"LABELS": {"SYNC": "Sync Databases"}}}, "THRESHOLDS": {"CREATE": {"TITLE": "Create <PERSON><PERSON><PERSON><PERSON>"}, "EDIT": {"TITLE": "<PERSON>"}, "TITLE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DETAILS": {"TITLE": "T<PERSON><PERSON>old Details", "SEARCH_BAR": "Search for Thresholds...", "FILTERS": "Filters", "HISTORY": "History", "ERRORS": {"UNABLE_TO_FIND_HISTORY": "It appears this threshold has no history, strange... please report this error"}, "TABS": {"LABELS": {"THRESHOLD_DETAILS": "Details"}}}, "FIELDS": {"ID": "ID", "COUNTRY": "Country", "CURRENCY": "<PERSON><PERSON><PERSON><PERSON>", "AMOUNT": "Amount", "CREATED_AT": "Created At", "CREATED_BY": "Created By", "UPDATED_AT": "Updated At", "UPDATED_BY": "Updated By"}, "LIST": {"TITLE": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "NOTIFICATIONS": {"CREATE_THRESHOLD_SUCCESS": "Successfully created threshold.", "UPDATE_THRESHOLD_SUCCESS": "Successfully updated threshold {{id}}."}, "WRITE": {"ERRORS": {"COUNTRY_IS_REQUIRED": "Country is required", "CURRENCY_IS_REQUIRED": "Currency is required", "AMOUNT_IS_REQUIRED": "Amount is required"}, "OPERATION_TYPES": {"CREATE": "Create <PERSON><PERSON><PERSON><PERSON>", "EDIT": "<PERSON>"}}}, "STATEMENT_FILES": {"TITLE": "Statement Files", "DETAILS": {"TITLE": "Statement Files Details", "ERRORS": {"UNABLE_TO_FIND_DETAILS": "It appears this log has no details, strange... please report this error"}, "TABS": {"LABELS": {"STATEMENT_FILES_DETAILS": "Details", "STATUS_DESCRIPTION": "Status Description"}}}, "FIELDS": {"ID": "ID", "NAME": "Name", "URL": "Url", "PROCESSING_STATUS": "Processing Status", "ACCOUNT": "Account", "EXECUTION_LOG": "Execution Log", "STATEMENTS": "Statements"}, "LIST": {"TITLE": "Statement Files"}, "NOTIFICATIONS": {"SCAN_FILES_SUCCESS": "Successfully started scan of Statements files.", "REPROCESS_FILES_SUCCESS": "Successfully started reprocessing of files.", "DOWNLOAD_FILES_SUCCESS": "Successfully Downloaded Statements Files {{id}}."}}}