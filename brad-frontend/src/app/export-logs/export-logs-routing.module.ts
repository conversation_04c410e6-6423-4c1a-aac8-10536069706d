import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {NavigationComponent} from "../navigation/navigation.component";
import {csFinAuthCanActivateGuard, CsFinAuthorizationOnAnyTargetOfTypeService} from "@jumia-cs-fin/common";
import {bradPermissions} from "../auth/constants/permission.constants";
import {bradAuthTarget} from "../auth/constants/auth.constants";
import {ExportLogsComponent} from "./export-logs.component";
import {ExportLogsDetailsComponent} from "./component/details/export-logs-details.component";

export const csFinAuthorizationOnAnyTargetOfTypeService =  new CsFinAuthorizationOnAnyTargetOfTypeService();
const routes: Routes = [
  {
    path: '',
    component: NavigationComponent,
    children: [
      {
        path: '',
        canActivate: [csFinAuthCanActivateGuard],
        data: {
          auth: {
            permissions: bradPermissions.BRAD_ACCESS_EXPORT_LOG,
            targets: bradAuthTarget,
            authTypeService: csFinAuthorizationOnAnyTargetOfTypeService,
          }
        },
        component: ExportLogsComponent,
        children: [
          {
            path: ':exportLogID',
            component: ExportLogsDetailsComponent
          }
        ]
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ExportLogsRoutingModule { }
