import {ChangeDetectorRef, Component, Input, On<PERSON><PERSON>es, OnDestroy, OnInit} from '@angular/core';
import {Subject} from "rxjs";
import {ExecutionLog} from "../../../../entities/execution-log/execution-log";
import {ExportLog} from "../../../../entities/export-log/export-log";
import {finalize, takeUntil} from "rxjs/operators";
import {FileDownload} from "../../../../entities/export-log/file-download";
import {HttpErrorResponse} from "@angular/common/http";
import {MediaMatcher} from "@angular/cdk/layout";
import {MatDialog} from "@angular/material/dialog";
import {ActivatedRoute, Router} from "@angular/router";
import {NotificationService} from "../../../../api/service/notification.service";
import {ExportLogFacade} from "../../../facade/export-log-facade";
import {CsFinAddRemoveColumnsFacade, CsFinLastUsedColumns} from "@jumia-cs-fin/common";

@Component({
  selector: 'brad-export-logs-details-info',
  templateUrl: './export-logs-details-info.component.html',
  styleUrls: ['./export-logs-details-info.component.scss']
})
export class ExportLogsDetailsInfoComponent implements OnInit, OnDestroy, OnChanges {

  @Input() detailedExportLog!: ExportLog;



  constructor(
    private notificationService: NotificationService,
    private exportLogFacade: ExportLogFacade
  ) {
  }

  isLoading = false;
  private _onDestroy: Subject<void> = new Subject<void>();

  ngOnInit(): void {
    this.isLoading = true;
  }

  ngOnChanges() {
    if(this.detailedExportLog){
      this.isLoading = false;
    }
  }

  ngOnDestroy(): void {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  downloadFile(): void {
    this.isLoading = true;

    this.exportLogFacade.download(this.detailedExportLog.id)
      .pipe(takeUntil(this._onDestroy))
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next:(result: FileDownload) => {
          window.open(result.url, '_blank');
        },
        error: (error:HttpErrorResponse) => {
          this.notificationService.errorWithResponse(error);
        }
      });
  }

}
