<div class="details-information">
  <div *ngIf="isLoading" class="loading">
    <mat-progress-spinner mode="indeterminate" diameter="75" strokeWidth="5"></mat-progress-spinner>
    <span class="label">{{'GENERAL.DETAILS.LOADING' | translate}}</span>
  </div>
  <div *ngIf="!isLoading && !detailedExportLog" class="detail-failed">
    <mat-icon>report_problem</mat-icon>
    <span class="label">{{'EXECUTION_LOGS.DETAILS.ERRORS.UNABLE_TO_FIND_DETAILS' | translate}}</span>
  </div>

  <main *ngIf="!isLoading && detailedExportLog">
    <section class="header">
      <button mat-mini-fab disabled>
        <mat-icon>compare</mat-icon>
      </button>
      <span class="item-name">{{detailedExportLog.id}}</span>
    </section>

    <ul class="description">
      <li>
        <span class="field" data-cy="execution-log-details-field-records_amount">{{'EXPORT_LOGS.FIELDS.ID' | translate}}: </span>
        <span class="value" data-cy="execution-log-details-value-records_amount">{{detailedExportLog.id}}</span>
      </li>
      <li>
        <span class="field" data-cy="execution-log-details-field-status">{{ 'EXPORT_LOGS.FIELDS.STATUS' | translate }}: </span>
        <span class="value" data-cy="execution-log-details-value-status">{{ 'EXPORT_LOGS.STATUS.' + detailedExportLog.status | translate }}</span>
      </li>
      <li>
        <span class="field" data-cy="execution-log-details-field-type">{{'EXPORT_LOGS.FIELDS.TYPE' | translate}}: </span>
        <span class="value" data-cy="execution-log-details-value-type">{{ 'EXPORT_LOGS.TYPE.' + detailedExportLog.type | translate}}</span>
      </li>
      <li>
        <span class="field" data-cy="execution-log-details-field-records_amount">{{'EXPORT_LOGS.FIELDS.RECORDS_AMOUNT' | translate}}: </span>
        <span class="value" data-cy="execution-log-details-value-records_amount">{{detailedExportLog.rowCount}}</span>
      </li>
      <li>
        <span class="field" data-cy="execution-log-details-field-records_amount">{{'EXPORT_LOGS.FIELDS.EXECUTION_TIME' | translate}}: </span>
        <span class="value" data-cy="execution-log-details-value-records_amount">{{detailedExportLog.executionTime | date:'HH:mm:ss'}}</span>
      </li>
      <li>
        <span class="field" data-cy="execution-log-details-field-execution-end-time">{{'EXPORT_LOGS.FIELDS.COUNTRIES' | translate}}: </span>
        <span class="value" data-cy="execution-log-details-value-execution-end-time">{{detailedExportLog.countries}}</span>
      </li>
      <li *ngIf="detailedExportLog.status == 'COMPLETED'">
        <span class="field" data-cy="execution-log-details-field-execution-end-time">{{'EXPORT_LOGS.FIELDS.FILE' | translate}}: </span>
        <mat-icon (click)="downloadFile()">download</mat-icon>
      </li>
      <li>
        <span class="field" data-cy="execution-log-details-field-execution-start-time">{{'GENERAL.FIELDS.CREATED_AT' | translate}}: </span>
        <span class="value" data-cy="execution-log-details-value-execution-start-time">{{detailedExportLog.createdAt | date:'medium'}}</span>
      </li>
      <li>
        <span class="field" data-cy="execution-log-details-field-execution-start-time">{{'GENERAL.FIELDS.CREATED_BY' | translate}}: </span>
        <span class="value" data-cy="execution-log-details-value-execution-start-time">{{detailedExportLog.createdBy}}</span>
      </li>
      <li>
        <span class="field" data-cy="execution-log-details-field-execution-start-time">{{'GENERAL.FIELDS.UPDATED_AT' | translate}}: </span>
        <span class="value" data-cy="execution-log-details-value-execution-start-time">{{detailedExportLog.updatedAt | date:'medium'}}</span>
      </li>
    </ul>

    <div class="payloads">
      <mat-expansion-panel *ngIf="detailedExportLog.filters">
        <mat-expansion-panel-header>
          <mat-panel-title>
            <div>
              <span class="page-title">{{'EXECUTION_LOGS.FIELDS.APPLIED_FILTERS' | translate}}:</span>
            </div>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <div class="code-action-card">
          <pre data-cy="execution-log-details-value-request">{{detailedExportLog.filters | jsonFormatter}}</pre>
        </div>
      </mat-expansion-panel><br>
    </div>
  </main>
</div>
