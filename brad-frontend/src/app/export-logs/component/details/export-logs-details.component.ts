import {Component, OnDestroy, OnInit} from '@angular/core';
import {Subject} from "rxjs";
import {ActivatedRoute, Params, Router} from "@angular/router";
import {takeUntil} from "rxjs/operators";
import {ExportLogFacade} from "../../facade/export-log-facade";
import {ExportLog} from "../../../entities/export-log/export-log";

@Component({
  selector: 'brad-export-logs-details',
  templateUrl: './export-logs-details.component.html',
  styleUrls: ['./export-logs-details.component.scss']
})
export class ExportLogsDetailsComponent implements OnInit, OnDestroy {

  isFullscreen = true;
  exportLogID!: number;
  exportLog!: ExportLog;

  private _onDestroy:Subject<void> = new Subject<void>();

  constructor(
    private exportLogFacade: ExportLogFacade,
    private router: Router,
    private route: ActivatedRoute,
  ) {
  }
  ngOnInit(): void {
    this.route.params
      .pipe(takeUntil(this._onDestroy))
      .subscribe((params: Params) => {
        this.exportLogID = Number(params['exportLogID']);
        if (this.exportLogID != null) {
          this.exportLogFacade.selectedExportLogChangeBehaviorSubject.next(this.exportLogID);
        }
      });

    this.exportLogFacade.selectedExportLogChangeBehaviorSubject
      .pipe(takeUntil(this._onDestroy))
      .subscribe((exportLogID: number) => {
        this.exportLogID = exportLogID;
        this.loadExportLogDetails();
      });
  }

  ngOnDestroy() {
    this._onDestroy.next();
    this._onDestroy.complete();
    this.exportLogFacade.detailsCloseBehaviorSubject.next(true);
  }

  onFullscreenClick(): void {
    this.isFullscreen = !this.isFullscreen;
    this.exportLogFacade.fullscreenChangeBehaviorSubject.next(this.isFullscreen);
  }

  onCloseClick(): void {
    this.exportLogFacade.detailsCloseBehaviorSubject.next(true);
    this.router.navigate(['export-logs'], {queryParams: this.route.snapshot.queryParams});
  }

  loadExportLogDetails(): void {
    if (this.exportLogID != null) {
      this.exportLogFacade.getById(this.exportLogID)
        .pipe(takeUntil(this._onDestroy))
        .subscribe((exportLog: ExportLog) => {
          this.exportLog = exportLog;
        });
    }

  }


}
