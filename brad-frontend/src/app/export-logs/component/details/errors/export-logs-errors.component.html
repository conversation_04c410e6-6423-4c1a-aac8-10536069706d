<div *ngIf="isLoading" class="loading">
  <mat-progress-spinner mode="indeterminate" diameter="75" strokeWidth="5"></mat-progress-spinner>
  <span class="label">{{'GENERAL.DETAILS.LOADING' | translate}}</span>
</div>
<main *ngIf="!isLoading && detailedExecutionLog">
  <div *ngFor="let error of detailedExecutionLog.errors">
    <ul class="description">
      <li>
        <span class="field" data-cy="execution-log-error-description">{{error.errorDescription}}</span>
      </li>
    </ul>
  </div>
</main>
