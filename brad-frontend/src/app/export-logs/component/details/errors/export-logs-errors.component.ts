import {Component, Input, On<PERSON>hanges, OnDestroy, OnInit} from '@angular/core';
import {ExecutionLog} from "../../../../entities/execution-log/execution-log";
import {Subject} from "rxjs";

@Component({
  selector: 'brad-export-logs-errors',
  templateUrl: './export-logs-errors.component.html',
  styleUrls: ['./export-logs-errors.component.scss']
})
export class ExportLogsErrorsComponent implements OnInit, OnDestroy, OnChanges {

  @Input() detailedExecutionLog!: ExecutionLog;

  isLoading = false;
  private _onDestroy: Subject<void> = new Subject<void>();

  ngOnInit(): void {
    if(!this.detailedExecutionLog) {
      this.isLoading = true;
    }
  }

  ngOnChanges() {
    if(this.detailedExecutionLog){
      this.isLoading = false;
    }
  }

  ngOnDestroy(): void {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

}
