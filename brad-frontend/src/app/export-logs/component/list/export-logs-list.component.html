<main class="container" cdkDropListGroup>
  <section class="filters" *ngIf="dataSource">
    <cs-fin-active-filters [chipsMap]="activeFilterChips"
                           (onRemove)="onActiveFilterRemoveClick($event)">
    </cs-fin-active-filters>
    <span fxFlex></span>
    <span class="actions">
      <button mat-stroked-button id="change-columns-btn" color="primary" fxShow [fxHide.xs]="true" [fxHide.sm]="true"
              [disabled]="executionLogDetailsOpened" cdkOverlayOrigin #trigger="cdkOverlayOrigin"
              (click)="triggerOverlay(trigger)">
        <mat-icon>table_rows</mat-icon>
      </button>
    </span>

  </section>
  <mat-progress-bar mode="indeterminate" *ngIf="isLoading"></mat-progress-bar>

  <section class="table-detail responsive-table"
           *ngIf="!isLoading"
           [class.detail-opened]="executionLogDetailsOpened"
           [class.fullscreen]="isExecutionLogDetailsInFullscreen">

    <div class="table-container" [class.detail-opened]="executionLogDetailsOpened"
         [class.fullscreen]="isExecutionLogDetailsInFullscreen">

      <mat-progress-bar mode="indeterminate" *ngIf="isLoading"></mat-progress-bar>

      <table mat-table class="cs-fin-table" matSort [dataSource]="dataSource"  [class.loading]="isLoading"
             (matSortChange)="onSortChange($event)" cdkDropList cdkDropListSortingDisabled>

        <ng-container matColumnDef="id">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'EXPORT_LOGS.FIELDS.ID' | translate }}</th>
          <td mat-cell *matCellDef="let exportLog"> {{exportLog?.id}} </td>
        </ng-container>

        <ng-container matColumnDef="status">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'EXPORT_LOGS.FIELDS.STATUS' | translate }}</th>
          <td mat-cell *matCellDef="let exportLog">
            <div class="status-container">
              <mat-icon [ngClass]="getStatusClass(exportLog.status)">circle</mat-icon>
              <span>{{ 'EXPORT_LOGS.STATUS.' + exportLog.status | translate }}</span>
            </div>
          </td>
        </ng-container>

        <ng-container matColumnDef="type">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'EXPORT_LOGS.FIELDS.TYPE' | translate }}</th>
          <td mat-cell *matCellDef="let exportLog">{{ 'EXPORT_LOGS.TYPE.' + exportLog?.type | translate}} </td>
        </ng-container>

        <ng-container matColumnDef="recordsAmount">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'EXPORT_LOGS.FIELDS.RECORDS_AMOUNT' | translate }}</th>
          <td mat-cell *matCellDef="let exportLog" >{{ isExportCompleted(exportLog) ? exportLog?.rowCount : '-'}}</td>
        </ng-container>

        <ng-container matColumnDef="executionTime">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'EXPORT_LOGS.FIELDS.EXECUTION_TIME' | translate }}</th>
          <td mat-cell *matCellDef="let exportLog">{{exportLog.executionTime | date:'HH:mm:ss'}}</td>
        </ng-container>

        <ng-container matColumnDef="countries">
          <th mat-header-cell *matHeaderCellDef>{{ 'EXPORT_LOGS.FIELDS.COUNTRIES' | translate }}</th>
          <td mat-cell *matCellDef="let exportLog">
            <span *ngFor="let country of exportLog.countries" class="imgContainer">
                          {{country}}&nbsp;
              </span>
          </td>
        </ng-container>

        <ng-container matColumnDef="createdAt">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'GENERAL.FIELDS.CREATED_AT' | translate }}</th>
          <td mat-cell *matCellDef="let exportLog">{{exportLog.createdAt | date:'short'}}</td>
        </ng-container>

        <ng-container matColumnDef="createdBy">
          <th mat-header-cell *matHeaderCellDef>{{ 'GENERAL.FIELDS.CREATED_BY' | translate }}</th>
          <td mat-cell *matCellDef="let exportLog">{{exportLog.createdBy}}</td>
        </ng-container>

        <ng-container matColumnDef="updatedAt">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'GENERAL.FIELDS.UPDATED_AT' | translate }}</th>
          <td mat-cell *matCellDef="let exportLog">{{exportLog.updatedAt | date:'short'}}</td>
        </ng-container>

        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef>{{ 'GENERAL.FIELDS.ACTIONS' | translate }}</th>
          <td mat-cell *matCellDef="let exportLog">
            <mat-icon (click)="onDownloadClick(exportLog, $event)" csFinHasPermissionOnAnyTarget
                      disabled="!isExportCompleted(exportLog)"
                      [ngClass]="{'action-disabled': !isExportCompleted(exportLog)}"
                      [authPermissions]="auth.permissions.BRAD_MANAGE_ACCOUNTS"
                      [authTarget]="bradAuthTarget"
                      [authAction]="auth.actions.HIDE">
              download
            </mat-icon>
          </td>
        </ng-container>


        <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"
            (click)="onOpenItemDetailsClick(row.id)"
            [class.item-opened]="isItemDetailsOpened(row.id)"
            class="with-detail" style="cursor: pointer;"></tr>
      </table>

      <mat-paginator [pageSizeOptions]="pagination.pageSizeOptions"
                     [pageSize]="pagination.pageSize"
                     [length]="pagination.totalItems"
                     [pageIndex]="pagination.pageIndex"
                     (page)="onPageChange($event)"
                     *ngIf="isLoading || dataSource.data.length > 0"
                     showFirstLastButtons>
      </mat-paginator>

      <span id="empty-table" ngClass="empty-table" *ngIf="!isLoading && !dataSource.data.length">
        {{'GENERAL.TABLE.NO_RESULTS_FOUND' | translate}}
      </span>
    </div>
    <div class="{{isExecutionLogDetailsInFullscreen ? 'details-full-screen' : 'details-container'}}"
         *ngIf="executionLogDetailsOpened">
      <router-outlet></router-outlet>
    </div>
  </section>

  <cs-fin-add-remove-columns [triggerOrigin]="triggerOrigin"
                             [isOverlayOpen$]="isOverlayOpen$"
                             [menu]="MENU">
  </cs-fin-add-remove-columns>
</main>
