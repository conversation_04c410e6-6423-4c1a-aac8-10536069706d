import {ChangeDetector<PERSON><PERSON>, <PERSON>mponent, OnDestroy, OnInit, ViewChild} from '@angular/core';
import {MatPaginator} from "@angular/material/paginator";
import {MatSort, SortDirection} from "@angular/material/sort";
import {MenusConsts} from "../../../shared/constants/menus.constants";
import {MatTableDataSource} from "@angular/material/table";
import {
  activeFiltersSeparator,
  CsFinActiveFilterChip,
  CsFinAddRemoveColumns,
  CsFinAddRemoveColumnsFacade,
  CsFinColumnDetails,
  CsFinLastUsedColumns,
  CsFinPagination,
  csFinPaginationConsts,
  CsFinSortingDataAccessorHelperService
} from "@jumia-cs-fin/common";
import {authParams, bradAuthTarget} from "../../../auth/constants/auth.constants";
import {Observable, Subject} from "rxjs";
import {CdkOverlayOrigin} from "@angular/cdk/overlay";
import {MediaMatcher} from "@angular/cdk/layout";
import {MatDialog} from "@angular/material/dialog";
import {ActivatedRoute, Router} from "@angular/router";
import {finalize, takeUntil} from "rxjs/operators";
import {PageResponse} from "../../../entities/page-response";
import * as _ from "lodash";
import {SortFilters} from "../../../entities/SortFilters";
import {ExecutionLogFilters} from "../../../entities/execution-log/execution-log-filters";
import {NotificationService} from "../../../api/service/notification.service";
import {HttpErrorResponse} from "@angular/common/http";
import {ExportLogFacade} from "../../facade/export-log-facade";
import {ExportLog} from "../../../entities/export-log/export-log";
import {FileDownload} from "../../../entities/export-log/file-download";
import {ExportLogFilters} from "../../../entities/export-log/export-log-filters";

@Component({
  selector: 'brad-export-logs-list',
  templateUrl: './export-logs-list.component.html',
  styleUrls: ['./export-logs-list.component.scss']
})
export class ExportLogsListComponent implements OnInit, OnDestroy {


  @ViewChild(MatPaginator, {static: true}) paginator!: MatPaginator;
  @ViewChild(MatSort, {static: true}) sort!: MatSort;

  readonly MENU = MenusConsts.executionLogs;

  isExecutionLogDetailsInFullscreen = false;
  lastOpenedExecutionLogDetailsID!: number | null;
  private displayedColumnsOnDetailsMode = ['id', 'type', 'status', 'recordsAmount'];
  private displayedColumnsOnFullscreenMode = ['id', 'type', 'status'];

  isLoading = true;
  dataSource: MatTableDataSource<ExportLog> = new MatTableDataSource<ExportLog>([]);
  displayedColumns: string[] = [];
  mobileQuery: MediaQueryList;
  pagination: CsFinPagination = {
    pageSizeOptions: csFinPaginationConsts.pageSizeOptions,
    pageSize: csFinPaginationConsts.defaultPageSize,
    pageIndex: 0,
    totalItems: 0
  };

  activeFilterChips!: Map<string, CsFinActiveFilterChip>;
  auth = authParams;
  executionLogDetailsOpened = false;

  isOverlayOpen$!: Observable<boolean>;
  triggerOrigin!: CdkOverlayOrigin;
  localStorageDetailSelectedTabVariableName = 'executionLogDetailsTabGroupSelectedIndex';
  private _mobileQueryListener: () => void;
  private _onDestroy: Subject<void> = new Subject<void>();

  protected readonly bradAuthTarget = bradAuthTarget;
  private filters: ExecutionLogFilters = {
    page: 1,
    size: csFinPaginationConsts.defaultPageSize
  };
  private lastDisplayedColumns!: string[];

  filterButtonActive = false;

  constructor(
    public ref: ChangeDetectorRef,
    public media: MediaMatcher,
    private dialog: MatDialog,
    private route: ActivatedRoute,
    private router: Router,
    private notificationService: NotificationService,
    private exportLogFacade: ExportLogFacade,
    private addRemoveColumnsFacade: CsFinAddRemoveColumnsFacade
  ) {
    this.mobileQuery = media.matchMedia('(max-width: 960px)');
    this._mobileQueryListener = () => ref.detectChanges();
    this.mobileQuery?.addEventListener('change', this._mobileQueryListener);

    CsFinLastUsedColumns.getInstance().initColumns(this.MENU, this.getColumnDetails());
  }

  ngOnInit(): void {
    this.isLoading = true;
    this.subscribeFiltersChange();
    this.subscribeActiveFilterChipsChange();
    this.subscribeDisplayedColumnsChange();
    this.subscribeFullscreenChange();
    this.subscribeSelectedExecutionLogChange()
    this.subscribeDetailsCloseChange();

    this.displayedColumns = this.displayedColumnsOnFullscreenMode;
  }


  ngOnDestroy(): void {
    this.exportLogFacade.filtersChanged({});
    this.mobileQuery?.removeEventListener('change', this._mobileQueryListener);
    this._onDestroy.next();
    this._onDestroy.complete();
    this.addRemoveColumnsFacade.isOverlayOpen.next(false);
    this.exportLogFacade.selectedExportLogChangeBehaviorSubject.next(-1);

  }

  private subscribeFiltersChange(): void {
    this.exportLogFacade.filters$
      .pipe(takeUntil(this._onDestroy))
      .subscribe(async (filters: ExportLogFilters) => {
        if (Object.keys(filters).length > 0 && this.isInExportLogsScreen()) {
          this.closeDetailsSliderIfOpened();
          this.filters = filters;
          this.loadExportLogs();
        }
      });
  }

  private closeDetailsSliderIfOpened(): void {
    if (this.lastOpenedExecutionLogDetailsID) {
      this.closeExecutionLogDetails();
    }
  }

  private isInExportLogsScreen(): boolean {
    return window.location.href.includes('export-logs');
  }

  private subscribeActiveFilterChipsChange(): void {

    this.exportLogFacade.activeFiltersChips$
      .pipe(takeUntil(this._onDestroy))
      .subscribe((activeFilterChips: Map<string, CsFinActiveFilterChip>) => {
        this.activeFilterChips = activeFilterChips;
      });

  }


  private subscribeDisplayedColumnsChange(): void {
    this.addRemoveColumnsFacade.fetchDisplayedColumns
      .pipe(takeUntil(this._onDestroy))
      .subscribe((columns: CsFinAddRemoveColumns) => {
        setTimeout(() => {
            if (!this.executionLogDetailsOpened) {
              this.lastDisplayedColumns = this.displayedColumns;
              this.displayedColumns = columns.displayedColumns ? columns.displayedColumns : [];
            } else {
              this.lastDisplayedColumns = this.displayedColumns;
              this.displayedColumns = this.displayedColumnsOnDetailsMode;
            }

          }
          , 0);
      });
  }

  private subscribeFullscreenChange(): void {
    this.exportLogFacade.fullscreenChangeBehaviorSubject
      .pipe(takeUntil(this._onDestroy))
      .subscribe((isFullscreen: boolean) => {
        this.handleFullscreenChange(isFullscreen);
      });
  }

  private subscribeSelectedExecutionLogChange(): void {
    this.exportLogFacade.selectedExportLogChangeBehaviorSubject
      .pipe(takeUntil(this._onDestroy))
      .subscribe((executionLogID: number) => {
        if (executionLogID > 0 && this.lastOpenedExecutionLogDetailsID != executionLogID) {
          setTimeout(() => {
            this.onOpenItemDetailsClick(executionLogID);
            this.ref.markForCheck();
          }, 0);
        }
      });
  }

  private subscribeDetailsCloseChange(): void {
    this.exportLogFacade.detailsCloseBehaviorSubject
      .pipe(takeUntil(this._onDestroy))
      .subscribe((isClosed: boolean) => {
        if (isClosed) {
          this.closeExecutionLogDetails();
        }
      });
  }


  loadExportLogs() {
    this.exportLogFacade.getAll(this.filters)
      .pipe(takeUntil(this._onDestroy))
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next:(result: PageResponse<ExportLog>) => {
          this.dataSource = new MatTableDataSource<ExportLog>(result.results);
          this.dataSource.sort = this.sort;
          this.dataSource.paginator = this.paginator;
          this.dataSource.sortingDataAccessor = CsFinSortingDataAccessorHelperService.objectSortingDataAccessor;
          this.pagination.totalItems = result.total;
          this.pagination.pageSize = result.size;
          this.pagination.pageIndex = result.page - 1;
          this.setSort();
        },
        error: (error:HttpErrorResponse) => {
          this.notificationService.errorWithResponse(error);
        }
      });
  }

  triggerOverlay(trigger: CdkOverlayOrigin): void {
    this.triggerOrigin = trigger;
    this.addRemoveColumnsFacade.isOverlayOpen.next(true);
  }

  private handleFullscreenChange(isFullscreen: boolean): void {
    this.isExecutionLogDetailsInFullscreen = isFullscreen;
    if (this.isExecutionLogDetailsInFullscreen) {
      this.displayedColumns = this.displayedColumnsOnFullscreenMode
    } else {
      this.displayedColumns = this.displayedColumnsOnDetailsMode;
    }
    this.ref.markForCheck();
  }

  getColumnDetails(): CsFinColumnDetails[] {
    return [
      {position: 0, name: 'ID', code: 'id', isActive: true, isRemovable: false, isDefault: true},
      {position: 1, name: 'Status', code: 'status', isActive: true, isRemovable: true, isDefault: true},
      {position: 2, name: 'Type', code: 'type', isActive: true, isRemovable: true, isDefault: true},
      {position: 3, name: 'Records Amount', code: 'recordsAmount', isActive: true, isRemovable: true, isDefault: true},
      {position: 4, name: 'Execution Time', code: 'executionTime', isActive: true, isRemovable: true, isDefault: true},
      {position: 5, name: 'Countries', code: 'countries', isActive: true, isRemovable: true, isDefault: true},
      {position: 6, name: 'Created At', code: 'createdAt', isActive: true, isRemovable: true, isDefault: true},
      {position: 7, name: 'Created By', code: 'createdBy', isActive: true, isRemovable: true, isDefault: true},
      {position: 8, name: 'Updated At', code: 'updatedAt', isActive: false, isRemovable: true, isDefault: false},
      {position: 9, name: 'Actions', code: 'actions', isActive: true, isRemovable: false, isDefault: true},
    ];
  }

  onPageChange(event: any) {
    this.filters.page = event.pageIndex + 1;
    this.filters.size = event.pageSize;
    this.exportLogFacade.filtersChanged(this.filters);
  }

  onActiveFilterRemoveClick(removedChipKey: string): void {
    const filtersToRemove = removedChipKey.split(activeFiltersSeparator);
    if (filtersToRemove) {
      filtersToRemove.forEach((filterKey: string) => {
        // @ts-ignore
        delete this.filters[filterKey];
      })
    }
    this.exportLogFacade.filtersChanged(this.filters);
  }

  private setSort(): void {
    if (!this.sort) {
      return;
    }

    const previousSort = {
      active: this.sort.active,
      direction: this.sort.direction
    };
    if (this.filters.orderField) {
      this.sort.active = this.decodeSortField(this.filters.orderField);
    }
    if (this.filters.orderDirection) {
      this.sort.direction = this.filters.orderDirection.toLowerCase() as SortDirection;
    }

    const sort = {
      active: this.sort.active,
      direction: this.sort.direction
    };
    if (!_.isEqual(previousSort, sort)) {
      this.sort.sortChange.emit(this.sort);
    }
  }

  onSortChange(event: any): void {
    const sortFiltersBefore = {
      orderDirection: this.filters.orderDirection,
      orderField: this.filters.orderField
    };

    this.filters.orderDirection = event.direction?.toUpperCase();
    this.filters.orderField = this.encodeSortField(event.active);

    if (!_.isEqual(sortFiltersBefore, this.filters as SortFilters)) {
      this.exportLogFacade.filtersChanged(this.filters);
    }

  }

  private encodeSortField(field: string): string {
    switch (field) {
      case 'id':
        return 'ID';
      case 'status':
        return 'STATUS';
      case 'type':
        return 'TYPE';
      case 'recordsAmount':
        return 'ROW_COUNT';
      case 'executionTime':
        return 'EXECUTION_TIME';
      case 'createdAt':
        return 'CREATED_AT';
      case 'updatedAt':
        return 'UPDATED_AT';
      default:
        return field.toUpperCase();
    }
  }

  private decodeSortField(field: string): string {
    switch (field) {
      case 'ID':
        return 'id';
      case 'STATUS':
        return 'status';
      case 'TYPE':
        return 'type';
      case 'RECORDS_AMOUNT':
        return 'recordsAmount';
      case 'EXECUTION_START_TIME':
        return 'executionStartTime';
      case 'EXECUTION_END_TIME':
        return 'executionEndTime';
      default:
        return field.toLowerCase();
    }
  }

  onOpenItemDetailsClick(executionLogID: number): void {
    this.isExecutionLogDetailsInFullscreen = false;

    if (this.executionLogDetailsOpened) {
      this.resetSelectedDetailsTab();
    }
    if (!this.executionLogDetailsOpened) {
      this.executionLogDetailsOpened = true;
      this.lastDisplayedColumns = this.displayedColumns;
      this.displayedColumns = this.displayedColumnsOnFullscreenMode;

    } else if (this.lastOpenedExecutionLogDetailsID === executionLogID) {
      this.router.navigate(['.'], {queryParamsHandling: 'preserve', relativeTo: this.route});
      this.closeExecutionLogDetails();
      return;
    }
    this.lastOpenedExecutionLogDetailsID = executionLogID;
    this.router.navigate([`./${executionLogID}`], {queryParamsHandling: 'preserve', relativeTo: this.route});

  }

  resetSelectedDetailsTab(): void {
    const detailTabGroupSelectedIndex = localStorage.getItem(this.localStorageDetailSelectedTabVariableName);
    if (detailTabGroupSelectedIndex === '1')
      localStorage.setItem(this.localStorageDetailSelectedTabVariableName, '0');
  }

  closeExecutionLogDetails(): void {
    this.executionLogDetailsOpened = false;
    this.displayedColumns = this.lastDisplayedColumns;
    this.lastOpenedExecutionLogDetailsID = null;
  }

  isItemDetailsOpened(executionLogID: number): boolean {
    return executionLogID === this.lastOpenedExecutionLogDetailsID;
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'PENDING':
        return 'status-pending';
      case 'RUNNING':
        return 'status-running';
      case 'FAILED':
        return 'status-failed';
      case 'COMPLETED':
        return 'status-completed';
      default:
        return '';
    }
  }

  isExportCompleted(log: ExportLog): boolean {
    return log.status === 'COMPLETED';
  }

  onDownloadClick(log: ExportLog, event:MouseEvent) {
    event.stopPropagation();
    if (log.status !== 'COMPLETED') {
      return;
    }

    this.isLoading = true;

    this.exportLogFacade.download(log.id)
      .pipe(takeUntil(this._onDestroy))
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next:(result: FileDownload) => {
          window.open(result.url, '_blank');
        },
        error: (error:HttpErrorResponse) => {
          this.notificationService.errorWithResponse(error);
        }
      });
  }

}
