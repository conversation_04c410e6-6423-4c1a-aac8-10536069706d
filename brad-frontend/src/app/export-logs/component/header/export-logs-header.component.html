<mat-toolbar id="header-toolbar">
  <button mat-icon-button (click)="sidenav.toggle()" fxHide [fxShow.xs]="true" [fxShow.sm]="true">
    <mat-icon>menu</mat-icon>
  </button>
  <span class="page-title">{{ 'EXPORT_LOGS.TITLE' | translate }}</span>

  <section class="search-bar" [formGroup]="form">
    <!-- filter text (code) -->
    <mat-form-field id="search" class="change-header" appearance="outline" fxShow [fxShow.xs]="false" [fxShow.sm]="false"
                    cdkOverlayOrigin #trigger="cdkOverlayOrigin">
      <mat-icon matPrefix>search</mat-icon>
      <mat-label class="theLabel">{{'EXPORT_LOGS.DETAILS.SEARCH_BAR' | translate}}</mat-label>
      <input type="text" #input matInput [formControl]="filterTextFormControl"
             (keyup.enter)="submit(input)">
      <mat-icon matSuffix id="filters" (click)="triggerOverlay()">filter_list</mat-icon>
    </mat-form-field>

    <button mat-raised-button color="primary" id="apply-search-btn" class="raised-primary-btn" fxShow [fxShow.xs]="false"
            [fxShow.sm]="false" [disabled]="noFiltersSelected()" (click)="submit()">
      {{ 'GENERAL.BUTTONS.LABELS.SEARCH' | translate }}
    </button>
  </section>

  <span fxFlex fxHide [fxShow.xs]="true" [fxShow.sm]="true"></span>
  <button mat-icon-button aria-label="Filter accounts" fxHide [fxShow.xs]="true" [fxShow.sm]="true"
          *ngIf="showFilters" id="show-mobile-filters">
    <mat-icon (click)="triggerOverlay()">filter_list</mat-icon>
  </button>
  <ng-template cdkConnectedOverlay class="template"
               [cdkConnectedOverlayHasBackdrop]="true"
               [cdkConnectedOverlayOrigin]="trigger"
               [cdkConnectedOverlayOpen]="(isOpen$ | async) || false">
    <div class="filters-overlay">
      <div class="filters-header">
        <mat-icon fxHide [fxShow.xs]="true" [fxShow.sm]="true" (click)="closeOverlay()">close</mat-icon>
        <p class="filters-title">{{'GENERAL.FILTERS.TITLE' | translate}}</p>
        <button fxHide class="flat-white-btn filters-cancel-btn" [disabled]="noFiltersSelected()" [fxShow.xs]="true"
                [fxShow.sm]="true"
                mat-flat-button (click)="clearFilters()" id="clear-btn">
          {{ 'GENERAL.BUTTONS.CLEAR' | translate }}
        </button>
      </div>

      <div class="filters-container">

        <!-- log type -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-type-entity-filter"
                        (click)="loadLogTypes()">
          <mat-label>{{'EXECUTION_LOGS.FIELDS.TYPE' | translate}}</mat-label>
          <mat-select [formControl]="logTypesFormControl">
            <ng-container *ngIf="filteredLogTypesList != null; else loadingLogType">
              <mat-option>
                <ngx-mat-select-search [formControl]="logTypesSearchFormControl"
                                       [noEntriesFoundLabel]="'GENERAL.FILTERS.NO_MATCHING_RESULTS_FOUND' | translate"
                                       [placeholderLabel]="'GENERAL.FILTERS.SEARCH' | translate">
                </ngx-mat-select-search>
              </mat-option>
              <mat-option *ngFor="let type of filteredLogTypesList" [value]="type">
                {{ 'EXPORT_LOGS.TYPE.' + type | translate }}
              </mat-option>
              <button mat-button color="primary" class="clear-selection-btn"
                      [disabled]="!logTypesFormControl.value?.length"
                      (click)="logTypesFormControl.reset([])">
                {{'GENERAL.FILTERS.ACTIONS.CLEAR_SELECTION' | translate}}
              </button>
            </ng-container>
            <ng-template #loadingLogType>
              <mat-option disabled>
                <div class="filters-loading-container">
                  <span>{{'GENERAL.FILTERS.LOADING' | translate}}</span>
                  <mat-spinner diameter="20"></mat-spinner>
                </div>
              </mat-option>
            </ng-template>
          </mat-select>
        </mat-form-field>

        <!-- log status -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-status-entity-filter"
                        (click)="loadLogStatuses()">
          <mat-label>{{'EXECUTION_LOGS.FIELDS.STATUS' | translate}}</mat-label>
          <mat-select [formControl]="logStatusesFormControl">
            <ng-container *ngIf="filteredLogStatusesList != null; else loadingLogStatus">
              <mat-option>
                <ngx-mat-select-search [formControl]="logStatusesSearchFormControl"
                                       [noEntriesFoundLabel]="'GENERAL.FILTERS.NO_MATCHING_RESULTS_FOUND' | translate"
                                       [placeholderLabel]="'GENERAL.FILTERS.SEARCH' | translate">
                </ngx-mat-select-search>
              </mat-option>
              <mat-option *ngFor="let status of filteredLogStatusesList" [value]="status">
                {{ 'EXPORT_LOGS.STATUS.' + status | translate }}
              </mat-option>
              <button mat-button color="primary" class="clear-selection-btn"
                      [disabled]="!logStatusesFormControl.value?.length"
                      (click)="logStatusesFormControl.reset([])">
                {{'GENERAL.FILTERS.ACTIONS.CLEAR_SELECTION' | translate}}
              </button>
            </ng-container>
            <ng-template #loadingLogStatus>
              <mat-option disabled>
                <div class="filters-loading-container">
                  <span>{{'GENERAL.FILTERS.LOADING' | translate}}</span>
                  <mat-spinner diameter="20"></mat-spinner>
                </div>
              </mat-option>
            </ng-template>
          </mat-select>
        </mat-form-field>

        <!-- country -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-country-entity-filter"
                        (click)="loadCountries()">
          <mat-label>{{'ACCOUNTS.FIELDS.COUNTRY' | translate}}</mat-label>
          <mat-select [formControl]="countryFormControl" [compareWith]="compareIdFn" multiple>
            <ng-container *ngIf="filteredCountryList != null; else loadingCountry">
              <mat-option>
                <ngx-mat-select-search [formControl]="countrySearchFormControl"
                                       [noEntriesFoundLabel]="'GENERAL.FILTERS.NO_MATCHING_RESULTS_FOUND' | translate"
                                       [placeholderLabel]="'GENERAL.FILTERS.SEARCH' | translate">
                </ngx-mat-select-search>
              </mat-option>
              <mat-option *ngFor="let country of filteredCountryList" [value]="country">
                <span class="country-name-flag">{{country.name}} <cs-fin-flag [countryCode]="country.code"></cs-fin-flag></span>
              </mat-option>
              <button mat-button color="primary" class="clear-selection-btn"
                      [disabled]="!countryFormControl.value?.length"
                      (click)="countryFormControl.reset([])">
                {{'GENERAL.FILTERS.ACTIONS.CLEAR_SELECTION' | translate}}
              </button>
            </ng-container>
            <ng-template #loadingCountry>
              <mat-option disabled>
                <div class="filters-loading-container">
                  <span>{{'GENERAL.FILTERS.LOADING' | translate}}</span>
                  <mat-spinner diameter="20"></mat-spinner>
                </div>
              </mat-option>
            </ng-template>
          </mat-select>
        </mat-form-field>

        <!-- execution end time -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-execution-end-time-filter">
          <mat-label>{{'GENERAL.FIELDS.CREATED_AT' | translate}}</mat-label>
          <mat-date-range-input [rangePicker]="datePickerCreationDate">
            <input matStartDate [placeholder]="'GENERAL.FILTERS.DATEPICKERS.FROM' | translate"
                   [formControl]="executionStartTimeFormControl">
            <input matEndDate [placeholder]="'GENERAL.FILTERS.DATEPICKERS.TO' | translate"
                   [formControl]="executionEndTimeFormControl">
          </mat-date-range-input>
          <mat-datepicker-toggle matSuffix [for]="datePickerCreationDate"></mat-datepicker-toggle>
          <mat-date-range-picker #datePickerCreationDate></mat-date-range-picker>
        </mat-form-field>
      </div>

      <div class="filters-actions">
        <button class="flat-white-btn filters-cancel-btn" [disabled]="noFiltersSelected()" mat-flat-button fxShow
                (click)="clearFilters()" [fxShow.xs]="false" [fxShow.sm]="false">
          {{'GENERAL.FILTERS.ACTIONS.CLEAR' | translate}}
        </button>
        <button class="raised-primary-btn filters-apply-btn" [disabled]="noFiltersSelected()" mat-raised-button
                color="primary"
                (click)="submit()">
          {{'GENERAL.FILTERS.ACTIONS.APPLY' | translate}}
        </button>
      </div>

    </div>

  </ng-template>

</mat-toolbar>
