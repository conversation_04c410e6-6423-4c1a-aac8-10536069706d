import {ChangeDetectorRef, Component, Input, OnDestroy, OnInit, ViewChild, ViewEncapsulation} from '@angular/core';
import {FormControl, FormGroup} from "@angular/forms";
import {BehaviorSubject, Observable, Subject, tap} from "rxjs";
import {CdkConnectedOverlay} from "@angular/cdk/overlay";
import {CsFinActiveFiltersFacade, CsFinApiService, CsFinSidenavService} from "@jumia-cs-fin/common";
import {ActivatedRoute, Router} from "@angular/router";
import {takeUntil} from "rxjs/operators";
import * as _ from "lodash";
import {ExportLogFacade} from "../../facade/export-log-facade";
import {ExportLogFilters} from "../../../entities/export-log/export-log-filters";
import {EnumCode} from "../../../entities/enum-code";
import {Country} from "../../../entities/account/country";
import {CountryApiService} from "../../../api/service/country-api.service";

@Component({
  selector: 'brad-export-logs-header',
  templateUrl: './export-logs-header.component.html',
  styleUrls: ['./export-logs-header.component.scss','../../../../assets/brad-custom.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ExportLogsHeaderComponent implements OnInit, OnDestroy {
  @Input() showFilters!: boolean;
  filterTextFormControl!: FormControl;
  queryParams = {};

  form!: FormGroup;

  executionStartTimeFormControl!: FormControl;
  executionEndTimeFormControl!: FormControl;
  logStatusesFormControl!: FormControl;
  logTypesFormControl!: FormControl;
  countryFormControl!: FormControl;

  logStatusesSearchFormControl = new FormControl();
  logStatusesList:string[] = [];
  filteredLogStatusesList:string[] = [];

  countrySearchFormControl = new FormControl();
  countryList: Country[] = [];
  filteredCountryList: Country[] = [];

  logTypesSearchFormControl = new FormControl();
  logTypesList:string[] = [];
  filteredLogTypesList:string[] = [];


  isOpen$!: Observable<boolean>;
  isRefreshing = false;
  isInitializing = false;


  private readonly refreshTimeout = 2000;
  private _onDestroy: Subject<void> = new Subject<void>();
  private _isOpen!: BehaviorSubject<boolean>;

  @ViewChild(CdkConnectedOverlay, {static: true})
  private connectedOverlay!: CdkConnectedOverlay;

  constructor(public ref: ChangeDetectorRef,
              public sidenav: CsFinSidenavService,
              private activatedRoute: ActivatedRoute,
              private activeFiltersFacade: CsFinActiveFiltersFacade,
              private router: Router,
              private apiService: CsFinApiService,
              private countryApiService: CountryApiService,
              private exportLogFacade: ExportLogFacade) { }

  async ngOnInit(): Promise<void> {
    this.isInitializing = true;
    this.initializeOverlay();
    this.initFiltersSearch();
    this.subscribeUrlParamsChanges();
    this.subscribeActiveFiltersChange();
    this.subscribeFiltersChange();
  }

  ngOnDestroy(): void {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  private subscribeUrlParamsChanges(): void {
    this.activatedRoute.queryParams
      .pipe(takeUntil(this._onDestroy))
      .subscribe(params => this.initializeFormData(params));
  }

  private subscribeActiveFiltersChange(): void {
    this.activeFiltersFacade.openFiltersOverlay
      .pipe(takeUntil(this._onDestroy))
      .subscribe(value => this._isOpen.next(value));
  }

  private subscribeFiltersChange(): void {
    this.exportLogFacade.filters$
      .pipe(takeUntil(this._onDestroy))
      .subscribe((filters:ExportLogFilters) => {
        if(!this.isInitializing) {
          this.updateFormData(filters);
          this.updateMissingUrlFilters(filters);
        }
      });
  }

  private initFiltersSearch(): void {
    this.initLogTypesSearch();
    this.initLogStatusesSearch();
  }


  private initializeOverlay(): void {
    this._isOpen = new BehaviorSubject<boolean>(false);
    this.connectedOverlay.backdropClass = 'cdk-overlay-transparent-backdrop';
    this.connectedOverlay.panelClass = 'filters-overlay-panel';
    this.connectedOverlay.backdropClick
      .pipe(tap(() => this._isOpen.next(false)))
      .subscribe();
    this.isOpen$ = this._isOpen.asObservable();
  }

  private initLogTypesSearch(): void {
    this.logTypesSearchFormControl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe((value: string) => {
        const searchTerm = value.trim();
        if (searchTerm) {
          this.filteredLogTypesList = this.logTypesList.filter((type) => {
            return type.toLowerCase().includes(searchTerm.toLowerCase());
          })
        } else {
          this.filteredLogTypesList = this.logTypesList;
        }
      });
  }

  private initLogStatusesSearch(): void {
    this.logStatusesSearchFormControl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe((value: string) => {
        const searchTerm = value.trim();
        if (searchTerm) {
          this.filteredLogStatusesList = this.logStatusesList.filter((status) => {
            return status.toLowerCase().includes(searchTerm.toLowerCase());
          })
        } else {
          this.filteredLogStatusesList = this.logStatusesList;
        }
      });
  }

  clearFilters(): void {
    this.form.reset('', {emitEvent: false});
    this.exportLogFacade.filtersChanged({});
  }

  submit(input?: HTMLInputElement) {
    this.exportLogFacade.filtersChanged(this.getFormValues());
    const formQueryParams = this.apiService.buildQueryParams(this.getFormValues());
    if (_.isEqual(this.queryParams, formQueryParams)) {
      this.isRefreshing = true;
      this.ref.markForCheck();
      setTimeout(() => {
        this.isRefreshing = false;
        this.ref.markForCheck();
      }, this.refreshTimeout);
    } else {
      input?.blur();
      this.router.navigate(['/export-logs'], {queryParams: formQueryParams});
    }
    this.closeOverlay();

  }

  triggerOverlay(): void {
    this._isOpen.next(!this._isOpen.value);
  }

  closeOverlay(): void {
    this._isOpen.next(false);
  }

  private getFormValues(): ExportLogFilters {
    return this.form.value as ExportLogFilters;
  }

  private initializeFormData(params:any): void {
    this.queryParams = params;
    if(!this.form) {
      this.form = new FormGroup({});
      this.initializeFormControlsAndFilters(params);
    }
  }

  private initializeFormControlsAndFilters(params:any): void {
    const filters: ExportLogFilters = {};

    this.filterTextFormControl = new FormControl(params.filterText);
    filters.filterText = params.filterText;

    let types: string[] = params.types === undefined ? undefined : params.types.split(',');
    this.logTypesFormControl = new FormControl(types);
    filters.types = types;

    let status: string[] = params.status === undefined ? undefined : params.status.split(',');
    this.logStatusesFormControl = new FormControl(status);
    filters.status = status;

    this.executionStartTimeFormControl = new FormControl(params.createdAtStart);
    filters.createdAtFrom = params.createdAtStart;

    this.executionEndTimeFormControl = new FormControl(params.createdAtEnd);
    filters.createdAtTo = params.createdAtEnd;

    filters.orderField = params.orderField;
    filters.orderDirection = params.orderDirection;
    filters.page = params.page;
    filters.size = params.size;

    Promise.all([
      this.applyCountryUrlFilter(params, filters),
      this.applyLogTypesUrlFilter(params, filters),
      this.applyLogStatusesUrlFilter(params, filters)
    ]).then(() => {
      this.setFormControlsToForm();
      this.exportLogFacade.filtersChanged(filters);
      this.updateMissingUrlFilters(filters);
      this.isInitializing = false;
    });

  }


  private applyCountryUrlFilter(params: any, filters: ExportLogFilters): Promise<void> {
    return new Promise<void>(async (resolve) => {
      this.countryFormControl = new FormControl();
      if (!params.countryCodes) {
        resolve();
        return;
      }

      await this.loadCountries();
      const filterCountriesIds = params.countryCodes.split(',')?.map((countryId: string) => Number(countryId));
      if (filterCountriesIds) {
        filters.countryCodes = this.countryList.filter((country: Country) => filterCountriesIds.includes(country.id));
        this.countryFormControl = new FormControl(filters.countryCodes);
      }
      resolve();
    });
  }

  private applyLogTypesUrlFilter(params: any, filters: ExportLogFilters): Promise<void> {
    return new Promise<void>(async (resolve) => {
      this.logTypesFormControl = new FormControl();
      if(!params.logTypes) {
        resolve();
        return;
      }

      await this.loadLogTypes();
      let logTypesArray = Array.isArray(params.logTypes?.split(',')) ?
        params.logTypes?.split(',') :
        [params.logTypes];
      filters.types = this.logTypesList.filter((type: string) => logTypesArray.includes(type));
      this.logTypesFormControl.setValue(filters.types)
      resolve();
    });
  }

  private applyLogStatusesUrlFilter(params: any, filters: ExportLogFilters): Promise<void> {
    return new Promise<void>(async (resolve) => {
      this.logStatusesFormControl = new FormControl();

      if(!params.logStatuses) {
        resolve();
        return;
      }

      await this.loadLogStatuses();
      let logStatusesArray = Array.isArray(params.logStatuses?.split(',')) ?
        params.logStatuses?.split(',') :
        [params.logStatuses];
      filters.status = this.logStatusesList.filter((status: string) => logStatusesArray.includes(status));
      this.logStatusesFormControl.setValue(filters.status)
      resolve();
    });
  }

  loadLogTypes(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if(this.logTypesList.length === 0) {
        this.exportLogFacade.getLogTypes()
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (types: EnumCode[]) => {
              this.logTypesList = types.map((type: EnumCode) => type.code);
              this.filteredLogTypesList = this.logTypesList;
              resolve();
            }, error: (error) => reject(error)
          });
      } else {
        resolve();
      }
    });
  }

  loadLogStatuses(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if(this.logStatusesList.length === 0) {
        this.exportLogFacade.getLogStatus()
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (status: EnumCode[]) => {
              this.logStatusesList = status.map((status: EnumCode) => status.code);
              this.filteredLogStatusesList = this.logStatusesList;
              resolve();
            }, error: (error) => reject(error)
          });
      } else {
        resolve();
      }
    });
  }

  loadCountries(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if (this.countryList.length <= 0) {
        this.countryApiService.getAll()
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (countries: Country[]) => {
              this.countryList = countries;
              this.filteredCountryList = this.countryList;
              resolve();
            }, error: (error) => reject(error)
          });
      } else {
        resolve();
      }
    });
  }

  private updateFormData (params: ExportLogFilters): void {
    this.filterTextFormControl.setValue(params.filterText, {emitEvent: false});
    this.countryFormControl.setValue(params.countryCodes, {emitEvent: false});
    this.executionStartTimeFormControl.setValue(params.createdAtFrom, {emitEvent: false});
    this.executionEndTimeFormControl.setValue(params.createdAtTo, {emitEvent: false});
    this.logTypesFormControl.setValue(params.types, {emitEvent: false});
    this.logStatusesFormControl.setValue(params.status, {emitEvent: false});
  }

  private updateMissingUrlFilters(filters: ExportLogFilters): void {
    const formQueryParams = this.apiService.buildQueryParams(filters);
    if (!_.isEqual(this.queryParams, formQueryParams)) {
      this.router.navigate(['export-logs'], {queryParams: formQueryParams});
    }
  }

  private setFormControlsToForm(): void {
    this.form.addControl(this.exportLogFacade.filterTextKey, this.filterTextFormControl);
    this.form.addControl(this.exportLogFacade.countryKey, this.countryFormControl);
    this.form.addControl(this.exportLogFacade.createdAtStartKey, this.executionStartTimeFormControl);
    this.form.addControl(this.exportLogFacade.createdAtEndKey, this.executionEndTimeFormControl);
    this.form.addControl(this.exportLogFacade.logTypesKey, this.logTypesFormControl);
    this.form.addControl(this.exportLogFacade.logStatusesKey, this.logStatusesFormControl);
  }

  noFiltersSelected() {
    return !!this.form.errors || this.isRefreshing;
  }

  compareIdFn(o1: any, o2: any): boolean {
    return o1 && o2 && o1.id === o2.id;
  }

}
