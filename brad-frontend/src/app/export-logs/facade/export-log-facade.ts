import {Injectable} from "@angular/core";
import {BehaviorSubject, Observable, of, Subject} from "rxjs";
import {getPropertyKey} from "../../shared/service/ts-utils.service";
import {activeFiltersSeparator, CsFinActiveFilterChip} from "@jumia-cs-fin/common";

import {PageResponse} from "../../entities/page-response";
import {ExecutionLogFilters} from "../../entities/execution-log/execution-log-filters";
import {ExportLogFilters} from "../../entities/export-log/export-log-filters";
import {ExportLog} from "../../entities/export-log/export-log";
import {ExportLogApiService} from "../../api/service/export-log-api.service";
import {FileDownload} from "../../entities/export-log/file-download";
import {EnumCode} from "../../entities/enum-code";
import {AccountFilters} from "../../entities/account/account-filters";
import {Country} from "../../entities/account/country";

@Injectable({providedIn: 'root'})
export class ExportLogFacade {

  forceRefresh: Subject<boolean> = new Subject<boolean>();

  public fullscreenChangeBehaviorSubject = new BehaviorSubject<boolean>(true);
  public detailsCloseBehaviorSubject = new BehaviorSubject<boolean>(false);
  public selectedExportLogChangeBehaviorSubject = new BehaviorSubject<number>(-1);

  readonly filterTextKey = getPropertyKey<ExportLogFilters>(p => p.filterText);
  readonly countryKey = getPropertyKey<ExportLogFilters>(p => p.countryCodes);
  readonly createdAtStartKey = getPropertyKey<ExportLogFilters>(p => p.createdAtFrom);
  readonly createdAtEndKey = getPropertyKey<ExportLogFilters>(p => p.createdAtTo);
  readonly logTypesKey = getPropertyKey<ExportLogFilters>(p => p.types);
  readonly logStatusesKey = getPropertyKey<ExportLogFilters>(p => p.status);


  private readonly activeFiltersConfigMap = new Map<string, any>([
    [
      this.filterTextKey,
      (filterText: string) => {
        return {labelKey: 'GENERAL.FIELDS.FILTER_TEXT', displayText: filterText}
      }
    ],
    [
      this.countryKey,
      (countries: Country[]) => {
        return {
          labelKey: 'ACCOUNTS.FIELDS.COUNTRY',
          displayText: countries.map((country: Country) => country.name).join(activeFiltersSeparator)
        }
      }
    ],
    [
      this.createdAtStartKey,
      (createdAt: string) => {
        return {labelKey: 'ACCOUNTS.FIELDS.CREATED_AT', displayText: createdAt}
      }
    ],
    [
      this.logTypesKey,
      (logTypes: string[]) => {
        return {
          labelKey: 'EXECUTION_LOGS.FIELDS.TYPE',
          displayText: logTypes
        }
      }
    ],
    [
      this.logStatusesKey,
      (logStatuses: string[]) => {
        return {
          labelKey: 'EXECUTION_LOGS.FIELDS.STATUS',
          displayText: logStatuses
        }
      }
    ],
  ]);

  private filtersBehaviorSubject = new BehaviorSubject<ExecutionLogFilters>({});
  private activeFilterChips = new Map<string, CsFinActiveFilterChip>();
  public filters$: Observable<ExecutionLogFilters> = this.filtersBehaviorSubject.asObservable();
  public activeFiltersChips$: Observable<Map<string, CsFinActiveFilterChip>> = of(this.activeFilterChips);

  constructor(public exportLogApiService: ExportLogApiService) {
  }

  filtersChanged(filters: ExportLogFilters) {
    this.filtersBehaviorSubject.next(filters);
    this.updateActiveFilterChips(filters);
  }


  private updateActiveFilterChips(filters: ExportLogFilters): void {
    if (!filters) {
      return;
    }
    this.activeFilterChips.clear();

    if (filters.filterText) {
      this.activeFilterChips.set(this.filterTextKey, this.activeFiltersConfigMap.get(this.filterTextKey)(filters.filterText));
    }
    if (filters.countryCodes && filters.countryCodes.length > 0) {
      this.activeFilterChips.set(this.countryKey, this.activeFiltersConfigMap.get(this.countryKey)(filters.countryCodes));
    }
    if (filters.createdAtFrom) {
      let dateToDisplay = new Date(filters.createdAtFrom!).toDateString();
      if (filters.createdAtTo) {
        dateToDisplay += " - " + new Date(filters.createdAtTo!).toDateString();
      }
      this.activeFilterChips.set(this.createdAtStartKey, this.activeFiltersConfigMap.get(this.createdAtStartKey)(dateToDisplay));
    }
    if (filters.types && filters.types.length > 0) {
      this.activeFilterChips.set(this.logTypesKey, this.activeFiltersConfigMap.get(this.logTypesKey)(filters.types));
    }
    if (filters.status && filters.status.length > 0) {
      this.activeFilterChips.set(this.logStatusesKey, this.activeFiltersConfigMap.get(this.logStatusesKey)(filters.status));
    }

  }

  getAll(filters?: ExportLogFilters): Observable<PageResponse<ExportLog>> {
    return this.exportLogApiService.getAll(filters);
  }

  download(id: number): Observable<FileDownload> {
    return this.exportLogApiService.download(id);
  }

  getById(id: number): Observable<ExportLog> {
    return this.exportLogApiService.getById(id);
  }

  getLogTypes(): Observable<EnumCode[]> {
    return this.exportLogApiService.getLogTypes();
  }

  getLogStatus(): Observable<EnumCode[]> {
    return this.exportLogApiService.getLogStatus();
  }



}
