import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';

import {ExportLogsRoutingModule} from './export-logs-routing.module';
import {ExportLogsComponent} from "./export-logs.component";
import {CdkDropList, CdkDropListGroup} from "@angular/cdk/drag-drop";
import {CdkConnectedOverlay, CdkOverlayOrigin} from "@angular/cdk/overlay";
import {
  CsFinActiveFiltersModule,
  CsFinAddRemoveColumnsModule,
  CsFinAuthModule,
  CsFinFlagModule
} from "@jumia-cs-fin/common";
import {ExtendedModule, FlexModule} from "@angular/flex-layout";
import {MatButtonModule} from "@angular/material/button";
import {MatIconModule} from "@angular/material/icon";
import {MatPaginatorModule} from "@angular/material/paginator";
import {MatProgressBarModule} from "@angular/material/progress-bar";
import {MatSortModule} from "@angular/material/sort";
import {MatTableModule} from "@angular/material/table";
import {TranslateModule} from "@ngx-translate/core";
import {MatDatepickerModule} from "@angular/material/datepicker";
import {MatFormFieldModule} from "@angular/material/form-field";
import {MatInputModule} from "@angular/material/input";
import {MatOptionModule} from "@angular/material/core";
import {MatProgressSpinnerModule} from "@angular/material/progress-spinner";
import {MatSelectModule} from "@angular/material/select";
import {MatToolbarModule} from "@angular/material/toolbar";
import {NgxMatSelectSearchModule} from "ngx-mat-select-search";
import {ReactiveFormsModule} from "@angular/forms";
import {JsonFormatter} from "../shared/helpers/json-formatter";
import {MatExpansionModule} from "@angular/material/expansion";
import {MatCardModule} from "@angular/material/card";
import {MatTabsModule} from "@angular/material/tabs";
import {ExportLogsListComponent} from "./component/list/export-logs-list.component";
import {ExportLogsHeaderComponent} from "./component/header/export-logs-header.component";
import {ExportLogsDetailsInfoComponent} from "./component/details/details-info/export-logs-details-info.component";
import {ExportLogsErrorsComponent} from "./component/details/errors/export-logs-errors.component";
import {ExportLogsDetailsComponent} from "./component/details/export-logs-details.component";


@NgModule({
  declarations: [
    ExportLogsListComponent,
    ExportLogsDetailsComponent,
    ExportLogsComponent,
    ExportLogsHeaderComponent,
    ExportLogsDetailsInfoComponent,
    ExportLogsErrorsComponent
  ],
  imports: [
    CommonModule,
    ExportLogsRoutingModule,
    CdkDropList,
    CdkDropListGroup,
    CdkOverlayOrigin,
    CsFinActiveFiltersModule,
    CsFinAddRemoveColumnsModule,
    ExtendedModule,
    FlexModule,
    MatButtonModule,
    MatIconModule,
    MatPaginatorModule,
    MatProgressBarModule,
    MatSortModule,
    MatTableModule,
    TranslateModule,
    CdkConnectedOverlay,
    MatDatepickerModule,
    MatFormFieldModule,
    MatInputModule,
    MatOptionModule,
    MatProgressSpinnerModule,
    MatSelectModule,
    MatToolbarModule,
    NgxMatSelectSearchModule,
    ReactiveFormsModule,
    JsonFormatter,
    MatExpansionModule,
    MatCardModule,
    MatTabsModule,
    CsFinAuthModule,
    CsFinFlagModule
  ]
})
export class ExportLogsModule { }
