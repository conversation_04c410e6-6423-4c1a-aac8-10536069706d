import {ChangeDetectionStrategy, Component} from '@angular/core';
import {CashEvolutionFilters, EntityType, FilterTypes} from "./types/dashboard.interface";
import {Country} from "../entities/account/country";

@Component({
    selector: 'brad-dashboard',
    templateUrl: './dashboard.component.html',
    styleUrls: ['./dashboard.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class DashboardComponent {

    protected readonly EntityType = EntityType;
    filtersData: CashEvolutionFilters | null = null;

    selectedDate: string | null = null;
    selectedDateRange: {start: string, end: string} | null = null;
    usedDateRange = false;
    selectedEntity: string | null = null;
    selectedAccountType: string | null = null;
    selectedCountry: Country[] | null = null;
    selectedCurrency: string | null = null;
    selectedAggregateBy: string | null = null;
    isAggregatedByPeriod = false;

    filterToShow: FilterTypes[] = [FilterTypes.DATE];

    //ToDo: set all these to true once they are ready for dev
    showCashPosition = false;
    showStatementReport = false;
    showBalances = false;

    receiveFiltersData(event: any) {
        const {
          selectedDate,
          selectedDateRange,
          usedDateRange,
          selectedAggregateBy,
          selectedEntity,
          selectedAccountType,
          selectedCountry,
          selectedCurrency,
          isAggregatedByPeriod
        } = event;
        this.selectedDate = selectedDate;
        this.selectedDateRange = selectedDateRange;
        this.usedDateRange = usedDateRange;
        this.selectedAggregateBy = selectedAggregateBy;
        this.selectedAccountType = selectedAccountType;
        this.selectedCountry = selectedCountry;
        this.selectedCurrency = selectedCurrency;
        this.selectedEntity = selectedEntity;
        this.isAggregatedByPeriod = isAggregatedByPeriod;

        this.filtersData = {
            selectedDate,
            selectedDateRange,
            usedDateRange,
            selectedAggregateBy,
            selectedEntity,
            selectedCountry,
            selectedCurrency,
            selectedAccountType,
            isAggregatedByPeriod
        }
    }

}
