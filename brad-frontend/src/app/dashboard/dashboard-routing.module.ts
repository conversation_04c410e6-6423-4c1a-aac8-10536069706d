import {RouterModule, Routes} from '@angular/router';
import {NgModule} from '@angular/core';
import {NavigationComponent} from '../navigation/navigation.component';
import {
  csFinAuthCanActivateGuard,
  CsFinAuthorizationOnAnyTargetOfTypeService
} from "@jumia-cs-fin/common";
import {bradPermissions} from "../auth/constants/permission.constants";
import {bradAuthCountryTarget} from "../auth/constants/auth.constants";
import {DashboardComponent} from "./dashboard.component";
import {CashEvolutionReportComponent} from "./component/cash-reports/cash-evolution-report/cash-evolution-report.component";

export const csFinAuthorizationOnAnyTargetOfTypeService =  new CsFinAuthorizationOnAnyTargetOfTypeService();
const routes: Routes = [
  {
    path: '',
    component: NavigationComponent,
    children: [
      {
        path: '',
        canActivate: [csFinAuthCanActivateGuard],
        data: {
          auth: {
            permissions: bradPermissions.BRAD_ACCESS_ACCOUNTS,
            targets: bradAuthCountryTarget,
            authTypeService: csFinAuthorizationOnAnyTargetOfTypeService,
          }
        },
        component: DashboardComponent
      }
    ]
  },
  {
    path: 'cash-evolution-report',
    component: NavigationComponent,
    children: [
      {
        path: '',
        component: CashEvolutionReportComponent
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class DashboardRoutingModule { }
