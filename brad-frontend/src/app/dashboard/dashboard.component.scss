.dashboard {
  display: flex;
  flex-direction: column;
  padding: 15px;

  &-filters {
    width: 100%;
    margin-bottom: 10px;
  }

  &-content {
    display: flex;
    flex-direction: row;

    &__left {
      flex: 7;
      margin-right: 8px;

      .cash-position {
        min-width: 700px;
        padding: 30px 15px 0 15px;
        border-radius: 15px;
        margin: 20px 0 10px 0;
        background: #EEEEEE;

        &-title {
          text-align: center;
          font-size: 24px;
          font-weight: 800;
          margin-bottom: 30px;

          a {
            color: #000000;
            text-decoration: none;

            &:hover {
              text-decoration: underline;
            }
          }
        }

        section {
          display: flex;
          flex-direction: row;
          align-items: flex-start;
          justify-content: space-between;
        }
      }
    }

    &__right {
      flex: 3;
      margin-left: 8px;
    }
  }

}
