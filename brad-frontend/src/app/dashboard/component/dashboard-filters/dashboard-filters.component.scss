.filters {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;

  &-card {
    min-width: 155px;
    margin-right: 10px;
    margin-bottom: 10px;
    padding: 8px;
    display: flex;
    flex-direction: row;
    border-radius: 8px;
    align-items: center;

    &__select {
      border: none;
      background: #FFFFFF;
      font-size: 12px;
      transition: width 0.3s ease-in-out;

      &-loader {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
      }

      &-clearCountry-btn {
        display: flex;
        align-items: center;
        margin: 0 auto;
      }

    }


    &__title {
      cursor: pointer;
      font-weight: 600;
      margin-right: 8px;
    }

    &__flag {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      cs-fin-flag {
        margin: 0 5px;
      }
    }

    mat-icon {
      font-size: 20px;
      height: 20px;
      width: 20px;
      margin-left: 5px;
      cursor: pointer;
      transition: transform 0.3s ease-in-out;
    }
  }
}

input {
  &#dateInput {
    width: 0;
    height: 0;
    border: none;
  }

  &#accountInput,
  &#entityInput {
    border: none;
    padding: 3px 5px;
    background: #FFFFFF;
    width: 68px;
    border-radius: 5px;
    transition: width 0.3s ease-in-out;

    &.expandedInputFocused {
      width: 150px;
    }

    &:active,
    &:focus,
    &:focus-visible {
      border: 1px solid #20c4fc;
      border-radius: 5px;
      background: #FFFFFF;
    }
  }
}

mat-select {
  &#country-filter {
    min-width: 180px;
  }

  &#currency-filter {
    min-width: 180px;
  }

  &#accountType-filter {
    min-width: 180px;
  }
}

.wide-title-mid {
  width: 114px;
}

.wide-title {
  width: 165px;
}

.very-wide-title {
  width: 252px;
}

.rotate-icon {
  transform: rotate(90deg);
  transition: transform 0.3s ease-in-out;
}

::ng-deep{
  [id^="cdk-overlay-"] {
    width: auto !important;
  }
}
