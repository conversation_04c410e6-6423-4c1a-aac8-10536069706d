<section class="filters">
  <!--  Date filter-->
  <mat-card *ngIf="showDateFilter" class="filters-card" appearance="raised">
    <span class="filters-card__title" (click)="picker.open()">{{'ACCOUNTS.FIELDS.AT_DATE' | translate}}:</span>
    <input id="dateInput" (dateChange)="dateChanged()" [(ngModel)]="selectedDate" matInput [matDatepicker]="picker">
    <span (click)="picker.open()">{{selectedDate | date:'MM/dd/yyyy'}}</span>

    <mat-icon
      (click)="picker.open()"
      [ngClass]="{ 'rotate-icon': openDatePicker }"
      fontIcon="chevron_right">
    </mat-icon>

    <mat-datepicker
      (opened)="onDatePickerOpened()"
      (closed)="onDatePickerClosed()"
      #picker>
    </mat-datepicker>
  </mat-card>

  <!--  Date range filter-->
  <mat-card *ngIf="showDateRangeFilter" class="filters-card" appearance="raised">
    <span class="filters-card__title wide-title-mid">{{'ACCOUNTS.FIELDS.DATE_RANGE' | translate}}:</span>

    <mat-date-range-input [rangePicker]="rangePicker">
      <input
        (click)="rangePicker.open()"
        (dateChange)="dateRangeChanged()"
        [(ngModel)]="selectedDateRange.start"
        matStartDate
        placeholder="Start date">
      <input
        (click)="rangePicker.open()"
        (dateChange)="dateRangeChanged()"
        [(ngModel)]="selectedDateRange.end"
        matEndDate
        placeholder="End date">
    </mat-date-range-input>

    <mat-date-range-picker
      (opened)="onDatePickerOpened()"
      (closed)="onDatePickerClosed()"
      #rangePicker>
    </mat-date-range-picker>

    <mat-icon
      (click)="rangePicker.open()"
      [ngClass]="{ 'rotate-icon': openDatePicker }"
      fontIcon="chevron_right">
    </mat-icon>
  </mat-card>

  <!--  Country filter-->
  <mat-card *ngIf="showCountryFilter" class="filters-card" appearance="raised">
    <span class="filters-card__title">{{'ACCOUNTS.FIELDS.COUNTRY' | translate}}:</span>
    <mat-select
      [id]="isCountryExpanded ? 'country-filter' : null"
      class="filters-card__select"
      (selectionChange)="countryChanged()"
      [(ngModel)]="selectedCountry"
      (click)="isCountryExpanded = true"
      (closed)="collapseCountrySelect()"
      [attr.aria-hidden]="!isCountryExpanded"
      [compareWith]="compareCountries"
      multiple>
      <ng-container *ngIf="countryList; else loadingCountry">
        <mat-option>
          <ngx-mat-select-search
            ngModel
            (ngModelChange)="filterCountryOptions($event)"
            [noEntriesFoundLabel]="'GENERAL.FILTERS.NO_MATCHING_RESULTS_FOUND' | translate"
            [placeholderLabel]="'GENERAL.FILTERS.SEARCH' | translate">
          </ngx-mat-select-search>
        </mat-option>
        <mat-option *ngFor="let country of filteredCountryList" [value]="country">
          <span class="filters-card__flag">
            {{country.name}}
            <cs-fin-flag [countryCode]="country.code"></cs-fin-flag>
          </span>
        </mat-option>
        <button
          mat-button
          color="primary"
          class="filters-card__select-clearCountry-btn"
          [disabled]="!selectedCountry"
          (click)="resetCountry()">
          {{'GENERAL.FILTERS.ACTIONS.CLEAR_SELECTION' | translate}}
          <mat-icon>close</mat-icon>
        </button>
      </ng-container>
      <ng-template #loadingCountry>
        <mat-option disabled>
          <div class="filters-card__select-loader">
            <span>{{'GENERAL.FILTERS.LOADING' | translate}}</span>
            <mat-spinner diameter="20"></mat-spinner>
          </div>
        </mat-option>
      </ng-template>
    </mat-select>
  </mat-card>

  <!--  Currency filter-->
  <mat-card *ngIf="showCurrencyFilter" class="filters-card" appearance="raised">
    <span class="filters-card__title">{{'ACCOUNTS.FIELDS.FILTER_CURRENCY' | translate}}:</span>
    <mat-select
      [id]="isCurrencyExpanded ? 'currency-filter' : null"
      class="filters-card__select"
      (selectionChange)="currencyChanged()"
      [(ngModel)]="selectedCurrency"
      (click)="isCurrencyExpanded = true"
      (closed)="collapseCurrencySelect()"
      [attr.aria-hidden]="!isCurrencyExpanded"
      multiple>
      <ng-container *ngIf="currencyList; else loadingCurrency">
        <mat-option>
          <ngx-mat-select-search
            ngModel (ngModelChange)="filterCurrencyOptions($event)"
            [noEntriesFoundLabel]="'GENERAL.FILTERS.NO_MATCHING_RESULTS_FOUND' | translate"
            [placeholderLabel]="'GENERAL.FILTERS.SEARCH' | translate">
          </ngx-mat-select-search>
        </mat-option>
        <mat-option *ngFor="let currency of filteredCurrencyList" [value]="currency.code">
          {{currency.code}}
        </mat-option>
        <button
          mat-button
          color="primary"
          class="filters-card__select-clearCountry-btn"
          [disabled]="!selectedCurrency"
          (click)="resetCurrency()">
          {{'GENERAL.FILTERS.ACTIONS.CLEAR_SELECTION' | translate}}
          <mat-icon>close</mat-icon>
        </button>
      </ng-container>
      <ng-template #loadingCurrency>
        <mat-option disabled>
          <div class="filters-card__select-loader">
            <span>{{'GENERAL.FILTERS.LOADING' | translate}}</span>
            <mat-spinner diameter="20"></mat-spinner>
          </div>
        </mat-option>
      </ng-template>
    </mat-select>
  </mat-card>

  <!--  Account type filter-->
  <mat-card *ngIf="showAccountTypeFilter" class="filters-card" appearance="raised">
    <span class="filters-card__title wide-title">{{ 'ACCOUNTS.FIELDS.ACCOUNT_TYPE' | translate }}:</span>
    <mat-select
      [id]="isAccountTypeExpanded ? 'accountType-filter' : null"
      class="filters-card__select"
      (selectionChange)="accountTypeChanged()"
      [(ngModel)]="selectedAccountType"
      (click)="isAccountTypeExpanded = true"
      (closed)="collapseAccountTypeSelect()"
      [attr.aria-hidden]="!isAccountTypeExpanded"
      multiple>
      <ng-container *ngIf="accountTypesList; else loadingAccountTypes">
        <mat-option *ngFor="let accountType of accountTypesList" [value]="accountType">
          {{accountType | underscoreToSpace}}
        </mat-option>
        <button
          mat-button
          color="primary"
          class="filters-card__select-clearCountry-btn"
          [disabled]="!selectedAccountType"
          (click)="resetAccountType()">
          {{'GENERAL.FILTERS.ACTIONS.CLEAR_SELECTION' | translate}}
          <mat-icon>close</mat-icon>
        </button>
      </ng-container>
      <ng-template #loadingAccountTypes>
        <mat-option disabled>
          <div class="filters-card__select-loader">
            <span>{{'GENERAL.FILTERS.LOADING' | translate}}</span>
            <mat-spinner diameter="20"></mat-spinner>
          </div>
        </mat-option>
      </ng-template>
    </mat-select>
  </mat-card>

  <!--  Account name filter-->
  <mat-card *ngIf="showAccountFilter" class="filters-card" appearance="raised">
    <span class="filters-card__title">{{ 'ACCOUNTS.FIELDS.ACCOUNT_NAME' | translate }}:</span>
    <input
      id="accountInput"
      matInput
      type="text"
      [(ngModel)]="selectedAccount"
      (focus)="isAccountInputFocused = true"
      (blur)="isAccountInputFocused = false"
      (input)="onAccountInput($event)"
      [ngClass]="{ 'expandedInputFocused': isAccountInputFocused }"
    />
    <mat-icon *ngIf="selectedAccount" (click)="clearAccountNameFilter()" matSuffix>close</mat-icon>
  </mat-card>

  <!--  Legal Entity type filter-->
  <mat-card *ngIf="showEntityFilter" class="filters-card" appearance="raised">
    <span class="filters-card__title">{{ 'ACCOUNTS.FIELDS.ENTITY' | translate }}:</span>
    <input
      id="entityInput"
      matInput
      type="text"
      [(ngModel)]="selectedEntity"
      (focus)="isEntityInputFocused = true"
      (blur)="isEntityInputFocused = false"
      (input)="onEntityInput($event)"
      [ngClass]="{ 'expandedInputFocused': isEntityInputFocused }"
    />

    <mat-icon
      fontIcon="chevron_right">
    </mat-icon>
  </mat-card>

  <!--  Aggregate period By filter-->
  <mat-card *ngIf="showAggregateByFilter" class="filters-card" appearance="raised">
    <span class="filters-card__title">{{ 'ACCOUNTS.FIELDS.PERIODICITY' | translate }}:</span>
    <mat-select
      [id]="isAggregateByExpanded ? 'accountType-filter' : null"
      class="filters-card__select"
      (selectionChange)="aggregateByChanged()"
      [(ngModel)]="selectedAggregateBy"
      (click)="isAggregateByExpanded = true"
      (closed)="collapseAggregateBySelect()"
      [attr.aria-hidden]="!isAggregateByExpanded">
      <ng-container *ngIf="statementPeriodicityList; else loadingStatementPeriodicity">
        <mat-option *ngFor="let periodicity of statementPeriodicityList" [value]="periodicity">
          {{periodicity | underscoreToSpace}}
        </mat-option>
        <button
          mat-button
          color="primary"
          class="filters-card__select-clearCountry-btn"
          [disabled]="!selectedAggregateBy"
          (click)="resetAggregateBy()">
          {{'GENERAL.FILTERS.ACTIONS.CLEAR_SELECTION' | translate}}
          <mat-icon>close</mat-icon>
        </button>
      </ng-container>
      <ng-template #loadingStatementPeriodicity>
        <mat-option disabled>
          <div class="filters-card__select-loader">
            <span>{{'GENERAL.FILTERS.LOADING' | translate}}</span>
            <mat-spinner diameter="20"></mat-spinner>
          </div>
        </mat-option>
      </ng-template>
    </mat-select>
  </mat-card>
</section>
