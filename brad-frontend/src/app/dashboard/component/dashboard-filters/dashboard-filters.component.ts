import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges
} from '@angular/core';
import {formatDate} from "@angular/common";
import {FilterTypes} from "../../types/dashboard.interface";
import {CountryApiService} from "../../../api/service/country-api.service";
import {debounceTime, takeUntil} from "rxjs/operators";
import {Country} from "../../../entities/account/country";
import {Subject} from "rxjs";
import {Currency} from "../../../entities/currency/currency";
import {CurrencyApiService} from "../../../api/service/currency-api.service";
import {AccountTypes} from "../../../entities/account/account-types";
import {EvolutionPeriodicity} from "../../../entities/account/statement-periodicity";
import {Router} from "@angular/router";

@Component({
  selector: 'brad-dashboard-filters',
  templateUrl: './dashboard-filters.component.html',
  styleUrls: ['./dashboard-filters.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class DashboardFiltersComponent implements OnInit, OnChanges {

  constructor(
    private countryApiService: CountryApiService,
    private currencyApiService: CurrencyApiService,
    private router: Router
  ) {
    this.accountInputSubject.pipe(debounceTime(1500)).subscribe(() => {
      this.emitFilterData();
    });

    this.entityInputSubject.pipe(debounceTime(1500)).subscribe(() => {
      this.emitFilterData();
    });
  }

  private accountInputSubject = new Subject<Event>();
  private entityInputSubject = new Subject<Event>();
  private _onDestroy: Subject<void> = new Subject<void>();

  showDateFilter = false;
  showDateRangeFilter = false;
  showAccountTypeFilter = false;
  showAccountFilter = false;
  showEntityFilter = false;
  showCountryFilter = false;
  showCurrencyFilter = false;
  showAggregateByFilter = false;

  currencyList: Currency[] = [];
  countryList: Country[] = [];
  filteredCountryList: Country[] = [];
  filteredCurrencyList: Currency[] = [];
  accountTypesList =  Object.keys(AccountTypes).filter((item) => {
    return isNaN(Number(item));
  });
  statementPeriodicityList: String[] =  Object.keys(EvolutionPeriodicity).filter((item) => {
    return isNaN(Number(item));
  });

  isCountryExpanded = false;
  isCurrencyExpanded = false;
  isAccountTypeExpanded = false;
  isAccountInputFocused = false;
  isEntityInputFocused = false;
  isAggregateByExpanded = false;
  openDatePicker = false;

  selectedDate = formatDate(new Date(new Date().setDate(new Date().getDate() - 1)), 'MM/dd/yyyy', 'en-US');
  selectedDateRange = {
    start: new Date(new Date(this.selectedDate).setDate(new Date(this.selectedDate).getDate() - 32)),
    end: new Date(this.selectedDate)
  };
  usedDateRange = false;
  selectedAccount: string | null = null;
  selectedEntity: string | null = null;
  selectedCountry: Country[] = [];
  selectedCurrency: string[] = [];
  selectedAccountType: string[] = [];
  selectedAggregateBy: string | null = 'DAILY';
  isAggregatedByPeriod = true;

  @Input() filtersToShow: Array<FilterTypes> = [FilterTypes.ALL];
  @Output() filterDataEmitter = new EventEmitter<any>();

  ngOnInit() {
    this.loadCurrencies();
    this.loadCountries();
    this.setFiltersVisible(this.filtersToShow);
    this.setDatePickerTypeVisible();
    this.emitFilterData();
  }

  ngOnChanges(changes: SimpleChanges) {
    const filtersToShow = changes['filtersToShow'].currentValue
    this.setFiltersVisible(filtersToShow);
  }

  isMainDashboardRoute(): boolean {
    return this.router.url === '/';
  }

  setDatePickerTypeVisible() {
    if (this.isMainDashboardRoute()) {
      this.showDateFilter = true;
      this.showDateRangeFilter = false;
    } else {
      this.showDateFilter = false;
      this.showDateRangeFilter = true;
    }
  }

  onDatePickerOpened() {
    this.openDatePicker = true;
  }

  onDatePickerClosed() {
    this.openDatePicker = false;
  }

  private setFiltersVisible(filters: FilterTypes[]) {
    if (filters.includes(<FilterTypes>'All')) {
      this.showDateFilter = true;
      this.showDateRangeFilter = true;
      this.showAccountFilter = true;
      this.showAccountTypeFilter = true
      this.showEntityFilter = true;
      this.showCountryFilter = true;
      this.showCurrencyFilter = true;
      this.showAggregateByFilter = true;
      return
    }

    this.showDateFilter = filters.includes(<FilterTypes>'Date');
    this.showDateRangeFilter = filters.includes(<FilterTypes>'Date');
    this.showAccountFilter = filters.includes(<FilterTypes>'Account Name');
    this.showAccountTypeFilter = filters.includes(<FilterTypes>'Account Type');
    this.showEntityFilter = filters.includes(<FilterTypes>'Entity');
    this.showCountryFilter = filters.includes(<FilterTypes>'Country');
    this.showCurrencyFilter = filters.includes(<FilterTypes>'Currency');
    this.showAggregateByFilter = filters.includes(<FilterTypes>'Aggregate By');
  }

  loadCurrencies() {
    if (this.currencyList.length) return;

    this.currencyApiService.getAll()
      .pipe(takeUntil(this._onDestroy))
      .subscribe({
        next: (currencies: Currency[]) => {
          this.currencyList = currencies;
          this.filteredCurrencyList = [...this.currencyList];
        }, error: (error) => console.error('Error loading currencies:', error)
      });

  }

  loadCountries() {
    if (this.countryList.length) return;

    this.countryApiService.getAll()
      .pipe(takeUntil(this._onDestroy))
      .subscribe({
        next: (countries: Country[]) => {
          this.countryList = countries;
          this.filteredCountryList = [...this.countryList];
        },
        error: (error) => console.error('Error loading countries:', error)
      });
  }

  filterCurrencyOptions($event: any) {
    const searchTerm = $event.trim();
    if (searchTerm) {
      this.filteredCurrencyList = this.currencyList.filter((currency) => {
        return currency.code.trim().toLowerCase().includes(searchTerm.toLowerCase());
      })
    } else {
      this.filteredCurrencyList = [...this.currencyList];
    }
  }

  filterCountryOptions($event: any) {
    const searchTerm = $event.trim();
    if (searchTerm) {
      this.filteredCountryList = this.countryList.filter((country) => {
        return country.name.trim().toLowerCase().includes(searchTerm.toLowerCase());
      })
    } else {
      this.filteredCountryList = [...this.countryList];
    }
  }

  storeSelectedFilters() {
    const filterData = {
      selectedDate: this.formatDateForAPI(this.selectedDate),
      selectedDateRange: this.selectedDateRange,
      usedDateRange: this.usedDateRange,
      selectedAccount: this.selectedAccount,
      selectedAccountType: this.selectedAccountType,
      selectedCountry: this.selectedCountry,
      selectedEntity: this.selectedEntity,
      selectedCurrency: this.selectedCurrency,
      selectedAggregateBy: this.selectedAggregateBy,
      isAggregatedByPeriod: this.isAggregatedByPeriod
    };

    localStorage.setItem('filters', JSON.stringify(filterData));
  }

  emitFilterData() {
    if(this.isMainDashboardRoute()) {
      this.storeSelectedFilters();
      this.filterValues();
    } else {
      const storedFilters = localStorage.getItem('filters');
      if (storedFilters) {
        const filters = JSON.parse(storedFilters);
        this.selectedDate = filters.selectedDate;
        this.selectedDateRange = filters.selectedDateRange;
        this.usedDateRange = filters.usedDateRange;
        this.selectedAccount = filters.selectedAccount;
        this.selectedAccountType = filters.selectedAccountType;
        this.selectedCountry = filters.selectedCountry;
        this.selectedEntity = filters.selectedEntity;
        this.selectedCurrency = filters.selectedCurrency;
        this.selectedAggregateBy = filters.selectedAggregateBy;
        this.isAggregatedByPeriod = filters.isAggregatedByPeriod;
      }
      this.filterValues();
    }
  }

  filterValues() {
    const filterData = {
      selectedDate: this.formatDateForAPI(this.selectedDate),
      selectedDateRange: this.selectedDateRange,
      usedDateRange: this.usedDateRange,
      selectedAccount: this.selectedAccount,
      selectedAccountType: this.selectedAccountType,
      selectedCountry: this.selectedCountry,
      selectedEntity: this.selectedEntity,
      selectedCurrency: this.selectedCurrency,
      selectedAggregateBy: this.selectedAggregateBy,
      isAggregatedByPeriod: this.isAggregatedByPeriod
    };
    this.filterDataEmitter.emit(filterData);
  }

  dateChanged() {
    const endDate = new Date(this.selectedDate);
    endDate.setDate(endDate.getDate() - 32);

    this.selectedDateRange = {
      start: new Date(endDate.toISOString()),
      end: new Date(this.selectedDate)
    }
    this.usedDateRange = false;
    this.storeSelectedFilters();
    this.emitFilterData();
  }

  dateRangeChanged() {
    this.usedDateRange = true;
    this.storeSelectedFilters();
    this.emitFilterData();
  }

  countryChanged() {
    this.storeSelectedFilters();
    this.emitFilterData();
  }

  currencyChanged() {
    this.storeSelectedFilters();
    this.emitFilterData();
  }

  aggregateByChanged() {
    this.storeSelectedFilters();
    this.emitFilterData();
  }

  accountTypeChanged() {
    this.storeSelectedFilters();
    this.emitFilterData();
  }

  onAccountInput($event: Event) {
    this.accountInputSubject.next($event);
  }

  onEntityInput($event: Event) {
    this.entityInputSubject.next($event);
    this.storeSelectedFilters();
  }

  collapseCountrySelect(): void {
    this.isCountryExpanded = false;
  }

  collapseCurrencySelect(): void {
    this.isCurrencyExpanded = false;
  }

  collapseAccountTypeSelect(): void {
    this.isAccountTypeExpanded = false;
  }

  collapseAggregateBySelect(): void {
    this.isAggregateByExpanded = false;
  }

  resetCurrency() {
    this.selectedCurrency = [];

    this.storeSelectedFilters();
    this.emitFilterData();
  }

  resetCountry() {
    this.selectedCountry = [];

    this.storeSelectedFilters();
    this.emitFilterData();
  }

  resetAccountType() {
    this.selectedAccountType = [];

    this.storeSelectedFilters();
    this.emitFilterData();
  }

  resetAggregateBy() {
    this.selectedAggregateBy= null;

    this.storeSelectedFilters();
    this.emitFilterData();
  }

  clearAccountNameFilter() {
    this.selectedAccount = null;
    this.isAccountInputFocused = false;

    this.storeSelectedFilters();
    this.emitFilterData();
  }

  formatDateForAPI(date: string) {
    return new Date(date)
  }

  compareCountries(country1: any, country2: any): boolean {
    return country1 && country2 ? country1.code === country2.code : country1 === country2;
  }

}
