<section class="accounts">
  <mat-card *ngFor="let account of accountData; index as i" appearance="raised">
    <div class="accounts-amount">
      {{account.accountAmount | thousandsFormatter}}
      <span class="accounts-amount__currency">
        {{ account.accountCurrency }}
      </span>
    </div>
    <div class="accounts-type">
      <span>{{ account.accountType | underscoreToSpace }}</span>
      account
    </div>
  </mat-card>
</section>
