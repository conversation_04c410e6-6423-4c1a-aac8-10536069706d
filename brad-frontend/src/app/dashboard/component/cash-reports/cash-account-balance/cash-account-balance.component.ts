import { Component } from '@angular/core';

@Component({
  selector: 'brad-cash-account-balance',
  templateUrl: './cash-account-balance.component.html',
  styleUrls: ['./cash-account-balance.component.scss']
})

export class CashAccountBalanceComponent {

  accountData = [
    {
      accountType: 'bank',
      accountAmount: '-',
      accountCurrency: 'usd',
    },
    {
      accountType: 'psp',
      accountAmount: '-',
      accountCurrency: 'usd',
    },
    {
      accountType: 'mobile_money',
      accountAmount: '-',
      accountCurrency: 'usd',
    },
    {
      accountType: 'wallet',
      accountAmount: '-',
      accountCurrency: 'usd',
    },
  ]

}
