<mat-card class="evo-chart" appearance="raised">
  <div class="evo-chart__title" [class]="{'disableLink': !isMainDashboardPage}">
    <a routerLink="/cash-evolution-report">{{title}}</a>
  </div>
  <div class="evo-chart__no-data" *ngIf="noChartData">{{'DASHBOARD.GENERAL.NO_RESULTS_FOUND' | translate}}</div>
  <div
    *ngIf="!noChartData"
    class="evo-chart__dimensions"
    (chartClick)="onChartClick($event)"
    [loadingOpts]="loadOptions"
    [options]="chartOption"
    [loading]="loading$ | async"
    echarts></div>
</mat-card>
