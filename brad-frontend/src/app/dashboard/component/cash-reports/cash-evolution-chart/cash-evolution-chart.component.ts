import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component, EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit, Output, SimpleChanges
} from '@angular/core';
import { Location } from '@angular/common';
import {EChartsCoreOption} from 'echarts/core';
import {CashEvolutionFacade} from "../../../facade/cash-evolution.facade";
import {Observable, Subject, Subscription, switchMap} from "rxjs";
import {CashEvolutionFilters} from "../../../types/dashboard.interface";
import {ECElementEvent} from "echarts";
import {takeUntil} from "rxjs/operators";

@Component({
  selector: 'brad-cash-evolution-chart',
  templateUrl: './cash-evolution-chart.component.html',
  styleUrls: ['./cash-evolution-chart.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class CashEvolutionChartComponent implements OnInit, OnChanges, OnDestroy {

  private subscription!: Subscription;
  private filterChanges$ = new Subject<CashEvolutionFilters>();
  private destroy$ = new Subject<void>();
  title = 'Cash Evolution';
  chartOption: EChartsCoreOption = {};
  loading$: Observable<boolean>;
  isMainDashboardPage = false;
  noChartData = false;
  rawXaxisData: string[] = [];
  today = new Date().toString();

  loadOptions = {
    color: '#20C4FC',
    textColor: '#000',
    maskColor: 'rgba(255, 255, 255, 0.8)'
  }


  @Input() selectedFilters: CashEvolutionFilters | null = null;
  @Output() showDatatable = new EventEmitter<{ show: boolean, clickTime: number }>();

  constructor(
    private cashEvolutionFacade: CashEvolutionFacade,
    private cdr: ChangeDetectorRef,
    private location: Location) {
    this.loading$ = this.cashEvolutionFacade.cashEvolutionChartLoading$
  }

  ngOnInit() {
    this.isDashboardPage();
    const selectedDate = new Date(this.selectedFilters?.selectedDate ?? this.today);
    selectedDate.setDate(selectedDate.getDate() + 1);// Add 1 day
    const formatedFilters = {
      ...this.selectedFilters,
      selectedDate: selectedDate.toISOString().split('T')[0],
      usedDateRange: this.selectedFilters?.usedDateRange || false,
      isAggregatedByPeriod: this.selectedFilters?.isAggregatedByPeriod ?? false
    }

    this.filterChanges$.pipe(
      switchMap(filters => this.cashEvolutionFacade.getCashEvolution(filters)),
      takeUntil(this.destroy$)
    ).subscribe();
    // Initial data load
    this.filterChanges$.next(formatedFilters);

    this.checkFetchErrors();
    this.populateChart();

    // Initial chart config
    this.chartOption = this.getUpdatedChartOption([], [], [], [], [], [], []);
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['selectedFilters']) {
      const changedFilter = changes['selectedFilters'].currentValue;
      const newDate = new Date(changedFilter.selectedDate)
      newDate.setDate(newDate.getDate() + 1); // Add 1 day

      const formattedDate = newDate.toISOString().split('T')[0];
      const filters = { ...changedFilter, selectedDate: formattedDate };
      this.filterChanges$.next(filters);
      this.showDatatable.emit({show:false, clickTime: new Date().getTime()});
      this.checkFetchErrors();
      this.populateChart();
    }
  }

  checkFetchErrors() {
    this.subscription = this.cashEvolutionFacade.cashEvolutionFetchError$.subscribe((data: boolean) => {
    });
  }

  populateChart() {
    this.subscription = this.cashEvolutionFacade.cashEvolutionChartData$.subscribe((data: any) => {
      if (data !== 'loading') {
        const xAxisData = [...data.xAxis];
        const formattedCashOutData = [...data.formattedCashOutData];
        const formattedCashInData = [...data.formattedCashInData];
        const formattedOpeningBalData = [...data.formattedOpeningBal];
        const cashInBaseValue = [...data.cashInBaseValue];
        const cashOutBaseValue = [...data.cashOutBaseValue];
        const formattedClosingBalData = [...data.formattedClosingBal];

        // Ensure a new reference is assigned to trigger change detection
        this.chartOption = this.getUpdatedChartOption(
          xAxisData,
          formattedCashOutData,
          formattedCashInData,
          formattedOpeningBalData,
          cashInBaseValue,
          cashOutBaseValue,
          formattedClosingBalData
        );
        this.rawXaxisData = [...data.rawXAxisData];
        this.noChartData = !xAxisData.length;

        // Mark for change detection
        this.cdr.markForCheck();
      }
    });
  }

  /**
   * Generates a new chart configuration.
   */
  getUpdatedChartOption(
    xAxisData: string[],
    formattedCashOutData: any[],
    formattedCashInData: any[],
    formattedOpeningBalData: any[],
    cashInBaseValue: any[],
    cashOutBaseValue: any[],
    formattedClosingBalData: any[]
  ): EChartsCoreOption {
    return {
      title: {
        text: 'Cash Evolution',
        left: 'center',
        show: false
      },
      toolbox: {
        feature: {
          magicType: {
            type: ['stack', 'line', 'bar']
          },
          restore: {},
          saveAsImage: {}
        },
        right: '1%'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: function (params: any[]) {
          const formatValue = (tar: { value: { toLocaleString: (arg0: string) => any; } | null; }) => {
            if (!tar || tar.value == null) return null;
            return tar.value.toLocaleString('en-US');
          };

          let tar0 = params[0]?.value !== '-' ? params[0] : null;
          let tar3 = params[3]?.value !== '-' ? params[3] : null;
          let tar4 = params[4]?.value !== '-' ? params[4] : null;
          let tar5 = params[5]?.value !== '-' ? params[5] : null;

          if (!tar0 && !tar3 && !tar4 && !tar5) return '';

          return [
            tar0 ? `${tar0.name}<br/>${tar0.marker} ${tar0.seriesName}: <strong>${formatValue(tar0)} USD</strong>` : `${tar3.name}`,
            tar5 ? `${tar5.marker} ${tar5.seriesName}: <strong>${formatValue(tar5)} USD</strong>` : '',
            tar3 ? `${tar3.marker} ${tar3.seriesName}: <strong>${formatValue(tar3)} USD</strong>` : '',
            tar4 ? `${tar4.marker} ${tar4.seriesName}: <strong>-${formatValue(tar4)} USD</strong>` : ''
          ].filter(Boolean).join('<br/>');
        }
      },
      legend: {
        data: ['Cash-In', 'Cash-Out'],
        left: '1%',
        top: '1%'
      },
      grid: {
        left: '1%',
        right: '1%',
        bottom: 60,
        containLabel: true
      },
      dataZoom: [
        // TODO: Return later, when people want to appreciate good things
        // {
        //   type: 'inside',
        //   start: 0,
        //   end: 100
        // },
        // {
        //   start: 0,
        //   end: 100
        // }
      ],
      xAxis: [
        {
          type: 'category',
          splitLine: {
            show: true
          },
          axisLabel: {
            rotate: 45,
            margin: 15
          },
          data: xAxisData
        }
      ],
      yAxis: {
        type: 'value',
      },
      series: [
        {
          name: 'Opening bal',
          type: 'bar',
          stack: 'Total',
          stackStrategy: 'all',
          label: {
            show: true,
            position: 'top',
            fontSize: 11
          },
          itemStyle: {
            color: '#A5A5A5'
          },
          data: formattedOpeningBalData
        },
        {
          name: 'PlaceholderIn',
          type: 'bar',
          stack: 'TotalIn',
          stackStrategy: 'all',
          silent: true,
          itemStyle: {
            borderColor: 'transparent',
            color: 'transparent'
          },
          emphasis: {
            itemStyle: {
              borderColor: 'transparent',
              color: 'transparent'
            }
          },
          data: cashInBaseValue
        },
        {
          name: 'PlaceholderEx',
          type: 'bar',
          stack: 'TotalOut',
          stackStrategy: 'all',
          silent: true,
          itemStyle: {
            borderColor: 'transparent',
            color: 'transparent'
          },
          emphasis: {
            itemStyle: {
              borderColor: 'transparent',
              color: 'transparent'
            }
          },
          data: cashOutBaseValue
        },
        {
          name: 'Cash-In',
          type: 'bar',
          stack: 'TotalIn',
          stackStrategy: 'all',
          label: {
            show: true,
            position: 'top',
            rotate: 45,
            align: 'left',
            verticalAlign: 'middle',
            distance: 8,
            fontSize: 10
          },
          itemStyle: {
            color: '#91CC75'
          },
          data: formattedCashInData
        },
        {
          name: 'Cash-Out',
          type: 'bar',
          stack: 'TotalOut',
          stackStrategy: 'all',
          label: {
            show: true,
            position: 'bottom',
            rotate: 45,
            align: 'left',
            verticalAlign: 'middle',
            distance: 15,
            fontSize: 10
          },
          itemStyle: {
            color: '#EE6666'
          },
          data: formattedCashOutData
        },
        {
          name: 'Closing bal',
          type: 'bar',
          stack: 'ClosingBal',
          stackStrategy: 'all',
          label: {
            show: true,
            position: 'top',
            fontSize: 11
          },
          itemStyle: {
            color: '#A5A5A5'
          },
          data: formattedClosingBalData
        }
      ]
    };
  }

  onChartClick($event: ECElementEvent) {
    if (this.isMainDashboardPage) return;

    const selectedDate = new Date(this.selectedFilters?.selectedDateRange?.end || this.today).toISOString().split('T')[0];

    const { dataIndex } = $event;

    let selectedDateRange: { start: string; end: string };
    let usedDateRange = this.selectedFilters?.usedDateRange || false;

    if (this.isDailyPeriod()) {
      const date = new Date(this.rawXaxisData[dataIndex]);
      date.setDate(date.getDate() - 1);

      selectedDateRange = {
        start: date.toISOString(),
        end: date.toISOString()
      }
      usedDateRange = true;

    } else {
      selectedDateRange = {
        start: new Date(this.selectedFilters?.selectedDateRange?.start || this.today).toISOString(),
        end: new Date(this.selectedFilters?.selectedDateRange?.end || this.today).toISOString()
      }
      usedDateRange = false;
    }

    const filters = {
      ...this.selectedFilters,
      selectedDate,
      selectedDateRange,
      usedDateRange,
      isAggregatedByPeriod: false
    }

    this.cashEvolutionFacade.resetTableData();

    this.cashEvolutionFacade.getCashEvolutionCountry(filters);

    this.showDatatable.emit({show:true, clickTime: new Date().getTime()});
  }

  isDailyPeriod() {
    return this.selectedFilters?.selectedAggregateBy === 'DAILY'
  }

  isDashboardPage() {
    const path = this.location.path();
    this.isMainDashboardPage = path === '';
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
    this.destroy$.next();
    this.destroy$.complete();
  }
}
