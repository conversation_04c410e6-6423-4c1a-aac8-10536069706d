<brad-dashboard-header class="header" [pageName]="pageName"></brad-dashboard-header>
<div class="reports">
    <section class="reports-filters">
        <brad-dashboard-filters [filtersToShow]="filterToShow" (filterDataEmitter)="receiveFiltersData($event)">
        </brad-dashboard-filters>
    </section>

    <section class="reports-content">
        <brad-cash-evolution-chart
          (showDatatable) = setDataTableVisible($event)
          [selectedFilters]="filtersData">
        </brad-cash-evolution-chart>
    </section>

    <section *ngIf="showDataTable" class="reports-content">
        <brad-cash-evolution-datatable [timeClicked]="timeClicked"></brad-cash-evolution-datatable>
    </section>
</div>
