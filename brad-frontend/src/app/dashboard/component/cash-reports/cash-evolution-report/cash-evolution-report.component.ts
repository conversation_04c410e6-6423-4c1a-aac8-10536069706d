import {Component, OnD<PERSON>roy} from '@angular/core';
import {CashEvolutionFilters, EntityType, FilterTypes} from "../../../types/dashboard.interface";
import {formatDate} from "@angular/common";

@Component({
  selector: 'brad-cash-evolution-report',
  templateUrl: './cash-evolution-report.component.html',
  styleUrls: ['./cash-evolution-report.component.scss']
})
export class CashEvolutionReportComponent implements OnDestroy {

  protected readonly EntityType = EntityType;
  pageName = 'cash evolution'
  filtersData: CashEvolutionFilters | null = null;
  showDataTable = false;
  timeClicked = 0;

  selectedDate = formatDate(new Date(), 'MM/dd/yyyy', 'en-US');
  selectedDateRange: {start: string, end: string} | null = null;
  usedDateRange = false;
  selectedAccount: string | null = null;
  selectedAccountType: string | null = null;
  selectedEntity: string | null = null;
  selectedCountry: string | null = null;
  selectedCurrency: string | null = null;
  selectedAggregateBy: string | null = null;
  isAggregatedByPeriod = false;
  filterToShow: Array<FilterTypes> = [
    FilterTypes.DATE,
    FilterTypes.COUNTRY,
    FilterTypes.CURRENCY,
    FilterTypes.ACCOUNT_TYPE,
    FilterTypes.AGGREGATE_BY,
    FilterTypes.ENTITY
  ];

  receiveFiltersData($event: any) {
    const {
      selectedDate,
      selectedDateRange,
      usedDateRange,
      selectedAccountType,
      selectedEntity,
      selectedCurrency,
      selectedCountry,
      selectedAggregateBy,
      isAggregatedByPeriod
    } = $event;

    this.selectedDate = selectedDate;
    this.selectedDateRange = selectedDateRange;
    this.usedDateRange = usedDateRange;
    this.selectedAccountType = selectedAccountType;
    this.selectedAggregateBy = selectedAggregateBy;
    this.selectedEntity = selectedEntity;
    this.selectedCurrency = selectedCurrency;
    this.selectedCountry = selectedCountry;
    this.isAggregatedByPeriod = isAggregatedByPeriod;

    this.filtersData = {
      selectedDate,
      selectedDateRange,
      usedDateRange,
      selectedEntity,
      selectedCountry,
      selectedCurrency,
      selectedAccountType,
      selectedAggregateBy,
      isAggregatedByPeriod
    }
  }

  setDataTableVisible($event: { show: boolean, clickTime: number }) {
    this.showDataTable = $event.show;
    this.timeClicked = $event.clickTime;
  }

  ngOnDestroy() {
    localStorage.removeItem('filters');
  }
}
