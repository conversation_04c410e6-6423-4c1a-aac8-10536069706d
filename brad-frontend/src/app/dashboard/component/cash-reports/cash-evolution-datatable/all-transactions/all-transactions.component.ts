import {ChangeDetectionStrategy, Component, Input} from '@angular/core';
import {Transaction} from "../../../../../entities/transaction/transaction";

@Component({
  selector: 'brad-all-transactions',
  templateUrl: './all-transactions.component.html',
  styleUrls: ['./all-transactions.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AllTransactionsComponent {
  @Input() transaction: Transaction | null = null;
}
