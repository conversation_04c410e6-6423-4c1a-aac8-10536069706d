<!--Transactions data-->
<section class="all-transaction">
  <div>{{ transaction?.direction }}</div>
  <div>
    <div>
      {{transaction?.currency?.symbol}}
      {{ transaction?.amount ?? '-' | number:'1.2-2'| thousandsFormatter }}
    </div>
    <div *ngIf="transaction?.amountUsd; else emptyAmount;">
      $ {{ transaction?.amountUsd | number:'1.2-2'| thousandsFormatter }}
    </div>
  </div>
  <div>{{ transaction?.currency?.code }}</div>
  <div>{{ transaction?.transactionDate }}</div>
</section>

<ng-template #emptyAmount>
  <div> $ -- </div>
</ng-template>

