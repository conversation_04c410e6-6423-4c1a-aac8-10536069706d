import {ChangeDetectionStrategy, Component, Input} from '@angular/core';
import {EntityTableData} from "../../../../types/dashboard.interface";

@Component({
  selector: 'brad-total-bank-transactions',
  templateUrl: './total-bank-transactions.component.html',
  styleUrls: ['./total-bank-transactions.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TotalBankTransactionsComponent {
  @Input() transaction: Partial<EntityTableData> | null = null;
}
