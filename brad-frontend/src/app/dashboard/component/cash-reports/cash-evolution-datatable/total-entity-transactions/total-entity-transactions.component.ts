import {ChangeDetectionStrategy, Component, Input} from '@angular/core';
import {EntityTableData} from "../../../../types/dashboard.interface";

@Component({
  selector: 'brad-total-entity-transactions',
  templateUrl: './total-entity-transactions.component.html',
  styleUrls: ['./total-entity-transactions.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TotalEntityTransactionsComponent {
  @Input() transaction: Partial<EntityTableData> | null = null;
}
