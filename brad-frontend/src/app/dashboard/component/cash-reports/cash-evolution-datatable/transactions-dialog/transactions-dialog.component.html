<div class="transaction-dialog">
  <div class="transaction-dialog__title" mat-dialog-title>
    <section>
      <span>{{'DASHBOARD.DATATABLE.BANK_ACCOUNT' | translate}}: {{data.account}}</span>
      <mat-icon (click)="closeDialog()">close</mat-icon>
    </section>
    <mat-progress-bar *ngIf="loading$ | async" mode="indeterminate"></mat-progress-bar>
  </div>

  <mat-dialog-content>
    <div class="transaction-dialog__content">
      <table mat-table [dataSource]="dataSource" matSort class="alternate-rows" (matSortChange)="onSortChange($event)">
        <!-- Transaction type Column -->
        <ng-container matColumnDef="direction">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'DASHBOARD.DATATABLE.TRANSACTION_DESCRIPTION' | translate}} </th>
          <td mat-cell *matCellDef="let element">{{element?.direction}}</td>
        </ng-container>

        <!-- Amount Column -->
        <ng-container matColumnDef="amount">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'DASHBOARD.DATATABLE.VALUE' | translate}} </th>
          <td mat-cell *matCellDef="let element">
            <div>{{element?.currency.symbol}} {{ element?.amount | number:'1.2-2' | thousandsFormatter }}</div>
            <div *ngIf="element?.amountUsd; else emptyAmount;">
              $ {{element?.amountUsd | number:'1.2-2'| thousandsFormatter}}
            </div>
          </td>
        </ng-container>

        <!-- Currency Column -->
        <ng-container matColumnDef="currency">
          <th mat-header-cell *matHeaderCellDef> {{'DASHBOARD.DATATABLE.CURRENCY' | translate}} </th>
          <td mat-cell *matCellDef="let element">{{ element?.currency.code }}</td>
        </ng-container>

        <!-- Date Column -->
        <ng-container matColumnDef="transactionDate">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'DASHBOARD.DATATABLE.DATE' | translate}} </th>
          <td mat-cell *matCellDef="let element">{{element?.transactionDate}}</td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>

      <mat-paginator
        [pageSizeOptions]="pagination.pageSizeOptions"
        [pageSize]="pagination.pageSize"
        [length]="pagination.totalItems"
        [pageIndex]="pagination.pageIndex"
        (page)="onPageChange($event)"
        showFirstLastButtons>
      </mat-paginator>
    </div>
  </mat-dialog-content>

  <ng-template #emptyAmount>
    <div> $ -- </div>
  </ng-template>
</div>
