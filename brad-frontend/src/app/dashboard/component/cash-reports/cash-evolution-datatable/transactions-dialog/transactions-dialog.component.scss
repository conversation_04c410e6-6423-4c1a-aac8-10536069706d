.transaction-dialog {
  min-width: 1000px;
  padding: 0 10px 10px 10px;
  position: relative;

  &__title {
    padding: 0;
    z-index: 10;
    background-color: white;
    position: relative;

    section {
      display: flex;
      flex-direction: row;
      font-size: 20px;
      font-weight: 500;
      justify-content: space-between;
      align-items: center;
      min-width: 1000px;
      height: 53px;
      padding: 0 0 0 10px;
      position: absolute;
      top: 0;
      mat-icon {
        cursor: pointer;
        margin-right: 30px;
      }
    }
  }

  &__content {
    padding: 0 10px;

    table {
      th {
        background-color: #f0f0f0;
      }
    }
  }
}

.mdc-dialog__content {
  padding: 0;
  max-height: 700px;
}

.alternate-rows {
  & tr:nth-child(even) {
    background-color: #FAFAFB;
  }
  & tr:nth-child(odd) {
    background-color: #ffffff;
  }
}
