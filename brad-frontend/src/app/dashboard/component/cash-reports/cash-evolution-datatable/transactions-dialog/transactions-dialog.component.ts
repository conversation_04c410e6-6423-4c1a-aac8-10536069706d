import {AfterViewInit, Component, Inject, OnInit, ViewChild} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from "@angular/material/dialog";
import {CsFinPagination, CsFinSortingDataAccessorHelperService} from "@jumia-cs-fin/common";
import {Transaction} from "../../../../../entities/transaction/transaction";
import {MatPaginator, PageEvent} from "@angular/material/paginator";
import {MatTableDataSource} from "@angular/material/table";
import {paginationConsts} from "../../../../../shared/constants/core.constants";
import {MatSort, Sort} from "@angular/material/sort";
import {TransactionFilters} from "../../../../../entities/transaction/transaction-filters";
import {debounceTime, takeUntil} from "rxjs/operators";
import {CashEvolutionFacade} from "../../../../facade/cash-evolution.facade";
import {Observable, Subject} from "rxjs";
import {TransactionFacade} from "../../../../../accounts/facade/transaction.facade";
import isEqual from "lodash/isEqual";
import {SortFilters} from "../../../../../entities/SortFilters";

interface DialogData {
  account: string;
  accountId: number;
}

@Component({
  selector: 'brad-transactions-dialog',
  templateUrl: './transactions-dialog.component.html',
  styleUrls: ['./transactions-dialog.component.scss']
})

export class TransactionsDialogComponent implements AfterViewInit {

  constructor(
    private cashEvolutionFacade: CashEvolutionFacade,
    public dialogRef: MatDialogRef<TransactionsDialogComponent>,
    private transactionFacade: TransactionFacade,
    @Inject(MAT_DIALOG_DATA) public data: DialogData
    ) {
    this.loading$ = this.cashEvolutionFacade.cashEvolutionTableLoading$
  }

  loading$: Observable<boolean>;
  displayedColumns: string[] = ['direction', 'amount', 'currency', 'transactionDate'];
  dataSource: MatTableDataSource<Transaction> = new MatTableDataSource<Transaction>([]);

  private destroy$ = new Subject<void>();
  private sortChangeSubject = new Subject<Sort>();
  private filters: TransactionFilters = {
    page: 1,
    size: paginationConsts.defaultPageSize
  };

  pagination: CsFinPagination = {
    pageSizeOptions: paginationConsts.pageSizeOptions,
    pageSize: paginationConsts.defaultPageSize,
    pageIndex: 0,
    totalItems: 0
  };

  @ViewChild(MatPaginator, {static: true}) paginator!: MatPaginator;
  @ViewChild(MatSort, {static: true}) sort!: MatSort;

  ngAfterViewInit() {
    this.sortChangeSubject
      .pipe(
        debounceTime(200),
        takeUntil(this.destroy$)
      )
      .subscribe(($event: { direction: string; active: string; }) => {
        const sortFiltersBefore = {
          orderDirection: this.filters.orderDirection,
          orderField: this.filters.orderField
        };

        this.filters.orderDirection = $event.direction?.toUpperCase();
        this.filters.orderField = this.transactionFacade.encodeSortField($event.active);

        if (!isEqual(sortFiltersBefore, this.filters as SortFilters)) {
          this.loadTransactions(this.filters);
        }
      });

    this.loadTransactions(this.filters);
  }

  loadTransactions(filters?: TransactionFilters): void {
    const filter = {
      ...filters,
      partitionKey: this.data.accountId.toString()
    };
    this.cashEvolutionFacade.getCashEvolutionTransactions(filter);

    this.cashEvolutionFacade.cashEvolutionTransactionsData$
      .pipe(takeUntil(this.destroy$))
      .subscribe((transactions) => {
        this.dataSource.data = transactions?.results || [];
        this.dataSource.sort = this.sort;
        this.dataSource.sortingDataAccessor = CsFinSortingDataAccessorHelperService.objectSortingDataAccessor;
        this.pagination.totalItems = transactions?.total || 0;
        this.pagination.pageSize = transactions?.size || paginationConsts.defaultPageSize;
        this.pagination.pageIndex = (transactions?.page || 1) - 1;
      });
  }

  onPageChange($event: PageEvent) {
    this.filters.page = $event.pageIndex + 1;
    this.filters.size = $event.pageSize;
    this.loadTransactions(this.filters);
  }

  onSortChange($event: Sort) {
    this.sortChangeSubject.next($event);
  }

  closeDialog(): void {
    this.dialogRef.close();
  }
}

