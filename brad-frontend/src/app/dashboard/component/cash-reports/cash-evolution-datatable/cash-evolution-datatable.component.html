<div class="dataTable">
  <div class="dataTable-header">
    <div>{{'DASHBOARD.DATATABLE.COUNTRY' | translate}}</div>
    <div>{{'DASHBOARD.DATATABLE.LEGAL_ENTITY' | translate}}</div>
    <div>{{'DASHBOARD.DATATABLE.BANK_ACCOUNT' | translate}}</div>
    <div>{{'DASHBOARD.DATATABLE.TRANSACTION_DESCRIPTION' | translate}}</div>
    <div>{{'DASHBOARD.DATATABLE.VALUE' | translate}}</div>
    <div>{{'DASHBOARD.DATATABLE.CURRENCY' | translate}}</div>
    <div>{{'DASHBOARD.DATATABLE.DATE' | translate}}</div>
  </div>

  <mat-progress-bar *ngIf="loading$ | async" mode="query"></mat-progress-bar>

  <section class="dataTable-container">
    <div *ngFor="let country of countries; let i = index" class="dataTable-container__row">
      <!--Country column-->
      <div class="dataTable-container__row-column">
        <div (click)="showEntities(country.id, i)" [ngClass]="{'selected-option': i === selectedIndex}">
          {{ country.name }}
          <mat-icon [ngClass]="{'rotated': i === selectedIndex}">
            keyboard_arrow_down</mat-icon>
        </div>
      </div>

      <!--Legal entity column-->
      <div *ngIf="i === selectedIndex" class="dataTable-container__row-column" >
        <div *ngFor="let entity of filteredLegalEntities; let j = index"
          (click)="showBankAccounts(entity.groupLabel, j)"
          [ngClass]="{'selected-option': j === selectedEntityIndex}">
          {{ entity.groupLabel }}
          <mat-icon [ngClass]="{'rotated': j === selectedEntityIndex}">
            keyboard_arrow_down</mat-icon>
        </div>
      </div>

      <!--Bank accounts column-->
      <div *ngIf="i === selectedIndex" class="dataTable-container__row-column">
        <div *ngFor="let account of filteredAccounts; let x = index"
          (click)="showTransactions(account.account?.id, account.account?.navReference, x)"
          [ngClass]="{'selected-option': x === selectedBankIndex}">
          {{ account.account?.navReference }}
          <mat-icon [ngClass]="{'rotated': x === selectedBankIndex}">keyboard_arrow_down</mat-icon>
        </div>
      </div>

      <!--**Transactions data column**-->
      <div *ngIf="i !== selectedIndex" class="dataTable-container__row-defaultSumColumn">
        <!-- Country total transactions-->
        <brad-total-country-transactions [transaction]="country"></brad-total-country-transactions>
      </div>

      <div *ngIf="filteredLegalEntities?.length && !filteredAccounts?.length && selectedIndex === i"
        class="dataTable-container__row-entitySumColumn">
        <!-- Entity total transactions-->
        <brad-total-entity-transactions *ngFor="let entity of filteredLegalEntities" [transaction]="entity">
        </brad-total-entity-transactions>
      </div>

      <div *ngIf="filteredAccounts?.length && !filteredTransactions?.results?.length && selectedIndex === i"
        class="dataTable-container__row-bankSumColumn">
        <!-- Bank total transactions-->
        <brad-total-bank-transactions *ngFor="let account of filteredAccounts" [transaction]="account">
        </brad-total-bank-transactions>
      </div>

      <!-- ALl bank transactions-->
      <div *ngIf="i === selectedIndex && filteredTransactions?.results?.length"
           class="dataTable-container__row-allTransactionsColumn">
        <ng-container *ngIf="filteredTransactions?.results?.slice(0, 10)?.length">
          <div class="alternate-rows" *ngFor="let transaction of filteredTransactions?.results?.slice(0, 10)">
            <brad-all-transactions [transaction]="transaction"></brad-all-transactions>
          </div>
        </ng-container>

        <div class="view-more" *ngIf="filteredTransactions?.results?.length > 9">
          <span (click)="openDialog()">{{'DASHBOARD.DATATABLE.VIEW_MORE' | translate}}</span>
        </div>
      </div>
    </div>
  </section>
</div>
