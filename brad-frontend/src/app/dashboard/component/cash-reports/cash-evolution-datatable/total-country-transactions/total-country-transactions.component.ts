import {ChangeDetectionStrategy, Component, Input} from '@angular/core';
import {CountryTableData} from "../../../../types/dashboard.interface";

@Component({
  selector: 'brad-total-country-transactions',
  templateUrl: './total-country-transactions.component.html',
  styleUrls: ['./total-country-transactions.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TotalCountryTransactionsComponent {
  @Input() transaction: Partial<CountryTableData> | null = null;
}
