.dataTable {
  display: flex;
  flex-direction: column;
  border: 1px solid #ccc;
  border-radius: 5px;
  overflow: hidden; /* Ensure rounded corners are respected */

  &-header {
    display: grid;
    grid-template-columns: repeat(7, minmax(100px, 1fr)); /* Adjust number of columns as needed */
    background-color: #f0f0f0;
    font-weight: bold;
    padding: 8px;
    text-align: left;
    border-bottom: 1px solid #ccc;

    div:not(:first-child) {
      margin-left: 4px;
    }
  }

  &-container {
    display: flex;
    flex-direction: column;

    &__row {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      border-bottom: 1px solid #eee;
      flex-wrap: nowrap;
      width: 100%;

      &:nth-child(even) {
        background-color: #f9f9f9;
      }

      &:last-child {
        border-bottom: none;
      }

      &-column {
        display: flex;
        flex-direction: column;
        flex: 1;

        div {
          padding: 4px;
          cursor: pointer;
          display: flex;
          align-items: center;
        }
      }

      &-defaultSumColumn,
      &-entitySumColumn,
      &-bankSumColumn,
      &-allTransactionsColumn {
        display: flex;
        flex-direction: column;
        justify-content: center;
        flex: 4;

        .alternate-rows {
          &:nth-child(odd) {
            background-color: #FAFAFB;
          }
          &:nth-child(even) {
            background-color: #ffffff;
          }
        }

        .view-more {
          margin: 5px auto;
          text-align: center;
          cursor: pointer;
          color: #20C4FC;
          width: 100%;
          font-weight: 600;
          text-transform: capitalize;
        }
      }
    }
  }
}

.rotated {
  transform: rotate(-90deg);
  transition: transform 0.3s ease-in-out;
}

.selected-option {
  color: #FF7133;
  border-radius: 5px;
  font-weight: 900;
}
