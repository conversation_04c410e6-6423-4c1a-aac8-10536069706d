import {ChangeDetectionStrategy, Component, Input, OnChanges, OnDestroy, OnInit, SimpleChanges} from '@angular/core';
import {CashEvolutionFacade} from "../../../facade/cash-evolution.facade";
import {MatDialog} from "@angular/material/dialog";
import {TransactionsDialogComponent} from "./transactions-dialog/transactions-dialog.component";
import {takeUntil} from "rxjs/operators";
import {Observable, Subject} from "rxjs";
import {CsFinPageResponse} from "@jumia-cs-fin/common";
import {Transaction} from "../../../../entities/transaction/transaction";
import {CountryTableData, EntityTableData} from "../../../types/dashboard.interface";

@Component({
  selector: 'brad-cash-evolution-datatable',
  templateUrl: './cash-evolution-datatable.component.html',
  styleUrls: ['./cash-evolution-datatable.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})

export class CashEvolutionDatatableComponent implements OnInit, OnChanges, OnDestroy {

  @Input() timeClicked = 0;
  private destroy$ = new Subject<void>();

  constructor(
    public dialog: MatDialog,
    private cashEvolutionFacade: CashEvolutionFacade
  ) {
    this.loading$ = this.cashEvolutionFacade.cashEvolutionTableLoading$
  }

  loading$: Observable<boolean>;
  countries: Partial<CountryTableData>[] = [];
  filteredLegalEntities: Partial<EntityTableData>[] = [];
  filteredAccounts: Partial<EntityTableData>[] = [];
  filteredTransactions: CsFinPageResponse<Transaction> | null = null;

  selectedIndex: number| null = null;
  selectedEntityIndex: number| null = null;
  selectedBankIndex: number| null = null;
  selectedBankAccountName: string | undefined = undefined;
  selectedBankAccId: number | undefined = undefined;
  selectedEntity: number | undefined  = undefined;

  ngOnInit() {
    this.fetchCountries();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['timeClicked']) {
      this.selectedIndex = null;
    }
  }

  fetchCountries() {
    this.cashEvolutionFacade.cashEvolutionCountriesData$
      .pipe(takeUntil(this.destroy$))
      .subscribe((data) => {
        this.countries = data;
      });
  }

  showEntities(countryId: number | undefined, index: number) {
    this.filteredLegalEntities = [];
    this.filteredAccounts = [];
    this.filteredTransactions = null;

    this.selectedEntityIndex = null;
    this.selectedBankIndex = null;

    this.selectedIndex = index;
    this.selectedEntity = countryId;

    this.cashEvolutionFacade.getCashEvolutionEntities(countryId);

    this.cashEvolutionFacade.cashEvolutionEntitiesData$
      .pipe(takeUntil(this.destroy$))
      .subscribe((data) => {
        this.filteredLegalEntities = data;
      });
  }

  showBankAccounts(entityName: string | undefined , index: number) {
    this.filteredAccounts = [];
    this.filteredTransactions = null;

    this.selectedBankIndex = null;

    this.selectedEntityIndex = index;

    this.cashEvolutionFacade.getCashEvolutionBanks(entityName || '');

    this.cashEvolutionFacade.cashEvolutionBanksData$
      .pipe(takeUntil(this.destroy$))
      .subscribe((data) => {
        this.filteredAccounts = data;
      });
  }

  showTransactions(bankAccountId: number | undefined, accountName: string | undefined, index: number) {
    this.filteredTransactions = null;

    this.selectedBankIndex = index;
    this.selectedBankAccountName = accountName;
    this.selectedBankAccId = bankAccountId;

    const filters = {
      partitionKey: `${bankAccountId}`
    }

    this.cashEvolutionFacade.getCashEvolutionTransactions(filters);

    this.cashEvolutionFacade.cashEvolutionTransactionsData$
      .pipe(takeUntil(this.destroy$))
      .subscribe((transactions) => {
        this.filteredTransactions = transactions;
      });
  }

  openDialog() {
    this.dialog.open(TransactionsDialogComponent, {
      data: {
        accountId: this.selectedBankAccId,
        account: this.selectedBankAccountName
      },
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
