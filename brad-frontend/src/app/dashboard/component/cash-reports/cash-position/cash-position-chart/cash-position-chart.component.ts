import {AfterViewInit, ChangeDetectionStrategy, Component, Inject, Input, On<PERSON><PERSON>roy, OnInit} from '@angular/core';
import {EChartsCoreOption} from "echarts/core";
import {EntityType} from "../../../../types/dashboard.interface";
import {CashPositionFacade} from "../../../../facade/cash-position.facade";

@Component({
  selector: 'brad-cash-position-chart',
  templateUrl: './cash-position-chart.component.html',
  styleUrls: ['./cash-position-chart.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class CashPositionChartComponent implements OnInit, AfterViewInit, OnDestroy{

  @Input() title: EntityType = EntityType.COUNTRY;

  data: any;
  private subscription: any;

  constructor(
    private cashPositionFacade: CashPositionFacade
  ) {}

  data1: number[] = [];
  data2: number[] = [];
  data3: number[] = [];
  data4: number[] = [];

  emphasisStyle = {
    itemStyle: {
      shadowBlur: 10,
      shadowColor: 'rgba(0,0,0,0.3)'
    }
  };

  chartOption: EChartsCoreOption = {};

  ngOnInit() {
    this.fetchCashPositionData('05-01-2025');


    this.generateValues();
    this.chartOption = {
      title: {
        text: `By ${this.title}`,
        left: 'center'
      },
      legend: {
        data: ['Jumia Mall', 'Jumia Pay', 'PSP', 'Wallet'], // accounts/entities
        left: '1%',
        bottom: '10%'
      },
      toolbox: {
        feature: {
          dataZoom: {
            yAxisIndex: 'none'
          },
          magicType: {
            type: ['stack', 'line', 'bar']
          },
          restore: {},
          saveAsImage: {}
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      xAxis: {
        data: ['Kenya', 'Nigeria', 'Ghana', 'Uganda', 'Egypt'], //countries
        name: 'X Axis',
        axisLabel: {
          rotate: 45
        },
        axisLine: { onZero: true },
        splitLine: { show: false },
        splitArea: { show: false }
      },
      yAxis: {},
      grid: {
        left: '1%',
        right: '1%',
        bottom: 90,
        containLabel: true
      },
      series: [
        {
          name: 'Jumia Mall',
          type: 'bar',
          stack: 'one',
          emphasis: this.emphasisStyle,
          data: this.data1
        },
        {
          name: 'Jumia Pay',
          type: 'bar',
          stack: 'one',
          emphasis: this.emphasisStyle,
          data: this.data2
        },
        {
          name: 'PSP',
          type: 'bar',
          stack: 'one',
          emphasis: this.emphasisStyle,
          data: this.data3
        },
        {
          name: 'Wallet',
          type: 'bar',
          stack: 'one',
          emphasis: this.emphasisStyle,
          data: this.data4
        }
      ]
    };
  }

  ngAfterViewInit() {
    this.subscription = this.cashPositionFacade.cashPositionChartData$.subscribe(value => {
      // console.log('Received new value:', value);
      this.data = value; // Update local variable
    });

  }

  generateValues() {
    for (let i = 0; i < 10; i++) {
      this.data1.push(+(Math.random() * 2).toFixed(2));
      this.data2.push(+(Math.random() * 5).toFixed(2));
      this.data3.push(+(Math.random() + 0.3).toFixed(2));
      this.data4.push(+Math.random().toFixed(2));
    }
    // console.table([this.data1, this.data2, this.data3, this.data4]);
    // console.log('data1:', this.data1)
  }

  fetchCashPositionData(date: string){
    // this.cashPositionFacade.getCashPosition(date);
  }

  onSelect(event: any) {
    // console.log(event);
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

}
