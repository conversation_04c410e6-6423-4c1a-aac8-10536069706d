import {Component, OnInit} from '@angular/core';
import { EChartsCoreOption } from 'echarts/core';

@Component({
  selector: 'brad-statement-status',
  templateUrl: './statement-status-chart.component.html',
  styleUrls: ['./statement-status-chart.component.scss']
})
export class StatementStatusChartComponent implements OnInit{

  chartOption: EChartsCoreOption = {};

  ngOnInit() {
    this.chartOption = {
      title: {
        text: 'Statement Status',
        left: 'center',
        show: false
      },
      toolbox: {
        feature: {
          saveAsImage: {}
        },
        right: '2%'
      },
      tooltip: {
        trigger: 'item'
      },
      legend: {
        orient: 'horizontal',
        bottom: 'left'
      },
      series: [
        {
          type: 'pie',
          radius: ['30%', '60%'],
          startAngle: 90,
          // label: { show: false },
          data: [
            {
              name: "Errors",
              value: 9,
              itemStyle: { color: '#FF7133' }
            },
            {
              name: "Success",
              value: 91,
              itemStyle: { color: '#A5A5A5' }
            }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    };
  }

}
