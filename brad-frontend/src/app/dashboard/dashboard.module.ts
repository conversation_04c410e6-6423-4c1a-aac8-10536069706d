import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {CashEvolutionChartComponent} from './component/cash-reports/cash-evolution-chart/cash-evolution-chart.component';
import {MatProgressBarModule} from '@angular/material/progress-bar';
import {TranslateModule} from '@ngx-translate/core';
import {MatTooltipModule} from '@angular/material/tooltip';
import {MatIconModule} from '@angular/material/icon';
import {MatButtonModule} from '@angular/material/button';
import {MatFormFieldModule} from '@angular/material/form-field';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {MatInputModule} from '@angular/material/input';
import {MatSelectModule} from '@angular/material/select';
import {SharedModule} from '../shared/shared.module';
import {MatTableModule} from '@angular/material/table';
import {MatSortModule} from '@angular/material/sort';
import {MatPaginatorModule} from '@angular/material/paginator';
import {MatDialogModule} from '@angular/material/dialog';
import {MatCardModule} from '@angular/material/card';
import {MatDividerModule} from '@angular/material/divider';
import {MatToolbarModule} from '@angular/material/toolbar';
import {MatMenuModule} from '@angular/material/menu';
import {MatCheckboxModule} from "@angular/material/checkbox";
import {MatListModule} from "@angular/material/list";
import {MatTabsModule} from "@angular/material/tabs";
import {MatDatepickerModule} from "@angular/material/datepicker";
import {MatExpansionModule} from "@angular/material/expansion";
import {MatProgressSpinnerModule} from "@angular/material/progress-spinner";
import {FlexLayoutModule} from "@angular/flex-layout";
import {OverlayModule} from "@angular/cdk/overlay";
import {NgxMatSelectSearchModule} from "ngx-mat-select-search";
import {DragDropModule} from "@angular/cdk/drag-drop";
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

import {NgxEchartsModule} from 'ngx-echarts';
import * as echarts from 'echarts/core';
import { BarChart, PieChart, LineChart } from 'echarts/charts';
import {
  GridComponent,
  TitleComponent,
  TooltipComponent,
  ToolboxComponent,
  LegendComponent,
  BrushComponent,
  DataZoomComponent
} from 'echarts/components';

import { CanvasRenderer } from 'echarts/renderers';
import { LabelLayout, UniversalTransition } from 'echarts/features';
echarts.use([
  BarChart,
  PieChart,
  LineChart,
  GridComponent,
  TitleComponent,
  TooltipComponent,
  ToolboxComponent,
  LegendComponent,
  BrushComponent,
  DataZoomComponent,
  CanvasRenderer,
  LabelLayout,
  UniversalTransition
]);

import {NgxDropzoneModule} from "ngx-dropzone";
import {
  CsFinActiveFiltersModule,
  CsFinAddRemoveColumnsModule,
  CsFinAuthModule,
  CsFinFlagModule,
  CsFinNotificationModule,
  CsFinUtilsModule
} from "@jumia-cs-fin/common";
import {NgxDocViewerModule} from "ngx-doc-viewer";
import {MatSlideToggleModule} from "@angular/material/slide-toggle";
import {DashboardRoutingModule} from "./dashboard-routing.module";
import {DashboardComponent} from './dashboard.component';
import { StatementsComponent } from './component/statements/statements.component';
import { StatementStatusChartComponent } from './component/statements/statement-status/statement-status-chart.component';
import { StatementErrorsComponent } from './component/statements/statement-errors/statement-errors.component';
import { CashPositionChartComponent } from './component/cash-reports/cash-position/cash-position-chart/cash-position-chart.component';
import { CashAccountBalanceComponent } from './component/cash-reports/cash-account-balance/cash-account-balance.component';
import { DashboardFiltersComponent } from './component/dashboard-filters/dashboard-filters.component';
import { CashEvolutionReportComponent } from './component/cash-reports/cash-evolution-report/cash-evolution-report.component';
import { DashboardHeaderComponent } from './component/dashboard-header/dashboard-header.component';
import { CashEvolutionDatatableComponent } from './component/cash-reports/cash-evolution-datatable/cash-evolution-datatable.component';
import {MatTreeModule} from "@angular/material/tree";
import {BrowserModule} from "@angular/platform-browser";
import { AllTransactionsComponent } from './component/cash-reports/cash-evolution-datatable/all-transactions/all-transactions.component';
import { TransactionsDialogComponent } from './component/cash-reports/cash-evolution-datatable/transactions-dialog/transactions-dialog.component';
import { TotalCountryTransactionsComponent } from './component/cash-reports/cash-evolution-datatable/total-country-transactions/total-country-transactions.component';
import { TotalEntityTransactionsComponent } from './component/cash-reports/cash-evolution-datatable/total-entity-transactions/total-entity-transactions.component';
import { TotalBankTransactionsComponent } from './component/cash-reports/cash-evolution-datatable/total-bank-transactions/total-bank-transactions.component';


@NgModule({
  declarations: [
    CashEvolutionChartComponent,
    DashboardComponent,
    StatementsComponent,
    StatementStatusChartComponent,
    StatementErrorsComponent,
    CashPositionChartComponent,
    CashAccountBalanceComponent,
    DashboardFiltersComponent,
    CashEvolutionReportComponent,
    DashboardHeaderComponent,
    CashEvolutionDatatableComponent,
    AllTransactionsComponent,
    TransactionsDialogComponent,
    TotalCountryTransactionsComponent,
    TotalEntityTransactionsComponent,
    TotalBankTransactionsComponent
  ],
  imports: [
    CommonModule,
    DashboardRoutingModule,
    SharedModule,
    MatDialogModule,
    MatProgressBarModule,
    TranslateModule,
    MatTooltipModule,
    MatIconModule,
    MatButtonModule,
    MatFormFieldModule,
    FormsModule,
    CsFinAuthModule,
    CsFinActiveFiltersModule,
    CsFinAddRemoveColumnsModule,
    CsFinNotificationModule,
    CsFinUtilsModule,
    MatInputModule,
    MatSelectModule,
    MatTableModule,
    MatSortModule,
    MatPaginatorModule,
    ReactiveFormsModule,
    MatCardModule,
    MatDividerModule,
    MatToolbarModule,
    MatMenuModule,
    MatCheckboxModule,
    MatListModule,
    MatTabsModule,
    MatDatepickerModule,
    MatExpansionModule,
    MatProgressSpinnerModule,
    MatTreeModule,
    FlexLayoutModule,
    OverlayModule,
    NgxMatSelectSearchModule,
    DragDropModule,
    NgxDropzoneModule,
    CsFinActiveFiltersModule,
    CsFinFlagModule,
    MatSlideToggleModule,
    NgxDocViewerModule,
    BrowserModule,
    BrowserAnimationsModule,
    NgxEchartsModule.forRoot({ echarts })
  ]
})
export class DashboardModule { }
