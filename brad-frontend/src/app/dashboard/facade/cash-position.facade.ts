import { Injectable } from '@angular/core';
import { DashboardApiService} from "../../api/service/dashboard-api.service";
import {BehaviorSubject, Observable} from "rxjs";

@Injectable({
  providedIn: 'root'
})
export class CashPositionFacade {

  private cashPositionChartDataSubject = new BehaviorSubject<any>('Loading...');
  cashPositionChartData$: Observable<string> = this.cashPositionChartDataSubject.asObservable();

  constructor(private dashboardService: DashboardApiService) {}

  getCashPosition(date: string) {
    this.dashboardService.getCashEvolution(date).subscribe(data => {
      this.formatDataForCashPosition(data)
    });
  }

  formatDataForCashPosition(data: any) {
    // Extract and sort by date
    const sortedData = data.data.sort((a: any, b: any): number => +new Date(a.date) - +new Date(b.date));

    // Extract local currencies
    const localCurrencies = sortedData.flatMap((entry: { entries: { currencies: { local: any; }; }[]; }) =>
      entry.entries.map((e: { currencies: { local: any; }; }) => e.currencies.local)
    );

    // Extract formatted dates
    const formattedDates = sortedData.map((entry: { date: string | number | Date; }) => {
      const date = new Date(entry.date);
      return `${date.getDate()} ${date.toLocaleString('en-US', { month: 'short' })}`;
    });

    // Extract grand total in USD
    const grandTotalUSD = sortedData.flatMap((entry: { entries: any[]; }) =>
      entry.entries.map(e => e.grand_total.usd.amount)
    );


    const chartData = {
      localCurrencies,
      formattedDates,
      grandTotalUSD
    };

    this.cashPositionChartDataSubject.next(chartData);
  }


}
