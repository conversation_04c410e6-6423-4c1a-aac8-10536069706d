import { Injectable } from '@angular/core';
import { DashboardApiService} from "../../api/service/dashboard-api.service";
import {BehaviorSubject, catchError, finalize, Observable, of, take, tap} from "rxjs";
import {
  CashEvolutionEntry,
  CashEvolutionFilters,
  CountryTableData,
  EntityTableData
} from "../types/dashboard.interface";
import {Country} from "../../entities/account/country";
import {MatSnackBar} from "@angular/material/snack-bar";
import {CustomSnackbarComponent} from "../../shared/component/custom-snackbar/custom-snackbar.component";
import {TransactionsApiService} from "../../api/service/transactions-api.service";
import {CsFinPageResponse} from "@jumia-cs-fin/common";
import {Transaction} from "../../entities/transaction/transaction";
import {TransactionFilters} from "../../entities/transaction/transaction-filters";

@Injectable({
  providedIn: 'root'
})
export class CashEvolutionFacade {

  openingBalArr: number[] = [];
  cashInData: number[] = [];
  cashOutData: number[] = [];
  urlQuery = '';

  private cashEvolutionChartLoadingSubject = new BehaviorSubject<boolean>(true);
  cashEvolutionChartLoading$: Observable<boolean> = this.cashEvolutionChartLoadingSubject.asObservable();

  private cashEvolutionTableLoadingSubject = new BehaviorSubject<boolean>(true);
  cashEvolutionTableLoading$: Observable<boolean> = this.cashEvolutionTableLoadingSubject.asObservable();

  private cashEvolutionChartDataSubject = new BehaviorSubject<any>('loading');
  cashEvolutionChartData$: Observable<any> = this.cashEvolutionChartDataSubject.asObservable();

  private cashEvolutionCountriesDataSubject = new BehaviorSubject<Partial<CountryTableData>[]>([]);
  cashEvolutionCountriesData$: Observable<Partial<CountryTableData>[]> = this.cashEvolutionCountriesDataSubject.asObservable();

  private cashEvolutionEntitiesDataSubject = new BehaviorSubject<Partial<EntityTableData>[]>([]);
  cashEvolutionEntitiesData$: Observable<Partial<EntityTableData>[]> = this.cashEvolutionEntitiesDataSubject.asObservable();

  private cashEvolutionBanksDataSubject = new BehaviorSubject<Partial<EntityTableData>[]>([]);
  cashEvolutionBanksData$: Observable<Partial<EntityTableData>[]> = this.cashEvolutionBanksDataSubject.asObservable();

  private cashEvolutionTransactionsDataSubject = new BehaviorSubject<CsFinPageResponse<Transaction> | null>(null);
  cashEvolutionTransactionsData$: Observable<CsFinPageResponse<Transaction> | null> = this.cashEvolutionTransactionsDataSubject.asObservable();

  private cashEvolutionFetchErrorSubject = new BehaviorSubject<boolean>(false);
  cashEvolutionFetchError$: Observable<boolean> = this.cashEvolutionFetchErrorSubject.asObservable();

  constructor(
    private dashboardService: DashboardApiService,
    private transactionsApiService: TransactionsApiService,
    private snackBar: MatSnackBar
  ) {}

  isValidDate(dateString: string) {
    // Regular expression to check the format YYYY-MM-DD
    const regex = /^\d{4}-\d{2}-\d{2}$/;

    if (!regex.test(dateString)) return false; // Invalid format

    // Check if it's a valid date
    const date = new Date(dateString);
    return !isNaN(date.getTime()); // Valid date
  }

  convertIdsToString(array: Country[]) {
    return array
      .map(item => item.id)
      .filter(id => id !== undefined)
      .join(',');
  }

  convertDateToISO(dateString: string | undefined) {
    if (!dateString) return new Date().toISOString().split('T')[0];

    const selectedDate = new Date(dateString);
    const correctDate = selectedDate.setDate(selectedDate.getDate() + 1);// Add 1 day
    const date = new Date(correctDate);

    return  new Date(date).toISOString().split('T')[0];
  }

  calculateValue(credit: number, debit: number) {
    if (credit && debit) {
      return credit - debit;
    } else if (credit) {
      return credit;
    } else if (debit) {
      return -debit;
    }
    return 0;
  }

  calculateDateFrom(date: string, period: string): string {
    const dateFrom = new Date(date);
    if (isNaN(dateFrom.getTime())) return date; // Handle invalid date

    let newDate;

    switch (period.toUpperCase()) {
      case "DAILY":
        newDate = new Date(dateFrom);
        newDate.setDate(dateFrom.getDate() - 32);
        break;
      case "MONTHLY":
        newDate = new Date(dateFrom);
        newDate.setMonth(dateFrom.getMonth() - 1);
        break;
      case "QUARTERLY":
        newDate = new Date(dateFrom);
        newDate.setMonth(dateFrom.getMonth() - 3);
        break;
      case "YEARLY":
        newDate = new Date(dateFrom);
        newDate.setFullYear(dateFrom.getFullYear() - 1);
        break;
      default:
        return date;
    }

    return newDate.toISOString().split('T')[0];
  }

  createQueryString(filters: CashEvolutionFilters | null){
    if (!filters) return;

    const period = filters?.selectedAggregateBy ?? 'DAILY';

    if (filters?.selectedDate && !filters.usedDateRange) {
      this.urlQuery = `?evolutionFromDate=${this.calculateDateFrom(filters?.selectedDate, period)}&evolutionToDate=${filters?.selectedDate}`
    }

    if (filters.usedDateRange) {
      this.urlQuery = `?evolutionFromDate=${this.convertDateToISO(filters.selectedDateRange?.start)}&evolutionToDate=${this.convertDateToISO(filters.selectedDateRange?.end)}`
    }

    if (filters?.selectedCountry?.length) {
      this.urlQuery = `${this.urlQuery}&countries=${this.convertIdsToString(filters.selectedCountry)}`
    }

    if (filters?.selectedCurrency?.length) {
      this.urlQuery = `${this.urlQuery}&currency=${filters?.selectedCurrency}`
    }

    if (filters?.selectedAccountType?.length) {
      this.urlQuery = `${this.urlQuery}&accountType=${filters?.selectedAccountType}`
    }

    if (filters?.selectedAccount) {
      this.urlQuery = `${this.urlQuery}&accountName=${filters?.selectedAccount}`
    }

    if (filters?.selectedEntity) {
      this.urlQuery = `${this.urlQuery}&legalEntity=${filters?.selectedEntity}`
    }

    if (period) {
      this.urlQuery = `${this.urlQuery}&evolutionAggregateBy=${period}`
    }

    this.urlQuery = `${this.urlQuery}&isAggregatedByPeriod=${filters.isAggregatedByPeriod}`
  }

  updateQueryString(query: string, queryKey: string, newValue: any) {
    let params = new URLSearchParams(query);
    if (newValue === null || newValue === "null") {
      params.delete(queryKey);
    } else {
      params.set(queryKey, newValue);
    }
    this.urlQuery = `?${params.toString()}`;
  }

  getUrlParamValue(queryString: string, paramName: any) {
    try {
      const dummyUrl = "https://dummy.com" + queryString;
      const params = new URLSearchParams(dummyUrl.split('?')[1]);
      return params.get(paramName) || undefined;
    } catch (error) {
      console.error("Invalid URL:", error);
      return undefined;
    }
  }

  getCashEvolutionCountry(filters: CashEvolutionFilters | null) {
    this.cashEvolutionTableLoadingSubject.next(true);
    this.createQueryString(filters);
    this.updateQueryString(this.urlQuery, 'groupBy', 'COUNTRY' );

    this.dashboardService.getCashEvolutionBreakDown(this.urlQuery).subscribe({
      next: (data) => {
        const countriesArr = data.map(info => {
          return {
            ...info.country,
            value: this.calculateValue(info.creditAmountUsd, info.debitAmountUsd),
          }
        })
        this.cashEvolutionCountriesDataSubject.next(countriesArr);
        this.cashEvolutionTableLoadingSubject.next(false);
      },
      error: (error) => {
        this.cashEvolutionCountriesDataSubject.next([]);
        this.cashEvolutionTableLoadingSubject.next(false);
        this.errorNotification(error.status);
        console.error("Error fetching cash evolution transactions data:", error);
      }
    });
  }

  getCashEvolutionEntities(countryId: number | undefined) {
    this.cashEvolutionTableLoadingSubject.next(true);
    this.updateQueryString(this.urlQuery, 'groupBy', 'LEGAL_ENTITY' );
    this.updateQueryString(this.urlQuery, 'countries', countryId );
    this.updateQueryString(this.urlQuery, 'legalEntity', null );

    this.dashboardService.getCashEvolutionBreakDown(this.urlQuery).subscribe({
      next: (data) => {
        const EntitiesArr = data.map(info => {
          return {
            ...info,
            value: this.calculateValue(info.creditAmountUsd, info.debitAmountUsd)
          }
        })
        this.cashEvolutionEntitiesDataSubject.next(EntitiesArr);
        this.cashEvolutionTableLoadingSubject.next(false);
      },
      error: (error) => {
        this.cashEvolutionEntitiesDataSubject.next([]);
        this.cashEvolutionTableLoadingSubject.next(false);
        this.errorNotification(error.status);
        console.error("Error fetching cash evolution transactions data:", error);
      }
    });
  }

  getCashEvolutionBanks(legalEntity: string) {
    this.cashEvolutionTableLoadingSubject.next(true);
    this.updateQueryString(this.urlQuery, 'groupBy', 'ACCOUNT' );
    this.updateQueryString(this.urlQuery, 'legalEntity', legalEntity);

    this.dashboardService.getCashEvolutionBreakDown(this.urlQuery).subscribe({
      next: (data) => {
        const banksAccountsArr = data.map(info => {
          return {
            ...info,
            value: this.calculateValue(info.creditAmountUsd, info.debitAmountUsd)
          }
        })
        this.cashEvolutionBanksDataSubject.next(banksAccountsArr);
        this.cashEvolutionTableLoadingSubject.next(false);
      },
      error: (error) => {
        this.cashEvolutionBanksDataSubject.next([]);
        this.cashEvolutionTableLoadingSubject.next(false);
        this.errorNotification(error.status);
        console.error("Error fetching cash evolution transactions data:", error);
      }
    });
  }

  getCashEvolutionTransactions(filters?: TransactionFilters) {
    this.cashEvolutionTableLoadingSubject.next(true);
    const dateFrom = this.getUrlParamValue(this.urlQuery, 'evolutionFromDate');
    const dateTo = this.getUrlParamValue(this.urlQuery, 'evolutionToDate');
    const currentDate = new Date();

    const filter: TransactionFilters = {
      ...filters,
      transactionDateEnd: new Date(dateTo || currentDate).toISOString().split('T')[0],
      transactionDateStart: new Date(dateFrom || currentDate).toISOString().split('T')[0],
      partitionKey: filters?.partitionKey
    }

    this.transactionsApiService.getAll(filter).subscribe({
      next: (data) => {
        this.cashEvolutionTransactionsDataSubject.next(data);
        this.cashEvolutionTableLoadingSubject.next(false);
      },
      error: (error) => {
        this.cashEvolutionTransactionsDataSubject.next(null);
        this.cashEvolutionTableLoadingSubject.next(false);
        this.errorNotification(error.status);
        console.error("Error fetching cash evolution transactions data:", error);
      }
    });
  }

  resetTableData() {
    this.cashEvolutionCountriesDataSubject.next([]);
    this.cashEvolutionEntitiesDataSubject.next([]);
    this.cashEvolutionBanksDataSubject.next([]);
    this.cashEvolutionTransactionsDataSubject.next(null);
  }

  getCashEvolution(filters: CashEvolutionFilters | null): Observable<any> {
    this.cashEvolutionChartLoadingSubject.next(true);
    this.createQueryString(filters);

    return this.dashboardService.getCashEvolution(this.urlQuery).pipe(
      take(1),
      finalize(() => this.cashEvolutionChartLoadingSubject.next(false)),
      tap(data => {
        this.formatDataForCashEvaluationChart(data);
        this.cashEvolutionFetchErrorSubject.next(false);
      }),
      catchError(error => {
        this.formatDataForCashEvaluationChart([]);
        this.cashEvolutionFetchErrorSubject.next(true);
        this.errorNotification(error.status);
        console.error("Error fetching cash evolution data:", error);
        return of(null);
      })
    );
  }

  formatDataForCashEvaluationChart(data: CashEvolutionEntry[]) {
    // Extract and format dates for XAxis
    const {xAxisData, rawXAxisData} = this.populateXAxisData(data);

    // Extract opening balance & closing balance total
    const {openBalanceValues, closingBalanceValues} = this.openingClosingBal(data);

    // Extract the cash-in & cash-out data
    const {cashInData, cashOutData} = this.getCashData(data);

    // Format cash data for chart display
    const {formattedCashInData, formattedCashOutData, formattedOpeningBal, formattedClosingBal} = this.formatCashData(cashInData, cashOutData, openBalanceValues, closingBalanceValues);

    //Calculate cash-in/cash-out base values for chart
    const {cashInBaseValue, cashOutBaseValue} = this.calculateIncomeBaseValues(cashInData, cashOutData);

    //Set min value for the chart to correct scale
    const minValue = this.getMinValues(openBalanceValues);

    const chartData = {
      xAxis: xAxisData,
      rawXAxisData,
      formattedOpeningBal,
      formattedClosingBal,
      formattedCashInData,
      formattedCashOutData,
      cashInBaseValue,
      cashOutBaseValue,
      minValue
    };

    this.cashEvolutionChartDataSubject.next(chartData);
    this.cashEvolutionChartLoadingSubject.next(false);
  }

  getMinValues(openingBalanceValues: (string|number)[]) {
    const openingBalance =  Number(openingBalanceValues.at(0));
    const openingBalancePercentage = Math.ceil(Math.abs(openingBalance) * 0.001);

    return openingBalance <= 0 ? undefined : openingBalance - openingBalancePercentage;
  }

  populateXAxisData (data: CashEvolutionEntry[]) {
    const xAxisData: string[] = []
    const rawXAxisData: string[] = []
    data.map((entry) => {
      if (!this.isValidDate(entry.groupLabel)) {
        xAxisData.push(entry.groupLabel);
        return
      }

      const date = new Date(entry.groupLabel);
      const month = date.toLocaleString('default', { month: 'short' });
      const day = date.getDate();
      xAxisData.push(`${month} ${day}`);
      rawXAxisData.push(entry.groupLabel);
    })

    return {xAxisData, rawXAxisData}
  };

  openingClosingBal(data: CashEvolutionEntry[]) {
    let openBalanceValues = new Array(data.length).fill('-');
    let closingBalanceValues = new Array(data.length).fill('-');

    openBalanceValues[0] = data[0]?.initialBalanceUsd || 0;
    closingBalanceValues[data.length - 1] = data.at(-1)?.finalBalanceUsd || 0;

    this.openingBalArr = [...openBalanceValues]

    return {openBalanceValues, closingBalanceValues}
  };

  getCashData(data: CashEvolutionEntry[]) {
    let cashIn: number[] = []
    let cashOut: number[] = []

    data.map(entry => {
      cashOut.push(entry.debitAmountUsd)
      cashIn.push(entry.creditAmountUsd)
    })
    this.cashInData = [...cashIn];
    this.cashOutData = [...cashOut];

    return { cashInData: cashIn, cashOutData: cashOut }
  };

  formatCashData(cashInData: number[], cashOutData: number[], openingTotals: (string|number)[], closingTotals: (string|number)[]) {
    const labelSkip = this.labelsToSkip(cashInData.length);

    const formatData = (dataArray: (number|string)[]) =>
      dataArray.map((entry, index) => ({
        value: entry,
        label: {
          show: index % labelSkip === 0,
          formatter: `${entry !== '-' ? this.formatLargeNumbers(entry) : '-'}`
        }
      }));

    return {
      formattedCashInData: formatData(cashInData),
      formattedCashOutData: formatData(cashOutData),
      formattedOpeningBal: formatData(openingTotals),
      formattedClosingBal: formatData(closingTotals)
    }
  };

  labelsToSkip(dataLength: number) {

    if (dataLength >= 0 && dataLength <= 30) {
      return 1;
    } else if (dataLength >= 31 && dataLength <= 40) {
      return 2;
    } else if (dataLength >= 41 && dataLength <= 60) {
      return 3;
    } else if (dataLength >= 61) {
      return 7;
    } else {
      return 1;
    }
  }

  formatLargeNumbers(num: number | string) {
    if (!num && num !== 0) return '';
    if (typeof num === 'string') return num;

    const absNum = Math.abs(num);
    let formatted;

    if (absNum >= 1_000_000) {
      formatted = (Math.floor(absNum / 1000000 * 10) / 10) + 'M';
    } else if (absNum >= 1000) {
      formatted = (Math.floor(absNum / 1000 * 10) / 10) + 'k';
    } else {
      formatted = absNum.toString();
    }

    return num < 0 ? '-' + formatted : formatted;
  };

  calculateIncomeBaseValues(income: number[], expenditure: number[]) {
    let baseValues = new Array(income.length).fill(0);
    baseValues[0] = this.openingBalArr[0] ?? 0;

    for (let i = 1; i < income.length; i++) {
      baseValues[i] = baseValues[i - 1] + (income[i - 1] - expenditure[i - 1]);
    }

    //Calculate cash-out base values
    const cashOutBaseValue = this.calculateExpenditureBaseValues(baseValues);
    return {
      cashInBaseValue: [...baseValues],
      cashOutBaseValue
    }
  };

  calculateExpenditureBaseValues(incomeBaseValues: number[]){

    const lastBaseValue = incomeBaseValues.at(-1);
    const lastCashIncomeValue = this.cashInData.at(-1);
    const lastCashOutValue = this.cashOutData.at(-1);

    const lastExpBaseValue = ((lastCashIncomeValue || 0) + (lastBaseValue || 0)) - (lastCashOutValue || 0);

    const firstValues = [...incomeBaseValues].slice(1);
    return [...firstValues, lastExpBaseValue];
  };

  errorNotification(errorCode: number) {
    const errorMessages: { [key: string]: string } = {
      '400': "Unable to fetch the data, please report the issue!",
      '401': "Unauthorized! Please login again!",
      '403': "Don't have permission to view resource!",
      '404': "Resource not Found!",
      '500': 'Internal server error. Please try again later!',
      '503': 'Service Unavailable! Please try again later!',
    };

    const message = errorMessages[errorCode.toString()] ||
      "An error occurred loading cash evolution transactions!";

    this.showErrorNotification(message);
  };

  showErrorNotification(message: string) {
    this.snackBar.openFromComponent(CustomSnackbarComponent, {
      duration: 4000,
      data: message,
      panelClass: ['error-snackbar'],
      horizontalPosition: 'center',
      verticalPosition: 'bottom'
    });
  };
}
