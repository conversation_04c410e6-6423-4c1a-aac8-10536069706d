import {Country} from "../../entities/account/country";
import {Account} from "../../entities/account/account";

export enum EntityType {
  COUNTRY = 'Country',
  ENTITY = 'Entity',
  CURRENCY = 'Currency',
  ACCOUNT_TYPE = 'Account Type'
}

export enum FilterTypes {
  ALL = 'All',
  DATE = 'Date',
  COUNTRY = 'Country',
  ENTITY = 'Entity',
  CURRENCY = 'Currency',
  ACCOUNT_NAME = 'Account Name',
  ACCOUNT_TYPE = 'Account Type',
  AGGREGATE_BY = 'Aggregate By'
}

export interface CashEvolutionEntry {
  groupLabel: string;
  initialBalanceUsd: number;
  finalBalanceUsd: number;
  initialBalanceLcy: number;
  finalBalanceLcy: number;
  creditAmountLcy: number;
  debitAmountLcy: number;
  creditAmountUsd: number;
  debitAmountUsd: number;
}

export type CashEvolutionResponse = CashEvolutionEntry[];

export interface CashEvolutionFilters {
  selectedDate: string;
  selectedDateRange?: {start: string; end: string};
  usedDateRange: boolean;
  selectedAccount?: string;
  selectedEntity?: string;
  selectedCountry?: Country[];
  selectedCurrency?: string;
  selectedAccountType?: string;
  selectedAggregateBy?: string;
  isAggregatedByPeriod: boolean;
}

export interface FinancialData {
  groupLabel: string;
  initialBalanceUsd: number | null;
  finalBalanceUsd: number;
  initialBalanceLcy: number | null;
  finalBalanceLcy: number | null;
  creditAmountLcy: number | null;
  debitAmountLcy: number | null;
  creditAmountUsd: number;
  debitAmountUsd: number;
  account: Account | null;
  country: Country | null;
}

export type CashEvolutionChartDataResponse  = FinancialData[];


export interface CountryTableData extends Country{
  value: number;
}

export interface EntityTableData extends FinancialData{
  value: number;
}
