<div class="dashboard">
  <section class="dashboard-filters">
    <brad-dashboard-filters
      [filtersToShow]="filterToShow"
      (filterDataEmitter)="receiveFiltersData($event)"
    ></brad-dashboard-filters>
  </section>

  <div class="dashboard-content">
    <section class="dashboard-content__left">
      <brad-cash-account-balance *ngIf="showBalances"></brad-cash-account-balance>
      <brad-cash-evolution-chart [selectedFilters]="filtersData"></brad-cash-evolution-chart>
      <mat-card *ngIf="showCashPosition" class="cash-position" appearance="raised">
        <div class="cash-position-title">
          <a routerLink="/cash_position">Cash Position</a>
        </div>
        <section>
          <brad-cash-position-chart [title]="EntityType.COUNTRY" ></brad-cash-position-chart>
          <brad-cash-position-chart [title]="EntityType.CURRENCY"></brad-cash-position-chart>
        </section>
      </mat-card>
    </section>

    <section  class="dashboard-content__right">
      <ng-container *ngIf=" showStatementReport">
        <brad-statements></brad-statements>
      </ng-container>
    </section>
  </div>
</div>
