import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {NavigationComponent} from "../navigation/navigation.component";
import {
  csFinAuthCanActivateGuard, CsFinAuthorizationOnAnyTargetOfTypeService
} from "@jumia-cs-fin/common";
import {bradPermissions} from "../auth/constants/permission.constants";
import {bradAuthCountryTarget} from "../auth/constants/auth.constants";
import {TroubleshootingComponent} from "./troubleshooting.component";
import {TroubleshootingDetailsComponent} from "./component/details/troubleshooting-details.component";
import {TroubleshootingStatementComponent} from "./component/statements/troubleshooting-statement.component";

export const csFinAuthorizationOnAnyTargetOfTypeService =  new CsFinAuthorizationOnAnyTargetOfTypeService();
const routes: Routes = [
  {
    path: '',
    component: NavigationComponent,
    children: [
      {
        path: '',
        canActivate: [csFinAuthCanActivateGuard],
        data: {
          auth: {
            permissions: bradPermissions.BRAD_ACCESS_TROUBLESHOOTING,
            targets: bradAuthCountryTarget,
            authTypeService: csFinAuthorizationOnAnyTargetOfTypeService,
          }
        },
        component: TroubleshootingComponent,
      },
      {
        path: 'statements/:accountId',
        canActivate: [csFinAuthCanActivateGuard],
        data: {
          auth: {
            permissions: bradPermissions.BRAD_ACCESS_TROUBLESHOOTING,
            targets: bradAuthCountryTarget,
            authTypeService: csFinAuthorizationOnAnyTargetOfTypeService,
          }
        },
        component: TroubleshootingStatementComponent,
      },

      {
        path: 'details/:statementID',
        canActivate: [csFinAuthCanActivateGuard],
        data: {
          auth: {
            permissions: bradPermissions.BRAD_ACCESS_TROUBLESHOOTING,
            targets: bradAuthCountryTarget,
            authTypeService: csFinAuthorizationOnAnyTargetOfTypeService,
          }
        },
        component: TroubleshootingDetailsComponent
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class TroubleshootingRoutingModule { }
