import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';

import {TroubleshootingRoutingModule} from './troubleshooting-routing.module';
import {TroubleshootingComponent} from './troubleshooting.component';
import {TroubleshootingDetailsComponent} from './component/details/troubleshooting-details.component';
import {AccountsModule} from "../accounts/accounts.module";
import {TroubleshootingListComponent} from './component/statements/list/troubleshooting-list.component';
import {CdkDropList, CdkDropListGroup} from "@angular/cdk/drag-drop";
import {CdkConnectedOverlay, CdkOverlayOrigin} from "@angular/cdk/overlay";
import {CsFinActiveFiltersModule, CsFinAddRemoveColumnsModule, CsFinAuthModule, CsFinFlagModule} from "@jumia-cs-fin/common";
import {ExtendedModule, FlexModule} from "@angular/flex-layout";
import {MatButtonModule} from "@angular/material/button";
import {MatIconModule} from "@angular/material/icon";
import {MatPaginatorModule} from "@angular/material/paginator";
import {MatProgressBarModule} from "@angular/material/progress-bar";
import {MatSortModule} from "@angular/material/sort";
import {MatTableModule} from "@angular/material/table";
import {TranslateModule} from "@ngx-translate/core";
import {TroubleshootingHeaderComponent} from './component/statements/header/troubleshooting-header.component';
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {MatDatepickerModule} from "@angular/material/datepicker";
import {MatFormFieldModule} from "@angular/material/form-field";
import {MatInputModule} from "@angular/material/input";
import {MatOptionModule} from "@angular/material/core";
import {MatProgressSpinnerModule} from "@angular/material/progress-spinner";
import {MatSelectModule} from "@angular/material/select";
import {MatToolbarModule} from "@angular/material/toolbar";
import {NgxMatSelectSearchModule} from "ngx-mat-select-search";
import {MatCardModule} from "@angular/material/card";
import {MatExpansionModule} from "@angular/material/expansion";
import {TroubleshootingDetailsListComponent} from './component/details/list/troubleshooting-details-list.component';
import {
  TroubleshootingDetailsHeaderComponent
} from './component/details/header/troubleshooting-details-header.component';
import {MatDividerModule} from "@angular/material/divider";
import {TroubleshootingHomeHeaderComponent} from "./component/troubleshooting-home-header/troubleshooting-home-header.component";
import {TroubleshootingHomeComponent} from "./component/troubleshooting-home/troubleshooting-home.component";
import {TroubleshootingStatementComponent} from "./component/statements/troubleshooting-statement.component";
import {MatTooltipModule} from "@angular/material/tooltip";


@NgModule({
  declarations: [
    TroubleshootingComponent,
    TroubleshootingDetailsComponent,
    TroubleshootingListComponent,
    TroubleshootingHeaderComponent,
    TroubleshootingDetailsListComponent,
    TroubleshootingDetailsHeaderComponent,
    TroubleshootingHomeComponent,
    TroubleshootingHomeHeaderComponent,
    TroubleshootingStatementComponent,
  ],
  imports: [
    CommonModule,
    TroubleshootingRoutingModule,
    AccountsModule,
    CdkDropList,
    CdkDropListGroup,
    CdkOverlayOrigin,
    CsFinActiveFiltersModule,
    CsFinAddRemoveColumnsModule,
    CsFinAuthModule,
    ExtendedModule,
    FlexModule,
    MatButtonModule,
    MatIconModule,
    MatPaginatorModule,
    MatProgressBarModule,
    MatSortModule,
    MatTableModule,
    TranslateModule,
    CdkConnectedOverlay,
    FormsModule,
    MatDatepickerModule,
    MatFormFieldModule,
    MatInputModule,
    MatOptionModule,
    MatProgressSpinnerModule,
    MatSelectModule,
    MatToolbarModule,
    NgxMatSelectSearchModule,
    ReactiveFormsModule,
    MatCardModule,
    MatExpansionModule,
    MatDividerModule,
    CsFinFlagModule,
    MatTooltipModule
  ]
})
export class TroubleshootingModule { }
