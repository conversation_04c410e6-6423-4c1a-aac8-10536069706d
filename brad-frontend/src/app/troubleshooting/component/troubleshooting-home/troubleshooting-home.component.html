<main class="container">
  <section class="filters" *ngIf="dataSource">
    <cs-fin-active-filters [chipsMap]="activeFilterChips"
                           (onRemove)="onActiveFilterRemoveClick($event)">
    </cs-fin-active-filters>

    <span fxFlex></span>
    <span class="actions">
      <button mat-stroked-button id="change-columns-btn" color="primary" fxShow [fxHide.xs]="true" [fxHide.sm]="true"
              [disabled]="troubleshootingDetailsOpened" cdkOverlayOrigin #trigger="cdkOverlayOrigin"
              (click)="triggerOverlay(trigger)">
        <mat-icon>table_rows</mat-icon>
      </button>
    </span>
  </section>
  <mat-progress-bar mode="indeterminate" *ngIf="isLoading"></mat-progress-bar>

  <section class="table-detail responsive-table"
           *ngIf="!isLoading"
           [class.detail-opened]="troubleshootingDetailsOpened"
           [class.fullscreen]="isTroubleshootingDetailsInFullscreen">

    <div class="table-container" [class.detail-opened]="troubleshootingDetailsOpened"
         [class.fullscreen]="isTroubleshootingDetailsInFullscreen">

      <mat-progress-bar mode="indeterminate" *ngIf="isLoading"></mat-progress-bar>

      <table mat-table class="cs-fin-table" matSort [dataSource]="dataSource" [class.loading]="isLoading"
             (matSortChange)="onSortChange($event)" cdkDropList cdkDropListSortingDisabled>

        <ng-container matColumnDef="id">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'ACCOUNTS.FIELDS.ID' | translate }}</th>
          <td mat-cell *matCellDef="let account"> {{ account?.id }}</td>
        </ng-container>

        <ng-container matColumnDef="companyID">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'ACCOUNTS.FIELDS.COMPANY_ID' | translate }}</th>
          <td mat-cell *matCellDef="let account">
            <span> {{ account?.companyID }}  </span>
            <span *ngIf="!account?.companyID"> - </span>
          </td>
        </ng-container>

        <ng-container matColumnDef="country">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'ACCOUNTS.FIELDS.COUNTRY' | translate }}</th>
          <td mat-cell class="country-cell" *matCellDef="let account">
            <div>
              {{ account?.country.name }}
              <cs-fin-flag [countryCode]="account?.country.code"></cs-fin-flag>
              <span *ngIf="!account?.country"> - </span>
            </div>
          </td>
        </ng-container>

        <ng-container matColumnDef="currency">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'ACCOUNTS.FIELDS.CURRENCY' | translate }}</th>
          <td mat-cell *matCellDef="let account">
            <span> {{ account?.currency.code }} </span>
            <span *ngIf="!account?.currency"> - </span>
          </td>
        </ng-container>

        <ng-container matColumnDef="navReference">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'ACCOUNTS.FIELDS.NAV_REFERENCE' | translate }}</th>
          <td mat-cell *matCellDef="let account">
            <span> {{ account?.navReference }} </span>
            <span *ngIf="!account?.navReference"> - </span>
          </td>
        </ng-container>

        <ng-container matColumnDef="accountName">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'ACCOUNTS.FIELDS.ACCOUNT_NAME' | translate }}</th>
          <td mat-cell *matCellDef="let account">
            <span> {{ account?.accountName }} </span>
            <span *ngIf="!account?.accountName"> - </span>
          </td>
        </ng-container>

        <ng-container matColumnDef="status">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'ACCOUNTS.FIELDS.STATUS' | translate }}</th>
          <td class="c-cell" mat-cell *matCellDef="let account">
            <span> {{ account?.status }} </span>
            <span *ngIf="!account?.status"> - </span>
          </td>
        </ng-container>

        <ng-container matColumnDef="createdAt">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'ACCOUNTS.FIELDS.CREATED_AT' | translate }}</th>
          <td mat-cell *matCellDef="let account"> {{ account?.createdAt | date:'short' }}</td>
        </ng-container>

        <ng-container matColumnDef="createdBy">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'ACCOUNTS.FIELDS.CREATED_BY' | translate }}</th>
          <td mat-cell *matCellDef="let account"> {{ account?.createdBy }}</td>
        </ng-container>

        <ng-container matColumnDef="updatedAt">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'ACCOUNTS.FIELDS.UPDATED_AT' | translate }}</th>
          <td mat-cell *matCellDef="let account"> {{ account?.updatedAt | date:'short' }}</td>
        </ng-container>

        <ng-container matColumnDef="updatedBy">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'ACCOUNTS.FIELDS.UPDATED_BY' | translate }}</th>
          <td mat-cell *matCellDef="let account"> {{ account?.updatedBy }}</td>
        </ng-container>

        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef>{{ 'GENERAL.FIELDS.ACTIONS' | translate }}</th>
          <td mat-cell *matCellDef="let account" [attr.data-label]="'Actions'">
            <div class="table-actions">
              <mat-icon *ngIf="account?.isOutOfSync" mat-icon-button class="action outOfSyncActionButton"
                        (click)="onStatementSyncClicked(account)" matTooltip="{{ 'TROUBLESHOOTING.ISSUES.SYNC_ISSUE' | translate }}"
                        fontIcon="sync_problem">

              </mat-icon>

              <mat-icon *ngIf="account?.failedStatementValidation" mat-icon-button class="action failedValidationActionButton"
                        (click)="onStatementValidationClicked(account)"
                        matTooltip="{{ 'TROUBLESHOOTING.ISSUES.VALIDATION_ISSUE' | translate }}" fontIcon="error">

              </mat-icon>

              <mat-icon *ngIf="account?.manualUploadMissing" mat-icon-button class="action manualUploadMissing"
                        (click)="onMissingUploadClicked(account)"
                        matTooltip="{{ 'TROUBLESHOOTING.ISSUES.MISSING_MANUAL_UPLOAD' | translate }}" fontIcon="upload">

              </mat-icon>

            </div>

          </td>
        </ng-container>


        <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"
            [class.item-opened]="isItemDetailsOpened(row.id)"
            class="with-detail" style="cursor: pointer;"></tr>
      </table>


      <mat-paginator [pageSizeOptions]="pagination.pageSizeOptions"
                     [pageSize]="pagination.pageSize"
                     [length]="pagination.totalItems"
                     [pageIndex]="pagination.pageIndex"
                     (page)="onPageChange($event)"
                     showFirstLastButtons>
      </mat-paginator>

      <span id="empty-table" *ngIf="!isLoading && !dataSource.data.length">
        {{ 'GENERAL.TABLE.NO_RESULTS_FOUND' | translate }}
      </span>
    </div>
    <div class="{{isTroubleshootingDetailsInFullscreen ? 'details-full-screen' : 'details-container'}}"
         *ngIf="troubleshootingDetailsOpened">
      <router-outlet></router-outlet>
    </div>
  </section>

  <cs-fin-add-remove-columns [triggerOrigin]="triggerOrigin"
                             [isOverlayOpen$]="isOverlayOpen$"
                             [menu]="MENU">
  </cs-fin-add-remove-columns>
</main>
