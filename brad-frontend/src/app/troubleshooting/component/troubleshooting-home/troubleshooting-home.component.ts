import {AfterViewInit, ChangeDetectorRef, Component, OnDestroy, OnInit, ViewChild} from '@angular/core';
import {finalize, takeUntil} from 'rxjs/operators';
import {MatTableDataSource} from '@angular/material/table';
import {Account} from '../../../entities/account/account';
import {Observable, Subject} from 'rxjs';
import {PageResponse} from '../../../entities/page-response';
import {AccountFilters} from '../../../entities/account/account-filters';
import {
  activeFiltersSeparator,
  CsFinActiveFilterChip,
  CsFinAddRemoveColumns,
  CsFinAddRemoveColumnsFacade,
  CsFinColumnDetails,
  CsFinLastUsedColumns,
  CsFinPagination,
  csFinPaginationConsts,
  CsFinSortFilters,
  CsFinSortingDataAccessorHelperService,
  csFintoSnakeCase
} from "@jumia-cs-fin/common";
import {CdkOverlayOrigin} from "@angular/cdk/overlay";
import {MenusConsts} from "../../../shared/constants/menus.constants";
import {MediaMatcher} from "@angular/cdk/layout";
import {MatPaginator} from "@angular/material/paginator";
import {authParams, bradAuthTarget} from "../../../auth/constants/auth.constants";
import {HttpErrorResponse} from "@angular/common/http";
import {NotificationService} from "../../../api/service/notification.service";
import {MatSort} from "@angular/material/sort";
import * as _ from "lodash";
import {SortFilters} from "../../../entities/SortFilters";
import {ActivatedRoute, Router} from "@angular/router";
import {TroubleshootingAccount} from "../../../entities/account/troubleshooting-account";
import {TroubleshootingAccountFacade} from "../../../accounts/facade/troubleshooting.facade";
import {EStatementSource} from "../../../entities/account/account-statement-source-types";
import {de} from "cronstrue/dist/i18n/locales/de";

@Component({
  selector: 'troubleshooting-home',
  templateUrl: './troubleshooting-home.component.html',
  styleUrls: ['./troubleshooting-home.component.scss']
})
export class TroubleshootingHomeComponent implements OnInit, OnDestroy, AfterViewInit {

  @ViewChild(MatPaginator, {static: true}) paginator!: MatPaginator;
  @ViewChild(MatSort, {static: true}) sort!: MatSort;

  readonly MENU = MenusConsts.accounts;

  isTroubleshootingDetailsInFullscreen = true;
  lastOpenedAccountDetailsID!: number | null;

  private displayedColumnsOnDetailsMode = ['id', 'accountNumber', 'companyID', 'country', 'navReference'];
  private displayedColumnsOnFullscreenMode = ['id', 'accountNumber'];

  isLoading = true;
  dataSource: MatTableDataSource<TroubleshootingAccount> = new MatTableDataSource<TroubleshootingAccount>([]);
  displayedColumns: string[] = [];
  mobileQuery: MediaQueryList;
  pagination: CsFinPagination = {
    pageSizeOptions: csFinPaginationConsts.pageSizeOptions,
    pageSize: csFinPaginationConsts.defaultPageSize,
    pageIndex: 0,
    totalItems: 0
  };

  activeFilterChips!: Map<string, CsFinActiveFilterChip>;
  auth = authParams;
  troubleshootingDetailsOpened = false;

  isOverlayOpen$!: Observable<boolean>;
  triggerOrigin!: CdkOverlayOrigin;
  private _mobileQueryListener: () => void;
  private _onDestroy: Subject<void> = new Subject<void>();

  private filters: AccountFilters = {
    page: 1,
    size: csFinPaginationConsts.defaultPageSize
  };

  private lastDisplayedColumns!: string[];

  constructor(
      public ref: ChangeDetectorRef,
      public media: MediaMatcher,
      private router: Router,
      private notificationService: NotificationService,
      private troubleshootingAccountFacade: TroubleshootingAccountFacade,
      private addRemoveColumnsFacade: CsFinAddRemoveColumnsFacade,
      private route: ActivatedRoute
  ) {
    this.mobileQuery = media.matchMedia('(max-width: 960px)');
    this._mobileQueryListener = () => ref.detectChanges();
    this.mobileQuery?.addEventListener('change', this._mobileQueryListener);

    CsFinLastUsedColumns.getInstance().initColumns(this.MENU, this.getColumnDetails());
  }

  ngOnInit(): void {
    this.isLoading = true;
    this.subscribeFiltersChange();
    this.subscribeActiveFilterChipsChange();
    this.subscribeDisplayedColumnsChange();
    this.subscribeFullscreenChange();
    this.subscribeSelectedAccountChange()

    this.displayedColumns = this.displayedColumnsOnFullscreenMode;
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
  }


  ngOnDestroy(): void {
    this.mobileQuery?.removeEventListener('change', this._mobileQueryListener);
    this._onDestroy.next();
    this._onDestroy.complete();
    this.addRemoveColumnsFacade.isOverlayOpen.next(false);
    this.troubleshootingAccountFacade.selectedAccountChangeBehaviorSubject.next(-1);

  }

  private subscribeFiltersChange(): void {
    this.troubleshootingAccountFacade.filters$
    .pipe(takeUntil(this._onDestroy))
      .pipe(finalize(() => this.isLoading = false))
    .subscribe(async (filters: AccountFilters) => {
      if (Object.keys(filters).length > 0 && this.isInTroubleshootingScreen()) {
        this.closeDetailsSliderIfOpened();
        this.filters = filters;
        this.loadAccounts();
      }
    });
  }

  private closeDetailsSliderIfOpened(): void {
    if (this.lastOpenedAccountDetailsID) {
      this.closeAccountDetails();
    }
  }

  private isInTroubleshootingScreen(): boolean {
    return window.location.href.includes('troubleshooting');
  }

  private subscribeActiveFilterChipsChange(): void {
    this.troubleshootingAccountFacade.activeFiltersChips$
    .pipe(takeUntil(this._onDestroy))
    .subscribe((activeFilterChips: Map<string, CsFinActiveFilterChip>) => {
      this.activeFilterChips = activeFilterChips;
    });

  }

  private subscribeDisplayedColumnsChange(): void {
    this.addRemoveColumnsFacade.fetchDisplayedColumns
    .pipe(takeUntil(this._onDestroy))
    .subscribe((columns: CsFinAddRemoveColumns) => {
      setTimeout(() => {
            if (!this.troubleshootingDetailsOpened) {
              this.lastDisplayedColumns = this.displayedColumns;
              this.displayedColumns = columns.displayedColumns ? columns.displayedColumns : [];
            } else {
              this.lastDisplayedColumns = this.displayedColumns;
              this.displayedColumns = this.displayedColumnsOnDetailsMode;
            }

          }
          , 0);
    });
  }

  private subscribeFullscreenChange(): void {
    this.troubleshootingAccountFacade.fullscreenChangeBehaviorSubject
    .pipe(takeUntil(this._onDestroy))
    .subscribe((isFullscreen: boolean) => {
      this.handleFullscreenChange(isFullscreen);
    });
  }

  private subscribeSelectedAccountChange(): void {
    this.troubleshootingAccountFacade.selectedAccountChangeBehaviorSubject
    .pipe(takeUntil(this._onDestroy))
    .subscribe((accountID: number) => {
      if (accountID > 0 && this.lastOpenedAccountDetailsID != accountID) {
        setTimeout(() => {
          this.ref.markForCheck();
        }, 0);
      }
    });
  }

  loadAccounts() {
    this.troubleshootingAccountFacade.getTroubleShooting()
    .pipe(takeUntil(this._onDestroy))
    .pipe(finalize(() => this.isLoading = false))
    .subscribe({
      next: (result: PageResponse<TroubleshootingAccount>) => {
        this.dataSource = new MatTableDataSource<TroubleshootingAccount>(result.results);
        this.dataSource.sort = this.sort;
        this.dataSource.paginator = this.paginator;
        this.dataSource.sortingDataAccessor = CsFinSortingDataAccessorHelperService.objectSortingDataAccessor;
        this.pagination.totalItems = result.total;
        this.pagination.pageSize = result.size;
        this.pagination.pageIndex = result.page - 1;
      },
      error: (error: HttpErrorResponse) => {
        this.notificationService.errorWithResponse(error);
      }
    });
  }

  triggerOverlay(trigger: CdkOverlayOrigin): void {
    this.triggerOrigin = trigger;
    this.addRemoveColumnsFacade.isOverlayOpen.next(true);
  }

  private handleFullscreenChange(isFullscreen: boolean): void {
    this.isTroubleshootingDetailsInFullscreen = isFullscreen;
    if (this.isTroubleshootingDetailsInFullscreen) {
      this.displayedColumns = this.displayedColumnsOnFullscreenMode
    } else {
      this.displayedColumns = this.displayedColumnsOnDetailsMode;
    }
    this.ref.markForCheck();
  }

  getColumnDetails(): CsFinColumnDetails[] {
    return [
      {position: 0, name: 'ID', code: 'id', isActive: true, isRemovable: false, isDefault: true},
      {position: 1, name: 'Country', code: 'country', isActive: true, isRemovable: true, isDefault: true},
      {position: 2, name: 'Company ID', code: 'companyID', isActive: true, isRemovable: true, isDefault: true},
      {position: 3, name: 'Currency', code: 'currency', isActive: true, isRemovable: true, isDefault: true},
      {position: 4, name: 'Nav Reference', code: 'navReference', isActive: true, isRemovable: true, isDefault: true},
      {position: 5, name: 'Account Name', code: 'accountName', isActive: true, isRemovable: true, isDefault: true},
      {position: 7, name: 'Created At', code: 'createdAt', isActive: false, isRemovable: true, isDefault: false},
      {position: 8, name: 'Created By', code: 'createdBy', isActive: false, isRemovable: true, isDefault: false},
      {position: 9, name: 'Updated At', code: 'updatedAt', isActive: false, isRemovable: true, isDefault: false},
      {position: 10, name: 'Updated By', code: 'updatedBy', isActive: false, isRemovable: true, isDefault: false},
      {position: 11, name: 'Actions', code: 'actions', isActive: true, isRemovable: false, isDefault: true},
    ];
  }

  onPageChange(event: any) {
    this.filters.page = event.pageIndex + 1;
    this.filters.size = event.pageSize;
    this.troubleshootingAccountFacade.filtersChanged(this.filters);
  }

  onActiveFilterRemoveClick(removedChipKey: string): void {
    const filtersToRemove = removedChipKey.split(activeFiltersSeparator);
    if (filtersToRemove) {
      filtersToRemove.forEach((filterKey: string) => {
        // @ts-ignore
        delete this.filters[filterKey];
      })
    }
    this.troubleshootingAccountFacade.filtersChanged(this.filters);
  }

  onSortChange(event: any): void {
    const sortFiltersBefore: CsFinSortFilters = {
      orderDirection: this.filters.orderDirection,
      orderField: this.filters.orderField
    };

    const orderField = csFintoSnakeCase(event.active);
    this.filters.orderDirection = event.direction?.toUpperCase();
    this.filters.orderField = orderField.toUpperCase();

    if (!_.isEqual(sortFiltersBefore, this.filters as SortFilters)) {
      this.troubleshootingAccountFacade.filtersChanged(this.filters);
    }
  }

  onStatementSyncClicked(account: Account) {
    event?.stopPropagation();
    let logType;
    switch (account.statementSource) {
      case EStatementSource.SFTP:
        logType = 'SFTP_BANK_STATEMENT_IMPORT'
        break;
      default:
        break;
    }
    this.router.navigate(['/api-log'], {queryParams: {logStatus: 'FAILURE', relatedEntityId: account.accountNumber, logType: logType}});
  }

  onStatementValidationClicked(account: Account) {
    event?.stopPropagation();
    this.router.navigate([`statements/${account.id}`], {relativeTo: this.route});
  }

  onMissingUploadClicked(account: Account) {
    event?.stopPropagation();
    this.router.navigate([`accounts/${account.id}/details`]);
  }


  closeAccountDetails(): void {
    this.troubleshootingDetailsOpened = false;
    this.displayedColumns = this.lastDisplayedColumns;
    this.lastOpenedAccountDetailsID = null;
  }

  isItemDetailsOpened(accountID: number): boolean {
    return accountID === this.lastOpenedAccountDetailsID;
  }

  protected readonly bradAuthTarget = bradAuthTarget;
}
