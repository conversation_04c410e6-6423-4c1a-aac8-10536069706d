import {ChangeDetectorRef, Component, Input, OnDestroy, OnInit, ViewChild, ViewEncapsulation} from '@angular/core';
import {FormControl, FormGroup} from "@angular/forms";
import {BehaviorSubject, Observable, Subject, tap} from "rxjs";
import {CdkConnectedOverlay} from "@angular/cdk/overlay";
import {CsFinActiveFiltersFacade, CsFinApiService, CsFinSidenavService} from "@jumia-cs-fin/common";
import {ActivatedRoute, Router} from "@angular/router";
import {StatementFacade} from "../../../../accounts/facade/statement.facade";
import {takeUntil} from "rxjs/operators";
import {StatementFilters} from "../../../../entities/statement/statement-filters";
import * as _ from "lodash";
import {CurrencyApiService} from "../../../../api/service/currency-api.service";
import {Currency} from "../../../../entities/currency/currency";
import {TroubleshootingFacade} from "../../../facade/troubleshooting.facade";
import { Location } from '@angular/common';
@Component({
  selector: 'brad-troubleshooting-header',
  templateUrl: './troubleshooting-header.component.html',
  styleUrls: ['./troubleshooting-header.component.scss','../../../../../assets/brad-custom.scss'],
  encapsulation: ViewEncapsulation.None
})
export class TroubleshootingHeaderComponent implements OnInit, OnDestroy {
  @Input() showFilters!: boolean;
  filterTextFormControl!: FormControl;
  queryParams = {};

  form!: FormGroup;

  currencyFormControl!: FormControl;
  currencySearchFormControl = new FormControl();
  currencyList:Currency[] = [];
  filteredCurrencyList:Currency[] = [];

  statementIdFormControl!: FormControl;
  previousStatementIDFormControl!: FormControl;

  initialDateStartFormControl!: FormControl;
  initialDateEndFormControl!: FormControl;
  finalDateStartFormControl!: FormControl;
  finalDateEndFormControl!: FormControl;

  initialDirectionFormControl!: FormControl;
  initialDirectionSearchFormControl = new FormControl();
  initialDirectionList:string[] = [];
  filteredInitialDirectionList:string[] = [];

  finalDirectionFormControl!: FormControl;
  finalDirectionSearchFormControl = new FormControl();
  finalDirectionList:string[] = [];
  filteredFinalDirectionList:string[] = [];

  initialAmountFormControl!: FormControl;
  finalAmountFormControl!: FormControl;

  statusFormControl!: FormControl;
  statusSearchFormControl = new FormControl();
  statusList:string[] = [];
  filteredStatusList:string[] = [];

  statusDescriptionFormControl!: FormControl;
  statusDescriptionSearchFormControl = new FormControl();
  statusDescriptionList:string[] = [];
  filteredStatusDescriptionList:string[] = [];

  accountIDFormControl!: FormControl;
  navReferenceFormControl!: FormControl;

  createdAtStartFormControl!: FormControl;
  createdAtEndFormControl!: FormControl;

  isOpen$!: Observable<boolean>;
  isRefreshing = false;
  isInitializing = false;


  private readonly refreshTimeout = 2000;
  private _onDestroy: Subject<void> = new Subject<void>();
  private _isOpen!: BehaviorSubject<boolean>;

  @ViewChild(CdkConnectedOverlay, {static: true})
  private connectedOverlay!: CdkConnectedOverlay;

  constructor(public ref: ChangeDetectorRef,
              public sidenav: CsFinSidenavService,
              private activatedRoute: ActivatedRoute,
              private activeFiltersFacade: CsFinActiveFiltersFacade,
              private router: Router,
              private location: Location,
              private apiService: CsFinApiService,
              private statementFacade: StatementFacade,
              private currencyApiService: CurrencyApiService,
              protected troubleshootingFacade: TroubleshootingFacade) { }


  async ngOnInit(): Promise<void> {
    this.isInitializing = true;
    this.initFiltersSearch();
    this.subscribeUrlParamsChanges();
    this.subscribeActiveFiltersChange();
    this.subscribeFiltersChange();
  }

  ngOnDestroy(): void {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  private subscribeUrlParamsChanges(): void {
    this.activatedRoute.queryParams
      .pipe(takeUntil(this._onDestroy))
      .subscribe(params => this.initializeFormData(params));
  }

  private subscribeActiveFiltersChange(): void {
    this.activeFiltersFacade.openFiltersOverlay
      .pipe(takeUntil(this._onDestroy))
      .subscribe(value => this._isOpen.next(value));
  }

  private subscribeFiltersChange(): void {
    this.statementFacade.filters$
      .pipe(takeUntil(this._onDestroy))
      .subscribe((filters:StatementFilters) => {
        if(!this.isInitializing) {
          this.updateFormData(filters);
          this.updateMissingUrlFilters(filters);
        }
      });
  }

  private updateMissingUrlFilters(filters: StatementFilters): void {
    const formQueryParams = this.apiService.buildQueryParams(filters);
    if (!_.isEqual(this.queryParams, formQueryParams)) {
      this.router.navigate(['troubleshooting'], {queryParams: formQueryParams});
    }
  }

  private initFiltersSearch(): void {
    this.initCurrencySearch();
    this.initStatusSearch();
    this.initErrorTypeSearch();
    this.initInitialDirectionSearch();
    this.initFinalDirectionSearch();
  }

  private initCurrencySearch(): void {
    this.currencySearchFormControl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe((value: String) => {
        const searchTerm = value.trim();
        if (searchTerm) {
          this.filteredCurrencyList = this.currencyList.filter((currency) => {
            return currency.code.trim().toLowerCase().includes(searchTerm.toLowerCase());
          })
        } else {
          this.filteredCurrencyList = this.currencyList;
        }
      });
  }

  private initStatusSearch(): void {
    this.statusSearchFormControl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe((value: String) => {
        const searchTerm = value.trim();
        if (searchTerm) {
          this.filteredStatusList = this.statusList.filter((status) => {
            return status.trim().toLowerCase().includes(searchTerm.toLowerCase());
          })
        } else {
          this.filteredStatusList = this.statusList;
        }
      });
  }


  private initErrorTypeSearch(): void {
    this.statusDescriptionSearchFormControl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe((value: String) => {
        const searchTerm = value.trim();
        if (searchTerm) {
          this.filteredStatusDescriptionList = this.statusDescriptionList.filter((status) => {
            return status.trim().toLowerCase().includes(searchTerm.toLowerCase());
          })
        } else {
          this.filteredStatusDescriptionList = this.statusDescriptionList;
        }
      });
  }

  private initInitialDirectionSearch(): void {
    this.initialDirectionSearchFormControl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe((value: String) => {
        const searchTerm = value.trim();
        if (searchTerm) {
          this.filteredInitialDirectionList = this.initialDirectionList.filter((direction) => {
            return direction.trim().toLowerCase().includes(searchTerm.toLowerCase());
          })
        } else {
          this.filteredInitialDirectionList = this.initialDirectionList;
        }
      });
  }

  private initFinalDirectionSearch(): void {
    this.finalDirectionSearchFormControl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe((value: String) => {
        const searchTerm = value.trim();
        if (searchTerm) {
          this.filteredFinalDirectionList = this.finalDirectionList.filter((direction) => {
            return direction.trim().toLowerCase().includes(searchTerm.toLowerCase());
          })
        } else {
          this.filteredFinalDirectionList = this.finalDirectionList;
        }
      });
  }


  clearFilters(): void {
    this.form.reset('', {emitEvent: false});
    this.statementFacade.filtersChanged({});
  }

  submit(input?: HTMLInputElement) {
    this.statementFacade.filtersChanged(this.getFormValues());
    const formQueryParams = this.apiService.buildQueryParams(this.getFormValues());
    if (_.isEqual(this.queryParams, formQueryParams)) {
      this.isRefreshing = true;
      this.ref.markForCheck();
      setTimeout(() => {
        this.isRefreshing = false;
        this.ref.markForCheck();
      }, this.refreshTimeout);
    } else {
      input?.blur();
    }
    this.closeOverlay();

  }

  triggerOverlay(): void {
    this._isOpen.next(!this._isOpen.value);
  }

  closeOverlay(): void {
    this._isOpen.next(false);
  }

  private getFormValues(): StatementFilters {
    return this.form.value as StatementFilters;
  }

  private initializeFormData(params:any): void {
    this.queryParams = params;
    if(!this.form) {
      this.form = new FormGroup({});
      this.initializeFormControlsAndFilters(params);
      this.setFormControlsToForm();
    }
  }

  private initializeFormControlsAndFilters(params:any): void {
    const filters: StatementFilters = {};

    this.filterTextFormControl = new FormControl(params.filterText);
    filters.filterText = params.filterText;

    this.statementIdFormControl = new FormControl(params.statementId);
    filters.statementId = params.statementId;

    this.previousStatementIDFormControl = new FormControl(params.previousStatementID);
    filters.previousStatementID = params.previousStatementID;

    this.initialDateStartFormControl = new FormControl(params.initialDateStart);
    filters.initialDateStart = params.initialDateStart;

    this.initialDateEndFormControl = new FormControl(params.initialDateEnd);
    filters.initialDateEnd = params.initialDateEnd;

    this.finalDateStartFormControl = new FormControl(params.finalDateStart);
    filters.finalDateStart = params.finalDateStart;

    this.finalDateEndFormControl = new FormControl(params.finalDateEnd);
    filters.finalDateEnd = params.finalDateEnd;

    let initialDirection: string[] = params.initialDirection === undefined ? undefined : params.initialDirection.split(',');
    this.initialDirectionFormControl = new FormControl(initialDirection);
    filters.initialDirection = params.initialDirection;

    let finalDirection: string[] = params.finalDirection === undefined ? undefined : params.finalDirection.split(',');
    this.finalDirectionFormControl = new FormControl(finalDirection);
    filters.finalDirection = params.finalDirection;

    this.initialAmountFormControl = new FormControl(params.initialAmount);
    filters.initialAmount = params.initialAmount;

    this.finalAmountFormControl = new FormControl(params.finalAmount);
    filters.finalAmount = params.finalAmount;

    let status: string[] = params.status === undefined ? undefined : params.status.split(',');
    this.statusFormControl = new FormControl(status);
    filters.status = params.status;

    this.statusDescriptionFormControl = new FormControl(params.statusDescription);
    filters.statusDescription = params.statusDescription;

    this.accountIDFormControl = new FormControl(params.accountID);
    filters.accountID = params.accountID;

    this.navReferenceFormControl = new FormControl(params.navReference);
    filters.navReference = params.navReference;

    this.createdAtStartFormControl = new FormControl(params.createdAtStart);
    filters.createdAtStart = params.createdAtStart;

    this.createdAtEndFormControl = new FormControl(params.createdAtEnd);
    filters.createdAtEnd = params.createdAtEnd;


    filters.orderField = params.orderField;
    filters.orderDirection = params.orderDirection;
    filters.page = params.page;
    filters.size = params.size;

    Promise.all([
      this.applyCurrencyFilterUrl(params, filters),
      this.applyStatusFilter(params, filters),
      this.applyErrorTypeFilter(params, filters),
      this.applyInitialDirectionFilter(params, filters),
      this.applyFinalDirectionFilter(params, filters)
    ]).then(() => {
      this.statementFacade.filtersChanged(filters);
      this.isInitializing = false;
    });

  }

  private applyCurrencyFilterUrl(params: any, filters: StatementFilters): Promise<void> {
    return new Promise<void>(async (resolve) => {
      this.currencyFormControl = new FormControl();
      if(!params.currencyCodes) {
        resolve();
        return;
      }

      await this.loadCurrencies();
      const filtersCurrencyCodes = params.currency.split(',')?.map((currencyId: string) => currencyId);
      if (filtersCurrencyCodes) {
        filters.currencyCodes = this.currencyList.filter((currency) => filtersCurrencyCodes.includes(currency.code))
          .map((currency) => currency.code);
        this.currencyFormControl = new FormControl(filters.currencyCodes);
      }
      resolve();
    });
  }

  private applyStatusFilter(params: any, filters: StatementFilters): Promise<void> {
    return new Promise<void>(async (resolve) => {

      if(!params.status) {
        resolve();
        return;
      }

      await this.loadStatus();

      this.statusFormControl.setValue(filters.status, {emitEvent: false});
      resolve();
    });
  }

  private applyErrorTypeFilter(params: any, filters: StatementFilters): Promise<void> {
    return new Promise<void>(async (resolve) => {

      if(!params.statusDescription) {
        resolve();
        return;
      }

      await this.loadStatusDescription();

      this.statusDescriptionFormControl.setValue(filters.statusDescription, {emitEvent: false});
      resolve();
    });
  }

  private applyInitialDirectionFilter(params: any, filters: StatementFilters): Promise<void> {
    return new Promise<void>(async (resolve) => {

      if(!params.initialDirection) {
        resolve();
        return;
      }

      await this.loadInitialDirection();

      this.initialDirectionFormControl.setValue(filters.initialDirection, {emitEvent: false});
      resolve();
    });
  }

  private applyFinalDirectionFilter(params: any, filters: StatementFilters): Promise<void> {
    return new Promise<void>(async (resolve) => {

      if(!params.finalDirection) {
        resolve();
        return;
      }

      await this.loadFinalDirection();

      this.finalDirectionFormControl.setValue(filters.finalDirection, {emitEvent: false});
      resolve();
    });
  }

  private updateFormData (params: StatementFilters): void {
    this.filterTextFormControl.setValue(params.filterText, {emitEvent: false});
    this.currencyFormControl.setValue(params.currencyCodes, {emitEvent: false});
    this.statementIdFormControl.setValue(params.statementId, {emitEvent: false});
    this.previousStatementIDFormControl.setValue(params.previousStatementID, {emitEvent: false});
    this.initialDateStartFormControl.setValue(params.initialDateStart, {emitEvent: false});
    this.initialDateEndFormControl.setValue(params.initialDateEnd, {emitEvent: false});
    this.finalDateStartFormControl.setValue(params.finalDateEnd, {emitEvent: false});
    this.finalDateEndFormControl.setValue(params.finalDateEnd, {emitEvent: false});
    this.initialDirectionFormControl.setValue(params.initialDirection, {emitEvent: false});
    this.finalDirectionFormControl.setValue(params.finalDirection, {emitEvent: false});
    this.initialAmountFormControl.setValue(params.initialAmount, {emitEvent: false});
    this.finalAmountFormControl.setValue(params.finalAmount, {emitEvent: false});
    this.statusFormControl.setValue(params.status, {emitEvent: false});
    this.statusDescriptionFormControl.setValue(params.statusDescription, {emitEvent: false});
    this.accountIDFormControl.setValue(params.accountID, {emitEvent: false});
    this.navReferenceFormControl.setValue(params.navReference, {emitEvent: false});
    this.createdAtStartFormControl.setValue(params.createdAtStart, {emitEvent: false});
    this.createdAtEndFormControl.setValue(params.createdAtEnd, {emitEvent: false});

  }

  private setFormControlsToForm(): void {
    this.form.addControl(this.statementFacade.filterTextKey, this.filterTextFormControl);
    this.form.addControl(this.statementFacade.currencyKey, this.currencyFormControl);
    this.form.addControl(this.statementFacade.statementIdKey, this.statementIdFormControl);
    this.form.addControl(this.statementFacade.previousStatementIDKey, this.previousStatementIDFormControl);
    this.form.addControl(this.statementFacade.initialDateStartKey, this.initialDateStartFormControl);
    this.form.addControl(this.statementFacade.initialDateEndKey, this.initialDateEndFormControl);
    this.form.addControl(this.statementFacade.finalDateStartKey, this.finalDateStartFormControl);
    this.form.addControl(this.statementFacade.finalDateEndKey, this.finalDateEndFormControl);
    this.form.addControl(this.statementFacade.initialDirectionKey, this.initialDirectionFormControl);
    this.form.addControl(this.statementFacade.finalDirectionKey, this.finalDirectionFormControl);
    this.form.addControl(this.statementFacade.initialAmountKey, this.initialAmountFormControl);
    this.form.addControl(this.statementFacade.finalAmountKey, this.finalAmountFormControl);
    this.form.addControl(this.statementFacade.statusKey, this.statusFormControl);
    this.form.addControl(this.statementFacade.statusDescriptionKey, this.statusDescriptionFormControl);
    this.form.addControl(this.statementFacade.accountIDKey, this.accountIDFormControl);
    this.form.addControl(this.statementFacade.navReferenceKey, this.navReferenceFormControl);
    this.form.addControl(this.statementFacade.createdAtStartKey, this.createdAtStartFormControl);
    this.form.addControl(this.statementFacade.createdAtEndKey, this.createdAtEndFormControl);
  }

  loadCurrencies(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if(this.currencyList.length <= 0) {
        this.currencyApiService.getAll()
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (currencies: Currency[]) => {
              this.currencyList = currencies;
              this.filteredCurrencyList = this.currencyList;
              resolve();
            }, error: (error) => reject(error)
          });
      } else {
        resolve();
      }
    });
  }

  loadStatus(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if(this.statusList.length <= 0) {
        this.statementFacade.getStatusTypes()
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (statuses: string[]) => {
              this.statusList = statuses;
              this.filteredStatusList = this.statusList;
              resolve();
            }, error: (error) => reject(error)
          });
      } else {
        resolve();
      }
    });
  }

  loadStatusDescription(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if(this.statusDescriptionList.length <= 0) {
        this.statementFacade.getErrorTypes()
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (errorTypes: string[]) => {
              this.statusDescriptionList = errorTypes;
              this.filteredStatusDescriptionList = this.statusDescriptionList;
              resolve();
            }, error: (error) => reject(error)
          });
      } else {
        resolve();
      }
    });
  }

  loadInitialDirection(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if(this.initialDirectionList.length <= 0) {
        this.statementFacade.getDirections()
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (directions: string[]) => {
              this.initialDirectionList = directions;
              this.filteredInitialDirectionList = this.initialDirectionList;
              resolve();
            }, error: (error) => reject(error)
          });
      } else {
        resolve();
      }
    });
  }

  loadFinalDirection(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if(this.finalDirectionList.length <= 0) {
        this.statementFacade.getDirections()
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (directions: string[]) => {
              this.finalDirectionList = directions;
              this.filteredFinalDirectionList = this.finalDirectionList;
              resolve();
            }, error: (error) => reject(error)
          });
      } else {
        resolve();
      }
    });
  }



  noFiltersSelected() {
    return !!this.form.errors || this.isRefreshing;
  }

  compareIdFn(o1: any, o2: any): boolean {
    return o1 && o2 && o1.id === o2.id;
  }

  return() {
    this.troubleshootingFacade.account = null;
    this.location.back();
  }
}
