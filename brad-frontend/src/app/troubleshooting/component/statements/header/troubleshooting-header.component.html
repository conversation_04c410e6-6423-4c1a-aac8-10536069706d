<mat-toolbar id="header-toolbar">
  <button (click)="return()" class="md-primary" mat-button *ngIf="troubleshootingFacade.account">
    <mat-icon id="back_bar_icon">keyboard_backspace</mat-icon>
  </button>
  <button mat-icon-button (click)="sidenav.toggle()" fxHide [fxShow.xs]="true" [fxShow.sm]="true">
    <mat-icon>menu</mat-icon>
  </button>

  <span class="page-title">{{ 'TROUBLESHOOTING.TITLE' | translate }}</span>


</mat-toolbar>
