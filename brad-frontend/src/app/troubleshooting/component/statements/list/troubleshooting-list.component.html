<main class="container" *ngIf="troubleshootingFacade.account" cdkDropListGroup>
  <section class="filters" *ngIf="dataSource">
    <cs-fin-active-filters [chipsMap]="activeFilterChips"
                           (onRemove)="onActiveFilterRemoveClick($event)">
    </cs-fin-active-filters>
    <span fxFlex></span>
    <span class="actions">

    <button mat-raised-button color="primary" style="color:white" (click)="navigateToAccountDetails()"
            csFinHasPermissionOnAnyTarget
            [authPermissions]="auth.permissions.BRAD_ACCESS_TROUBLESHOOTING"
            [authTarget]="bradAuthTarget"
            [authAction]="auth.actions.DISABLE">
        <mat-icon>open_in_new</mat-icon>
        <span class="label" fxShow [fxHide.xs]="true" [fxHide.sm]="true">
          {{ 'TROUBLESHOOTING.BUTTONS.LABELS.ACCOUNT_DETAILS' | translate }}
        </span>
      </button>

      <button mat-raised-button color="primary" style="color:white"  (click)="onClickRetryStatement($event)"
              csFinHasPermissionOnAnyTarget
              [authPermissions]="auth.permissions.BRAD_ACCESS_TROUBLESHOOTING"
              [authTarget]="bradAuthTarget"
              [authAction]="auth.actions.DISABLE">
        <mat-icon>replay</mat-icon>
        <span class="label" fxShow [fxHide.xs]="true" [fxHide.sm]="true">
          {{ 'TROUBLESHOOTING.BUTTONS.LABELS.RETRY' | translate }}
        </span>
    </button>
    </span>
  </section>

  <mat-progress-bar mode="indeterminate" *ngIf="isLoading"></mat-progress-bar>

  <section class="table-detail responsive-table" *ngIf="!isLoading">

    <div class="table-container">

      <mat-table mat-table matSort
        [dataSource]="dataSource" multiTemplateDataRows
        class="mat-elevation-z8 table">
        <ng-container matColumnDef="id">
          <mat-header-cell  *matHeaderCellDef>{{ 'TROUBLESHOOTING.FIELDS.ID' | translate }}</mat-header-cell>
          <mat-cell  *matCellDef="let statement"> {{statement?.id}} </mat-cell>
        </ng-container>

        <ng-container matColumnDef="accountID">
          <mat-header-cell  *matHeaderCellDef>{{ 'TROUBLESHOOTING.FIELDS.ACCOUNT_ID' | translate }}</mat-header-cell>
          <mat-cell  *matCellDef="let statement"> {{statement?.accountID}} </mat-cell>
        </ng-container>

        <ng-container matColumnDef="currency">
          <mat-header-cell  *matHeaderCellDef>{{ 'TROUBLESHOOTING.FIELDS.CURRENCY' | translate }}</mat-header-cell>
          <mat-cell  *matCellDef="let statement"> {{statement?.currency.code}} </mat-cell>
        </ng-container>

        <ng-container matColumnDef="statementId">
          <mat-header-cell  *matHeaderCellDef>{{ 'TROUBLESHOOTING.FIELDS.STATEMENT_NUMBER' | translate }}</mat-header-cell>
          <mat-cell  *matCellDef="let statement"> {{statement?.statementId}} </mat-cell>
        </ng-container>

        <ng-container matColumnDef="previousStatementId">
          <mat-header-cell  *matHeaderCellDef>{{ 'TROUBLESHOOTING.FIELDS.PREVIOUS_STATEMENT_NUMBER' | translate }}</mat-header-cell>
          <mat-cell  *matCellDef="let statement"> {{statement?.previousStatementId}} </mat-cell>
        </ng-container>

        <ng-container matColumnDef="initialDate">
          <mat-header-cell  *matHeaderCellDef>{{ 'TROUBLESHOOTING.FIELDS.INITIAL_DATE' | translate }}</mat-header-cell>
          <mat-cell  *matCellDef="let statement"> {{statement?.initialDate}} </mat-cell>
        </ng-container>

        <ng-container matColumnDef="finalDate">
          <mat-header-cell  *matHeaderCellDef>{{ 'TROUBLESHOOTING.FIELDS.FINAL_DATE' | translate }}</mat-header-cell>
          <mat-cell  *matCellDef="let statement"> {{statement?.finalDate}} </mat-cell>
        </ng-container>

        <ng-container matColumnDef="initialDirection">
          <mat-header-cell  *matHeaderCellDef>{{ 'TROUBLESHOOTING.FIELDS.INITIAL_DIRECTION' | translate }}</mat-header-cell>
          <mat-cell  *matCellDef="let statement"> {{statement?.initialDirection}} </mat-cell>
        </ng-container>

        <ng-container matColumnDef="finalDirection">
          <mat-header-cell  *matHeaderCellDef>{{ 'TROUBLESHOOTING.FIELDS.FINAL_DIRECTION' | translate }}</mat-header-cell>
          <mat-cell  *matCellDef="let statement">
            {{statement?.finalDirection}} </mat-cell>
        </ng-container>

        <ng-container matColumnDef="initialAmount">
          <mat-header-cell  *matHeaderCellDef>{{ 'TROUBLESHOOTING.FIELDS.INITIAL_AMOUNT' | translate }}</mat-header-cell>
          <mat-cell  *matCellDef="let statement">
            <span *ngIf="statement?.initialDirection == 'DEBIT'">-</span>
            <span *ngIf="statement?.initialDirection == 'CREDIT'">+</span>
            {{statement?.initialAmount | number:'1.2-2'}}{{statement?.currency.symbol}}
          </mat-cell>
        </ng-container>

        <ng-container matColumnDef="finalAmount">
          <mat-header-cell  *matHeaderCellDef>{{ 'TROUBLESHOOTING.FIELDS.FINAL_AMOUNT' | translate }}</mat-header-cell>
          <mat-cell  *matCellDef="let statement">
            <span *ngIf="statement?.finalDirection == 'DEBIT'">-</span>
            <span *ngIf="statement?.finalDirection == 'CREDIT'">+</span>
            {{statement?.finalAmount | number:'1.2-2'}}{{statement?.currency.symbol}}
          </mat-cell>
        </ng-container>

        <ng-container matColumnDef="status">
          <mat-header-cell  *matHeaderCellDef>{{ 'TROUBLESHOOTING.FIELDS.STATUS_DESCRIPTION' | translate }}</mat-header-cell>
          <mat-cell  *matCellDef="let statement">
            <div class="dot"
             [ngClass]="{'yellow-dot': statement?.statusDescription === EAccountStatementStatus.ERROR_PREVIOUS_STATEMENT,
             'red-dot' : statement?.statusDescription !== EAccountStatementStatus.ERROR_PREVIOUS_STATEMENT }">
             </div>
            {{statement?.statusDescription}}
          </mat-cell>
        </ng-container>

        <ng-container matColumnDef="actions">
          <mat-header-cell  *matHeaderCellDef>{{ 'TROUBLESHOOTING.FIELDS.ACTIONS' | translate }}</mat-header-cell>
          <mat-cell  *matCellDef="let statement">
            <div class="table-actions">
              <button mat-icon-button color="primary" (click)="onClickGotoTL($event, statement)">
                <mat-icon>
                  list
                </mat-icon>
              </button>

              <button mat-icon-button color="primary" (click)="onClickDiscardStatement($event, statement)">
                <mat-icon>
                  delete
                </mat-icon>
              </button>
            </div>

          </mat-cell>
        </ng-container>

        <!-- Expanded Content Column - The detail row is made up of this one column that spans across all columns -->
        <ng-container matColumnDef="expandedDetail">
          <mat-cell *matCellDef="let statement">
            <div [@detailExpand]="statement == expandedStatement ? 'expanded' : 'collapsed'">
              <div class="description">
                <ul>
                  <li *ngIf="statement.accountID">
                    <span class="field">{{'STATEMENTS.FIELDS.ACCOUNT_ID' | translate}} </span>
                    <span class="value">{{statement.accountID}}</span>
                  </li>
                  <li *ngIf="statement.currency">
                    <span class="field">{{'STATEMENTS.FIELDS.CURRENCY' | translate}} </span>
                    <span class="value">{{statement.currency.code}}</span>
                  </li>
                  <li *ngIf="statement.previousStatementId">
                    <span class="field">{{'TROUBLESHOOTING.FIELDS.PREVIOUS_STATEMENT_NUMBER' | translate}} </span>
                    <span class="value">{{statement.previousStatementId}}</span>
                  </li>
                  <li *ngIf="statement.initialDirection">
                    <span class="field">{{'STATEMENTS.FIELDS.INITIAL_DIRECTION' | translate}} </span>
                    <span class="value">{{statement.initialDirection}}</span>
                  </li>
                  <li *ngIf="statement.finalDirection">
                    <span class="field">{{'STATEMENTS.FIELDS.FINAL_DIRECTION' | translate}} </span>
                    <span class="value">{{statement.finalDirection}}</span>
                  </li>
                </ul>
                <ul class="additional-information">
                  <li *ngIf="statement.status">
                    <span class="field">{{'STATEMENTS.FIELDS.STATUS' | translate}} </span>
                    <span class="value">{{statement.status}}</span>
                  </li>
                  <li>
                    <span class="field">{{'GENERAL.FIELDS.CREATED_AT' | translate}} </span>
                    <span class="value">{{statement.createdAt | date:'short'}}</span>
                  </li>
                  <li>
                    <span class="field">{{'GENERAL.FIELDS.CREATED_BY' | translate}} </span>
                    <span class="value">{{statement.createdBy}}</span>
                  </li>
                  <li>
                    <span class="field">{{'GENERAL.FIELDS.UPDATED_AT' | translate}} </span>
                    <span class="value">{{statement.updatedAt | date:'short'}}</span>
                  </li>
                </ul>
                <ul class="additional-information">
                  <li>
                    <span class="field">{{'GENERAL.FIELDS.UPDATED_BY' | translate}} </span>
                    <span class="value-override">{{statement.updatedBy}}</span>
                  </li>
                </ul>

              </div>
            </div>
          </mat-cell>
        </ng-container>

        <ng-container matColumnDef="hoverToUploadFirst">
          <mat-cell *matCellDef="let statement">
            <div class="hoverToUpload">
              <button mat-raised-button color="primary" style="color:white" (click)="onClickInsertFirst($event, statement)">
                <mat-icon>cloud_upload</mat-icon> {{ 'TROUBLESHOOTING.BUTTONS.LABELS.UPLOAD_BEFORE_FIRST' | translate}}
              </button>
            </div>
          </mat-cell>
        </ng-container>

        <ng-container matColumnDef="hoverToUpload">
          <mat-cell *matCellDef="let statement">
            <div class="hoverToUpload">
              <button mat-raised-button color="primary" style="color:white" (click)="onClickInsertBetween($event, statement)">
                <mat-icon>cloud_upload</mat-icon> {{ 'TROUBLESHOOTING.BUTTONS.LABELS.UPLOAD_HERE' | translate}}
              </button>
            </div>
          </mat-cell>
        </ng-container>

        <ng-container matColumnDef="hoverToUploadLast">
          <mat-cell *matCellDef="let statement">
            <div class="hoverToUpload">
              <button mat-raised-button color="primary" style="color:white" (click)="onClickInsertLast($event)">
                <mat-icon>cloud_upload</mat-icon> {{ 'TROUBLESHOOTING.BUTTONS.LABELS.UPLOAD_AFTER_LAST' | translate}}
              </button>
            </div>
          </mat-cell>
        </ng-container>


        <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>

        <mat-row *matRowDef="let row; columns: ['hoverToUploadFirst']; when: isFirstRow" class="hover-row"></mat-row>

        <mat-row *matRowDef="let statement; columns: displayedColumns;"
                 class="example-statement-row"
                 [class.example-expanded-row]="expandedStatement === statement"
                 (click)="expandedStatement = expandedStatement === statement ? null : statement">
        </mat-row>
        <mat-row *matRowDef="let row; columns: ['expandedDetail']" class="example-detail-row"></mat-row>

        <mat-row *matRowDef="let row; columns: ['hoverToUpload']; when: isMiddleRows" class="hover-row"></mat-row>

        <mat-row *matRowDef="let row; columns: ['hoverToUploadLast']; when: isLastRow" class="hover-row"></mat-row>

      </mat-table>

      <span id="empty-table" *ngIf="!isLoading && !dataSource.data.length">
        {{'GENERAL.TABLE.NO_RESULTS_FOUND' | translate}}
      </span>

    </div>



    <cs-fin-add-remove-columns [triggerOrigin]="triggerOrigin"
                               [isOverlayOpen$]="isOverlayOpen$"
                               [menu]="MENU">
    </cs-fin-add-remove-columns>
  </section>

</main>
