@import "../../../../../../node_modules/@jumia-cs-fin/common/assets/styles/list-components";
@import "../../../../../../node_modules/@jumia-cs-fin/common/assets/styles/tables";
@import '../../../../../../node_modules/@jumia-cs-fin/common/assets/styles/details-components';

$brad-timeline-border-size: 6px;
$brad-timeline-icon-size: 35px;
$brad-timeline-element-space: 50px;
$brad-timeline-arrow-size: 8px;
$brad-timeline-label-padding: 10px;
$brad-timeline-element-size: $brad-timeline-icon-size + ($brad-timeline-border-size * 2);
$brad-button-color: #ffffff;
$brad-button-color-darker: #D8F6FF;
$brad-text-color: #1A2950;

mat-paginator {
  border-top: 1px solid #e0e0e0;
  background-color: var(--mat-table-background-color) !important;
}

ul {
  border-bottom: none !important;
}

.description {
  display: flex;
  flex-direction: row;
}

.mat-table {
  width: 100%;
}

.example-detail-row {
  min-height: 0;
  transition: all 0.2s;
}

.example-statement-row:not(.example-expanded-row):hover {
  cursor: pointer;
  background-color: var(--lighter-color) !important;
  transition: all 0.1s;
}

.example-statement-row:not(.example-expanded-row):active {
  background: #efefef;
  transition: all 0.1s;
}

.example-statement-row:nth-child(even) {
  background-color: var(--table-even-row-color);
}

.example-statement-row:nth-child(odd) {
  background-color: var(--table-odd-row-color);
}


.example-element-detail {
  overflow: hidden;
  display: flex;
}


.example-statement-row {
  min-height: 60px;

}

.hover-row{
  background-color: #f5f5f5;
  height: 10px;
  min-height: 0;
  max-height: 10px;
  transition: all 0.2s;
}

.hover-row:hover{
  background-color: #f5f5f5;
  height: 50px;
  min-height: 0;
  max-height: 50px;
  transition: all 0.2s;

  .hoverToUpload {
    button {
      opacity: 1;
      transition: all 0.2s;
    }
  }

}
.hoverToUpload {
  width: 100%;
  display: flex;
  justify-content: center;

  button {
    opacity: 0;
    transition: all 0.2s;
  }

}

.table {
  mat-header-row {
    position: sticky;
    z-index: 99;
    top: 0;
    background-color: var(--mat-table-background-color);
  }
  padding-bottom: 10px;
}

.table-actions{
  display: flex;
  justify-content: space-between;
  align-items: center;
  //gap: 10px;
}
.dot {
  height: 15px;
  width: 15px;
  border-radius: 55%;
  margin-right: 7px;
}

.yellow-dot {
  background-color: yellow;
}

.red-dot {
  background-color: red;
}

.value-override {
  font-size: 12px;
  line-height: 1.33;
  color: var(--primary-text-color);
  user-select: all;

  &:empty::after {
    content: '-';
  }
}
