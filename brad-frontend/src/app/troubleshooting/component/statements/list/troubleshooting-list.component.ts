import {ChangeDete<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild} from '@angular/core';
import {finalize, skipLast, takeUntil} from "rxjs/operators";
import {Statement} from "../../../../entities/statement/statement";
import {MatTableDataSource} from "@angular/material/table";
import {
  activeFiltersSeparator,
  CsFinActiveFilterChip,
  CsFinAddRemoveColumns,
  CsFinAddRemoveColumnsFacade,
  CsFinColumnDetails,
  CsFinConfirmationDialogComponent,
  CsFinLastUsedColumns,
  CsFinNotificationService,
  CsFinSortingDataAccessorHelperService,
  CsFinWritePopupType
} from "@jumia-cs-fin/common";
import {StatementFilters} from "../../../../entities/statement/statement-filters";
import {MatPaginator} from "@angular/material/paginator";
import {MatSort} from "@angular/material/sort";
import {MenusConsts} from "../../../../shared/constants/menus.constants";
import {CdkOverlayOrigin} from "@angular/cdk/overlay";
import {Observable, Subject} from "rxjs";
import {MatDialog} from "@angular/material/dialog";
import {StatementFacade} from "../../../../accounts/facade/statement.facade";
import {StatementsWriteComponent} from "../../../../accounts/component/details/statements/write/statements-write.component";
import {AccountFacade} from "../../../../accounts/facade/account.facade";
import {Account} from "../../../../entities/account/account";
import {ActivatedRoute, Params, Router} from "@angular/router";
import {TroubleshootingFacade} from "../../../facade/troubleshooting.facade";
import {animate, state, style, transition, trigger} from "@angular/animations";
import {authParamsCountry, bradAuthCountryTarget} from "../../../../auth/constants/auth.constants";
import {EAccountStatementErrorStatus} from "../../../../entities/enums/account-statement-error-status.enum";


@Component({
  selector: 'brad-troubleshooting-list',
  templateUrl: './troubleshooting-list.component.html',
  styleUrls: ['./troubleshooting-list.component.scss'],
  animations: [
    trigger('detailExpand', [
      state('collapsed', style({ height: '0px', minHeight: '0' })),
      state('expanded', style({ height: '*', minHeight: '*' })),
      transition(
        'expanded <=> collapsed',
        animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)')
      ),
    ]),
  ],
})
export class TroubleshootingListComponent implements OnInit, OnDestroy {

  @ViewChild(MatPaginator, {static: true}) paginator!: MatPaginator;
  @ViewChild(MatSort, {static: true}) sort!: MatSort;
  accountId!: number;

  readonly MENU = MenusConsts.troubleshooting;
  expandedStatement!: Statement | null;
  triggerOrigin!: CdkOverlayOrigin;
  isFirstRow = (index:number,rowData:any) => index === 0;
  isMiddleRows = (index:number,rowData:any) => index < this.dataSource.data.length - 1;
  isLastRow = (index:number,rowData:any) => index === this.dataSource.data.length - 1;

  isOverlayOpen$!: Observable<boolean>;

  activeFilterChips!: Map<string, CsFinActiveFilterChip>;


  protected readonly bradAuthTarget = bradAuthCountryTarget;
  auth = authParamsCountry;
  displayedColumns: string[] = [];
  dataSource: MatTableDataSource<Statement> = new MatTableDataSource<Statement>([]);

  lastImportedStatement: Statement | undefined;

  isLoading = false;
  private _onDestroy:Subject<void> = new Subject<void>();
  protected filters: StatementFilters = {
    page: 1,
    size: 0
  };


  constructor(
    public ref: ChangeDetectorRef,
    private dialog: MatDialog,
    private notificationService: CsFinNotificationService,
    private statementFacade: StatementFacade,
    private accountFacade: AccountFacade,
    protected troubleshootingFacade: TroubleshootingFacade,
    private router: Router,
    private route: ActivatedRoute,
    private addRemoveColumnsFacade: CsFinAddRemoveColumnsFacade,
  ) {
    CsFinLastUsedColumns.getInstance().initColumns(this.MENU, this.getColumnDetails());
  }

  ngOnInit(): void {
    this.isLoading = true;
    this.setAccountIdFromRoute();
    this.subscribeFiltersChange();
    this.subscribeActiveFilterChipsChange();
    this.subscribeDisplayedColumnsChange();
    this.loadAccount( this.accountId);
  }

  ngOnDestroy(): void {
    this.statementFacade.filtersChanged({});
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  private setAccountIdFromRoute() {
    this.route.params
    .pipe(takeUntil(this._onDestroy))
    .subscribe((params: Params) => {
      this.accountId = params["accountId"]
      this.filters.accountID=this.accountId;
      this.statementFacade.filtersChanged(this.filters);
    });
  }

  private subscribeDisplayedColumnsChange(): void {
    this.addRemoveColumnsFacade.fetchDisplayedColumns
      .pipe(takeUntil(this._onDestroy))
      .subscribe((columns: CsFinAddRemoveColumns) => {
        setTimeout(() => {
            this.displayedColumns = columns.displayedColumns ? columns.displayedColumns : [];
            if (!this.displayedColumns.includes('actions')) {
              this.displayedColumns.push('actions');
            }
          }
          , 0);
      });
  }

  private subscribeFiltersChange(): void {
    this.statementFacade.filters$
      .pipe(takeUntil(this._onDestroy))
      .pipe(skipLast(1))
      .subscribe(async (filters: StatementFilters) => {
        this.filters = filters;
        this.loadStatements();
      });
  }

  private subscribeActiveFilterChipsChange(): void {

    this.statementFacade.activeFiltersChips$
      .pipe(takeUntil(this._onDestroy))
      .subscribe((activeFilterChips: Map<string, CsFinActiveFilterChip>) => {
        this.activeFilterChips = activeFilterChips;
      });

  }


  loadStatements(): void {
    if (!this.filters.status) {
      this.filters.status = ['REVIEW'];
    }
    this.statementFacade.getAllOrdered(this.filters)
      .pipe(takeUntil(this._onDestroy))
      .pipe(finalize(() => {
        this.isLoading = false;
        this.loadLastImportedStatement()
      }))
      .subscribe((statements: Statement[]) => {
        this.dataSource = new MatTableDataSource<Statement>(statements);
        this.dataSource.sortingDataAccessor = CsFinSortingDataAccessorHelperService.objectSortingDataAccessor;

      }, err => {
        this.notificationService.error(err);
      });
  }

  loadLastImportedStatement(): void {
    if (!this.filters.accountID) {
      return;
    }
    this.statementFacade.getLastImportedStatement(this.filters.accountID)
      .pipe(takeUntil(this._onDestroy))
      .subscribe((statement: Statement) => {
        this.lastImportedStatement = statement;
      });
  }

  getColumnDetails(): CsFinColumnDetails[] {
    return [
      {position: 0, name: 'ID', code: 'id', isActive: true, isRemovable: false, isDefault: true},
      {position: 1, name: 'Account ID', code: 'accountID', isActive: false, isRemovable: true, isDefault: false},
      {position: 2, name: 'Currency', code: 'currency', isActive: false, isRemovable: true, isDefault: false},
      {position: 3, name: 'Statement ID', code: 'statementId', isActive: true, isRemovable: true, isDefault: true},
      {position: 4, name: 'Previous Statement ID', code: 'previousStatementId', isActive: false, isRemovable: true, isDefault: false},
      {position: 5, name: 'Initial Date', code: 'initialDate', isActive: true, isRemovable: true, isDefault: true},
      {position: 6, name: 'Final Date', code: 'finalDate', isActive: true, isRemovable: true, isDefault: true},
      {position: 7, name: 'Initial Direction', code: 'initialDirection', isActive: false, isRemovable: true, isDefault: false},
      {position: 8, name: 'Final Direction', code: 'finalDirection', isActive: false, isRemovable: true, isDefault: false},
      {position: 9, name: 'Initial Amount', code: 'initialAmount', isActive: true, isRemovable: true, isDefault: true},
      {position: 10, name: 'Final Amount', code: 'finalAmount', isActive: true, isRemovable: true, isDefault: true},
      {position: 11, name: 'Status Description', code: 'status', isActive: true, isRemovable: true, isDefault: true},
      {position: 12, name: 'Status Description', code: 'statusDescription', isActive: false, isRemovable: true, isDefault: false},
      {position: 13, name: 'Created At', code: 'createdAt', isActive: false, isRemovable: true, isDefault: false},
      {position: 14, name: 'Created By', code: 'createdBy', isActive: false, isRemovable: true, isDefault: false},
      {position: 15, name: 'Updated At', code: 'updatedAt', isActive: false, isRemovable: true, isDefault: false},
      {position: 16, name: 'Updated By', code: 'updatedBy', isActive: false, isRemovable: true, isDefault: false},
      {position: 17, name: 'Description', code: 'description', isActive: false, isRemovable: true, isDefault: false},
    ];
  }

  onActiveFilterRemoveClick(removedChipKey: string): void {
    if (removedChipKey === this.statementFacade.statusKey || removedChipKey === this.statementFacade.accountIDKey) {
      this.statementFacade.filtersChanged({});
      this.troubleshootingFacade.account = null;
      return;
    }
    const filtersToRemove = removedChipKey.split(activeFiltersSeparator);
    if (filtersToRemove) {
      filtersToRemove.forEach((filterKey: string) => {
        // @ts-ignore
        delete this.filters[filterKey];
      })
    }
    this.statementFacade.filtersChanged(this.filters);
  }

  onClickGotoTL(event: any, statement: Statement): void {
    this.troubleshootingFacade.troubleshootingFilterParamsBehaviorSubject.next(this.route.snapshot.queryParams);
    this.troubleshootingFacade.selectedStatementBehaviourSubject.next(statement);
    event.stopPropagation();
    this.router.navigate(['/troubleshooting/details/', statement.id]);
  }

  onClickInsertFirst(event: any, statement: Statement): void {
    event.stopPropagation();

    if (!this.filters.accountID) {
      this.notificationService.error('Please select an account first');
      return;
    }

    this.uploadStatement(statement);

  }

  onClickInsertBetween(event: any, statement: Statement): void {
    event.stopPropagation();

    if (!this.filters.accountID) {
      this.notificationService.error('Please select an account first');
      return;
    }

    let nextStatement: Statement | undefined = this.dataSource.data.find((st: Statement) => {
      return st.previousStatementId == statement.statementId;
    });

    this.uploadStatement(nextStatement!);

  }

  onClickInsertLast(event: any): void {
    event.stopPropagation();

    if (!this.filters.accountID) {
      this.notificationService.error('Please select an account first');
      return;
    }

    this.uploadStatement({} as Statement);
  }

  private uploadStatement(nextStatement: Statement): void {
    this.accountFacade.getById(this.filters.accountID!).subscribe((account: Account) => {
      const dialogRef = this.dialog.open(StatementsWriteComponent, {
        data: {
          type: CsFinWritePopupType.create,
          accountID: account.id,
          account: account,
          nextStatement: nextStatement,
          previousStatement: this.getPreviousStatementInDataSource(nextStatement),
        }
      });

      dialogRef.afterClosed().subscribe(async result => {
        if (result) {
          this.loadStatements();
        }
      });
    });
  }

  private getPreviousStatementInDataSource(statement: Statement): Statement | undefined {
    if (statement.statementId === this.dataSource.data[0].statementId) {
      return this.lastImportedStatement;
    }
    return this.dataSource.data.find((st: Statement) => {
      return st.statementId == statement.previousStatementId;
    })
  }

  loadAccount(id: number): void {
    this.accountFacade.getById(id)
      .pipe(takeUntil(this._onDestroy))
      .subscribe((account: Account) => {
        this.troubleshootingFacade.account = account;
      });
  }

  onClickRetryStatement(event: any): void {
    event.stopPropagation();
    this.statementFacade.retryStatement(this.dataSource.data[0].id)
      .subscribe({
        next: (result) => {
          this.notificationService.info(result.toString());
          this.loadStatements();
        },
        error: (error) => {
          this.notificationService.error(error);
          this.loadStatements();
        }
      })
  }

  navigateToAccountDetails(): void {
    if (this.accountId) {
      const url = `/accounts/${this.accountId}/details`;
      window.open(url, '_blank');
    }
  }

  onClickDiscardStatement(event: any, statement: Statement): void {
    event.stopPropagation();
    const dialogRef = this.dialog.open(CsFinConfirmationDialogComponent, {
      data: {
        titleKey: 'CONFIRMATION_DIALOG.MESSAGES.ABOUT_TO_DISCARD_STATEMENT',
        descriptionKey: 'CONFIRMATION_DIALOG.ACTIONS.ARE_YOU_SURE',
        params: {statement: statement.statementId},
        cancelActionKey: 'CONFIRMATION_DIALOG.BUTTONS.NO',
        confirmActionKey: 'CONFIRMATION_DIALOG.BUTTONS.YES'
      },
      width: '600px'
    });

    dialogRef.afterClosed().subscribe(response => {
      if (response && statement.id) {
        this.isLoading = true;
        this.statementFacade.discardStatement(statement.id)
          .pipe(
            finalize(() => this.isLoading = false)
          )
          .subscribe({
            next: (result) => {
              this.notificationService.successTranslated('TROUBLESHOOTING.NOTIFICATIONS.SUCCESSFULLY_DISCARDED_STATEMENT', {statementNumber:statement.statementId})
              this.loadStatements();
            },
            error: (error) => {
              this.notificationService.error(error);
              this.loadStatements();
            }
          })
      }
    });

  }

  protected readonly EAccountStatementStatus = EAccountStatementErrorStatus;
}
