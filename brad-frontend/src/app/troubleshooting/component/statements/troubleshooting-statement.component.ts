import {Component, OnDestroy, OnInit} from '@angular/core';
import {ActivatedRoute, Params} from "@angular/router";
import {Subject} from "rxjs";
import {takeUntil} from "rxjs/operators";

@Component({
  selector: 'brad-troubleshooting-statement',
  templateUrl: './troubleshooting-statement.component.html',
  styleUrls: ['./troubleshooting-statement.component.scss']
})
export class TroubleshootingStatementComponent implements OnInit, OnDestroy {

  private _onDestroy: Subject<void> = new Subject<void>();
  accountId?: number;

  constructor(private route: ActivatedRoute) {
  }

  ngOnInit(): void {
    this.route.params
    .pipe(takeUntil(this._onDestroy))
    .subscribe((params: Params) => {
      this.accountId = params["accountId"]
    });
  }

  ngOnDestroy(): void {
  }

}
