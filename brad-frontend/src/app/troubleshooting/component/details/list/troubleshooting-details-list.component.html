<main class="container" cdkDropListGroup>
  <section class="filters" *ngIf="dataSource">
    <cs-fin-active-filters [chipsMap]="activeFilterChips"
                           (onRemove)="onActiveFilterRemoveClick($event)">
    </cs-fin-active-filters>
    <span fxFlex></span>
    <span class="actions">
        <button mat-stroked-button id="change-columns-btn" color="primary" fxShow [fxHide.xs]="true" [fxHide.sm]="true"
                cdkOverlayOrigin #triggerTransaction="cdkOverlayOrigin"
                (click)="triggerOverlay(triggerTransaction)">
          <mat-icon>table_rows</mat-icon>
        </button>
      </span>
  </section>

  <div class="table-container">

    <mat-progress-bar mode="indeterminate" *ngIf="isLoading"></mat-progress-bar>
    <table mat-table class="cs-fin-table" matSort [dataSource]="dataSource"  [class.loading]="isLoading"
           (matSortChange)="onSortChange($event)" cdkDropList cdkDropListSortingDisabled>

      <ng-container matColumnDef="id">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'TRANSACTIONS.FIELDS.ID' | translate }}</th>
        <td mat-cell *matCellDef="let transaction"> {{transaction?.id}} </td>
      </ng-container>


      <ng-container matColumnDef="type">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'TRANSACTIONS.FIELDS.TYPE' | translate }}</th>
        <td mat-cell *matCellDef="let transaction"> {{transaction?.type}} </td>
      </ng-container>

      <ng-container matColumnDef="accountID">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'TRANSACTIONS.FIELDS.ACCOUNT_ID' | translate }}</th>
        <td mat-cell *matCellDef="let transaction"> {{transaction?.accountId}} </td>
      </ng-container>

      <ng-container matColumnDef="currency">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'TRANSACTIONS.FIELDS.CURRENCY' | translate }}</th>
        <td mat-cell *matCellDef="let transaction"> {{transaction?.currency.code}} </td>
      </ng-container>

      <ng-container matColumnDef="valueDate">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'TRANSACTIONS.FIELDS.VALUE_DATE' | translate }}</th>
        <td mat-cell *matCellDef="let transaction"> {{transaction?.valueDate}} </td>
      </ng-container>

      <ng-container matColumnDef="transactionDate">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'TRANSACTIONS.FIELDS.TRANSACTION_DATE' | translate }}</th>
        <td mat-cell *matCellDef="let transaction"> {{transaction?.transactionDate}} </td>
      </ng-container>

      <ng-container matColumnDef="statementDate">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'TRANSACTIONS.FIELDS.STATEMENT_DATE' | translate }}</th>
        <td mat-cell *matCellDef="let transaction"> {{transaction?.statementDate}} </td>
      </ng-container>

      <ng-container matColumnDef="direction">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'TRANSACTIONS.FIELDS.DIRECTION' | translate }}</th>
        <td mat-cell *matCellDef="let transaction"> {{transaction?.direction}} </td>
      </ng-container>

      <ng-container matColumnDef="amount">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'TRANSACTIONS.FIELDS.AMOUNT' | translate }}</th>
        <td mat-cell *matCellDef="let transaction"> {{transaction?.amount | number:'1.2-2'}} </td>
      </ng-container>

      <ng-container matColumnDef="reference">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'TRANSACTIONS.FIELDS.REFERENCE' | translate }}</th>
        <td mat-cell *matCellDef="let transaction"> {{transaction?.reference}} </td>
      </ng-container>

      <ng-container matColumnDef="description">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'TRANSACTIONS.FIELDS.DESCRIPTION' | translate }}</th>
        <td mat-cell *matCellDef="let transaction"> {{transaction?.description}} </td>
      </ng-container>

      <ng-container matColumnDef="accountStatementID">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'TRANSACTIONS.FIELDS.STATEMENT_ID' | translate }}</th>
        <td mat-cell *matCellDef="let transaction"> {{transaction?.accountStatementID}} </td>
      </ng-container>

      <ng-container matColumnDef="createdAt">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'TRANSACTIONS.FIELDS.CREATED_AT' | translate }}</th>
        <td mat-cell *matCellDef="let transaction"> {{transaction?.createdAt}} </td>
      </ng-container>

      <ng-container matColumnDef="createdBy">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'TRANSACTIONS.FIELDS.CREATED_BY' | translate }}</th>
        <td mat-cell *matCellDef="let transaction"> {{transaction?.createdBy}} </td>
      </ng-container>

      <ng-container matColumnDef="updatedAt">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'TRANSACTIONS.FIELDS.UPDATED_AT' | translate }}</th>
        <td mat-cell *matCellDef="let transaction"> {{transaction?.updatedAt}} </td>
      </ng-container>

      <ng-container matColumnDef="updatedBy">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'TRANSACTIONS.FIELDS.UPDATED_BY' | translate }}</th>
        <td mat-cell *matCellDef="let transaction"> {{transaction?.updatedBy}} </td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"
          class="with-detail" style="cursor: pointer;"></tr>
    </table>


    <mat-paginator [pageSizeOptions]="pagination.pageSizeOptions"
                   [pageSize]="pagination.pageSize"
                   [length]="pagination.totalItems"
                   [pageIndex]="pagination.pageIndex"
                   (page)="onPageChange($event)"
                   showFirstLastButtons>
    </mat-paginator>
  </div>


  <cs-fin-add-remove-columns [triggerOrigin]="triggerOrigin"
                             [isOverlayOpen$]="isOverlayOpen$"
                             [menu]="MENU">
  </cs-fin-add-remove-columns>
</main>
