import {After<PERSON>iewInit, Component, OnDestroy, OnInit, ViewChild} from '@angular/core';
import {Transaction} from "../../../../entities/transaction/transaction";
import {TransactionFilters} from "../../../../entities/transaction/transaction-filters";
import {
  activeFiltersSeparator,
  CsFinActiveFilterChip,
  CsFinAddRemoveColumns,
  CsFinAddRemoveColumnsFacade,
  CsFinColumnDetails,
  CsFinLastUsedColumns,
  CsFinNotificationService,
  CsFinPagination,
  csFinPaginationConsts,
  CsFinSortingDataAccessorHelperService
} from "@jumia-cs-fin/common";
import {Observable, Subject} from "rxjs";
import {TransactionFacade} from "../../../../accounts/facade/transaction.facade";
import {ActivatedRoute, Router} from "@angular/router";
import {finalize, takeUntil} from "rxjs/operators";
import {paginationConsts} from "../../../../shared/constants/core.constants";
import {MatTableDataSource} from "@angular/material/table";
import {MatPaginator} from "@angular/material/paginator";
import {MatSort, SortDirection} from "@angular/material/sort";
import {CdkOverlayOrigin} from "@angular/cdk/overlay";
import _ from "lodash";
import {SortFilters} from "../../../../entities/SortFilters";
import {authParams, bradAuthTarget} from "../../../../auth/constants/auth.constants";
import {MenusConsts} from "../../../../shared/constants/menus.constants";
import {PageResponse} from "../../../../entities/page-response";
import {TroubleshootingFacade} from "../../../facade/troubleshooting.facade";

@Component({
  selector: 'brad-troubleshooting-details-list',
  templateUrl: './troubleshooting-details-list.component.html',
  styleUrls: ['./troubleshooting-details-list.component.scss']
})
export class TroubleshootingDetailsListComponent implements OnInit, OnDestroy, AfterViewInit{

  @ViewChild(MatPaginator, {static: true}) paginator!: MatPaginator;
  @ViewChild(MatSort, {static: true}) sort!: MatSort;

  displayedColumns: string[] = [];
  dataSource: MatTableDataSource<Transaction> = new MatTableDataSource<Transaction>([]);
  pagination: CsFinPagination = {
    pageSizeOptions: paginationConsts.pageSizeOptions,
    pageSize: paginationConsts.defaultPageSize,
    pageIndex: 0,
    totalItems: 0
  };
  triggerOrigin!: CdkOverlayOrigin;
  isOverlayOpen$!: Observable<boolean>;
  statementId!: number;
  readonly MENU = MenusConsts.troubleshootingDetails;
  isLoading = false;
  activeFilterChips!: Map<string, CsFinActiveFilterChip>;

  protected readonly bradAuthTarget = bradAuthTarget;
  protected readonly auth = authParams;
  protected filters: TransactionFilters = {
    page: 1,
    size: csFinPaginationConsts.defaultPageSize
  };

  private _onDestroy: Subject<void> = new Subject<void>();
  constructor(private transactionFacade: TransactionFacade,
              private router: Router,
              private route: ActivatedRoute,
              private notificationService: CsFinNotificationService,
              private troubleshootingFacade: TroubleshootingFacade,
              private addRemoveColumnsFacade: CsFinAddRemoveColumnsFacade) {
    CsFinLastUsedColumns.getInstance().initColumns(this.MENU, this.getColumnDetails());
  }

  ngOnInit(): void {
    this.isLoading = true;
    this.subscribeFiltersChange();
    this.subscribeActiveFilterChipsChange();
    this.subscribeDisplayedColumnsChange();
  }

  ngAfterViewInit(): void {
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }

  ngOnDestroy(): void {
    this.transactionFacade.filtersChanged({});
    this._onDestroy.next();
    this._onDestroy.complete();
    this.addRemoveColumnsFacade.isOverlayOpen.next(false);
  }

  private subscribeActiveFilterChipsChange(): void {
    this.transactionFacade.activeFiltersChips$
      .pipe(takeUntil(this._onDestroy))
      .subscribe((activeFilterChips: Map<string, CsFinActiveFilterChip>) => {
        this.activeFilterChips = activeFilterChips;
      });

  }

  private subscribeDisplayedColumnsChange(): void {
    this.addRemoveColumnsFacade.fetchDisplayedColumns
      .pipe(takeUntil(this._onDestroy))
      .subscribe((columns: CsFinAddRemoveColumns) => {
        setTimeout(() => {
            this.displayedColumns = columns.displayedColumns ? columns.displayedColumns : [];
          }
          , 0);
      });
  }

  private subscribeFiltersChange(): void {
    this.transactionFacade.filters$
      .pipe(takeUntil(this._onDestroy))
      .subscribe(async (filters: TransactionFilters) => {
        if (filters && this.isInTransactionsScreen()) {
          this.filters = filters;
          this.loadTransactions();
        }
      });
  }

  private isInTransactionsScreen(): boolean {
    return window.location.href.includes('troubleshooting/details');
  }

  loadTransactions() {
    this.filters.accountStatementID = this.route.snapshot.params["partitionKey"]
    this.filters.accountStatementID = this.route.snapshot.params["statementID"]
    this.transactionFacade.getAll(this.filters)
      .pipe(takeUntil(this._onDestroy))
      .pipe(finalize(() => this.isLoading = false))
      .subscribe((result: PageResponse<Transaction>) => {
        this.dataSource = new MatTableDataSource<Transaction>(result.results);
        this.dataSource.sortingDataAccessor = CsFinSortingDataAccessorHelperService.objectSortingDataAccessor;
        this.pagination.totalItems = result.total;
        this.pagination.pageSize = result.size;
        this.pagination.pageIndex = result.page - 1;
        this.setSort();
      }, err => {
        this.notificationService.error(err);
      });
  }


  onPageChange(event: any) {
    this.filters.page = event.pageIndex + 1;
    this.filters.size = event.pageSize;
    this.transactionFacade.filtersChanged(this.filters);
  }

  getColumnDetails(): CsFinColumnDetails[] {
    return [
      {position: 0, name: 'ID', code: 'id', isActive: false, isRemovable: true, isDefault: false},
      {position: 1, name: 'Type', code: 'type', isActive: true, isRemovable: true, isDefault: true},
      {position: 2, name: 'Account ID', code: 'accountID', isActive: false, isRemovable: true, isDefault: false},
      {position: 3, name: 'Currency', code: 'currency', isActive: true, isRemovable: true, isDefault: true},
      {position: 4, name: 'Value Date', code: 'valueDate', isActive: true, isRemovable: true, isDefault: false},
      {position: 5, name: 'Transaction Date', code: 'transactionDate', isActive: true, isRemovable: true, isDefault: true},
      {position: 6, name: 'Statement Date', code: 'statementDate', isActive: true, isRemovable: true, isDefault: true},
      {position: 7, name: 'Direction', code: 'direction', isActive: false, isRemovable: true, isDefault: false},
      {position: 8, name: 'Amount', code: 'amount', isActive: false, isRemovable: true, isDefault: true},
      {position: 9, name: 'Reference', code: 'reference', isActive: false, isRemovable: true, isDefault: false},
      {position: 10, name: 'Description', code: 'description', isActive: false, isRemovable: true, isDefault: false},
      {position: 11, name: 'Statement ID', code: 'accountStatementID', isActive: false, isRemovable: true, isDefault: false},
      {position: 12, name: 'Created At', code: 'createdAt', isActive: false, isRemovable: true, isDefault: true},
      {position: 13, name: 'Created By', code: 'createdBy', isActive: false, isRemovable: true, isDefault: false},
      {position: 14, name: 'Updated At', code: 'updatedAt', isActive: false, isRemovable: true, isDefault: false},
      {position: 15, name: 'Updated By', code: 'updatedBy', isActive: false, isRemovable: true, isDefault: false},
    ];
  }

  private setSort(): void {
    if (!this.sort) {
      return;
    }
    const previousSort = {
      active: this.sort.active,
      direction: this.sort.direction
    };
    if (this.filters.orderField) {
      this.sort.active = this.decodeSortField(this.filters.orderField);
    }
    if (this.filters.orderDirection) {
      this.sort.direction = this.filters.orderDirection.toLowerCase() as SortDirection;
    }

    const sort = {
      active: this.sort.active,
      direction: this.sort.direction
    };
    if (!_.isEqual(previousSort, sort)) {
      this.sort.sortChange.emit(this.sort);
    }
  }

  onSortChange(event: any): void {
    const sortFiltersBefore = {
      orderDirection: this.filters.orderDirection,
      orderField: this.filters.orderField
    };

    this.filters.orderDirection = event.direction?.toUpperCase();
    this.filters.orderField = this.encodeSortField(event.active);

    if(!_.isEqual(sortFiltersBefore, this.filters as SortFilters)){
      this.transactionFacade.filtersChanged(this.filters);
    }

  }

  onActiveFilterRemoveClick(removedChipKey: string): void {
    const filtersToRemove = removedChipKey.split(activeFiltersSeparator);
    if (filtersToRemove) {
      filtersToRemove.forEach((filterKey: string) => {
        // @ts-ignore
        delete this.filters[filterKey];
      })
    }
    this.transactionFacade.filtersChanged(this.filters);
  }

  private encodeSortField(field: string): string {
    switch (field) {
      case 'id':
        return 'ID';
      case 'type':
        return 'TYPE';
      case 'accountID':
        return 'ACCOUNT_ID';
      case 'currency':
        return 'CURRENCY';
      case 'valueDate':
        return 'VALUE_DATE';
      case 'transactionDate':
        return 'TRANSACTION_DATE';
      case 'statementDate':
        return 'STATEMENT_DATE';
      case 'direction':
        return 'DIRECTION';
      case 'amount':
        return 'AMOUNT';
      case 'reference':
        return 'REFERENCE';
      case 'description':
        return 'DESCRIPTION';
      case 'finalAmount':
        return 'FINAL_AMOUNT';
      case 'status':
        return 'STATUS';
      case 'statusDescription':
        return 'STATUS_DESCRIPTION';
      case 'accountStatementID':
        return 'STATEMENT_ID';
      case 'createdAt':
        return 'CREATED_AT';
      case 'createdBy':
        return 'CREATED_BY';
      case 'updatedAt':
        return 'UPDATED_AT';
      case 'updatedBy':
        return 'UPDATED_BY';
      default:
        return field.toUpperCase();
    }
  }

  private decodeSortField(field: string): string {
    switch (field) {
      case 'ID':
        return 'id';
      case 'TYPE':
        return 'type';
      case 'ACCOUNT_ID':
        return 'accountID';
      case 'CURRENCY':
        return 'currency';
      case 'VALUE_DATE':
        return 'valueDate';
      case 'TRANSACTION_DATE':
        return 'transactionDate';
      case 'STATEMENT_DATE':
        return 'statementDate';
      case 'DIRECTION':
        return 'direction';
      case 'AMOUNT':
        return 'amount';
      case 'REFERENCE':
        return 'reference';
      case 'DESCRIPTION':
        return 'description';
      case 'FINAL_AMOUNT':
        return 'finalAmount';
      case 'STATUS':
        return 'status';
      case 'STATUS_DESCRIPTION':
        return 'statusDescription';
      case 'STATEMENT_ID':
        return 'accountStatementID';
      case 'CREATED_AT':
        return 'createdAt';
      case 'CREATED_BY':
        return 'createdBy';
      case 'UPDATED_AT':
        return 'updatedAt';
      case 'UPDATED_BY':
        return 'updatedBy';
      default:
        return field.toLowerCase();
    }
  }

  triggerOverlay(trigger: CdkOverlayOrigin): void {
    this.triggerOrigin = trigger;
    this.addRemoveColumnsFacade.isOverlayOpen.next(true);
  }

}
