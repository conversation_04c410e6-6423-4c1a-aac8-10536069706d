import {Component, OnDestroy, OnInit} from '@angular/core';
import {TransactionFacade} from "../../../accounts/facade/transaction.facade";
import {ActivatedRoute, Params} from "@angular/router";
import {takeUntil} from "rxjs/operators";
import {Subject} from "rxjs";
import {TroubleshootingFacade} from "../../facade/troubleshooting.facade";
import {StatementFacade} from "../../../accounts/facade/statement.facade";

@Component({
  selector: 'brad-troubleshooting-details',
  templateUrl: './troubleshooting-details.component.html',
  styleUrls: ['./troubleshooting-details.component.scss']
})
export class TroubleshootingDetailsComponent implements OnInit, OnDestroy {


  private _onDestroy: Subject<void> = new Subject<void>();

  constructor(private transactionFacade: TransactionFacade,
              private statementFacade: StatementFacade,
              private troubleshootingFacade: TroubleshootingFacade,
              private route: ActivatedRoute) {}

  ngOnDestroy(): void {
  }

  ngOnInit(): void {
    if (this.troubleshootingFacade.selectedStatementBehaviourSubject.value == null) {
      this.loadStatementFromRoute();
    }
  }

  loadStatementFromRoute() {
    this.route.params
      .pipe(takeUntil(this._onDestroy))
      .subscribe((params: Params) => {
        this.statementFacade.getAll({statementId: params['id']})
          .pipe(takeUntil(this._onDestroy))
          .subscribe((statement) => {
            this.troubleshootingFacade.selectedStatementBehaviourSubject.next(statement.results[0]);
          });
      });
  }
}
