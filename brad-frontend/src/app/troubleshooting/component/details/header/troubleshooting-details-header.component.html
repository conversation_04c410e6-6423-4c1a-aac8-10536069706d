<mat-toolbar id="header-toolbar">
  <button mat-icon-button (click)="sidenav.toggle()" fxHide [fxShow.xs]="true" [fxShow.sm]="true">
    <mat-icon>menu</mat-icon>
  </button>
  <button (click)="return()" class="md-primary" mat-button>
    <mat-icon id="back_bar_icon">keyboard_backspace</mat-icon>
  </button>
  <span class="page-title">{{ 'TROUBLESHOOTING.DETAILS.TITLE' | translate }} - {{ onDisplaySelectedStatement() }}</span>
  <span fxFlex></span>

  <section class="search-bar" [formGroup]="form">
    <!-- filter text (code) -->
    <mat-form-field id="search" appearance="outline" fxShow [fxShow.xs]="false" [fxShow.sm]="false"
                    cdkOverlayOrigin #trigger="cdkOverlayOrigin">
      <mat-icon matPrefix>search</mat-icon>
      <mat-label class="theLabel">{{'TRANSACTIONS.DETAILS.SEARCH_BAR' | translate}}</mat-label>
      <input type="text" #input matInput [formControl]="filterTextFormControl"
             (keyup.enter)="submit(input)">
      <mat-icon matSuffix id="filters" (click)="triggerOverlay()">filter_list</mat-icon>
    </mat-form-field>

    <button mat-raised-button color="primary" id="apply-search-btn" class="raised-primary-btn" fxShow [fxShow.xs]="false"
            [fxShow.sm]="false" [disabled]="noFiltersSelected()" (click)="submit()" style="margin-left: 10px;">
      {{ 'GENERAL.BUTTONS.LABELS.SEARCH' | translate }}
    </button>
  </section>


  <span fxFlex fxHide [fxShow.xs]="true" [fxShow.sm]="true"></span>
  <button mat-icon-button aria-label="Filter accounts" fxHide [fxShow.xs]="true" [fxShow.sm]="true"
          *ngIf="showFilters" id="show-mobile-filters">
    <mat-icon (click)="triggerOverlay()">filter_list</mat-icon>
  </button>
  <ng-template cdkConnectedOverlay class="template"
               [cdkConnectedOverlayHasBackdrop]="true"
               [cdkConnectedOverlayOrigin]="trigger"
               [cdkConnectedOverlayOpen]="(isOpen$ | async) || false">
    <div class="filters-overlay">

      <div class="filters-header">
        <mat-icon fxHide [fxShow.xs]="true" [fxShow.sm]="true" (click)="closeOverlay()">close</mat-icon>
        <p class="filters-title">{{'GENERAL.FILTERS.TITLE' | translate}}</p>
        <button fxHide class="flat-white-btn filters-cancel-btn" [disabled]="noFiltersSelected()" [fxShow.xs]="true"
                [fxShow.sm]="true"
                mat-flat-button (click)="clearFilters()" id="clear-btn">
          {{ 'GENERAL.BUTTONS.CLEAR' | translate }}
        </button>
      </div>

      <div class="filters-container">

        <!-- type -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-type-filter">
          <mat-label>{{ 'TRANSACTIONS.FIELDS.TYPE' | translate }}</mat-label>
          <input matInput type="text" [formControl]="typeFormControl">
        </mat-form-field>

        <!-- account id -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-account-id-filter">
          <mat-label>{{ 'TRANSACTIONS.FIELDS.ACCOUNT_ID' | translate }}</mat-label>
          <input matInput type="text" [formControl]="accountIdFormControl">
        </mat-form-field>

        <!-- currency -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-currency-filter"
                        (click)="loadCurrencies()">
          <mat-label>{{'TRANSACTIONS.FIELDS.CURRENCY' | translate}}</mat-label>
          <mat-select [formControl]="currencyFormControl" multiple>
            <ng-container *ngIf="filteredCurrencyList != null; else loadingCurrency">
              <mat-option>
                <ngx-mat-select-search [formControl]="currencySearchFormControl"
                                       [noEntriesFoundLabel]="'GENERAL.FILTERS.NO_MATCHING_RESULTS_FOUND' | translate"
                                       [placeholderLabel]="'GENERAL.FILTERS.SEARCH' | translate">
                </ngx-mat-select-search>
              </mat-option>
              <mat-option *ngFor="let currency of filteredCurrencyList" [value]="currency.code">
                {{currency.code}}
              </mat-option>
              <button mat-button color="primary" class="clear-selection-btn"
                      [disabled]="!currencyFormControl.value?.length"
                      (click)="currencyFormControl.reset([])">
                {{'GENERAL.FILTERS.ACTIONS.CLEAR_SELECTION' | translate}}
              </button>
            </ng-container>
            <ng-template #loadingCurrency>
              <mat-option disabled>
                <div class="filters-loading-container">
                  <span>{{'GENERAL.FILTERS.LOADING' | translate}}</span>
                  <mat-spinner diameter="20"></mat-spinner>
                </div>
              </mat-option>
            </ng-template>
          </mat-select>
        </mat-form-field>

        <!-- value CustomDateAdapter.ts -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-value-date-filter">
          <mat-label>{{'TRANSACTIONS.FIELDS.VALUE_DATE' | translate}}</mat-label>
          <input matInput [matDatepicker]="picker1" [formControl]="valueDateFormControl">
          <mat-datepicker-toggle matSuffix [for]="picker1"></mat-datepicker-toggle>
          <mat-datepicker #picker1></mat-datepicker>
        </mat-form-field>

        <!-- transaction CustomDateAdapter.ts -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-transaction-date-filter">
          <mat-label>{{'TRANSACTIONS.FIELDS.TRANSACTION_DATE' | translate}}</mat-label>
          <input matInput [matDatepicker]="picker2" [formControl]="transactionDateFormControl">
          <mat-datepicker-toggle matSuffix [for]="picker2"></mat-datepicker-toggle>
          <mat-datepicker #picker2></mat-datepicker>
        </mat-form-field>

        <!-- statement CustomDateAdapter.ts -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-statement-date-filter">
          <mat-label>{{'TRANSACTIONS.FIELDS.STATEMENT_DATE' | translate}}</mat-label>
          <input matInput [matDatepicker]="picker3" [formControl]="statementDateFormControl">
          <mat-datepicker-toggle matSuffix [for]="picker3"></mat-datepicker-toggle>
          <mat-datepicker #picker3></mat-datepicker>
        </mat-form-field>

        <!-- direction -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-initial-direction-filter"
                        (click)="loadDirection()">
          <mat-label>{{'TRANSACTIONS.FIELDS.DIRECTION' | translate}}</mat-label>
          <mat-select [formControl]="directionFormControl" multiple>
            <ng-container *ngIf="filteredDirectionList != null; else loadingDirection">
              <mat-option>
                <ngx-mat-select-search [formControl]="directionSearchFormControl"
                                       [noEntriesFoundLabel]="'GENERAL.FILTERS.NO_MATCHING_RESULTS_FOUND' | translate"
                                       [placeholderLabel]="'GENERAL.FILTERS.SEARCH' | translate">
                </ngx-mat-select-search>
              </mat-option>
              <mat-option *ngFor="let direction of directionList" [value]="direction">
                {{direction}}
              </mat-option>
              <button mat-button color="primary" class="clear-selection-btn"
                      [disabled]="!directionFormControl.value?.length"
                      (click)="directionFormControl.reset([])">

                {{'GENERAL.FILTERS.ACTIONS.CLEAR_SELECTION' | translate}}
              </button>
            </ng-container>
            <ng-template #loadingDirection>
              <mat-option disabled>
                <div class="filters-loading-container">
                  <span>{{'GENERAL.FILTERS.LOADING' | translate}}</span>
                  <mat-spinner diameter="20"></mat-spinner>
                </div>
              </mat-option>
            </ng-template>
          </mat-select>
        </mat-form-field>

        <!-- amount -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-amount-filter">
          <mat-label>{{ 'TRANSACTIONS.FIELDS.AMOUNT' | translate }}</mat-label>
          <input matInput type="number" [formControl]="amountFormControl">
        </mat-form-field>

        <!-- reference -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-reference-filter">
          <mat-label>{{ 'TRANSACTIONS.FIELDS.REFERENCE' | translate }}</mat-label>
          <input matInput type="text" [formControl]="referenceFormControl">
        </mat-form-field>

        <!-- description -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-description-filter">
          <mat-label>{{ 'TRANSACTIONS.FIELDS.DESCRIPTION' | translate }}</mat-label>
          <input matInput type="text" [formControl]="descriptionFormControl">
        </mat-form-field>

        <!-- statement ID -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-statement-id-filter">
          <mat-label>{{ 'TRANSACTIONS.FIELDS.STATEMENT_ID' | translate }}</mat-label>
          <input matInput type="text" [formControl]="accountStatementIDFormControl">
        </mat-form-field>

        <!-- created at -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-created-at-filter">
          <mat-label>{{'STATEMENTS.FIELDS.CREATED_AT' | translate}}</mat-label>
          <input matInput [matDatepicker]="picker4" [formControl]="createdAtFormControl">
          <mat-datepicker-toggle matSuffix [for]="picker4"></mat-datepicker-toggle>
          <mat-datepicker #picker4></mat-datepicker>
        </mat-form-field>

      </div>

      <div class="filters-actions">
        <button class="flat-white-btn filters-cancel-btn" [disabled]="noFiltersSelected()" mat-flat-button fxShow
                (click)="clearFilters()" [fxShow.xs]="false" [fxShow.sm]="false">
          {{'GENERAL.FILTERS.ACTIONS.CLEAR' | translate}}
        </button>
        <button class="raised-primary-btn filters-apply-btn" [disabled]="noFiltersSelected()" mat-raised-button
                color="primary"
                (click)="submit()">
          {{'GENERAL.FILTERS.ACTIONS.APPLY' | translate}}
        </button>
      </div>

    </div>

  </ng-template>
  <span fxFlex></span>
</mat-toolbar>
