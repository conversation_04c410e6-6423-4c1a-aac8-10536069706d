import {ChangeDetectorRef, Component, Input, OnDestroy, OnInit, ViewChild, ViewEncapsulation} from '@angular/core';
import {TroubleshootingFacade} from "../../../facade/troubleshooting.facade";
import {Activated<PERSON>oute, Params, Router} from "@angular/router";
import {FormControl, FormGroup} from "@angular/forms";
import {Currency} from "../../../../entities/currency/currency";
import {BehaviorSubject, Observable, Subject, takeUntil, tap} from "rxjs";
import {CdkConnectedOverlay} from "@angular/cdk/overlay";
import {TransactionFilters} from "../../../../entities/transaction/transaction-filters";
import _ from "lodash";
import {CsFinActiveFiltersFacade, CsFinApiService, CsFinSidenavService} from "@jumia-cs-fin/common";
import {TransactionFacade} from "../../../../accounts/facade/transaction.facade";
import {CurrencyApiService} from "../../../../api/service/currency-api.service";
import {Location} from "@angular/common";

@Component({
  selector: 'brad-troubleshooting-details-header',
  templateUrl: './troubleshooting-details-header.component.html',
  styleUrls: ['./troubleshooting-details-header.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class TroubleshootingDetailsHeaderComponent implements OnInit, OnDestroy {

  @Input() showFilters!: boolean;
  queryParams = {};
  form!: FormGroup;
  directionList:string[] = [];
  filteredDirectionList:string[] = []

  currencyList:Currency[] = [];
  filteredCurrencyList:Currency[] = [];

  isOpen$!: Observable<boolean>;
  isInitializing = false;
  isRefreshing = false;

  private _isOpen!: BehaviorSubject<boolean>;

  @ViewChild(CdkConnectedOverlay, {static: true})
  private connectedOverlay!: CdkConnectedOverlay;

  private _onDestroy: Subject<void> = new Subject<void>();
  private readonly refreshTimeout = 2000;

  filterTextFormControl!: FormControl;
  typeFormControl!: FormControl;
  accountIdFormControl!: FormControl;
  partitionKeyFormControl!: FormControl;
  currencyFormControl!: FormControl;
  currencySearchFormControl = new FormControl();
  valueDateStartFormControl!: FormControl;
  valueDateEndFormControl!: FormControl;
  transactionDateStartFormControl!: FormControl;
  transactionDateEndFormControl!: FormControl;
  statementDateStartFormControl!: FormControl;
  statementDateEndFormControl!: FormControl;
  amountFormControl!: FormControl;
  referenceFormControl!: FormControl;
  descriptionFormControl!: FormControl;
  accountStatementIDFormControl!: FormControl;
  createdAtStartFormControl!: FormControl;
  createdAtEndFormControl!: FormControl;
  directionFormControl!: FormControl;
  directionSearchFormControl = new FormControl();
  troubleshootingListParams:Params = {}

  constructor(private troubleshootingFacade: TroubleshootingFacade,
              public ref: ChangeDetectorRef,
              public sidenav: CsFinSidenavService,
              private activatedRoute: ActivatedRoute,
              private activeFiltersFacade: CsFinActiveFiltersFacade,
              private router: Router,
              private apiService: CsFinApiService,
              private transactionFacade: TransactionFacade,
              private currencyApiService: CurrencyApiService,
              private location: Location) { }

  ngOnInit(): void {
    this.troubleshootingListParams = this.troubleshootingFacade.troubleshootingFilterParamsBehaviorSubject.value;
    this.isInitializing = true;
    this.initializeOverlay();
    this.initFiltersSearch();
    this.subscribeUrlParamsChanges();
    this.subscribeActiveFiltersChange();
    this.subscribeFiltersChange();

  }

  ngOnDestroy(): void {
    this._onDestroy.next();
    this._onDestroy.complete();
  }


  private subscribeUrlParamsChanges(): void {
    this.activatedRoute.queryParams
      .pipe(takeUntil(this._onDestroy))
      .subscribe(params => {
        this.initializeFormData(params)
      });
  }

  private subscribeActiveFiltersChange(): void {
    this.activeFiltersFacade.openFiltersOverlay
      .pipe(takeUntil(this._onDestroy))
      .subscribe(value => this._isOpen.next(value));
  }

  private subscribeFiltersChange(): void {
    this.transactionFacade.filters$
      .pipe(takeUntil(this._onDestroy))
      .subscribe((filters: TransactionFilters) => {
        if(!this.isInitializing) {
          this.updateFormData(filters);
          this.updateMissingUrlFilters(filters);
        }
      });
  }

  private initFiltersSearch(): void {
    this.initCurrencySearch();
    this.initDirectionSearch();
  }

  private initializeOverlay(): void {
    this._isOpen = new BehaviorSubject<boolean>(false);
    this.connectedOverlay.backdropClass = 'cdk-overlay-transparent-backdrop';
    this.connectedOverlay.panelClass = 'filters-overlay-panel';
    this.connectedOverlay.backdropClick
      .pipe(tap(() => this._isOpen.next(false)))
      .subscribe();
    this.isOpen$ = this._isOpen.asObservable();
  }

  private initializeFormData(params:any): void {
    this.queryParams = params;
    if(!this.form) {
      this.form = new FormGroup({});
      this.initializeFormControlsAndFilters(params);
    }

  }

  private updateMissingUrlFilters(filters: TransactionFilters): void {
    const formQueryParams = this.apiService.buildQueryParams(filters);
    if (!_.isEqual(this.queryParams, formQueryParams)) {
      this.location.go(this.router.createUrlTree(['troubleshooting/details'], {queryParams: formQueryParams}).toString());
    }
  }


  private initCurrencySearch(): void {
    this.currencySearchFormControl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe((value: String) => {
        const searchTerm = value.trim();
        if (searchTerm) {
          this.filteredCurrencyList = this.currencyList.filter((currency) => {
            return currency.name.trim().toLowerCase().includes(searchTerm.toLowerCase());
          })
        } else {
          this.filteredCurrencyList = this.currencyList;
        }
      });
  }

  private initDirectionSearch(): void {
    this.directionSearchFormControl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe((value: String) => {
        const searchTerm = value.trim();
        if (searchTerm) {
          this.filteredDirectionList = this.directionList.filter((direction) => {
            return direction.trim().toLowerCase().includes(searchTerm.toLowerCase());
          })
        } else {
          this.filteredDirectionList = this.directionList;
        }
      });
  }

  private updateFormData (params: TransactionFilters): void {
    this.filterTextFormControl.setValue(params.filterText, {emitEvent: false});
    this.typeFormControl.setValue(params.type, {emitEvent: false});
    this.accountIdFormControl.setValue(params.accountId, {emitEvent: false});
    this.partitionKeyFormControl.setValue(params.partitionKey, {emitEvent: false});
    this.currencyFormControl.setValue(params.currencyCodes, {emitEvent: false});
    this.valueDateStartFormControl.setValue(params.valueDateStart, {emitEvent: false});
    this.valueDateEndFormControl.setValue(params.valueDateEnd, {emitEvent: false});
    this.transactionDateStartFormControl.setValue(params.transactionDateStart, {emitEvent: false});
    this.transactionDateEndFormControl.setValue(params.transactionDateEnd, {emitEvent: false});
    this.statementDateStartFormControl.setValue(params.statementDateStart, {emitEvent: false});
    this.statementDateEndFormControl.setValue(params.statementDateEnd, {emitEvent: false});
    this.directionFormControl.setValue(params.direction, {emitEvent: false});
    this.amountFormControl.setValue(params.amount, {emitEvent: false});
    this.referenceFormControl.setValue(params.reference, {emitEvent: false});
    this.descriptionFormControl.setValue(params.description, {emitEvent: false});
    this.accountStatementIDFormControl.setValue(params.accountStatementID, {emitEvent: false});
    this.createdAtStartFormControl.setValue(params.createdAtStart, {emitEvent: false});
    this.createdAtEndFormControl.setValue(params.createdAtEnd, {emitEvent: false});
  }

  private setFormControlsToForm(): void {
    this.form.addControl(this.transactionFacade.filterTextKey, this.filterTextFormControl);
    this.form.addControl(this.transactionFacade.typeKey, this.typeFormControl);
    this.form.addControl(this.transactionFacade.accountIDKey, this.accountIdFormControl);
    this.form.addControl(this.transactionFacade.partitionKey, this.partitionKeyFormControl);
    this.form.addControl(this.transactionFacade.currencyKey, this.currencyFormControl);
    this.form.addControl(this.transactionFacade.valueDateStartKey, this.valueDateStartFormControl);
    this.form.addControl(this.transactionFacade.valueDateEndKey, this.valueDateEndFormControl);
    this.form.addControl(this.transactionFacade.transactionDateStartKey, this.transactionDateStartFormControl);
    this.form.addControl(this.transactionFacade.transactionDateEndKey, this.transactionDateEndFormControl);
    this.form.addControl(this.transactionFacade.statementDateStartKey, this.statementDateStartFormControl);
    this.form.addControl(this.transactionFacade.statementDateEndKey, this.statementDateEndFormControl);
    this.form.addControl(this.transactionFacade.directionKey, this.directionFormControl);
    this.form.addControl(this.transactionFacade.amountKey, this.amountFormControl);
    this.form.addControl(this.transactionFacade.referenceKey, this.referenceFormControl);
    this.form.addControl(this.transactionFacade.descriptionKey, this.descriptionFormControl);
    this.form.addControl(this.transactionFacade.accountStatementIDKey, this.accountStatementIDFormControl);
    this.form.addControl(this.transactionFacade.createdAtStartKey, this.createdAtStartFormControl);
    this.form.addControl(this.transactionFacade.createdAtEndKey, this.createdAtEndFormControl);
  }

  private initializeFormControlsAndFilters(params:any): void {
    const filters: TransactionFilters = {};

    this.filterTextFormControl = new FormControl(params.filterText);
    filters.filterText = params.filterText;

    this.typeFormControl = new FormControl(params.type);
    filters.type = params.type;

    this.accountIdFormControl = new FormControl(params.accountID);
    filters.accountId = params.accountId;

    this.partitionKeyFormControl = new FormControl(params.partitionKey);
    filters.partitionKey = params.partitionKey;

    this.valueDateStartFormControl = new FormControl(params.valueDateStart);
    filters.valueDateStart = params.valueDateStart;

    this.valueDateEndFormControl = new FormControl(params.valueDateEnd);
    filters.valueDateEnd = params.valueDateEnd;

    this.transactionDateStartFormControl = new FormControl(params.transactionDateStart);
    filters.transactionDateStart = params.transactionDateStart;

    this.transactionDateEndFormControl = new FormControl(params.transactionDateEnd);
    filters.transactionDateEnd = params.transactionDateEnd;

    this.statementDateStartFormControl = new FormControl(params.statementDateStart);
    filters.statementDateStart = params.statementDateStart;

    this.statementDateEndFormControl = new FormControl(params.statementDateEnd);
    filters.statementDateEnd = params.statementDateEnd;

    let direction: string[] = params.direction === undefined ? undefined : params.direction.split(',');
    this.directionFormControl = new FormControl(direction);
    filters.direction = direction;

    this.amountFormControl = new FormControl(params.amount);
    filters.amount = params.amount;

    this.referenceFormControl = new FormControl(params.reference);
    filters.reference = params.reference;

    this.descriptionFormControl = new FormControl(params.description);
    filters.description = params.description;

    let accountStatementFilter = params.accountStatementID;
    if (this.transactionFacade.mostRecentStatementIDBehaviorSubject.value !== -1) {
      accountStatementFilter = this.transactionFacade.mostRecentStatementIDBehaviorSubject.value;
    }

    this.accountStatementIDFormControl = new FormControl(accountStatementFilter);
    filters.accountStatementID = accountStatementFilter;

    this.createdAtStartFormControl = new FormControl(params.createdAtStart);
    filters.createdAtStart = params.createdAtStart;

    this.createdAtEndFormControl = new FormControl(params.createdAtEnd);
    filters.createdAtEnd = params.createdAtEnd;

    filters.orderField = params.orderField;
    filters.orderDirection = params.orderDirection;
    filters.page = params.page;
    filters.size = params.size;

    Promise.all([
      this.applyCurrencyFilterUrl(params, filters),
      this.applyDirectionFilter(params, filters),
    ]).then(() => {
      this.setFormControlsToForm();
      this.transactionFacade.filtersChanged(filters);
      this.updateMissingUrlFilters(filters);
      this.isInitializing = false;

    });

  }

  private applyCurrencyFilterUrl(params: any, filters: TransactionFilters): Promise<void> {
    return new Promise<void>(async (resolve) => {
      this.currencyFormControl = new FormControl();
      if(!params.currency) {
        resolve();
        return;
      }

      await this.loadCurrencies();
      const filtersCurrencyCodes = params.currency.split(',')?.map((currencyId: string) => currencyId);
      if (filtersCurrencyCodes) {
        filters.currencyCodes = this.currencyList.filter((currency) => filtersCurrencyCodes.includes(currency.code))
          .map((currency) => currency.code);
        this.currencyFormControl = new FormControl(filters.currencyCodes);
      }
      resolve();
    });
  }

  private applyDirectionFilter(params: any, filters: TransactionFilters): Promise<void> {
    return new Promise<void>(async (resolve) => {

      if(!params.direction) {
        resolve();
        return;
      }

      await this.loadDirection();

      this.directionFormControl.setValue(filters.direction, {emitEvent: false});
      resolve();
    });
  }

  loadCurrencies(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if(this.currencyList.length <= 0) {
        this.currencyApiService.getAll()
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (currencies: Currency[]) => {
              this.currencyList = currencies;
              this.filteredCurrencyList = this.currencyList;
              resolve();
            }, error: (error) => reject(error)
          });
      } else {
        resolve();
      }
    });
  }

  loadDirection(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if(this.directionList.length <= 0) {
        this.transactionFacade.getDirections()
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (directions: string[]) => {
              this.directionList = directions;
              this.filteredDirectionList = this.directionList;
              resolve();
            }, error: (error) => reject(error)
          });
      } else {
        resolve();
      }
    });
  }

  clearFilters(): void {
    this.form.reset('', {emitEvent: false});
    this.transactionFacade.filtersChanged({});
    this.closeOverlay();
  }

  submit(input?: HTMLInputElement) {
    this.transactionFacade.filtersChanged(this.getFormValues());
    const formQueryParams = this.apiService.buildQueryParams(this.getFormValues());
    if (_.isEqual(this.queryParams, formQueryParams)) {
      this.isRefreshing = true;
      this.ref.markForCheck();
      setTimeout(() => {
        this.isRefreshing = false;
        this.ref.markForCheck();
      }, this.refreshTimeout);
    } else {
      input?.blur();
      this.location.go(this.router.createUrlTree([], {queryParams: formQueryParams}).toString());
    }
    this.closeOverlay();
  }

  triggerOverlay(): void {
    this._isOpen.next(!this._isOpen.value);
  }

  closeOverlay(): void {
    this._isOpen.next(false);
  }

  private getFormValues(): TransactionFilters {
    return this.form.value as TransactionFilters;
  }

  noFiltersSelected() {
    return !!this.form.errors || this.isRefreshing;
  }

  compareIdFn(o1: any, o2: any): boolean {
    return o1 && o2 && o1.id === o2.id;
  }

  onDisplaySelectedStatement() {
    return this.troubleshootingFacade.selectedStatementBehaviourSubject.value?.statementId;
  }

  return() {
    this.router.navigate(['troubleshooting'], { queryParams: this.troubleshootingListParams });
  }

}
