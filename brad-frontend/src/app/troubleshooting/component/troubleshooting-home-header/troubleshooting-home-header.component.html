<mat-toolbar id="header-toolbar">
  <button mat-icon-button (click)="sidenav.toggle()" fxHide [fxShow.xs]="true" [fxShow.sm]="true">
    <mat-icon>menu</mat-icon>
  </button>
  <span class="page-title">{{ 'TROUBLESHOOTING.TITLE' | translate }}</span>

  <section class="search-bar" [formGroup]="form">
    <!-- filter text (code) -->
    <mat-form-field id="search" class="change-header" appearance="outline" fxShow [fxShow.xs]="false" [fxShow.sm]="false"
                    cdkOverlayOrigin #trigger="cdkOverlayOrigin">
      <mat-icon matPrefix>search</mat-icon>
      <mat-label class="theLabel">{{'ACCOUNTS.DETAILS.SEARCH_BAR' | translate}}</mat-label>
      <input type="text" #input matInput [formControl]="filterTextFormControl"
             (keyup.enter)="submit(input)">
      <mat-icon matSuffix id="filters" (click)="triggerOverlay()">filter_list</mat-icon>
    </mat-form-field>

    <button mat-raised-button color="primary" id="apply-search-btn" class="raised-primary-btn" fxShow [fxShow.xs]="false"
            [fxShow.sm]="false" [disabled]="noFiltersSelected()" (click)="submit()">
      {{ 'GENERAL.BUTTONS.LABELS.SEARCH' | translate }}
    </button>
  </section>

  <span fxFlex fxHide [fxShow.xs]="true" [fxShow.sm]="true"></span>
  <button mat-icon-button aria-label="Filter accounts" fxHide [fxShow.xs]="true" [fxShow.sm]="true"
          *ngIf="showFilters" id="show-mobile-filters">
    <mat-icon (click)="triggerOverlay()">filter_list</mat-icon>
  </button>
  <ng-template cdkConnectedOverlay class="template"
               [cdkConnectedOverlayHasBackdrop]="true"
               [cdkConnectedOverlayOrigin]="trigger"
                [cdkConnectedOverlayOpen]="(isOpen$ | async) || false">
    <div class="filters-overlay">
      <div class="filters-header">
        <mat-icon fxHide [fxShow.xs]="true" [fxShow.sm]="true" (click)="closeOverlay()">close</mat-icon>
        <p class="filters-title">{{'GENERAL.FILTERS.TITLE' | translate}}</p>
        <button fxHide class="flat-white-btn filters-cancel-btn" [disabled]="noFiltersSelected()" [fxShow.xs]="true"
                [fxShow.sm]="true"
                mat-flat-button (click)="clearFilters()" id="clear-btn">
          {{ 'GENERAL.BUTTONS.CLEAR' | translate }}
        </button>
      </div>

      <div class="filters-container">

        <!-- companyID -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-companyID-filter">
          <mat-label>{{ 'ACCOUNTS.FIELDS.COMPANY_ID' | translate }}</mat-label>
          <input matInput type="text" [formControl]="companyIDFormControl">
        </mat-form-field>

        <!-- country -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-country-entity-filter"
                        (click)="loadCountries()">
          <mat-label>{{'ACCOUNTS.FIELDS.COUNTRY' | translate}}</mat-label>
          <mat-select [formControl]="countryFormControl" [compareWith]="compareIdFn" multiple>
            <ng-container *ngIf="filteredCountryList != null; else loadingCountry">
              <mat-option>
                <ngx-mat-select-search [formControl]="countrySearchFormControl"
                                       [noEntriesFoundLabel]="'GENERAL.FILTERS.NO_MATCHING_RESULTS_FOUND' | translate"
                                       [placeholderLabel]="'GENERAL.FILTERS.SEARCH' | translate">
                </ngx-mat-select-search>
              </mat-option>
              <mat-option *ngFor="let country of filteredCountryList" [value]="country">
                <span class="country-name-flag">{{country.name}} <cs-fin-flag [countryCode]="country.code"></cs-fin-flag></span>
              </mat-option>
              <button mat-button color="primary" class="clear-selection-btn"
                      [disabled]="!countryFormControl.value?.length"
                      (click)="countryFormControl.reset([])">
                {{'GENERAL.FILTERS.ACTIONS.CLEAR_SELECTION' | translate}}
              </button>
            </ng-container>
            <ng-template #loadingCountry>
              <mat-option disabled>
                <div class="filters-loading-container">
                  <span>{{'GENERAL.FILTERS.LOADING' | translate}}</span>
                  <mat-spinner diameter="20"></mat-spinner>
                </div>
              </mat-option>
            </ng-template>
          </mat-select>
        </mat-form-field>

        <!-- navReference -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-navReference-filter">
          <mat-label>{{ 'ACCOUNTS.FIELDS.NAV_REFERENCE' | translate }}</mat-label>
          <input matInput type="text" [formControl]="navReferenceFormControl">
        </mat-form-field>

        <!-- accountName -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-accountName-filter">
          <mat-label>{{ 'ACCOUNTS.FIELDS.ACCOUNT_NAME' | translate }}</mat-label>
          <input matInput type="text" [formControl]="accountNameFormControl">
        </mat-form-field>

        <!-- currency -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-country-entity-filter"
                        (click)="loadCurrencies()">
          <mat-label>{{'ACCOUNTS.FIELDS.CURRENCY' | translate}}</mat-label>
          <mat-select [formControl]="currencyFormControl" multiple>
            <ng-container *ngIf="filteredCurrencyList != null; else loadingCurrency">
              <mat-option>
                <ngx-mat-select-search [formControl]="currencySearchFormControl"
                                       [noEntriesFoundLabel]="'GENERAL.FILTERS.NO_MATCHING_RESULTS_FOUND' | translate"
                                       [placeholderLabel]="'GENERAL.FILTERS.SEARCH' | translate">
                </ngx-mat-select-search>
              </mat-option>
              <mat-option *ngFor="let currency of filteredCurrencyList" [value]="currency.code">
                {{currency.code}}
              </mat-option>
              <button mat-button color="primary" class="clear-selection-btn"
                      [disabled]="!currencyFormControl.value?.length"
                      (click)="currencyFormControl.reset([])">
                {{'GENERAL.FILTERS.ACTIONS.CLEAR_SELECTION' | translate}}
              </button>
            </ng-container>
            <ng-template #loadingCurrency>
              <mat-option disabled>
                <div class="filters-loading-container">
                  <span>{{'GENERAL.FILTERS.LOADING' | translate}}</span>
                  <mat-spinner diameter="20"></mat-spinner>
                </div>
              </mat-option>
            </ng-template>
          </mat-select>
        </mat-form-field>

        <!-- troubleshooting -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-troubleshooting-filter" (click)="loadStatuses()">
          <mat-label>{{'TROUBLESHOOTING.FIELDS.TROUBLESHOOTING' | translate}}</mat-label>
          <mat-select [formControl]="troubleshootingFormControl">
            <ng-container *ngIf="troubleshootingStatusList.length > 0; else loadingStatus">
              <mat-option *ngFor="let status of troubleshootingStatusList" [value]="status">
                {{status}}
              </mat-option>
              <button mat-button color="primary" class="clear-selection-btn"
                      [disabled]="!troubleshootingFormControl.value?.length"
                      (click)="troubleshootingFormControl.reset([])">
                {{'GENERAL.FILTERS.ACTIONS.CLEAR_SELECTION' | translate}}
              </button>
            </ng-container>
            <ng-template #loadingStatus>
              <mat-option disabled>
                <div class="filters-loading-container">
                  <span>{{'GENERAL.FILTERS.LOADING' | translate}}</span>
                  <mat-spinner diameter="20"></mat-spinner>
                </div>
              </mat-option>
            </ng-template>
          </mat-select>
        </mat-form-field>

        <!-- created at -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-created-at-filter">
          <mat-label>{{'ACCOUNTS.FIELDS.CREATED_AT' | translate}}</mat-label>
          <mat-date-range-input [rangePicker]="picker">
            <input matStartDate [formControl]="createdAtStartFormControl" placeholder="Start date">
            <input matEndDate [formControl]="createdAtEndFormControl" placeholder="End date">
          </mat-date-range-input>
          <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
          <mat-date-range-picker #picker></mat-date-range-picker>
        </mat-form-field>

      </div>

      <div class="filters-actions">
        <button class="flat-white-btn filters-cancel-btn" [disabled]="noFiltersSelected()" mat-flat-button fxShow
                (click)="clearFilters()" [fxShow.xs]="false" [fxShow.sm]="false">
          {{'GENERAL.FILTERS.ACTIONS.CLEAR' | translate}}
        </button>
        <button class="raised-primary-btn filters-apply-btn" [disabled]="noFiltersSelected()" mat-raised-button
                color="primary"
                (click)="submit()">
          {{'GENERAL.FILTERS.ACTIONS.APPLY' | translate}}
        </button>
      </div>

    </div>

  </ng-template>

</mat-toolbar>
