import {ChangeDetectorRef, Component, Input, OnDestroy, OnInit, ViewChild, ViewEncapsulation} from '@angular/core';
import {ActivatedRoute, Router} from "@angular/router";
import {FormControl, FormGroup} from "@angular/forms";
import {BehaviorSubject, Observable, Subject, tap} from "rxjs";
import {CdkConnectedOverlay} from "@angular/cdk/overlay";
import {AccountFilters} from "../../../entities/account/account-filters";
import * as _ from "lodash";
import {takeUntil} from "rxjs/operators";
import {CountryApiService} from "../../../api/service/country-api.service";
import {Country} from "../../../entities/account/country";
import {CsFinActiveFiltersFacade, CsFinApiService, CsFinSidenavService} from "@jumia-cs-fin/common";
import {Currency} from "../../../entities/currency/currency";
import {CurrencyApiService} from "../../../api/service/currency-api.service";
import {TroubleshootingStatus} from "../../../entities/troubleshooting/troubleshooting-status";
import {TroubleshootingAccountFacade} from "../../../accounts/facade/troubleshooting.facade";

@Component({
  selector: 'troubleshooting-home-header',
  templateUrl: './troubleshooting-home-header.component.html',
  styleUrls: ['./troubleshooting-home-header.component.scss', '../../../../assets/brad-custom.scss'],
  encapsulation: ViewEncapsulation.None
})
export class TroubleshootingHomeHeaderComponent implements OnInit, OnDestroy {
  @Input() showFilters!: boolean;
  filterTextFormControl!: FormControl;
  queryParams = {};

  form!: FormGroup;
  companyIDFormControl!: FormControl;
  countryFormControl!: FormControl;
  navReferenceFormControl!: FormControl;
  accountNameFormControl!: FormControl;
  currencyFormControl!: FormControl;
  createdAtStartFormControl!: FormControl;
  createdAtEndFormControl!: FormControl;
  troubleshootingFormControl!: FormControl;

  isOpen$!: Observable<boolean>;
  isRefreshing = false;
  isInitializing = false;

  currencySearchFormControl = new FormControl();
  currencyList: Currency[] = [];
  filteredCurrencyList: Currency[] = [];

  countrySearchFormControl = new FormControl();
  countryList: Country[] = [];
  filteredCountryList: Country[] = [];

  troubleshootingStatusList: String[] =  Object.keys(TroubleshootingStatus).filter((item) => {
    return isNaN(Number(item));
  });

  private readonly refreshTimeout = 2000;
  private _onDestroy: Subject<void> = new Subject<void>();
  private _isOpen!: BehaviorSubject<boolean>;

  @ViewChild(CdkConnectedOverlay, {static: true})
  private connectedOverlay!: CdkConnectedOverlay;

  constructor(public ref: ChangeDetectorRef,
              public sidenav: CsFinSidenavService,
              private activatedRoute: ActivatedRoute,
              private activeFiltersFacade: CsFinActiveFiltersFacade,
              private router: Router,
              private apiService: CsFinApiService,
              private troubleshootingAccountFacade: TroubleshootingAccountFacade,
              private countryApiService: CountryApiService,
              private currencyApiService: CurrencyApiService) {
  }


  async ngOnInit(): Promise<void> {
    this.isInitializing = true;
    this.initializeOverlay();
    this.initFiltersSearch();
    this.subscribeUrlParamsChanges();
    this.subscribeActiveFiltersChange();
    this.subscribeFiltersChange();
  }

  ngOnDestroy(): void {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  private subscribeUrlParamsChanges(): void {
    this.activatedRoute.queryParams
    .pipe(takeUntil(this._onDestroy))
    .subscribe(params => this.initializeFormData(params));
  }

  private subscribeActiveFiltersChange(): void {
    this.activeFiltersFacade.openFiltersOverlay
    .pipe(takeUntil(this._onDestroy))
    .subscribe(value => this._isOpen.next(value));
  }

  private subscribeFiltersChange(): void {
    this.troubleshootingAccountFacade.filters$
    .pipe(takeUntil(this._onDestroy))
    .subscribe((filters: AccountFilters) => {
      if (!this.isInitializing) {
        this.updateFormData(filters);
        this.updateMissingUrlFilters(filters);
      }
    });
  }

  private initFiltersSearch(): void {
    this.initCountrySearch();
    this.initCurrencySearch();
  }


  private initializeOverlay(): void {
    this._isOpen = new BehaviorSubject<boolean>(false);
    this.connectedOverlay.backdropClass = 'cdk-overlay-transparent-backdrop';
    this.connectedOverlay.panelClass = 'filters-overlay-panel';
    this.connectedOverlay.backdropClick
    .pipe(tap(() => this._isOpen.next(false)))
    .subscribe();
    this.isOpen$ = this._isOpen.asObservable();
  }

  private initCountrySearch(): void {
    this.countrySearchFormControl.valueChanges
    .pipe(takeUntil(this._onDestroy))
    .subscribe((value: String) => {
      const searchTerm = value.trim();
      if (searchTerm) {
        this.filteredCountryList = this.countryList.filter((country) => {
          return country.name.trim().toLowerCase().includes(searchTerm.toLowerCase());
        })
      } else {
        this.filteredCountryList = this.countryList;
      }
    });
  }

  private initCurrencySearch(): void {
    this.currencySearchFormControl.valueChanges
    .pipe(takeUntil(this._onDestroy))
    .subscribe((value: String) => {
      const searchTerm = value.trim();
      if (searchTerm) {
        this.filteredCurrencyList = this.currencyList.filter((currency) => {
          return currency.code.trim().toLowerCase().includes(searchTerm.toLowerCase());
        })
      } else {
        this.filteredCurrencyList = this.currencyList;
      }
    });
  }

  clearFilters(): void {
    this.form.reset('', {emitEvent: false});
    this.troubleshootingAccountFacade.filtersChanged({});
  }

  submit(input?: HTMLInputElement) {
    this.troubleshootingAccountFacade.filtersChanged(this.getFormValues());
    const formQueryParams = this.apiService.buildQueryParams(this.getFormValues());
    if (_.isEqual(this.queryParams, formQueryParams)) {
      this.isRefreshing = true;
      this.ref.markForCheck();
      setTimeout(() => {
        this.isRefreshing = false;
        this.ref.markForCheck();
      }, this.refreshTimeout);
    } else {
      input?.blur();
      this.router.navigate(['/troubleshooting'], {queryParams: formQueryParams});
    }
    this.closeOverlay();

  }

  triggerOverlay(): void {
    this._isOpen.next(!this._isOpen.value);
  }

  closeOverlay(): void {
    this._isOpen.next(false);
  }

  private getFormValues(): AccountFilters {
    return this.form.value as AccountFilters;
  }

  private initializeFormData(params: any): void {
    this.queryParams = params;
    if (!this.form) {
      this.form = new FormGroup({});
      this.initializeFormControlsAndFilters(params);
    }
  }

  private initializeFormControlsAndFilters(params: any): void {
    const filters: AccountFilters = {};

    this.filterTextFormControl = new FormControl(params.filterText);
    filters.filterText = params.filterText;

    this.companyIDFormControl = new FormControl(params.companyID);
    filters.companyID = params.companyID;

    this.navReferenceFormControl = new FormControl(params.navReference);
    filters.navReference = params.navReference;

    this.accountNameFormControl = new FormControl(params.accountName);
    filters.accountName = params.accountName;

    this.createdAtStartFormControl = new FormControl(params.createdAtStart);
    filters.createdAtStart = params.createdAtStart;

    this.createdAtEndFormControl = new FormControl(params.createdAtEnd);
    filters.createdAtEnd = params.createdAtEnd;

    this.troubleshootingFormControl = new FormControl(params.troubleshooting);
    filters.troubleshooting = params.troubleshooting;

    filters.orderField = params.orderField;
    filters.orderDirection = params.orderDirection;
    filters.page = params.page;
    filters.size = params.size;

    Promise.all([
      this.applyCountryUrlFilter(params, filters),
      this.applyCurrencyUrlFilter(params, filters),
    ]).then(() => {
      this.setFormControlsToForm();
      this.troubleshootingAccountFacade.filtersChanged(filters);
      this.updateMissingUrlFilters(filters)
      this.isInitializing = false;
    });

  }

  private applyCountryUrlFilter(params: any, filters: AccountFilters): Promise<void> {
    return new Promise<void>(async (resolve) => {
      this.countryFormControl = new FormControl();
      if (!params.countryCodes) {
        resolve();
        return;
      }

      await this.loadCountries();
      const filterCountriesIds = params.countryCodes.split(',')?.map((countryId: string) => Number(countryId));
      if (filterCountriesIds) {
        filters.countryCodes = this.countryList.filter((country: Country) => filterCountriesIds.includes(country.id));
        this.countryFormControl = new FormControl(filters.countryCodes);
      }
      resolve();
    });
  }

  private applyCurrencyUrlFilter(params: any, filters: AccountFilters): Promise<void> {
    return new Promise<void>(async (resolve) => {
      this.currencyFormControl = new FormControl();
      if (!params.currencyCodes) {
        resolve();
        return;
      }

      await this.loadCurrencies();
      const filterCurrenciesIds = params.currencyCodes.split(',')?.map((currencyId: string) => Number(currencyId));
      if (filterCurrenciesIds) {
        filters.currencyCodes = this.currencyList.filter((currency: Currency) => filterCurrenciesIds.includes(currency.id));
        this.currencyFormControl = new FormControl(filters.currencyCodes);
      }
      resolve();
    });
  }

  private updateFormData(params: AccountFilters): void {
    this.filterTextFormControl.setValue(params.filterText, {emitEvent: false});
    this.companyIDFormControl.setValue(params.companyID, {emitEvent: false});
    this.countryFormControl.setValue(params.countryCodes, {emitEvent: false});
    this.navReferenceFormControl.setValue(params.navReference, {emitEvent: false});
    this.accountNameFormControl.setValue(params.accountName, {emitEvent: false});
    this.currencyFormControl.setValue(params.currencyCodes, {emitEvent: false});
    this.createdAtStartFormControl.setValue(params.createdAtStart, {emitEvent: false});
    this.createdAtEndFormControl.setValue(params.createdAtEnd, {emitEvent: false});
    this.troubleshootingFormControl.setValue(params.troubleshooting, {emitEvent: false});
  }

  private updateMissingUrlFilters(filters: AccountFilters): void {
    const formQueryParams = this.apiService.buildQueryParams(filters);
    if (!_.isEqual(this.queryParams, formQueryParams)) {
      this.router.navigate(['troubleshooting'], {queryParams: formQueryParams});
    }
  }

  private setFormControlsToForm(): void {
    this.form.addControl(this.troubleshootingAccountFacade.filterTextKey, this.filterTextFormControl);
    this.form.addControl(this.troubleshootingAccountFacade.companyIDKey, this.companyIDFormControl);
    this.form.addControl(this.troubleshootingAccountFacade.countryKey, this.countryFormControl);
    this.form.addControl(this.troubleshootingAccountFacade.navReferenceKey, this.navReferenceFormControl);
    this.form.addControl(this.troubleshootingAccountFacade.accountNameKey, this.accountNameFormControl);
    this.form.addControl(this.troubleshootingAccountFacade.currencyKey, this.currencyFormControl);
    this.form.addControl(this.troubleshootingAccountFacade.createdAtStartKey, this.createdAtStartFormControl);
    this.form.addControl(this.troubleshootingAccountFacade.createdAtEndKey, this.createdAtEndFormControl);
    this.form.addControl(this.troubleshootingAccountFacade.troubleshootingKey, this.troubleshootingFormControl);
  }

  noFiltersSelected() {
    return !!this.form.errors || this.isRefreshing;
  }

  loadCountries(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if (this.countryList.length <= 0) {
        this.countryApiService.getAll()
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (countries: Country[]) => {
            this.countryList = countries;
            this.filteredCountryList = this.countryList;
            resolve();
          }, error: (error) => reject(error)
        });
      } else {
        resolve();
      }
    });
  }

  loadCurrencies(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if (this.currencyList.length <= 0) {
        this.currencyApiService.getAll()
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (currencies: Currency[]) => {
            this.currencyList = currencies;
            this.filteredCurrencyList = this.currencyList;
            resolve();
          }, error: (error) => reject(error)
        });
      } else {
        resolve();
      }
    });
  }

  compareIdFn(o1: any, o2: any): boolean {
    return o1 && o2 && o1.id === o2.id;
  }

}
