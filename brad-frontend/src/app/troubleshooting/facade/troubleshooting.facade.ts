import {Injectable} from "@angular/core";
import {BehaviorSubject} from "rxjs";
import {Statement} from "../../entities/statement/statement";
import {Params} from "@angular/router";
import {Account} from "../../entities/account/account";

@Injectable({providedIn: 'root'})
export class TroubleshootingFacade {

  public account: Account|null = null;
  public selectedStatementBehaviourSubject = new BehaviorSubject<Statement|null>(null);
  public troubleshootingFilterParamsBehaviorSubject = new BehaviorSubject<Params>({});

}
