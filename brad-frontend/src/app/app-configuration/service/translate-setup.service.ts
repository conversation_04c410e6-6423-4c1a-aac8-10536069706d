import {Injectable} from '@angular/core';
import {TranslateService} from '@ngx-translate/core';
import {languageConfigs} from "../../../environments/environment";

@Injectable({
  providedIn: 'root'
})
export class TranslateSetupService {

  constructor(private translateService: TranslateService) {
  }

  configure(): Promise<any> {
    return new Promise((resolve) => {
      this.translateService.addLangs(languageConfigs.languages);
      this.translateService.setDefaultLang(languageConfigs.defaultLanguage);
      this.translateService.use(languageConfigs.defaultLanguage);
      resolve(`Translate service configured using lang ${languageConfigs.defaultLanguage}`);
    });
  }
}
