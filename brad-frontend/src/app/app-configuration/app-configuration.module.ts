import {APP_INITIALIZER, NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {TranslateSetupService} from './service/translate-setup.service';


export const configTranslate = (translateSetupService: TranslateSetupService) => () => translateSetupService.configure();

@NgModule({
  declarations: [],
  imports: [
    CommonModule
  ],
  providers: [
    TranslateSetupService,
    {
      provide: APP_INITIALIZER, useFactory: configTranslate, deps: [TranslateSetupService], multi: true
    }
  ]
})
export class AppConfigurationModule { }
