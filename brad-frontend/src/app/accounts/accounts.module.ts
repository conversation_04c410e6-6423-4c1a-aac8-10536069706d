import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {AccountsListComponent} from './component/list/accounts-list.component';
import {AccountsRoutingModule} from './accounts-routing.module';
import {AccountsWriteComponent} from './component/write/accounts-write.component';
import {MatProgressBarModule} from '@angular/material/progress-bar';
import {TranslateModule} from '@ngx-translate/core';
import {MatTooltipModule} from '@angular/material/tooltip';
import {MatIconModule} from '@angular/material/icon';
import {MatButtonModule} from '@angular/material/button';
import {MatFormFieldModule} from '@angular/material/form-field';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {MatInputModule} from '@angular/material/input';
import {MatSelectModule} from '@angular/material/select';
import {SharedModule} from '../shared/shared.module';
import {MatTableModule} from '@angular/material/table';
import {MatSortModule} from '@angular/material/sort';
import {MatPaginatorModule} from '@angular/material/paginator';
import {MatDialogModule} from '@angular/material/dialog';
import {MatCardModule} from '@angular/material/card';
import {MatDividerModule} from '@angular/material/divider';
import {MatToolbarModule} from '@angular/material/toolbar';
import {MatMenuModule} from '@angular/material/menu';
import {MatCheckboxModule} from "@angular/material/checkbox";
import {MatListModule} from "@angular/material/list";
import {MatTabsModule} from "@angular/material/tabs";
import {MatDatepickerModule} from "@angular/material/datepicker";
import {MatExpansionModule} from "@angular/material/expansion";
import {
  AccountsDetailsHistoryComponent
} from './component/details/details-history/accounts-details-history.component';
import {MatProgressSpinnerModule} from "@angular/material/progress-spinner";
import {
  AccountsStatementsInformationComponent
} from './component/details/statements-information/accounts-statements-information.component';
import {AccountsHeaderComponent} from "./component/header/accounts-header.component";
import {FlexLayoutModule} from "@angular/flex-layout";
import {OverlayModule} from "@angular/cdk/overlay";
import {NgxMatSelectSearchModule} from "ngx-mat-select-search";
import {DragDropModule} from "@angular/cdk/drag-drop";
import {AccountsDetailsComponent} from "./component/details/accounts-details.component";
import {AccountsComponent} from './accounts.component';
import {UsersComponent} from './component/details/users/users.component';
import {UsersWriteComponent} from './component/details/users/write/users-write.component';
import {
  UsersDetailsDialogComponent
} from './component/details/users/users-details-dialog/users-details-dialog.component';
import {ContactComponent} from './component/details/contact/contact.component';
import {ContactsWriteComponent} from './component/details/contact/write/contacts-write.component';
import {
  ContactsDetailsDialogComponent
} from './component/details/contact/contacts-details-dialog/contacts-details-dialog.component';
import {DocumentsComponent} from './component/details/documents/documents.component';
import {DocumentsWriteComponent} from './component/details/documents/write/documents-write.component';
import {NgxDropzoneModule} from "ngx-dropzone";
import {DetailsDialogComponent} from './component/details/documents/details-dialog/details-dialog.component';
import {
  CsFinActiveFiltersModule,
  CsFinAddRemoveColumnsModule,
  CsFinAuthModule,
  CsFinFlagModule,
  CsFinNotificationModule,
  CsFinUtilsModule
} from "@jumia-cs-fin/common";
import {StatementsComponent} from './component/details/statements/statements.component';
import {StatementsHeaderComponent} from './component/details/statements/header/statements-header.component';
import {StatementsWriteComponent} from "./component/details/statements/write/statements-write.component";
import {TransactionsComponent} from './component/details/transactions/transactions.component';
import {TransactionsHeaderComponent} from './component/details/transactions/header/transactions-header.component';
import {AccountsDetailHeaderComponent} from './component/details/header/accounts-detail-header.component';
import {AccountsSelectorComponent} from './component/selector/accounts-selector.component';
import {
  StatementsWriteWarningDialogComponent
} from "./component/details/statements/write/warning-dialog/statements-write-warning-dialog.component";
import {NgxDocViewerModule} from "ngx-doc-viewer";
import {NgxExtendedPdfViewerModule} from "ngx-extended-pdf-viewer";
import {MatSlideToggleModule} from "@angular/material/slide-toggle";


@NgModule({
    declarations: [
      AccountsListComponent,
      AccountsWriteComponent,
      AccountsDetailsComponent,
      AccountsDetailsHistoryComponent,
      AccountsStatementsInformationComponent,
      AccountsHeaderComponent,
      AccountsComponent,
      UsersComponent,
      UsersWriteComponent,
      UsersDetailsDialogComponent,
      ContactComponent,
      ContactsWriteComponent,
      ContactsDetailsDialogComponent,
      DocumentsComponent,
      DocumentsWriteComponent,
      DetailsDialogComponent,
      StatementsComponent,
      StatementsHeaderComponent,
      StatementsWriteComponent,
      StatementsWriteWarningDialogComponent,
      TransactionsComponent,
      TransactionsHeaderComponent,
      AccountsDetailHeaderComponent,
      AccountsSelectorComponent],
  exports: [
    AccountsStatementsInformationComponent,
    AccountsListComponent,
    AccountsHeaderComponent,
    StatementsHeaderComponent,
    AccountsSelectorComponent,
  ],
  imports: [
    CommonModule,
    AccountsRoutingModule,
    SharedModule,
    MatDialogModule,
    MatProgressBarModule,
    TranslateModule,
    MatTooltipModule,
    MatIconModule,
    MatButtonModule,
    MatFormFieldModule,
    FormsModule,
    CsFinAuthModule,
    CsFinActiveFiltersModule,
    CsFinAddRemoveColumnsModule,
    CsFinNotificationModule,
    CsFinUtilsModule,
    MatInputModule,
    MatSelectModule,
    MatTableModule,
    MatSortModule,
    MatPaginatorModule,
    ReactiveFormsModule,
    MatCardModule,
    MatDividerModule,
    MatToolbarModule,
    MatMenuModule,
    MatCheckboxModule,
    MatListModule,
    MatTabsModule,
    MatDatepickerModule,
    MatExpansionModule,
    MatProgressSpinnerModule,
    FlexLayoutModule,
    OverlayModule,
    NgxMatSelectSearchModule,
    DragDropModule,
    NgxDropzoneModule,
    CsFinActiveFiltersModule,
    CsFinFlagModule,
    MatSlideToggleModule,
    NgxDocViewerModule
    ]
})
export class AccountsModule { }
