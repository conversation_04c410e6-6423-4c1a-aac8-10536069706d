<div class="dialog-container">

  <header class="dialog-header">
    <span class="title" mat-dialog-title>{{getTitle() | translate}}</span>
  </header>


  <div class="dialog-content">
    <div class="form-container">

      <div class="form-row">
        <div class="form-column">

          <!-- currency -->
          <mat-form-field appearance="outline" data-cy="input-currency-filter"
                          (click)="loadCurrencies()">
            <mat-label>{{ 'STATEMENTS.FIELDS.CURRENCY' | translate }}</mat-label>

            <mat-select [formControl]="currencyFormControl" required>
              <ng-container *ngIf="filteredCurrencyList != null; else loadingCurrency">
                <mat-option>
                  <ngx-mat-select-search [formControl]="currencySearchFormControl"
                                         [noEntriesFoundLabel]="'GENERAL.FILTERS.NO_MATCHING_RESULTS_FOUND' | translate"
                                         [placeholderLabel]="'GENERAL.FILTERS.SEARCH' | translate">
                  </ngx-mat-select-search>
                </mat-option>
                <mat-option *ngFor="let currency of filteredCurrencyList" [value]="currency.code">
                  {{currency.code}}
                </mat-option>
                <button mat-button color="primary" class="clear-selection-btn"
                        [disabled]="!currencyFormControl.value?.length"
                        (click)="currencyFormControl.reset([])">
                  {{'GENERAL.FILTERS.ACTIONS.CLEAR_SELECTION' | translate}}
                </button>
              </ng-container>
              <ng-template #loadingCurrency>
                <mat-option disabled>
                  <div class="filters-loading-container">
                    <span>{{'GENERAL.FILTERS.LOADING' | translate}}</span>
                    <mat-spinner diameter="20"></mat-spinner>
                  </div>
                </mat-option>
              </ng-template>
            </mat-select>
            <mat-error *ngIf="statementForm.get('currency')?.errors" data-cy="input-currency-errors">
              {{ 'STATEMENTS.WRITE.ERRORS.CURRENCY_IS_REQUIRED' | translate }}
            </mat-error>
          </mat-form-field>

          <!-- initialDate -->
          <mat-form-field appearance="outline">
            <mat-label>{{ 'STATEMENTS.FIELDS.INITIAL_DATE' | translate }}</mat-label>
            <input matInput [matDatepicker]="pickerInitialDate" [formControl]="initialDateFormControl"
                   data-cy="input-initial-date" [max]="currentDayBefore" required>
            <mat-datepicker-toggle matSuffix [for]="pickerInitialDate"></mat-datepicker-toggle>
            <mat-datepicker #pickerInitialDate></mat-datepicker>
            <mat-error *ngIf="statementForm.get('in_date')?.errors" data-cy="input-initial-date-errors">
              {{ 'STATEMENTS.WRITE.ERRORS.START_DATE_IS_REQUIRED' | translate }}
            </mat-error>
          </mat-form-field>

          <!-- initialAmount -->
          <mat-form-field appearance="outline">
            <mat-label>{{ 'STATEMENTS.FIELDS.INITIAL_AMOUNT' | translate }}</mat-label>
            <input matInput type="number" [formControl]="initialAmountFormControl" data-cy="input-initial-amount" required>
            <mat-error *ngIf="statementForm.get('in_amount')?.errors" data-cy="input-initial-amount-errors">
              {{ 'STATEMENTS.WRITE.ERRORS.INITIAL_BALANCE_IS_REQUIRED' | translate }}
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-column">
          <!-- accountID -->
          <mat-form-field appearance="outline">
            <mat-label>{{ 'STATEMENTS.FIELDS.STATEMENT_NUMBER' | translate }}</mat-label>
            <input matInput type="text" [formControl]="statementIDFormControl" data-cy="input-statement-id" required>
            <mat-error *ngIf="statementForm.get('statement_id')?.errors" data-cy="input-statement-id-errors">
              {{ 'STATEMENTS.WRITE.ERRORS.STATEMENT_NUMBER_IS_REQUIRED' | translate }}
            </mat-error>
          </mat-form-field>

          <!-- finalDate -->
          <mat-form-field appearance="outline">
            <mat-label>{{ 'STATEMENTS.FIELDS.FINAL_DATE' | translate }}</mat-label>
            <input matInput [matDatepicker]="pickerFinalDate" [min]="initialDateFormControl.value"
                   [formControl]="finalDateFormControl" data-cy="input-final-date" [max]="currentDayBefore" required>
            <mat-datepicker-toggle matSuffix [for]="pickerFinalDate"></mat-datepicker-toggle>
            <mat-datepicker #pickerFinalDate></mat-datepicker>
            <mat-error *ngIf="statementForm.get('fi_date')?.errors" data-cy="input-final-date-errors">
              {{ 'STATEMENTS.WRITE.ERRORS.END_DATE_IS_REQUIRED' | translate }}
            </mat-error>
          </mat-form-field>

          <!-- finalAmount -->
          <mat-form-field appearance="outline">
            <mat-label>{{ 'STATEMENTS.FIELDS.FINAL_AMOUNT' | translate }}</mat-label>
            <input matInput type="number" [formControl]="finalAmountFormControl" data-cy="input-final-amount" required>
            <mat-error *ngIf="statementForm.get('fi_amount')?.errors" data-cy="input-final-amount-errors">
              {{ 'STATEMENTS.WRITE.ERRORS.FINAL_BALANCE_IS_REQUIRED' | translate }}
            </mat-error>
          </mat-form-field>
        </div>
      </div>

      <div class="form-row" style="width: 100%;">
        <div class="form-column">
          <mat-form-field appearance="outline">
            <mat-label>{{ 'STATEMENTS.FIELDS.DESCRIPTION' | translate }}</mat-label>
            <textarea matInput rows="3" [formControl]="descriptionFormControl" data-cy="input-description" required></textarea>
            <mat-error *ngIf="statementForm.get('description')?.errors" data-cy="input-description-errors">
              {{ 'STATEMENTS.WRITE.ERRORS.DESCRIPTION_IS_REQUIRED' | translate }}
            </mat-error>
          </mat-form-field>
        </div>
      </div>

      <ngx-dropzone [multiple]="false" data-cy="dropzone" (change)="onSelectFile($event)"
                    [accept]="'.csv'" [class.dropzone-disabled]="file" [class.dropzone-files]="true">
        <ngx-dropzone-label>
          <ng-container>
            <mat-icon>cloud_upload</mat-icon>
            <span id="upload-message">{{'UPLOAD.NOTIFICATIONS.MESSAGES.DESKTOP' | translate}}</span>
          </ng-container>
        </ngx-dropzone-label>
        <ngx-dropzone-preview [removable]="true" (removed)="onRemoveFile()" *ngIf="file">
          <ngx-dropzone-label>{{ file.name }}</ngx-dropzone-label>
        </ngx-dropzone-preview>
      </ngx-dropzone>
    </div>
  </div>


  <div class="dialog-actions" mat-dialog-actions>
    <button mat-flat-button id="download-template-btn" (click)="onTemplateDownloadClick()" data-cy="btn-template-download">
      <mat-icon>cloud_download</mat-icon>
      {{ 'UPLOAD.ACTIONS.DOWNLOAD_TEMPLATE' | translate }}
    </button>
    <span fxFlex></span>
    <button mat-flat-button mat-dialog-close id="cancel-btn" [fxShow]="true" [fxHide.xs]="true" [fxHide.sm]="true">
      {{ 'GENERAL.BUTTONS.LABELS.CANCEL' | translate }}
    </button>
    <button mat-flat-button color="primary" id="create-item-btn"
            (click)="onSaveClick()" [disabled]="isButtonDisabled()" data-cy="btn-save">
      {{ getButtonAction() | translate }}
    </button>
  </div>

</div>
