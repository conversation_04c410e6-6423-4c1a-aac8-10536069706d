@import "node_modules/@jumia-cs-fin/common/assets/styles/dialog-components";

ngx-dropzone {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;
  width: 95%;

  ngx-dropzone-label {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
}

.form-container{
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;
}

.form-column{
  width: 100%;
  display: flex;
  flex-direction: column;
  margin: 0 1vh;

}

.form-row{
  width: 100%;
  margin: 0 1vh;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

mat-form-field ::ng-deep .mat-mdc-form-field-infix{
  width: auto !important;
}

#download-template-btn{
  margin: 1vh 0;
  color: var(--primary-color);
}
