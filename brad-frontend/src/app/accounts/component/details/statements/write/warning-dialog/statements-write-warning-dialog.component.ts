import {Component, EventEmitter, Inject, OnInit, Output} from "@angular/core";
import {MAT_DIALOG_DATA, MatDialogRef} from "@angular/material/dialog";
import {Subject} from "rxjs";

@Component({
    selector: 'brad-write-warning-dialog',
    templateUrl: './statements-write-warning-dialog.component.html',
    styleUrls: ['./statements-write-warning-dialog.component.scss']
})
export class StatementsWriteWarningDialogComponent implements OnInit {

    @Output() uploadStatementEvent = new EventEmitter();

    private _onDestroy:Subject<void> = new Subject<void>();

    constructor(public dialogRef: MatDialogRef<StatementsWriteWarningDialogComponent>,
                @Inject(MAT_DIALOG_DATA) public data: {
                    initialStatementDate: string | null,
                    lastStatementDate: string | null
                },
                ) { }

    ngOnInit(): void {
    }

    ngOnDestroy(): void {
        this._onDestroy.next();
        this._onDestroy.complete();
    }

    onUploadClick(): void {
        this.uploadStatementEvent.emit();
    }
}
