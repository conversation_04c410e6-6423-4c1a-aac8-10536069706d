import {Component, EventEmitter, Inject, Input, OnDestroy, OnInit, Output} from '@angular/core';
import {FormControl, FormGroup, Validators} from "@angular/forms";
import {Subject} from "rxjs";
import {MAT_DIALOG_DATA, MatDialog, MatDialogRef} from "@angular/material/dialog";
import {CsFinWritePopupType} from "@jumia-cs-fin/common";
import {Statement} from "../../../../../entities/statement/statement";
import {StatementFacade} from "../../../../facade/statement.facade";
import {finalize, takeUntil} from "rxjs/operators";
import {StatementWithFileRequest} from "../../../../../entities/statement/statement-with-file-request";
import {Account} from "../../../../../entities/account/account";
import {DateAdapter} from "@angular/material/core";
import {HttpErrorResponse} from "@angular/common/http";
import {NotificationService} from "../../../../../api/service/notification.service";
import {Currency} from "../../../../../entities/currency/currency";
import {CurrencyApiService} from "../../../../../api/service/currency-api.service";
import {StatementsWriteWarningDialogComponent} from "./warning-dialog/statements-write-warning-dialog.component";

@Component({
  selector: 'brad-write',
  templateUrl: './statements-write.component.html',
  styleUrls: ['./statements-write.component.scss']
})
export class StatementsWriteComponent implements OnInit, OnDestroy {

  @Output() createStatementEvent = new EventEmitter<void>();
  @Input() previousStatement: Statement | null = null;

  currentDayBefore = new Date();
  statementForm!: FormGroup;
  accountIDFormControl!: FormControl;
  currencyFormControl!: FormControl;
  statementIDFormControl!: FormControl;
  initialDateFormControl!: FormControl;
  finalDateFormControl!: FormControl;
  initialAmountFormControl!: FormControl;
  finalAmountFormControl!: FormControl;
  fileFormControl!: FormControl;
  descriptionFormControl!: FormControl;

  initialDirectionSearchFormControl = new FormControl();
  initialDirectionList:string[] = [];
  filteredInitialDirectionList :string[]= [];
  isInitialDirectionLoading = true;

  finalDirectionSearchFormControl = new FormControl();
  finalDirectionList:string[] = [];
  filteredFinalDirectionList :string[]= [];
  isFinalDirectionLoading = true;

  currencySearchFormControl = new FormControl();
  currencyList: Currency[] = [];
  filteredCurrencyList: Currency[] = [];


  file!: File | null;
  apiErrorOccurred = false;

  isSavingData = false;
  private _onDestroy:Subject<void> = new Subject<void>();

  constructor(public dialogRef: MatDialogRef<StatementsWriteComponent>,
              @Inject(MAT_DIALOG_DATA) public data: {
                type: CsFinWritePopupType,
                statement: Statement | null,
                accountID: number,
                account: Account,
                nextStatement: Statement | null,
                previousStatement: Statement | null
              },
              private dialog: MatDialog,
              private notificationService: NotificationService,
              private statementFacade: StatementFacade,
              private readonly adapter: DateAdapter<Date>,
              private currencyApiService: CurrencyApiService
             ) { }

  ngOnInit(): void {
    this.statementForm = new FormGroup({});
    this.initFiltersSearch();
    this.initFormControls();
    this.setFormControlsToForm();
    this.accountIDFormControl.setValue(this.data.accountID);
    this.accountIDFormControl.disable();
    this.adapter.setLocale('en-GB');
    this.previousStatement = this.data.previousStatement;
    this.currentDayBefore.setDate(this.currentDayBefore.getDate() - 1);
  }

  ngOnDestroy(): void {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  private initFiltersSearch(): void {
    this.initDirectionSearch();
    this.initFinalSearch();
    this.initCurrencySearch();
  }

  private initDirectionSearch(): void {
    this.initialDirectionSearchFormControl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .pipe(
        finalize(() => this.isInitialDirectionLoading = false)
      )
      .subscribe((value: string) => {

        const searchTerm = value.trim();
        if (searchTerm) {
          this.filteredInitialDirectionList = this.initialDirectionList.filter((direction) => {
            return direction.trim().toLowerCase().includes(searchTerm.toLowerCase());
          })
        } else {
          this.filteredInitialDirectionList = this.initialDirectionList;
        }
      });
  }
  private initFinalSearch(): void {
    this.finalDirectionSearchFormControl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .pipe(
        finalize(() => this.isFinalDirectionLoading = false)
      )
      .subscribe((value: string) => {

        const searchTerm = value.trim();
        if (searchTerm) {
          this.filteredFinalDirectionList = this.finalDirectionList.filter((direction) => {
            return direction.trim().toLowerCase().includes(searchTerm.toLowerCase());
          })
        } else {
          this.filteredFinalDirectionList = this.finalDirectionList;
        }
      });
  }

  private initCurrencySearch(): void {
    this.currencySearchFormControl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe((value: String) => {
        const searchTerm = value.trim();
        if (searchTerm) {
          this.filteredCurrencyList = this.currencyList.filter((currency) => {
            return currency.code.trim().toLowerCase().includes(searchTerm.toLowerCase());
          })
        } else {
          this.filteredCurrencyList = this.currencyList;
        }
      });
  }


  initFormControls(): void {
    this.accountIDFormControl = new FormControl(null, [Validators.required]);
    this.currencyFormControl = new FormControl(null, [Validators.required, Validators.maxLength(3)]);
    this.statementIDFormControl = new FormControl(null, [Validators.required]);
    this.initialDateFormControl = new FormControl(null, [Validators.required]);
    this.finalDateFormControl = new FormControl(null, [Validators.required]);
    this.initialAmountFormControl = new FormControl(null, [Validators.required]);
    this.finalAmountFormControl = new FormControl(null, [Validators.required]);
    this.fileFormControl = new FormControl(null, [Validators.required]);
    this.descriptionFormControl = new FormControl(null, [Validators.required]);
  }

  setFormControlsToForm(): void {
    this.statementForm.addControl('account_id', this.accountIDFormControl);
    this.statementForm.addControl('currency', this.currencyFormControl);
    this.statementForm.addControl('in_date', this.initialDateFormControl);
    this.statementForm.addControl('fi_date', this.finalDateFormControl);
    this.statementForm.addControl('in_amount', this.initialAmountFormControl);
    this.statementForm.addControl('fi_amount', this.finalAmountFormControl);
    this.statementForm.addControl('file', this.fileFormControl);
    this.statementForm.addControl('description', this.descriptionFormControl);

    this.statementForm.valueChanges.subscribe(() => {
      this.apiErrorOccurred = false;
    });
  }


  isCreateView(): boolean {
    return this.data.type === CsFinWritePopupType.create;
  }

  buildStatementRequest(): StatementWithFileRequest {
    return {
      account_statement: {
        account_id: this.accountIDFormControl.value,
        currency: this.currencyFormControl.value,
        statement_id: this.statementIDFormControl.value,
        in_date: this.initialDateFormControl.value,
        fi_date: this.finalDateFormControl.value,
        in_amount: this.initialAmountFormControl.value,
        fi_amount: this.finalAmountFormControl.value,
        description: this.descriptionFormControl.value
      },
      transactions_csv: this.fileFormControl.value,
      next_statement_id: 0
    };
  }

  onSaveClick(): void {

    const statementReq = this.buildStatementRequest();

    if (this.previousStatement !== null) {
      const daysDifference = this.getDaysBetweenDates(new Date(this.previousStatement.finalDate!), new Date(statementReq.account_statement.in_date));
      if (daysDifference > 1) {
        const dialogRef = this.dialog.open(StatementsWriteWarningDialogComponent, {
          data: {
            initialStatementDate: this.formatDate(statementReq.account_statement.in_date),
            lastStatementDate: this.formatDate(this.previousStatement.finalDate!)
          }
        });

        dialogRef.componentInstance.uploadStatementEvent.subscribe(() => {
              if(this.isCreateView()) {
                this.doCreate(statementReq);
                dialogRef.close();
              }
            }
        );

      } else if (this.isCreateView()) {
        this.doCreate(statementReq);
      }
    } else if (this.isCreateView()) {
      this.doCreate(statementReq);
    }
  }

  doCreate(statement: StatementWithFileRequest): void {
    this.isSavingData = true;
    statement.account_statement.account_id = String(this.data.account.accountNumber);

    if (this.file==null) {
      this.notificationService.errorTranslated('STATEMENTS.NOTIFICATIONS.MESSAGES.FILE_ERROR',
        {statement_id: statement.account_statement.statement_id});
      return;
    }
    statement.account_statement.in_date = this.formatDate(statement.account_statement.in_date);
    statement.account_statement.fi_date = this.formatDate(statement.account_statement.fi_date);
    statement.next_statement_id = this.data.nextStatement?.id || 0;
    this.statementFacade.createWithFile(statement)
      .pipe(
        finalize(() => this.isSavingData = false)
      )
      .subscribe({
        next: () => {
          this.notificationService.successTranslated('STATEMENTS.NOTIFICATIONS.MESSAGES.CREATE_STATEMENT_SUCCESS',
            {statementId: statement.account_statement.statement_id});
          this.dialogRef.close(true);
          this.createStatementEvent.emit();
        },
        error: (error:HttpErrorResponse) => {
          this.apiErrorOccurred = true;
          this.notificationService.errorWithResponse(error);
        }
      });
  }

  loadCurrencies(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if(this.currencyList.length <= 0) {
        this.currencyApiService.getAll()
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (currency: Currency[]) => {
              this.currencyList = currency;
              this.filteredCurrencyList = this.currencyList;
              resolve();
            }, error: (error) => reject(error)
          });
      } else {
        resolve();
      }
    });
  }

  onSelectFile(event: any) {
    if (event.rejectedFiles.length) {
      return this.notificationService.errorTranslated('UPLOAD.NOTIFICATIONS.MESSAGES.ERROR', {});
    }
    this.file = event.addedFiles[0];
    let reader = new FileReader();
    if (this.file!=null) {
      reader.readAsDataURL(this.file);
      reader.onload = () => {
        this.fileFormControl.setValue(reader.result);
      }
    }
  }

  onRemoveFile() {
    this.file = null;
  }

  getButtonAction() {
    return this.isCreateView() ? 'GENERAL.BUTTONS.LABELS.CREATE' : 'GENERAL.BUTTONS.LABELS.SAVE';
  }

  getTitle() {
    return this.isCreateView() ? 'STATEMENTS.CREATE.TITLE' : 'STATEMENTS.EDIT.TITLE';
  }

  onTemplateDownloadClick () {
    const link = document.createElement('a');
    link.setAttribute('target', '_blank');
    link.setAttribute('href', '/assets/templates/transactions-template.csv');
    link.setAttribute('download', 'transactions-template.csv');
    document.body.appendChild(link);
    link.click();
    link.remove();
  }

  formatDate(dateString: string): string {
    let date = new Date(dateString);
    let day = ('0' + date.getDate()).slice(-2);
    let month = ('0' + (date.getMonth() + 1)).slice(-2);
    let year = date.getFullYear();

    return `${day}-${month}-${year}`;
  }

  getDaysBetweenDates(startDate: Date, endDate: Date): number {
    const diffInMs = Math.abs(endDate.getTime() - startDate.getTime());
    return diffInMs / (1000 * 60 * 60 * 24);
  }

  compareIdFn(o1: any, o2: any): boolean {
    return o1 && o2 && o1.id === o2.id;
  }

  isButtonDisabled(): boolean {
    return this.statementForm.invalid || this.apiErrorOccurred;
  }
}
