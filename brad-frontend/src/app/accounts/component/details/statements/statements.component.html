<main class="container" cdkDropListGroup>

  <div class="table-container">

    <mat-progress-bar mode="indeterminate" *ngIf="isLoading"></mat-progress-bar>
    <table mat-table class="cs-fin-table" matSort [dataSource]="dataSource"  [class.loading]="isLoading"
           (matSortChange)="onSortChange($event)" cdkDropList cdkDropListSortingDisabled>

      <ng-container matColumnDef="id">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'STATEMENTS.FIELDS.ID' | translate }}</th>
        <td mat-cell *matCellDef="let statement"> {{statement?.id}} </td>
      </ng-container>

      <ng-container matColumnDef="accountID">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'STATEMENTS.FIELDS.ACCOUNT_ID' | translate }}</th>
        <td mat-cell *matCellDef="let statement"> {{statement?.accountID}} </td>
      </ng-container>

      <ng-container matColumnDef="currency">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'STATEMENTS.FIELDS.CURRENCY' | translate }}</th>
        <td mat-cell *matCellDef="let statement"> {{statement?.currency.code}} </td>
      </ng-container>

      <ng-container matColumnDef="statementId">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'STATEMENTS.FIELDS.STATEMENT_NUMBER' | translate }}</th>
        <td mat-cell *matCellDef="let statement"> {{statement?.statementId}} </td>
      </ng-container>

      <ng-container matColumnDef="previousStatementId">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'STATEMENTS.FIELDS.PREVIOUS_STATEMENT_NUMBER' | translate }}</th>
        <td mat-cell *matCellDef="let statement"> {{statement?.previousStatementId}} </td>
      </ng-container>

      <ng-container matColumnDef="initialDate">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'STATEMENTS.FIELDS.INITIAL_DATE' | translate }}</th>
        <td mat-cell *matCellDef="let statement"> {{statement?.initialDate}} </td>
      </ng-container>

      <ng-container matColumnDef="finalDate">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'STATEMENTS.FIELDS.FINAL_DATE' | translate }}</th>
        <td mat-cell *matCellDef="let statement"> {{statement?.finalDate}} </td>
      </ng-container>

      <ng-container matColumnDef="initialDirection">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'STATEMENTS.FIELDS.INITIAL_DIRECTION' | translate }}</th>
        <td mat-cell *matCellDef="let statement"> {{statement?.initialDirection}} </td>
      </ng-container>

      <ng-container matColumnDef="finalDirection">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'STATEMENTS.FIELDS.FINAL_DIRECTION' | translate }}</th>
        <td mat-cell *matCellDef="let statement"> {{statement?.finalDirection}} </td>
      </ng-container>

      <ng-container matColumnDef="initialAmount">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'STATEMENTS.FIELDS.INITIAL_AMOUNT' | translate }}</th>
        <td mat-cell *matCellDef="let statement">
          <span>
              {{statement?.initialAmount | number:'1.2-2'}} {{statement?.currency.symbol}}
          </span>

        </td>
      </ng-container>

      <ng-container matColumnDef="finalAmount">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'STATEMENTS.FIELDS.FINAL_AMOUNT' | translate }}</th>
        <td mat-cell *matCellDef="let statement">
          <span>
              {{statement?.finalAmount | number:'1.2-2'}} {{statement?.currency.symbol}}
          </span>
        </td>
      </ng-container>


      <ng-container matColumnDef="status">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'STATEMENTS.FIELDS.STATUS' | translate }}</th>
        <td mat-cell *matCellDef="let statement"> {{statement?.status}} </td>
      </ng-container>

      <ng-container matColumnDef="statusDescription">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'STATEMENTS.FIELDS.STATUS_DESCRIPTION' | translate }}</th>

        <td mat-cell *matCellDef="let statement; let i = index" class="status-cell">
          <span>{{statement?.statusDescription}}</span>
          <button mat-icon-button (click)="onRetry(statement, $event); $event.stopPropagation()" *ngIf="statement.status !== 'IMPORTED'"
                  csFinHasPermissionOnAnyTarget
                  [disabled]="isLoading || (statement.status=='OPEN' && isLasUpdatedInTheLastHour(statement))"
                  [authPermissions]="auth.permissions.BRAD_RETRY_STATEMENT"
                  [authTarget]="bradAuthTarget"
                  [authAction]="auth.actions.HIDE"
                  [matTooltip]="('STATEMENTS.WRITE.OPERATION_TYPES.RETRY' | translate)"
          >
            <mat-icon>autorenew</mat-icon>
          </button>
          <button mat-icon-button *ngIf="shouldShowDeleteIcon(statement, i)"
                  (click)="onDeleteClick(statement, $event)" >
            <mat-icon color="primary">delete</mat-icon>
          </button>
        </td>

      </ng-container>

      <ng-container matColumnDef="description">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'STATEMENTS.FIELDS.DESCRIPTION' | translate }}</th>
        <td mat-cell *matCellDef="let statement"> {{statement?.description}} </td>
      </ng-container>

      <ng-container matColumnDef="createdAt">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'STATEMENTS.FIELDS.CREATED_AT' | translate }}</th>
        <td mat-cell *matCellDef="let statement"> {{statement?.createdAt}} </td>
      </ng-container>

      <ng-container matColumnDef="createdBy">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'STATEMENTS.FIELDS.CREATED_BY' | translate }}</th>
        <td mat-cell *matCellDef="let statement"> {{statement?.createdBy}} </td>
      </ng-container>

      <ng-container matColumnDef="updatedAt">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'STATEMENTS.FIELDS.UPDATED_AT' | translate }}</th>
        <td mat-cell *matCellDef="let statement"> {{statement?.updatedAt}} </td>
      </ng-container>

      <ng-container matColumnDef="updatedBy">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'STATEMENTS.FIELDS.UPDATED_BY' | translate }}</th>
        <td mat-cell *matCellDef="let statement"> {{statement?.updatedBy}} </td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="getDisplayedColumns(); sticky: true"></tr>
      <tr mat-row *matRowDef="let row; columns: getDisplayedColumns();"
          class="with-detail" style="cursor: pointer;" (click)="onSelectStatement(row)"></tr>
    </table>


    <mat-paginator class="table-paginator"
                   [pageSizeOptions]="pagination.pageSizeOptions"
                   [pageSize]="pagination.pageSize"
                   [length]="pagination.totalItems"
                   [pageIndex]="pagination.pageIndex"
                   (page)="onPageChange($event)"
                   showFirstLastButtons>
    </mat-paginator>
  </div>

  <shared-add-remove-columns-alt [triggerOrigin]="triggerOrigin"
                                 [isOverlayOpen$]="isOverlayOpen$"
                                 [menu]="MENU">
  </shared-add-remove-columns-alt>
</main>
