import {
  ChangeDetectorRef,
  Component,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  SimpleChanges,
  ViewEncapsulation
} from '@angular/core';
import {FormControl, FormGroup} from "@angular/forms";
import {Observable, Subject} from "rxjs";
import {CsFinActiveFiltersFacade, CsFinApiService, CsFinSidenavService} from "@jumia-cs-fin/common";
import {ActivatedRoute, Router} from "@angular/router";
import {takeUntil} from "rxjs/operators";
import {StatementFacade} from "../../../../facade/statement.facade";
import {StatementFilters} from "../../../../../entities/statement/statement-filters";
import {AccountFacade} from "../../../../facade/account.facade";
import {Currency} from "../../../../../entities/currency/currency";
import {CurrencyApiService} from "../../../../../api/service/currency-api.service";

@Component({
  selector: 'brad-statements-header',
  templateUrl: './statements-header.component.html',
  styleUrls: ['./statements-header.component.scss'],
  providers: [CsFinActiveFiltersFacade],
  encapsulation:ViewEncapsulation.Emulated
})
export class StatementsHeaderComponent implements OnInit, OnChanges, OnDestroy {
  @Output() submitFilters = new Subject<StatementFilters>();
  @Input() filters!: StatementFilters;
  @Input() showFilters!: boolean;
  @Input() accountID!: number;

  filterTextFormControl!: FormControl;
  queryParams = {};

  form!: FormGroup;

  currencyFormControl!: FormControl;
  currencySearchFormControl = new FormControl();
  currencyList:Currency[] = [];
  filteredCurrencyList:Currency[] = [];

  statementIdFormControl!: FormControl;
  previousStatementIDFormControl!: FormControl;

  initialDateStartFormControl!: FormControl;
  initialDateEndFormControl!: FormControl;
  finalDateStartFormControl!: FormControl;
  finalDateEndFormControl!: FormControl;

  initialDirectionFormControl!: FormControl;
  initialDirectionSearchFormControl = new FormControl();
  initialDirectionList:string[] = [];
  filteredInitialDirectionList:string[] = [];

  finalDirectionFormControl!: FormControl;
  finalDirectionSearchFormControl = new FormControl();
  finalDirectionList:string[] = [];
  filteredFinalDirectionList:string[] = [];

  initialAmountFormControl!: FormControl;
  finalAmountFormControl!: FormControl;

  statusFormControl!: FormControl;
  statusSearchFormControl = new FormControl();
  statusList:string[] = [];
  filteredStatusList:string[] = [];

  statusDescriptionFormControl!: FormControl;
  statusDescriptionSearchFormControl = new FormControl();
  statusDescriptionList:string[] = [];
  filteredStatusDescriptionList:string[] = [];

  accountIDFormControl!: FormControl;

  createdAtStartFormControl!: FormControl;
  createdAtEndFormControl!: FormControl;

  isOpen$!: Observable<boolean>;
  isRefreshing = false;
  isInitializing = false;

  private _onDestroy: Subject<void> = new Subject<void>();

  constructor(public ref: ChangeDetectorRef,
              public sidenav: CsFinSidenavService,
              private activatedRoute: ActivatedRoute,
              private activeFiltersFacade: CsFinActiveFiltersFacade,
              private router: Router,
              private apiService: CsFinApiService,
              private statementFacade: StatementFacade,
              private accountFacade: AccountFacade,
              private currencyApiService: CurrencyApiService) { }


  ngOnInit() {
    this.isInitializing = true;
    this.initFiltersSearch();
    this.initializeFormData(this.filters);
  }

  ngOnChanges(changes: SimpleChanges) {
    if(changes['filters'] && changes['filters'].currentValue && !this.isInitializing) {
      this.initializeFormData(changes['filters'].currentValue);
    }
  }

  ngOnDestroy(): void {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  private initFiltersSearch(): void {
    this.initCurrencySearch();
    this.initStatusSearch();
    this.initErrorTypeSearch();
    this.initInitialDirectionSearch();
    this.initFinalDirectionSearch();
  }

  private initCurrencySearch(): void {
    this.currencySearchFormControl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe((value: String) => {
        const searchTerm = value.trim();
        if (searchTerm) {
          this.filteredCurrencyList = this.currencyList.filter((currency) => {
            return currency.code.trim().toLowerCase().includes(searchTerm.toLowerCase());
          })
        } else {
          this.filteredCurrencyList = this.currencyList;
        }
      });
  }

  private initStatusSearch(): void {
    this.statusSearchFormControl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe((value: String) => {
        const searchTerm = value.trim();
        if (searchTerm) {
          this.filteredStatusList = this.statusList.filter((status) => {
            return status.trim().toLowerCase().includes(searchTerm.toLowerCase());
          })
        } else {
          this.filteredStatusList = this.statusList;
        }
      });
  }


  private initErrorTypeSearch(): void {
    this.statusDescriptionSearchFormControl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe((value: String) => {
        const searchTerm = value.trim();
        if (searchTerm) {
          this.filteredStatusDescriptionList = this.statusDescriptionList.filter((status) => {
            return status.trim().toLowerCase().includes(searchTerm.toLowerCase());
          })
        } else {
          this.filteredStatusDescriptionList = this.statusDescriptionList;
        }
      });
  }

  private initInitialDirectionSearch(): void {
    this.initialDirectionSearchFormControl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe((value: String) => {
        const searchTerm = value.trim();
        if (searchTerm) {
          this.filteredInitialDirectionList = this.initialDirectionList.filter((direction) => {
            return direction.trim().toLowerCase().includes(searchTerm.toLowerCase());
          })
        } else {
          this.filteredInitialDirectionList = this.initialDirectionList;
        }
      });
  }

  private initFinalDirectionSearch(): void {
    this.finalDirectionSearchFormControl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe((value: String) => {
        const searchTerm = value.trim();
        if (searchTerm) {
          this.filteredFinalDirectionList = this.finalDirectionList.filter((direction) => {
            return direction.trim().toLowerCase().includes(searchTerm.toLowerCase());
          })
        } else {
          this.filteredFinalDirectionList = this.finalDirectionList;
        }
      });
  }


  clearFilters(): void {
    this.form.reset('', {emitEvent: false});
    this.statementFacade.filtersChanged({});
  }

  submit() {
    this.submitFilters.next(this.getFormValues());
  }


  private getFormValues(): StatementFilters {
    return this.form.value as StatementFilters;
  }

  private initializeFormData(params:any): void {
    this.queryParams = params;
    if(!this.form) {
      this.form = new FormGroup({});
      this.initializeFormControlsAndFilters(params);
    }
  }

  private initializeFormControlsAndFilters(params:any): void {
    const filters: StatementFilters = {};

    this.filterTextFormControl = new FormControl(params.filterText);
    filters.filterText = params.filterText;

    let currencyCodes: string[] = params.currencyCodes === undefined ? [] : params.currencyCodes;
    this.currencyFormControl = new FormControl(currencyCodes);
    filters.currencyCodes = currencyCodes;

    this.statementIdFormControl = new FormControl(params.statementId);
    filters.statementId = params.statementId;

    this.previousStatementIDFormControl = new FormControl(params.previousStatementID);
    filters.previousStatementID = params.previousStatementID;

    this.initialDateStartFormControl = new FormControl(params.initialDateStart);
    filters.initialDateStart = params.initialDateStart;

    this.initialDateEndFormControl = new FormControl(params.initialDateEnd);
    filters.initialDateEnd = params.initialDateEnd;

    this.finalDateStartFormControl = new FormControl(params.finalDateStart);
    filters.finalDateStart = params.finalDateStart;

    this.finalDateEndFormControl = new FormControl(params.finalDateEnd);
    filters.finalDateEnd = params.finalDateEnd;

    let initialDirection: string[] = params.initialDirection === undefined ? [] : params.initialDirection;
    this.initialDirectionFormControl = new FormControl(initialDirection);
    filters.initialDirection = initialDirection;

    let finalDirection: string[] = params.finalDirection === undefined ? [] : params.finalDirection;
    this.finalDirectionFormControl = new FormControl(finalDirection);
    filters.finalDirection = finalDirection;

    this.initialAmountFormControl = new FormControl(params.initialAmount);
    filters.initialAmount = params.initialAmount;

    this.finalAmountFormControl = new FormControl(params.finalAmount);
    filters.finalAmount = params.finalAmount;

    let status: string[] = params.status === undefined ? [] : params.status;
    this.statusFormControl = new FormControl(status);
    filters.status = status;

    let statusDescription: string[] = params.statusDescription === undefined ? [] : params.statusDescription;
    this.statusDescriptionFormControl = new FormControl(statusDescription);
    filters.statusDescription = statusDescription;

    this.accountIDFormControl = new FormControl(this.accountID);
    filters.accountID = this.accountID;

    this.createdAtStartFormControl = new FormControl(params.createdAtStart);
    filters.createdAtStart = params.createdAtStart;

    this.createdAtEndFormControl = new FormControl(params.createdAtEnd);
    filters.createdAtEnd = params.createdAtEnd;

    filters.orderField = params.orderField;
    filters.orderDirection = params.orderDirection;
    filters.page = params.page;
    filters.size = params.size;

    Promise.all([
      this.applyCurrencyFilterUrl(params, filters),
      this.applyStatusFilter(params, filters),
      this.applyErrorTypeFilter(params, filters),
      this.applyInitialDirectionFilter(params, filters),
      this.applyFinalDirectionFilter(params, filters)
    ]).then(() => {
      this.setFormControlsToForm();
      this.isInitializing = false;
    });

  }

  private applyCurrencyFilterUrl(params: any, filters: StatementFilters): Promise<void> {
    return new Promise<void>(async (resolve) => {

      if(!params.currencyCodes) {
        resolve();
        return;
      }

      await this.loadCurrencies();
      this.currencyFormControl.setValue(filters.currencyCodes, {emitEvent: false});
      resolve();
    });
  }

  private applyStatusFilter(params: any, filters: StatementFilters): Promise<void> {
    return new Promise<void>(async (resolve) => {

      if(!params.status) {
        resolve();
        return;
      }

      await this.loadStatus();

      this.statusFormControl.setValue(filters.status, {emitEvent: false});
      resolve();
    });
  }

  private applyErrorTypeFilter(params: any, filters: StatementFilters): Promise<void> {
    return new Promise<void>(async (resolve) => {

      if(!params.statusDescription) {
        resolve();
        return;
      }

      await this.loadStatusDescription();

      this.statusDescriptionFormControl.setValue(filters.statusDescription, {emitEvent: false});
      resolve();
    });
  }

  private applyInitialDirectionFilter(params: any, filters: StatementFilters): Promise<void> {
    return new Promise<void>(async (resolve) => {

      if(!params.initialDirection) {
        resolve();
        return;
      }

      await this.loadInitialDirection();

      this.initialDirectionFormControl.setValue(filters.initialDirection, {emitEvent: false});
      resolve();
    });
  }

  private applyFinalDirectionFilter(params: any, filters: StatementFilters): Promise<void> {
    return new Promise<void>(async (resolve) => {

      if(!params.finalDirection) {
        resolve();
        return;
      }

      await this.loadFinalDirection();

      this.finalDirectionFormControl.setValue(filters.finalDirection, {emitEvent: false});
      resolve();
    });
  }


  private setFormControlsToForm(): void {
    this.form.addControl(this.statementFacade.filterTextKey, this.filterTextFormControl);
    this.form.addControl(this.statementFacade.currencyKey, this.currencyFormControl);
    this.form.addControl(this.statementFacade.statementIdKey, this.statementIdFormControl);
    this.form.addControl(this.statementFacade.previousStatementIDKey, this.previousStatementIDFormControl);
    this.form.addControl(this.statementFacade.initialDateStartKey, this.initialDateStartFormControl);
    this.form.addControl(this.statementFacade.initialDateEndKey, this.initialDateEndFormControl);
    this.form.addControl(this.statementFacade.finalDateStartKey, this.finalDateStartFormControl);
    this.form.addControl(this.statementFacade.finalDateEndKey, this.finalDateEndFormControl);
    this.form.addControl(this.statementFacade.initialDirectionKey, this.initialDirectionFormControl);
    this.form.addControl(this.statementFacade.finalDirectionKey, this.finalDirectionFormControl);
    this.form.addControl(this.statementFacade.initialAmountKey, this.initialAmountFormControl);
    this.form.addControl(this.statementFacade.finalAmountKey, this.finalAmountFormControl);
    this.form.addControl(this.statementFacade.statusKey, this.statusFormControl);
    this.form.addControl(this.statementFacade.statusDescriptionKey, this.statusDescriptionFormControl);
    this.form.addControl(this.statementFacade.accountIDKey, this.accountIDFormControl);
    this.form.addControl(this.statementFacade.createdAtStartKey, this.createdAtStartFormControl);
    this.form.addControl(this.statementFacade.createdAtEndKey, this.createdAtEndFormControl);
  }

  loadCurrencies(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if(this.currencyList.length <= 0) {
        this.currencyApiService.getAll()
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (currencies: Currency[]) => {
              this.currencyList = currencies;
              this.filteredCurrencyList = this.currencyList;
              resolve();
            }, error: (error) => reject(error)
          });
      } else {
        resolve();
      }
    });
  }

  loadStatus(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if(this.statusList.length <= 0) {
        this.statementFacade.getStatusTypes()
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (statuses: string[]) => {
              this.statusList = statuses;
              this.filteredStatusList = this.statusList;
              resolve();
            }, error: (error) => reject(error)
          });
      } else {
        resolve();
      }
    });
  }

  loadStatusDescription(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if(this.statusDescriptionList.length <= 0) {
        this.statementFacade.getErrorTypes()
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (errorTypes: string[]) => {
              this.statusDescriptionList = errorTypes;
              this.filteredStatusDescriptionList = this.statusDescriptionList;
              resolve();
            }, error: (error) => reject(error)
          });
      } else {
        resolve();
      }
    });
  }

  loadInitialDirection(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if(this.initialDirectionList.length <= 0) {
        this.statementFacade.getDirections()
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (directions: string[]) => {
              this.initialDirectionList = directions;
              this.filteredInitialDirectionList = this.initialDirectionList;
              resolve();
            }, error: (error) => reject(error)
          });
      } else {
        resolve();
      }
    });
  }

  loadFinalDirection(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if(this.finalDirectionList.length <= 0) {
        this.statementFacade.getDirections()
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (directions: string[]) => {
              this.finalDirectionList = directions;
              this.filteredFinalDirectionList = this.finalDirectionList;
              resolve();
            }, error: (error) => reject(error)
          });
      } else {
        resolve();
      }
    });
  }



  noFiltersSelected() {
    return !!this.form.errors || this.isRefreshing;
  }

  compareIdFn(o1: any, o2: any): boolean {
    return o1 && o2 && o1.id === o2.id;
  }

}
