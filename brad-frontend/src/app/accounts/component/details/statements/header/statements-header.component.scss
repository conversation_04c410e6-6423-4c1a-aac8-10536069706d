@import 'node_modules/@jumia-cs-fin/common/assets/styles/header-components';

.filter-field{
  mat-label {
    color: var(--extra-light-color) !important;
  }
  input {
    color: var(--primary-text-color) !important;
  }

}

.mat-date-range-input-container {
  display: flex;
  justify-content: space-between;
  .mat-date-range-input-wrapper {
    width: 100%;
    input {
      width: 100%;
      text-align: center!important;
    }
    input::placeholder {
      text-align: center !important;
    }
  }
}
