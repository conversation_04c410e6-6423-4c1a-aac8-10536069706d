<div class="filters-container">

  <!-- currency -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-currency-filter"
                  (click)="loadCurrencies()">
    <mat-label>{{'STATEMENTS.FIELDS.CURRENCY' | translate}}</mat-label>
    <mat-select [formControl]="currencyFormControl" multiple>
      <ng-container *ngIf="filteredCurrencyList != null; else loadingCurrency">
        <mat-option>
          <ngx-mat-select-search [formControl]="currencySearchFormControl"
                                 [noEntriesFoundLabel]="'GENERAL.FILTERS.NO_MATCHING_RESULTS_FOUND' | translate"
                                 [placeholderLabel]="'GENERAL.FILTERS.SEARCH' | translate">
          </ngx-mat-select-search>
        </mat-option>
        <mat-option *ngFor="let currency of filteredCurrencyList" [value]="currency.code">
          {{currency.code}}
        </mat-option>
        <button mat-button color="primary" class="clear-selection-btn"
                [disabled]="!currencyFormControl.value?.length"
                (click)="currencyFormControl.reset([])">
          {{'GENERAL.FILTERS.ACTIONS.CLEAR_SELECTION' | translate}}
        </button>
      </ng-container>
      <ng-template #loadingCurrency>
        <mat-option disabled>
          <div class="filters-loading-container">
            <span>{{'GENERAL.FILTERS.LOADING' | translate}}</span>
            <mat-spinner diameter="20"></mat-spinner>
          </div>
        </mat-option>
      </ng-template>
    </mat-select>
  </mat-form-field>

  <!-- statement id -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-statement-id-filter">
    <mat-label>{{ 'STATEMENTS.FIELDS.STATEMENT_NUMBER' | translate }}</mat-label>
    <input matInput type="text" [formControl]="statementIdFormControl">
  </mat-form-field>

  <!-- previous statement id -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-previous-statement-id-filter">
    <mat-label>{{ 'STATEMENTS.FIELDS.PREVIOUS_STATEMENT_NUMBER' | translate }}</mat-label>
    <input matInput type="text" [formControl]="previousStatementIDFormControl">
  </mat-form-field>

  <!-- initial date -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-initial-date-filter">
    <mat-label>{{'STATEMENTS.FIELDS.INITIAL_DATE' | translate}}</mat-label>
    <mat-date-range-input [rangePicker]="picker1">
      <input matStartDate [formControl]="initialDateStartFormControl" placeholder="Start date">
      <input matEndDate [formControl]="initialDateEndFormControl" placeholder="End date">
    </mat-date-range-input>
    <mat-datepicker-toggle matIconSuffix [for]="picker1"></mat-datepicker-toggle>
    <mat-date-range-picker #picker1></mat-date-range-picker>
  </mat-form-field>

  <!-- final date -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-final-date-filter">
    <mat-label>{{'STATEMENTS.FIELDS.FINAL_DATE' | translate}}</mat-label>
    <mat-date-range-input [rangePicker]="picker2">
      <input matStartDate [formControl]="finalDateStartFormControl" placeholder="Start date">
      <input matEndDate [formControl]="finalDateEndFormControl" placeholder="End date">
    </mat-date-range-input>
    <mat-datepicker-toggle matIconSuffix [for]="picker2"></mat-datepicker-toggle>
    <mat-date-range-picker #picker2></mat-date-range-picker>
  </mat-form-field>

  <!-- initial direction -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-initial-direction-filter"
                  (click)="loadInitialDirection()">
    <mat-label>{{'STATEMENTS.FIELDS.INITIAL_DIRECTION' | translate}}</mat-label>
    <mat-select [formControl]="initialDirectionFormControl" multiple>
      <ng-container *ngIf="filteredInitialDirectionList != null; else loadingInitialDirection">
        <mat-option>
          <ngx-mat-select-search [formControl]="initialDirectionSearchFormControl"
                                 [noEntriesFoundLabel]="'GENERAL.FILTERS.NO_MATCHING_RESULTS_FOUND' | translate"
                                 [placeholderLabel]="'GENERAL.FILTERS.SEARCH' | translate">
          </ngx-mat-select-search>
        </mat-option>
        <mat-option *ngFor="let initialDirection of filteredInitialDirectionList" [value]="initialDirection">
          {{initialDirection}}
        </mat-option>
        <button mat-button color="primary" class="clear-selection-btn"
                [disabled]="!initialDirectionFormControl.value?.length"
                (click)="initialDirectionFormControl.reset([])">
          {{'GENERAL.FILTERS.ACTIONS.CLEAR_SELECTION' | translate}}
        </button>
      </ng-container>
      <ng-template #loadingInitialDirection>
        <mat-option disabled>
          <div class="filters-loading-container">
            <span>{{'GENERAL.FILTERS.LOADING' | translate}}</span>
            <mat-spinner diameter="20"></mat-spinner>
          </div>
        </mat-option>
      </ng-template>
    </mat-select>
  </mat-form-field>

  <!-- final direction -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-final-direction-filter"
                  (click)="loadFinalDirection()">
    <mat-label>{{'STATEMENTS.FIELDS.FINAL_DIRECTION' | translate}}</mat-label>
    <mat-select [formControl]="finalDirectionFormControl" multiple>
      <ng-container *ngIf="filteredFinalDirectionList != null; else loadingFinalDirection">
        <mat-option>
          <ngx-mat-select-search [formControl]="finalDirectionSearchFormControl"
                                 [noEntriesFoundLabel]="'GENERAL.FILTERS.NO_MATCHING_RESULTS_FOUND' | translate"
                                 [placeholderLabel]="'GENERAL.FILTERS.SEARCH' | translate">
          </ngx-mat-select-search>
        </mat-option>
        <mat-option *ngFor="let finalDirection of filteredFinalDirectionList" [value]="finalDirection">
          {{finalDirection}}
        </mat-option>
        <button mat-button color="primary" class="clear-selection-btn"
                [disabled]="!finalDirectionFormControl.value?.length"
                (click)="finalDirectionFormControl.reset([])">
          {{'GENERAL.FILTERS.ACTIONS.CLEAR_SELECTION' | translate}}
        </button>
      </ng-container>
      <ng-template #loadingFinalDirection>
        <mat-option disabled>
          <div class="filters-loading-container">
            <span>{{'GENERAL.FILTERS.LOADING' | translate}}</span>
            <mat-spinner diameter="20"></mat-spinner>
          </div>
        </mat-option>
      </ng-template>
    </mat-select>
  </mat-form-field>

  <!-- initial amount -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-initial-amount-filter">
    <mat-label>{{ 'STATEMENTS.FIELDS.INITIAL_AMOUNT' | translate }}</mat-label>
    <input matInput type="number" [formControl]="initialAmountFormControl">
  </mat-form-field>

  <!-- final amount -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-final-amount-filter">
    <mat-label>{{ 'STATEMENTS.FIELDS.FINAL_AMOUNT' | translate }}</mat-label>
    <input matInput type="number" [formControl]="finalAmountFormControl">
  </mat-form-field>

  <!-- status -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-status-filter"
                  (click)="loadStatus()">
    <mat-label>{{'STATEMENTS.FIELDS.STATUS' | translate}}</mat-label>
    <mat-select [formControl]="statusFormControl" multiple>
      <ng-container *ngIf="filteredStatusList != null; else loadingStatus">
        <mat-option>
          <ngx-mat-select-search [formControl]="statusSearchFormControl"
                                 [noEntriesFoundLabel]="'GENERAL.FILTERS.NO_MATCHING_RESULTS_FOUND' | translate"
                                 [placeholderLabel]="'GENERAL.FILTERS.SEARCH' | translate">
          </ngx-mat-select-search>
        </mat-option>
        <mat-option *ngFor="let status of filteredStatusList" [value]="status">
          {{status}}
        </mat-option>
        <button mat-button color="primary" class="clear-selection-btn"
                [disabled]="!statusFormControl.value?.length"
                (click)="statusFormControl.reset([])">
          {{'GENERAL.FILTERS.ACTIONS.CLEAR_SELECTION' | translate}}
        </button>
      </ng-container>
      <ng-template #loadingStatus>
        <mat-option disabled>
          <div class="filters-loading-container">
            <span>{{'GENERAL.FILTERS.LOADING' | translate}}</span>
            <mat-spinner diameter="20"></mat-spinner>
          </div>
        </mat-option>
      </ng-template>
    </mat-select>
  </mat-form-field>

  <!-- status description -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-status-description-filter"
                  (click)="loadStatusDescription()">
    <mat-label>{{'STATEMENTS.FIELDS.STATUS_DESCRIPTION' | translate}}</mat-label>
    <mat-select [formControl]="statusDescriptionFormControl" multiple>
      <ng-container *ngIf="filteredStatusDescriptionList != null; else loadingStatusDescription">
        <mat-option>
          <ngx-mat-select-search [formControl]="statusDescriptionSearchFormControl"
                                 [noEntriesFoundLabel]="'GENERAL.FILTERS.NO_MATCHING_RESULTS_FOUND' | translate"
                                 [placeholderLabel]="'GENERAL.FILTERS.SEARCH' | translate">
          </ngx-mat-select-search>
        </mat-option>
        <mat-option *ngFor="let statusDescription of filteredStatusDescriptionList" [value]="statusDescription">
          {{statusDescription}}
        </mat-option>
        <button mat-button color="primary" class="clear-selection-btn"
                [disabled]="!statusDescriptionFormControl.value?.length"
                (click)="statusDescriptionFormControl.reset([])">
          {{'GENERAL.FILTERS.ACTIONS.CLEAR_SELECTION' | translate}}
        </button>
      </ng-container>
      <ng-template #loadingStatusDescription>
        <mat-option disabled>
          <div class="filters-loading-container">
            <span>{{'GENERAL.FILTERS.LOADING' | translate}}</span>
            <mat-spinner diameter="20"></mat-spinner>
          </div>
        </mat-option>
      </ng-template>
    </mat-select>
  </mat-form-field>

  <!-- created at -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-created-at-filter">
    <mat-label>{{'STATEMENTS.FIELDS.CREATED_AT' | translate}}</mat-label>
    <mat-date-range-input [rangePicker]="picker3">
      <input matStartDate [formControl]="createdAtStartFormControl" placeholder="Start date">
      <input matEndDate [formControl]="createdAtEndFormControl" placeholder="End date">
    </mat-date-range-input>
    <mat-datepicker-toggle matIconSuffix [for]="picker3"></mat-datepicker-toggle>
    <mat-date-range-picker #picker3></mat-date-range-picker>
  </mat-form-field>


</div>

<div class="filters-actions">
  <button class="flat-white-btn filters-cancel-btn" [disabled]="noFiltersSelected()" mat-flat-button fxShow
          (click)="clearFilters()" [fxShow.xs]="false" [fxShow.sm]="false">
    {{'GENERAL.FILTERS.ACTIONS.CLEAR' | translate}}
  </button>
  <button class="raised-primary-btn filters-apply-btn" [disabled]="noFiltersSelected()" mat-raised-button
          color="primary"
          (click)="submit()">
    {{'GENERAL.FILTERS.ACTIONS.APPLY' | translate}}
  </button>
</div>
