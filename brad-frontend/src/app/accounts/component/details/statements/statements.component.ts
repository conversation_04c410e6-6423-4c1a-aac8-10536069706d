import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  ViewChild
} from '@angular/core';
import {Statement} from "../../../../entities/statement/statement";
import {Observable, Subject} from "rxjs";
import {StatementFilters} from "../../../../entities/statement/statement-filters";
import {paginationConsts} from "../../../../shared/constants/core.constants";
import {
  CsFinAddRemoveColumns,
  CsFinConfirmationDialogComponent,
  CsFinPagination,
  CsFinSortingDataAccessorHelperService
} from "@jumia-cs-fin/common";
import {StatementFacade} from "../../../facade/statement.facade";
import {finalize, takeUntil} from "rxjs/operators";
import {PageResponse} from "../../../../entities/page-response";
import {authParams, bradAuthTarget} from "../../../../auth/constants/auth.constants";
import {MatTableDataSource} from "@angular/material/table";
import {MatSort, SortDirection} from "@angular/material/sort";
import * as _ from "lodash";
import {SortFilters} from "../../../../entities/SortFilters";
import {MatPaginator} from "@angular/material/paginator";
import {MenusConsts} from "../../../../shared/constants/menus.constants";
import {CdkOverlayOrigin} from "@angular/cdk/overlay";
import {Account} from "../../../../entities/account/account";
import {AccountFacade} from 'src/app/accounts/facade/account.facade';
import {HttpErrorResponse} from "@angular/common/http";
import {NotificationService} from "../../../../api/service/notification.service";
import {StatementAdditionalInfoFacade} from "../../../facade/statement-additional-info.facade";
import {
  AddRemoveColumnsAltFacade
} from "../../../../shared/component/add-remove-columns-alt/add-remove-columns-alt.facade";
import {LastUsedColumnsAlt} from "../../../../shared/util/last-used-columns-alt/last-used-columns-alt";
import {Contact} from "../../../../entities/contact/contact";
import {MatDialog} from "@angular/material/dialog";
import {DatetimeService} from "../../../../api/service/datetime.service";

@Component({
  selector: 'brad-account-details-statements',
  templateUrl: './statements.component.html',
  styleUrls: ['./statements.component.scss','../../../../../assets/brad-custom.scss']
})
export class StatementsComponent implements OnInit, OnDestroy, AfterViewInit {

  @ViewChild(MatPaginator, {static: true}) paginator!: MatPaginator;
  @ViewChild(MatSort, {static: true}) sort!: MatSort;

  @Input() accountID!: number;
  @Input() account!: Account;
  @Input() accountHasError!: boolean;
  @Input() chosenCurrency!: string;
  @Input() lastImportedStatement!: Statement | null;
  @Input() triggerOrigin!: CdkOverlayOrigin;

  @Output() selectedStatement = new EventEmitter<Statement | null>();

  readonly MENU = MenusConsts.statements;

  isOverlayOpen$!: Observable<boolean>;

  protected readonly bradAuthTarget = bradAuthTarget;
  auth = authParams;
  displayedColumns: string[] = [];
  dataSource: MatTableDataSource<Statement> = new MatTableDataSource<Statement>([]);

  isLoading = false;
  private _onDestroy: Subject<void> = new Subject<void>();
  private filters: StatementFilters = {
    page: 1,
    size: paginationConsts.defaultPageSize
  };
  pagination: CsFinPagination = {
    pageSizeOptions: paginationConsts.pageSizeOptions,
    pageSize: paginationConsts.defaultPageSize,
    pageIndex: 0,
    totalItems: 0
  };

  constructor(
    public ref: ChangeDetectorRef,
    private notificationService: NotificationService,
    private statementFacade: StatementFacade,
    private statementAdditionalInfoFacade: StatementAdditionalInfoFacade,
    private addRemoveColumnsFacade: AddRemoveColumnsAltFacade,
    private accountFacade: AccountFacade,
    private dialog: MatDialog,
  ) {
    LastUsedColumnsAlt.getInstance().initColumns(this.MENU, this.statementFacade.getColumnDetails());
  }

  ngOnInit(): void {
    this.isLoading = true;
    this.subscribeDisplayedColumnsChange();
    this.subscribeFiltersChange();
    this.subscribeSelectedAccountChange();
  }

  ngAfterViewInit() {
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }

  ngOnDestroy(): void {
    this.statementAdditionalInfoFacade.changeStatementFilters({});
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  private subscribeSelectedAccountChange(): void {
    this.accountFacade.selectedAccountChangeBehaviorSubject
      .pipe(takeUntil(this._onDestroy))
      .subscribe((accountID: number) => {
        if (accountID > 0) {
          setTimeout(() => {
            this.accountID = accountID;
          }, 0);
        }
      });
  }


  private subscribeFiltersChange(): void {
    this.statementFacade.filters$
      .pipe(takeUntil(this._onDestroy))
      .subscribe(async (filters: StatementFilters) => {
        if (filters) {
          this.filters = filters;
          this.loadStatements();
        }
      });
  }

  private subscribeDisplayedColumnsChange(): void {
    this.addRemoveColumnsFacade.fetchDisplayedColumns
      .pipe(takeUntil(this._onDestroy))
      .subscribe((columns: CsFinAddRemoveColumns) => {
        setTimeout(() => {
            this.displayedColumns = columns.displayedColumns ? columns.displayedColumns : [];
          }
          , 0);
      });
  }

  loadStatements(): void {
    this.filters.partitionKey = String(this.account.id);
    this.statementFacade.getAll(this.filters)
      .pipe(takeUntil(this._onDestroy))
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: (result: PageResponse<Statement>) => {
          this.dataSource = new MatTableDataSource<Statement>(result.results);
          this.dataSource.sortingDataAccessor = CsFinSortingDataAccessorHelperService.objectSortingDataAccessor;
          this.pagination.totalItems = result.total;
          this.pagination.pageSize = result.size;
          this.pagination.pageIndex = result.page - 1;
          this.setSort();
        },
        error: (error: HttpErrorResponse) => {
          this.notificationService.errorWithResponse(error);
        }
      });
  }

  onPageChange(event: any) {
    this.filters.page = event.pageIndex + 1;
    this.filters.size = event.pageSize;
    this.statementAdditionalInfoFacade.changeStatementFilters(this.filters);
  }

  private setSort(): void {
    if (!this.sort) {
      return;
    }
    const previousSort = {
      active: this.sort.active,
      direction: this.sort.direction
    };
    if (this.filters.orderField) {
      this.sort.active = this.decodeSortField(this.filters.orderField);
    }
    if (this.filters.orderDirection) {
      this.sort.direction = this.filters.orderDirection.toLowerCase() as SortDirection;
    }

    const sort = {
      active: this.sort.active,
      direction: this.sort.direction
    };
    if (!_.isEqual(previousSort, sort)) {
      this.sort.sortChange.emit(this.sort);
    }
  }

  onSortChange(event: any): void {
    const sortFiltersBefore = {
      orderDirection: this.filters.orderDirection,
      orderField: this.filters.orderField
    };

    this.filters.orderDirection = event.direction?.toUpperCase();
    this.filters.orderField = this.encodeSortField(event.active);

    if (!_.isEqual(sortFiltersBefore, this.filters as SortFilters)) {
      this.statementAdditionalInfoFacade.changeStatementFilters(this.filters);
    }
  }

  private encodeSortField(field: string): string {
    switch (field) {
      case 'id':
        return 'ID';
      case 'accountID':
        return 'ACCOUNT_ID';
      case 'currency':
        return 'CURRENCY';
      case 'statementId':
        return 'STATEMENT_ID';
      case 'previousStatementId':
        return 'PREVIOUS_STATEMENT_ID';
      case 'initialDate':
        return 'INITIAL_DATE';
      case 'finalDate':
        return 'FINAL_DATE';
      case 'initialDirection':
        return 'INITIAL_DIRECTION';
      case 'finalDirection':
        return 'FINAL_DIRECTION';
      case 'initialAmount':
        return 'INITIAL_AMOUNT';
      case 'finalAmount':
        return 'FINAL_AMOUNT';
      case 'status':
        return 'STATUS';
      case 'statusDescription':
        return 'STATUS_DESCRIPTION';
      case 'createdAt':
        return 'CREATED_AT';
      case 'createdBy':
        return 'CREATED_BY';
      case 'updatedAt':
        return 'UPDATED_AT';
      case 'updatedBy':
        return 'UPDATED_BY';
      default:
        return field.toUpperCase();
    }
  }

  private decodeSortField(field: string): string {
    switch (field) {
      case 'ID':
        return 'id';
      case 'ACCOUNT_ID':
        return 'accountID';
      case 'CURRENCY':
        return 'currency';
      case 'STATEMENT_ID':
        return 'statementId';
      case 'PREVIOUS_STATEMENT_ID':
        return 'previousStatementId';
      case 'INITIAL_DATE':
        return 'initialDate';
      case 'FINAL_DATE':
        return 'finalDate';
      case 'INITIAL_DIRECTION':
        return 'initialDirection';
      case 'FINAL_DIRECTION':
        return 'finalDirection';
      case 'INITIAL_AMOUNT':
        return 'initialAmount';
      case 'FINAL_AMOUNT':
        return 'finalAmount';
      case 'STATUS':
        return 'status';
      case 'STATUS_DESCRIPTION':
        return 'statusDescription';
      case 'CREATED_AT':
        return 'createdAt';
      case 'CREATED_BY':
        return 'createdBy';
      case 'UPDATED_AT':
        return 'updatedAt';
      case 'UPDATED_BY':
        return 'updatedBy';
      default:
        return field.toLowerCase();
    }
  }

  onSelectStatement(row: Statement) {
    this.selectedStatement.emit(row);
  }

  getDisplayedColumns(): string[] {
    return this.displayedColumns;
  }

  shouldShowDeleteIcon(statement: Statement, index: number): boolean {
    const { pageSize, pageIndex } = this.pagination;

    const startIndex = pageIndex * pageSize;
    const endIndex = startIndex + pageSize - 1;

    if (index < startIndex || index > endIndex) {
      return false;
    }

    if (statement.status === 'IMPORTED') {
      const firstImportedIndex = this.dataSource.data.findIndex(
        s => s.status === 'IMPORTED'
      );
      return firstImportedIndex === index;
    }
    return false;
  }


  onDeleteClick(statement: Statement, event: MouseEvent) {
    event.stopPropagation();
    const dialogRef = this.dialog.open(CsFinConfirmationDialogComponent, {
      autoFocus: false,
      width: '400px',
      height: '190px',
      panelClass: 'overflow-hidden-dialog',
      data: {
        titleKey: 'CONFIRMATION_DIALOG.MESSAGES.ABOUT_TO_DELETE_STATEMENT',
        descriptionKey: 'CONFIRMATION_DIALOG.ACTIONS.WOULD_YOU_LIKE_TO_PROCEED',
        cancelActionKey: 'CONFIRMATION_DIALOG.BUTTONS.CANCEL',
        confirmActionKey: 'CONFIRMATION_DIALOG.BUTTONS.DISCARD'
      }
    });

    dialogRef.afterClosed().subscribe(response => {
      if (response && statement.id) {
        this.isLoading = true;
        this.statementFacade.discardLastImportedStatement(statement.id)
          .pipe(
            finalize(() => this.isLoading = false)
          )
          .subscribe({
            next: () => {
              this.loadStatements();
              this.notificationService.successTranslated('ACCOUNTS.NOTIFICATIONS.DISCARD_STATEMENT_SUCCESS',
                {statement: statement.id});
            },
            error: (error:HttpErrorResponse) => {
              this.notificationService.errorWithResponse(error);
            }
          })
      }
    });
  }

  onRetry(statement: Statement, event: MouseEvent) {
    event.stopPropagation();
    this.isLoading = true;
    this.statementFacade.retryStatement(statement.id)
    .subscribe({
      next: () => {
        this.notificationService.successTranslated('ACCOUNTS.NOTIFICATIONS.RETRY_STATEMENT_SUCCESS', {statement: statement.id});
        setTimeout(() => {
          this.loadStatements();
        }, 60000);
      },
      error: (error: HttpErrorResponse) => {
        this.notificationService.errorWithResponse(error);
        this.isLoading = false;
      }
    })
  }

  isLasUpdatedInTheLastHour(statement: Statement): boolean {
    if (!statement || !statement.updatedAt) {
      return false;
    }
    const updatedAt = DatetimeService.convertDateToUtc(statement.updatedAt);
    const oneHourAgo = DatetimeService.getHoursAgo(1);
    return updatedAt >= oneHourAgo;
  }

  protected readonly DatetimeService = DatetimeService;
}
