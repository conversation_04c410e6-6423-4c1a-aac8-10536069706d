import {Component, Input, <PERSON><PERSON><PERSON><PERSON>, OnDestroy, OnInit} from '@angular/core';
import {PageResponse} from "../../../../entities/page-response";
import {Account} from "../../../../entities/account/account";
import {AuditResponse} from "../../../../entities/audit/audit-response";
import {AuditTimelineItem} from "../../../../entities/audit/audit-timeline-item";
import {takeUntil} from "rxjs/operators";
import {Subject} from "rxjs";
import {AuditFilters} from "../../../../entities/audit/audit-filters";
import {paginationConsts} from "../../../../shared/constants/core.constants";
import {CsFinPagination} from "@jumia-cs-fin/common";
import {DecodedAuditEntity} from "../../../../entities/audit/decoded-audit-entity";
import {AccountFacade} from "../../../facade/account.facade";
import {Country} from "../../../../entities/account/country";
import {CountryApiService} from "../../../../api/service/country-api.service";
import {Currency} from "../../../../entities/currency/currency";
import {CurrencyApiService} from "../../../../api/service/currency-api.service";

@Component({
  selector: 'brad-account-details-history',
  templateUrl: './accounts-details-history.component.html',
  styleUrls: ['./accounts-details-history.component.scss']
})
export class AccountsDetailsHistoryComponent implements OnInit, OnChanges, OnDestroy {

  @Input() accountID!: any;
  isLoading = false;
  accountAudit!: PageResponse<AuditResponse<Account>>;
  accountAuditItems!: AuditTimelineItem[];
  private _onDestroy: Subject<void> = new Subject<void>();
  private filters: AuditFilters = {
    page: 1,
    size: paginationConsts.auditTimelineDefaultPageSize
  };

  countryList: Country[] = [];
  currencyList: Currency[] = [];

  pagination: CsFinPagination = {
    pageSizeOptions: paginationConsts.auditTimelinePageSizeOptions,
    pageSize: paginationConsts.auditTimelineDefaultPageSize,
    pageIndex: 0,
    totalItems: 0
  };

  constructor(private accountFacade: AccountFacade,
              private countryApiService: CountryApiService,
              private currencyApiService: CurrencyApiService) {}

  ngOnInit(): void {
    this.loadCountries();
    this.loadCurrencies();
    this.loadAccountAudit();
  }

  ngOnChanges(): void {
    this.loadAccountAudit();
  }

  ngOnDestroy(): void {
    this._onDestroy.next();
    this._onDestroy.complete();
  }


  loadCountries(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      this.countryApiService.getAll()
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (countries: Country[]) => {
            this.countryList = countries;
            resolve();
          }, error: (error) => reject(error)
        });
    });
  }

  loadCurrencies(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      this.currencyApiService.getAll()
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (currencies: Currency[]) => {
            this.currencyList = currencies;
            resolve();
          }, error: (error) => reject(error)
        });
    });
  }

  private loadAccountAudit() {
    this.accountFacade.getHistory(this.accountID, this.filters)
      .pipe(takeUntil(this._onDestroy))
      .subscribe((response: PageResponse<AuditResponse<Account>>) => {
        this.accountAudit = response;
        this.pagination.totalItems = response.total;
        this.pagination.pageSize = response.size;
        this.accountAuditItems = this.buildAccountAuditTimelineItems();
      });
  }

  private buildAccountAuditTimelineItems(): AuditTimelineItem[] {
    if (!this.accountAudit) {
      return [];
    }
    return this.accountAudit.results.map((auditItem: AuditResponse<Account>) => {
        auditItem.revision.datetime = auditItem.entity.updatedAt == null ? "null" :
          new Date(auditItem.entity.updatedAt).toISOString().slice(0, 19).replace('T', ' ');
      return {
        mainText: auditItem.entity.accountNumber ?? '',
        auditedEntity: auditItem,
        decodedEntity: this.buildDecodedEntity(auditItem)
      };
    });
  }

  private buildDecodedEntity(auditItem: AuditResponse<Account>): DecodedAuditEntity[] {

    let createdAt = auditItem.entity.createdAt == null ? "null" :
      new Date(auditItem.entity.createdAt).toISOString().slice(0, 19).replace('T', ' ');
    let updatedAt = auditItem.entity.updatedAt == null ? "null" :
      new Date(auditItem.entity.updatedAt).toISOString().slice(0, 19).replace('T', ' ');

    let country = this.countryList.find((country: Country) => country.id === Number(auditItem.entity.country?.id));
    let currency = this.currencyList.find((currency: Currency) => currency.id === Number(auditItem.entity.currency?.id));

    if(!country || !currency){
      return [];
    }

    const commonFields: DecodedAuditEntity[] = [
      {key: 'ACCOUNTS.FIELDS.ID', value: auditItem.entity.id},
      {key: 'ACCOUNTS.FIELDS.COMPANY_ID', value: auditItem.entity.companyID},
      {key: 'ACCOUNTS.FIELDS.COUNTRY', value: country!.name},
      {key: 'ACCOUNTS.FIELDS.NAV_REFERENCE', value: auditItem.entity.navReference},
      {key: 'ACCOUNTS.FIELDS.ACCOUNT_NUMBER', value: auditItem.entity.accountNumber},
      {key: 'ACCOUNTS.FIELDS.ACCOUNT_NAME', value: auditItem.entity.accountName},
      {key: 'ACCOUNTS.FIELDS.CURRENCY', value: currency!.code},
      {key: 'ACCOUNTS.FIELDS.TYPE', value: auditItem.entity.type},
      {key: 'ACCOUNTS.FIELDS.STATUS', value: auditItem.entity.status},
      {key: 'ACCOUNTS.FIELDS.STATEMENT_SOURCE', value: auditItem.entity.statementSource},
      {key: 'ACCOUNTS.FIELDS.STATEMENT_PERIODICITY', value: auditItem.entity.statementPeriodicity},
      {key: 'GENERAL.FIELDS.CREATED_AT', value: createdAt, isDate: true},
      {key: 'GENERAL.FIELDS.CREATED_BY', value: auditItem.entity.createdBy},
      {key: 'GENERAL.FIELDS.UPDATED_AT', value: updatedAt, isDate: true},
      {key: 'GENERAL.FIELDS.UPDATED_BY', value: auditItem.entity.updatedBy}
    ];

    const typeSpecificFields = this.getTypeSpecificFields(auditItem.entity);

    return [...commonFields, ...typeSpecificFields];
  }

  private getTypeSpecificFields(entity: Account): DecodedAuditEntity[] {
    const fields: DecodedAuditEntity[] = [];

    switch (entity.type) {
      case 'BANK_ACCOUNT':
        if (entity.beneficiaryName) {
          fields.push({key: 'ACCOUNTS.FIELDS.BENEFICIARY_NAME', value: entity.beneficiaryName});
        }
        if (entity.beneficiaryAddress) {
          fields.push({key: 'ACCOUNTS.FIELDS.BENEFICIARY_ADDRESS', value: entity.beneficiaryAddress});
        }
        if (entity.iban) {
          fields.push({key: 'ACCOUNTS.FIELDS.IBAN', value: entity.iban});
        }
        if (entity.swiftCode) {
          fields.push({key: 'ACCOUNTS.FIELDS.SWIFT_CODE', value: entity.swiftCode});
        }
        if (entity.accountRoutingCode) {
          fields.push({key: 'ACCOUNTS.FIELDS.ACCOUNT_ROUTING_CODE', value: entity.accountRoutingCode});
        }
        if (entity.sortCode) {
          fields.push({key: 'ACCOUNTS.FIELDS.SORT_CODE', value: entity.sortCode});
        }
        if (entity.branchCode) {
          fields.push({key: 'ACCOUNTS.FIELDS.BRANCH_CODE', value: entity.branchCode});
        }
        if (entity.rib) {
          fields.push({key: 'ACCOUNTS.FIELDS.RIB', value: entity.rib});
        }
        break;

      case 'PSP':
        if (entity.partner) {
          fields.push({key: 'ACCOUNTS.FIELDS.PARTNER', value: entity.partner});
        }
        if (entity.phoneNumber) {
          fields.push({key: 'ACCOUNTS.FIELDS.PHONE_NUMBER', value: entity.phoneNumber});
        }
        break;

      case 'MOBILE_MONEY':
        if (entity.partner) {
          fields.push({key: 'ACCOUNTS.FIELDS.PARTNER', value: entity.partner});
        }
        if (entity.phoneNumber) {
          fields.push({key: 'ACCOUNTS.FIELDS.PHONE_NUMBER', value: entity.phoneNumber});
        }
        break;

      case 'WALLET':
        if (entity.partner) {
          fields.push({key: 'ACCOUNTS.FIELDS.PARTNER', value: entity.partner});
        }
        break;

      case 'INVESTMENTS':
        if (entity.subType) {
          fields.push({key: 'ACCOUNTS.FIELDS.SUB_TYPE', value: entity.subType});
        }

        const investmentFields = this.getInvestmentSpecificFields(entity);
        fields.push(...investmentFields);
        break;
    }

    return fields;
  }

  private getInvestmentSpecificFields(entity: Account): DecodedAuditEntity[] {
    const fields: DecodedAuditEntity[] = [];

    if (!entity.subType) {
      return fields;
    }

    const bondsSubTypes = ['BONDS'];
    const termDepositSubTypes = ['TERM_DEPOSITS', 'TLF', 'BANK_GUARANTEES'];
    const allInvestmentSubTypes = ['TERM_DEPOSITS', 'TLF', 'BANK_GUARANTEES', 'BONDS'];

    if (bondsSubTypes.includes(entity.subType)) {
      if (entity.isin) {
        fields.push({key: 'ACCOUNTS.FIELDS.ISIN', value: entity.isin});
      }
      if (entity.nominalAmount) {
        fields.push({key: 'ACCOUNTS.FIELDS.NOMINAL_AMOUNT', value: entity.nominalAmount});
      }
      if (entity.couponPaymentPeriodicity) {
        fields.push({key: 'ACCOUNTS.FIELDS.COUPON_PAYMENT_PERIODICITY', value: entity.couponPaymentPeriodicity});
      }
      if (entity.couponRate) {
        fields.push({key: 'ACCOUNTS.FIELDS.COUPON_RATE', value: entity.couponRate});
      }
    }

    if (termDepositSubTypes.includes(entity.subType)) {
      if (entity.contractId) {
        fields.push({key: 'ACCOUNTS.FIELDS.CONTRACT_ID', value: entity.contractId});
      }
      if (entity.interest) {
        fields.push({key: 'ACCOUNTS.FIELDS.INTEREST', value: entity.interest});
      }
    }

    if (allInvestmentSubTypes.includes(entity.subType)) {
      if (entity.amountDeposited) {
        fields.push({key: 'ACCOUNTS.FIELDS.AMOUNT_DEPOSITED', value: entity.amountDeposited});
      }
      if (entity.maturityDate) {
        const formattedMaturityDate = new Date(entity.maturityDate).toISOString().slice(0, 19).replace('T', ' ');
        fields.push({key: 'ACCOUNTS.FIELDS.MATURITY_DATE', value: formattedMaturityDate, isDate: true});
      }
    }

    return fields;
  }

  onPageChange(event: any): void {
    if (event) {
      this.filters.page = event.pageIndex + 1;
      this.filters.size = event.pageSize;
    }
    this.loadAccountAudit();
  }



}
