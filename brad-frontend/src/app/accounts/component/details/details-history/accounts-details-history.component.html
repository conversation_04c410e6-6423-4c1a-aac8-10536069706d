<div *ngIf="isLoading" class="loading">
  <mat-progress-spinner mode="indeterminate" diameter="75" strokeWidth="5"></mat-progress-spinner>
  <span class="label">{{'GENERAL.DETAILS.LOADING' | translate}}</span>
</div>
<div *ngIf="!isLoading && !accountAudit" class="detail-failed">
  <mat-icon>report_problem</mat-icon>
  <span class="label">{{'ACCOUNTS.DETAILS.ERRORS.UNABLE_TO_FIND_HISTORY' | translate: {id: accountID} }}</span>
</div>
<main *ngIf="!isLoading && accountAudit">
  <brad-item-history [items]="accountAuditItems"
                     [pagination]="pagination"
                     (onPageChange)="onPageChange($event)">
  </brad-item-history>
</main>
