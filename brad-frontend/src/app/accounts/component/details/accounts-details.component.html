<div class="main">
  <accounts-detail-header [detailedAccount]="detailedAccount" [currentHeader]="headerType"></accounts-detail-header>
  <main class="container details"  *ngIf="detailedAccount?.id">
    <section class="filters" *ngIf="headerType==HeaderType.TRANSACTION">
      <cs-fin-active-filters [chipsMap]="transactionActiveFilterChips"
                             (onRemove)="onTransactionActiveFilterRemoveClick($event)">
      </cs-fin-active-filters>
      <span fxFlex></span>
        <span class="actions">
            <ng-container *ngIf="activeTab=='Users'" >
            <mat-divider [vertical]="true"  [inset]="true" style="height:5px; margin:10px;"></mat-divider>
            <mat-slide-toggle [(ngModel)]="showAllUsers" labelPosition="before">
              <span style="color:gray; font-style:italic;">{{'Include inactive users'}}</span>
            </mat-slide-toggle>
            </ng-container>
          <button mat-raised-button color="primary" class="reconcile-button" (click)="goToReconciliation()">
            {{'ACCOUNTS.BUTTONS.LABELS.RECONCILE' | translate}}
          </button>
          <button mat-stroked-button color="primary" (click)="onDownloadClick(HeaderType.TRANSACTION)" class="export-btn"
                  csFinHasPermissionOnAnyTarget
                  [authPermissions]="auth.permissions.BRAD_EXPORT_STATEMENTS"
                  [authTarget]="bradAuthTarget"
                  [authAction]="auth.actions.DISABLE"
                  matTooltip="{{ 'GENERAL.BUTTONS.LABELS.DOWNLOAD' | translate }}">
            <mat-icon>cloud_download</mat-icon>
          </button>
          <button mat-stroked-button class="change-columns-btn" color="primary" fxShow [fxHide.xs]="true" [fxHide.sm]="true"
                  cdkOverlayOrigin #triggerTransaction="cdkOverlayOrigin"
                  (click)="triggerTransactionOverlay(triggerTransaction)">
            <mat-icon>table_rows</mat-icon>
          </button>
        </span>
    </section>
    <section class="filters" *ngIf="headerType==HeaderType.STATEMENT">
      <cs-fin-active-filters [chipsMap]="statementActiveFilterChips"
                             (onRemove)="onStatementActiveFilterRemoveClick($event)">
      </cs-fin-active-filters>
      <span fxFlex></span>
        <span class="actions">
          <button mat-raised-button color="primary" class="reconcile-button" (click)="goToReconciliation()">
            {{'ACCOUNTS.BUTTONS.LABELS.RECONCILE' | translate}}
          </button>
          <button mat-stroked-button color="primary" (click)="onUploadClick()" class="export-btn"
                  csFinHasPermissionOnAnyTarget
                  [authPermissions]="auth.permissions.BRAD_UPLOAD_STATEMENTS"
                  [authTarget]="bradAuthTarget"
                  [authAction]="auth.actions.DISABLE"
                  matTooltip="{{ 'GENERAL.BUTTONS.LABELS.UPLOAD' | translate }}">
            <mat-icon>cloud_upload</mat-icon>
          </button>
          <button mat-stroked-button color="primary" (click)="onDownloadClick(HeaderType.STATEMENT)" class="export-btn"
                  csFinHasPermissionOnAnyTarget
                  [authPermissions]="auth.permissions.BRAD_EXPORT_STATEMENTS"
                  [authTarget]="bradAuthTarget"
                  [authAction]="auth.actions.DISABLE"
                  matTooltip="{{ 'GENERAL.BUTTONS.LABELS.DOWNLOAD' | translate }}">
            <mat-icon>cloud_download</mat-icon>
          </button>
          <button mat-stroked-button class="change-columns-btn" color="primary" fxShow [fxHide.xs]="true" [fxHide.sm]="true"
                  cdkOverlayOrigin #triggerStatement="cdkOverlayOrigin"
                  (click)="triggerStatementOverlay(triggerStatement)">
            <mat-icon>table_rows</mat-icon>
          </button>
        </span>
    </section>

    <mat-card class="details-section">
      <mat-tab-group class="details-tab-group" fitInkBarToContent="false" dynamicHeight (selectedTabChange)="tabChanged($event)">

        <!-- account statements -->
        <mat-tab [label]="'ACCOUNTS.DETAILS.TABS.LABELS.ACCOUNT_DETAILS' | translate">
          <brad-account-statements-information [detailedAccount]="detailedAccount"
                                                    [isLoading]="isLoading"></brad-account-statements-information>
        </mat-tab>

        <!-- account history -->
        <mat-tab [label]="'ACCOUNTS.DETAILS.TABS.LABELS.ACCOUNT_HISTORY' | translate">
          <brad-account-details-history [accountID]="accountID"></brad-account-details-history>
        </mat-tab>

        <!-- Contacts -->
        <mat-tab [label]="'ACCOUNTS.DETAILS.TABS.LABELS.ACCOUNT_CONTACTS' | translate">
          <brad-account-details-contact [accountID]="accountID"></brad-account-details-contact>
        </mat-tab>

        <!-- Users -->
        <mat-tab [label]="'ACCOUNTS.DETAILS.TABS.LABELS.ACCOUNT_USERS' | translate">
          <brad-account-details-user [accountID]="accountID" [showAllUsers]="showAllUsers"></brad-account-details-user>
        </mat-tab>

        <!-- Documents -->
        <mat-tab [label]="'ACCOUNTS.DETAILS.TABS.LABELS.ACCOUNT_DOCUMENTS' | translate">
          <brad-account-details-document [accountID]="accountID"></brad-account-details-document>
        </mat-tab>
      </mat-tab-group>
    </mat-card>
    <mat-card class="transaction-statement-section">
      <mat-tab-group class="details-tab-group" dynamicHeight [selectedIndex]="selectedTabIndex" (selectedTabChange)="onRedirectTab($event.index)">
          <mat-tab>
            <ng-template mat-tab-label class="temp">
              <span>{{ 'ACCOUNTS.DETAILS.TABS.LABELS.ACCOUNT_GRID_TRANSACTION' | translate }}</span>
            </ng-template>
            <div *ngIf="currentTransactionFilters">
              <brad-account-details-transactions class="list-components"
                                                      [detailedAccount]="detailedAccount"
                                                      [lastImportedStatement]="lastImportedStatement"
                                                      [triggerOrigin]="transactionTriggerOrigin"
              ></brad-account-details-transactions>
            </div>
          </mat-tab>

          <mat-tab>
            <ng-template mat-tab-label>
              <div class="statement-button">
                <span>{{ 'ACCOUNTS.DETAILS.TABS.LABELS.ACCOUNT_GRID_STATEMENT' | translate }}</span>
                <mat-icon class="warning-icon" *ngIf="detailedAccount?.hasError" (click)="onErrorClick()">warning</mat-icon>
              </div>
            </ng-template>
            <div *ngIf="currentStatementFilters">
              <brad-account-details-statements class="list-components"
                                                    (selectedStatement)="reactStatementSelect($event)"
                                                    [lastImportedStatement]="lastImportedStatement"
                                                    [accountID]="detailedAccount.id"
                                                    [account]="detailedAccount"
                                                    [accountHasError]="detailedAccount?.hasError"
                                                    [triggerOrigin]="statementTriggerOrigin"
              ></brad-account-details-statements>
            </div>
          </mat-tab>
        </mat-tab-group>
    </mat-card>
  </main>
</div>
