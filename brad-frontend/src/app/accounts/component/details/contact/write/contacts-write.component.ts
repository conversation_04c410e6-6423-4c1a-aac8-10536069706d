import {Component, Inject, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog'
import {FormControl, FormGroup, Validators} from '@angular/forms';
import {finalize, takeUntil} from 'rxjs/operators';
import {Subject} from "rxjs";
import {CsFinWritePopupType} from "@jumia-cs-fin/common";
import {ContactFacade} from "../../../../facade/contact.facade";
import {ContactTypesApiService} from "../../../../../api/service/contacts-type-api.service";
import {Contact} from "../../../../../entities/contact/contact";
import {NotificationService} from "../../../../../api/service/notification.service";
import {HttpErrorResponse} from "@angular/common/http";

@Component({
  selector: 'brad-contacts-write',
  templateUrl: './contacts-write.component.html',
  styleUrls: ['./contacts-write.component.scss']
})
export class ContactsWriteComponent implements OnInit {

  contactForm!: FormGroup;
  contactTypeFormControl!: FormControl;
  otherContactTypeFormControl!: FormControl;
  nameFormControl!: FormControl;
  emailFormControl!: FormControl;
  mobilePhoneNumberFormControl!: FormControl;
  accountIDFormControl!: FormControl;


  contactTypeSearchFormControl = new FormControl();
  contactTypeList:String[] = [];
  filteredContactTypeList:String[] = [];

  isContactTypeLoading = true;
  isSavingData = false;
  private _onDestroy:Subject<void> = new Subject<void>();
  validEmailRegex = '[a-z0-9!#$%&\'*+/=?^_`{|}~-]+(?:\\.[a-z0-9!#$%&\'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?'
  validMobilePhoneNumberRegex = '^\\s*(?:\\+?(\\d{1,3}))?[-. (]*(\\d{3})[-. )]*(\\d{3})[-. ]*(\\d{4})(?: *x(\\d+))?\\s*$';

  constructor(public dialogRef: MatDialogRef<ContactsWriteComponent>,
              @Inject(MAT_DIALOG_DATA) public data: {
                type: CsFinWritePopupType,
                contact: Contact | null,
                accountID: number
              },
              private notificationService: NotificationService,
              private contactFacade: ContactFacade,
              private contactTypesApiService: ContactTypesApiService) { }

  ngOnInit(): void {
    this.contactForm = new FormGroup({});
    this.initFiltersSearch();
    this.initFormControls();
    this.setFormControlsToForm();
    this.loadContactTypes();
    this.accountIDFormControl.setValue(this.data.accountID);
    this.accountIDFormControl.disable();
    if (this.data.contact?.id != null) {
      this.loadContactAndSetFormGroup(this.data.contact.id);
    }
  }

  ngOnDestroy(): void {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  private initFiltersSearch(): void {
    this.initContactTypeSearch();
  }

  private initContactTypeSearch(): void {
    this.contactTypeSearchFormControl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .pipe(
        finalize(() => this.isContactTypeLoading = false)
      )
      .subscribe((value: string) => {

        const searchTerm = value.trim();
        if (searchTerm) {
          this.filteredContactTypeList = this.contactTypeList.filter((contactType) => {
            return contactType.trim().toLowerCase().includes(searchTerm.toLowerCase());
          })
        } else {
          this.filteredContactTypeList = this.contactTypeList;
        }
      });
  }

  initFormControls(): void {
    this.contactTypeFormControl = new FormControl('', [Validators.required]);
    this.otherContactTypeFormControl = new FormControl('');
    this.nameFormControl = new FormControl('', [Validators.required]);
    this.emailFormControl = new FormControl('', [Validators.required, Validators.pattern(this.validEmailRegex)]);
    this.mobilePhoneNumberFormControl = new FormControl('', [Validators.required, Validators.pattern(this.validMobilePhoneNumberRegex)]);
    this.accountIDFormControl = new FormControl('', [Validators.required]);

  }

  setFormControlsToForm(): void {
    this.contactForm.addControl('contactType', this.contactTypeFormControl);
    this.contactForm.addControl('otherContactType', this.otherContactTypeFormControl);
    this.contactForm.addControl('name', this.nameFormControl);
    this.contactForm.addControl('email', this.emailFormControl);
    this.contactForm.addControl('mobilePhoneNumber', this.mobilePhoneNumberFormControl);
    this.contactForm.addControl('accountID', this.accountIDFormControl);
  }

  loadContactAndSetFormGroup(id: number): void {
    this.contactFacade.getById(id)
    .subscribe({
      next: (contact) => {
        if (this.contactTypeList.includes(contact.contactType)) {
          this.contactTypeFormControl.setValue(contact.contactType);

        } else {
          this.contactTypeFormControl.setValue('OTHERS');
          this.otherContactTypeFormControl.setValue(contact.contactType);
        }
        this.nameFormControl.setValue(contact.name);
        this.emailFormControl.setValue(contact.email);
        this.mobilePhoneNumberFormControl.setValue(contact.mobilePhoneNumber)
        this.accountIDFormControl.setValue(contact.accountID);
      },
      error: (error:HttpErrorResponse) => {
        this.notificationService.errorWithResponse(error);
      }
    });
  }

  isCreateView(): boolean {
    return this.data.type === CsFinWritePopupType.create;
  }

  isEditView(): boolean {
    return this.data.type === CsFinWritePopupType.update;
  }

  buildContactRequest(): Contact {
    const contactReq: Contact = {
      contactType: this.contactTypeFormControl.value==='OTHERS'? this.otherContactTypeFormControl.value:this.contactTypeFormControl.value,
      name: this.nameFormControl.value,
      email: this.emailFormControl.value,
      mobilePhoneNumber:this.mobilePhoneNumberFormControl.value,
      accountID: this.accountIDFormControl.value
    };
    if (this.isEditView() && this.data.contact) {
      contactReq.id = this.data.contact.id;
    }
    return contactReq;
  }

  onSaveClick(): void {

    const contactReq = this.buildContactRequest();
    if (this.isCreateView()) {
      this.doCreate(contactReq);
    } else if (this.isEditView()) {
      this.doUpdate(contactReq);
    }

  }

  onContactTypeSelected() {
    if (this.contactTypeFormControl.value === 'OTHERS') {
      this.contactForm.controls['otherContactType'].addValidators(Validators.required);

    } else {
      this.contactForm.controls['otherContactType'].clearValidators();
    }

    this.contactForm.controls['otherContactType'].updateValueAndValidity();

  }

  doCreate(contact: Contact): void {
    this.isSavingData = true;
    this.contactFacade.create(contact)
      .pipe(
        finalize(() => this.isSavingData = false)
      )
      .subscribe({
        next: () => {
          this.notificationService.successTranslated('CONTACTS.NOTIFICATIONS.CREATE_CONTACT_SUCCESS', {name: contact.name});
          this.dialogRef.close(true);
        },
        error: (error:HttpErrorResponse) => {
          this.notificationService.errorWithResponse(error);
        }
      });
  }

  doUpdate(contact: Contact): void {

    this.isSavingData = true;
    if (this.data.contact?.id) {
      this.contactFacade.update(this.data.contact.id, contact)
        .pipe(
          finalize(() => this.isSavingData = false)
        )
        .subscribe({
          next: () => {
            this.notificationService.successTranslated('CONTACTS.NOTIFICATIONS.UPDATE_CONTACT_SUCCESS', {name: contact.name});
            this.dialogRef.close(true);
          },
          error: (error:HttpErrorResponse) => {
            this.notificationService.errorWithResponse(error);
          }
        });
    }
  }

  loadContactTypes(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if(this.contactTypeList.length <= 0) {
        this.contactTypesApiService.getAll()
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (countries: String[]) => {
              this.contactTypeList = countries;
              this.filteredContactTypeList = countries;
              resolve();
            }, error: (error) => reject(error)
          });
      } else {
        resolve();
      }
    });
  }

  getButtonAction() {
    return this.isCreateView() ? 'GENERAL.BUTTONS.LABELS.CREATE' : 'GENERAL.BUTTONS.LABELS.SAVE';
  }

  getTitle() {
    return this.isCreateView() ? 'CONTACTS.CREATE.TITLE' : 'CONTACTS.EDIT.TITLE';
  }
}
