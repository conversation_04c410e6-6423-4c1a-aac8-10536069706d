<div class="dialog-container">
  <header class="dialog-header">
    <span class="title" mat-dialog-title>{{getTitle() | translate}}</span>
  </header>


    <div class="dialog-content" mat-dialog-content>
      <div class="form-container">
        <!-- contactType -->
        <mat-form-field appearance="outline" data-cy="input-contactType-filter"
                        (click)="loadContactTypes()">
          <mat-label>{{ 'CONTACTS.FIELDS.CONTACT_TYPE' | translate }}</mat-label>

          <mat-select (selectionChange)="onContactTypeSelected()" [formControl]="contactTypeFormControl" required>
            <ng-container *ngIf="filteredContactTypeList != null; else loadingContactType">
              <mat-option>
                <ngx-mat-select-search [formControl]="contactTypeSearchFormControl"
                                       [noEntriesFoundLabel]="'GENERAL.FILTERS.NO_MATCHING_RESULTS_FOUND' | translate"
                                       [placeholderLabel]="'GENERAL.FILTERS.SEARCH' | translate">
                </ngx-mat-select-search>
              </mat-option>
              <mat-option *ngFor="let contactType of filteredContactTypeList" [value]="contactType">
                {{contactType}}
              </mat-option>
              <button mat-button color="primary" class="clear-selection-btn"
                      [disabled]="!contactTypeFormControl.value?.length"
                      (click)="contactTypeFormControl.reset([])">
                {{'GENERAL.FILTERS.ACTIONS.CLEAR_SELECTION' | translate}}
              </button>
            </ng-container>
            <ng-template #loadingContactType>
              <mat-option disabled>
                <div class="filters-loading-container">
                  <span>{{'GENERAL.FILTERS.LOADING' | translate}}</span>
                  <mat-spinner diameter="20"></mat-spinner>
                </div>
              </mat-option>
            </ng-template>
          </mat-select>
        </mat-form-field>

        <!-- other contact type -->
        <mat-form-field *ngIf="contactForm.get('contactType')?.value==='OTHERS'" appearance="outline">
          <mat-label>{{ 'CONTACTS.FIELDS.OTHER_CONTACT_TYPE' | translate }}</mat-label>
          <input matInput type="text" [formControl]="otherContactTypeFormControl" data-cy="input-other-contact-type" required>
          <mat-error *ngIf="contactForm.get('otherContactType')?.errors?.required" data-cy="input-other-contact-type-error-required">
            {{ 'CONTACTS.WRITE.ERRORS.OTHER_CONTACT_TYPE_IS_REQUIRED' | translate }}
          </mat-error>

        </mat-form-field>

        <!-- name -->
        <mat-form-field appearance="outline">
          <mat-label>{{ 'CONTACTS.FIELDS.NAME' | translate }}</mat-label>
          <input matInput type="text" [formControl]="nameFormControl" data-cy="input-name" required>
          <mat-error *ngIf="contactForm.get('name')?.errors" data-cy="input-name-errors">
            {{ 'CONTACTS.WRITE.ERRORS.NAME_IS_REQUIRED' | translate }}
          </mat-error>
        </mat-form-field>

        <!-- email -->
        <mat-form-field  appearance="outline">
          <mat-label>{{ 'CONTACTS.FIELDS.EMAIL' | translate }}</mat-label>
          <input matInput type="text" [formControl]="emailFormControl" data-cy="input-email" required>
          <mat-error *ngIf="contactForm.get('email')?.errors?.required" data-cy="input-email-errors-required">
            {{ 'CONTACTS.WRITE.ERRORS.EMAIL_IS_REQUIRED' | translate }}
          </mat-error>
          <mat-error *ngIf="contactForm.get('email')?.errors?.pattern" data-cy="input-email-errors-pattern">
            {{ 'CONTACTS.WRITE.ERRORS.INVALID_EMAIL_ADDRESS' | translate }}
          </mat-error>
        </mat-form-field>

        <!-- Mobile Phone Number -->
        <mat-form-field appearance="outline">
          <mat-label>{{ 'CONTACTS.FIELDS.MOBILE_PHONE_NUMBER' | translate }}</mat-label>
          <input matInput type="text" [formControl]="mobilePhoneNumberFormControl" data-cy="input-mobile-phone-number" required>
          <mat-error *ngIf="contactForm.get('mobilePhoneNumber')?.errors?.required" data-cy="input-mobile-phone-number-errors-required">
            {{ 'CONTACTS.WRITE.ERRORS.MOBILE_PHONE_NUMBER_IS_REQUIRED' | translate }}
          </mat-error>
          <mat-error *ngIf="contactForm.get('mobilePhoneNumber')?.errors?.pattern" data-cy="input-mobile-phone-number-errors-pattern">
            {{ 'CONTACTS.WRITE.ERRORS.INVALID_MOBILE_PHONE_NUMBER' | translate }}
          </mat-error>
        </mat-form-field>

        <!-- accountID -->
        <mat-form-field appearance="outline">
          <mat-label>{{ 'CONTACTS.FIELDS.ACCOUNT_ID' | translate }}</mat-label>
          <input matInput type="text" [formControl]="accountIDFormControl" data-cy="input-account-id" required>
          <mat-error *ngIf="contactForm.get('accountID')?.errors" data-cy="input-account-id-errors">
            {{ 'CONTACTS.WRITE.ERRORS.ACCOUNT_ID_IS_REQUIRED' | translate }}
          </mat-error>
        </mat-form-field>
      </div>

    </div>


  <div class="dialog-actions" mat-dialog-actions>
    <span fxFlex></span>
    <button mat-flat-button mat-dialog-close id="cancel-btn" [fxShow]="true" [fxHide.xs]="true" [fxHide.sm]="true">
      {{ 'GENERAL.BUTTONS.LABELS.CANCEL' | translate }}
    </button>
    <button mat-flat-button color="primary" id="create-item-btn"
            (click)="onSaveClick()" [disabled]="contactForm.invalid" data-cy="btn-save">
      {{ getButtonAction() | translate }}
    </button>
  </div>

</div>
