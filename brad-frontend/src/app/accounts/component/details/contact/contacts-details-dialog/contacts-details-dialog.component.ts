import {Component, Inject} from '@angular/core';
import {MAT_DIALOG_DATA} from "@angular/material/dialog";
import {Contact} from "../../../../../entities/contact/contact";

@Component({
  selector: 'app-contacts-details-dialog',
  templateUrl: './contacts-details-dialog.component.html',
  styleUrls: ['./contacts-details-dialog.component.scss']
})
export class ContactsDetailsDialogComponent {

  contact!: Contact;

  constructor(@Inject(MAT_DIALOG_DATA) public data: {contact:Contact}) { }

  ngOnInit(): void {
    this.contact = this.data.contact;
  }

}
