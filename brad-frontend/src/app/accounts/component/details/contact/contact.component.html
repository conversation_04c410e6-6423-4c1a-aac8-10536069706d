<main>
  <section class="container">
    <mat-card *ngFor="let contact of contactList" (click)="onContactCardClick(contact, $event)">
      <mat-card-header>

        <button mat-icon-button color="primary" class="btn-edit" (click)="onEditClick(contact, $event)"
                aria-label="edit" csFinHasPermissionOnAnyTarget
                [authPermissions]="auth.permissions.BRAD_MANAGE_ACCOUNTS"
                [authTarget]="bradAuthTarget"
                [authAction]="auth.actions.DISABLE">
          <mat-icon
            [matTooltip]="'CONTACTS.BUTTONS.LABELS.EDIT' | translate">edit
          </mat-icon>
        </button>

        <button mat-icon-button color="primary" class="btn-remove" aria-label="remove" (click)="onRemoveClick(contact, $event)"
                csFinHasPermissionOnAnyTarget
                [authPermissions]="auth.permissions.BRAD_MANAGE_ACCOUNTS"
                [authTarget]="bradAuthTarget"
                [authAction]="auth.actions.DISABLE">
          <mat-icon
            [matTooltip]="'CONTACTS.BUTTONS.LABELS.REMOVE' | translate">delete
          </mat-icon>
        </button>
      </mat-card-header>
      <mat-card-content>
        <ul class="contact">
          <li>
            <span class="field">{{'CONTACTS.FIELDS.CONTACT_TYPE' | translate}}</span>
            <span class="value">{{contact.contactType}}</span>
          </li>
          <li>
            <span class="field">{{'CONTACTS.FIELDS.NAME' | translate}}</span>
            <span class="value">{{contact.name}}</span>
          </li>
          <li>
            <span class="field">{{'CONTACTS.FIELDS.EMAIL' | translate}}</span>
            <span class="value">{{contact.email}}</span>
          </li>
          <li>
            <span class="field">{{'CONTACTS.FIELDS.MOBILE_PHONE_NUMBER' | translate}}</span>
            <span class="value">{{contact.mobilePhoneNumber}}</span>
            <span  *ngIf="!contact.mobilePhoneNumber">{{"-"}}</span>
          </li>
        </ul>
      </mat-card-content>
    </mat-card>
    <mat-card>
      <button mat-icon-button color="primary" class="btn-add" aria-label="add" (click)="onCreateClick()"
              csFinHasPermissionOnAnyTarget
              [authPermissions]="auth.permissions.BRAD_MANAGE_ACCOUNTS"
              [authTarget]="bradAuthTarget"
              [authAction]="auth.actions.DISABLE">
        <mat-icon
          [matTooltip]="'CONTACTS.BUTTONS.LABELS.ADD' | translate">add
        </mat-icon>
      </button>
    </mat-card>
  </section>
</main>
