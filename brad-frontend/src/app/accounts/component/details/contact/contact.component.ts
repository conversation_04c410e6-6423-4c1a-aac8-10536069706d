import {Component, Input, On<PERSON><PERSON>es, OnDestroy, OnInit} from '@angular/core';
import {Contact} from "../../../../entities/contact/contact";
import {Subject} from "rxjs";
import {ContactFacade} from "../../../facade/contact.facade";
import {finalize, takeUntil} from "rxjs/operators";
import {CsFinConfirmationDialogComponent, CsFinWritePopupType} from "@jumia-cs-fin/common";
import {MatDialog} from "@angular/material/dialog";
import {ContactsWriteComponent} from "./write/contacts-write.component";
import {ContactsDetailsDialogComponent} from "./contacts-details-dialog/contacts-details-dialog.component";
import {
  authParamsCountry,
  bradAuthCountryTarget
} from "../../../../auth/constants/auth.constants";
import {NotificationService} from "../../../../api/service/notification.service";
import {HttpErrorResponse} from "@angular/common/http";

@Component({
  selector: 'brad-account-details-contact',
  templateUrl: './contact.component.html',
  styleUrls: ['./contact.component.scss', '../../../../../assets/brad-custom.scss']
})
export class ContactComponent implements OnInit, OnDestroy, OnChanges  {

  @Input() accountID!: number;

  contactList!: Contact[];

  auth = authParamsCountry;

  isLoading = false;
  private _onDestroy:Subject<void> = new Subject<void>();


  constructor(
    private contactFacade: ContactFacade,
    private dialog: MatDialog,
    private notificationService: NotificationService
  ) {
  }

  ngOnInit(): void {
    this.isLoading = true;
  }

  ngOnChanges() {
    if(this.accountID){
      this.isLoading = false;
      this.loadContacts();
    }
  }

  ngOnDestroy(): void {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  loadContacts(): void {
    this.contactFacade.getAllFromAccount(this.accountID)
      .pipe(takeUntil(this._onDestroy))
      .subscribe((contacts: Array<Contact>) => {
        this.contactList = contacts;
      });
  }


  onCreateClick(): void {
    const dialogRef = this.dialog.open(ContactsWriteComponent, {
      data: {
        type: CsFinWritePopupType.create,
        accountID: this.accountID
      }
    });

    dialogRef.afterClosed().subscribe(async result => {
      if (result) {
        this.loadContacts();
      }
    });
  }

  onEditClick(contact: Contact, event:MouseEvent) {
    event.stopPropagation();
    const dialogRef = this.dialog.open(ContactsWriteComponent, {
      data: {type: CsFinWritePopupType.update,
        contact: contact,
        accountID: this.accountID}
    });

    dialogRef.afterClosed().subscribe(async result => {
      if (result) {
        this.loadContacts();
      }
    });
  }

  onRemoveClick(contact: Contact, event:MouseEvent) {
    event.stopPropagation();
    const dialogRef = this.dialog.open(CsFinConfirmationDialogComponent, {
      autoFocus: false,
      width: '400px',
      height: '190px',
      panelClass: 'overflow-hidden-dialog',
      data: {
        titleKey: 'CONFIRMATION_DIALOG.MESSAGES.ABOUT_TO_DELETE_CONTACT',
        descriptionKey: 'CONFIRMATION_DIALOG.ACTIONS.ARE_YOU_SURE',
        cancelActionKey: 'CONFIRMATION_DIALOG.BUTTONS.NO',
        confirmActionKey: 'CONFIRMATION_DIALOG.BUTTONS.YES'
      }
    });

    dialogRef.afterClosed().subscribe(response => {
      if (response && contact.id) {
        this.isLoading = true;
        this.contactFacade.delete(contact.id)
          .pipe(
            finalize(() => this.isLoading = false)
          )
          .subscribe({
            next: () => {
              this.loadContacts();
              this.notificationService.successTranslated('CONTACTS.NOTIFICATIONS.DELETE_CONTACT_SUCCESS', {name: contact.name});
            },
            error: (error:HttpErrorResponse) => {
              this.notificationService.errorWithResponse(error);
            }
          })
      }
    });
  }

  onContactCardClick(contact: Contact, event:MouseEvent): void {
    event.stopPropagation();
    this.dialog.open(ContactsDetailsDialogComponent, {
      autoFocus: false,
      minHeight: '370px',
      data: {
        contact: contact
      }
    });
  }

  protected readonly bradAuthTarget = bradAuthCountryTarget;
}
