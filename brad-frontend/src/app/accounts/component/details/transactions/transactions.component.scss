@import "node_modules/@jumia-cs-fin/common/assets/styles/list-components";
@import "../../../accounts.component.scss";
@import "../accounts-details.component.scss";

.container {
  padding: 0 !important;

  .table-container {
    overflow: auto;
  }
}


.cs-fin-table {
  width: 100%;
  font-size: 12px;
  border-top: 4px solid transparent;

  .cdk-column-actions {
    width: 90px;
  }

  tr.mat-mdc-row {
    height: 40px;
    cursor: default;
  }

  tr.mat-mdc-row.with-detail:hover {
    cursor: pointer;
    background-color: var(--lighter-color) !important;
  }

  tr:nth-child(even) {
    background-color: var(--table-even-row-color);
  }

  tr:nth-child(odd) {
    background-color: var(--table-odd-row-color);
  }

  .item-opened {
    background-color: var(--lighter-color) !important;
  }

  tr.mat-mdc-header-row {
    height: 46px;
    text-transform: uppercase;
    background-color: #FFFFFF;
  }

  &.loading {
    border-top: 0;
  }

  th {
    font-weight: 600;
  }

  td {
    color: var(--primary-text-color);
    border-bottom: 0;
  }

  th, td {
    overflow: hidden;
    width: auto;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: var(--primary-text-color);
    padding-right: 12px;
    font-size: 12px;
  }
}

.empty-table {
  color: var(--primary-text-color);
  text-align: center;
  font-size: 12px;
  padding: 48px 0;
  display: block;
  opacity: 0.8;
}



.th-center {
  text-align: center;
}

.th-left {
  text-align: left;
}

.th-right {
  text-align: right;
}
.warning-icon {
  color: #f8b27b;
  border-radius: 50%;
  transition: background-color 0.2s;
}
.warning-icon:hover {
  background-color: #fdede2;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.mat-column-inError {
  padding-left: 8px;
  padding-right: 0 !important;
  width: 18px !important;

  .warning-icon-div {
    width: 18px;
    height: 18px;
    display: flex;
    justify-content: center;
    align-items: center;
    mat-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
      justify-content: center;
      align-items: center;
    }
  }

}

.balance-field{
  display: flex;
  flex-direction: column;

  .balances{
    display: flex;
    flex-direction: row;
    justify-content: start;
  }

  .local-currency-balance {
    font-size: 10px;
    font-style: italic;
  }

}

.amountWithUsd {
  display: flex;
  flex-direction: column;
  div{
    font-size: 10px;
  }
}

::ng-deep .table-paginator {
  ::ng-deep .mat-mdc-paginator-navigation-first:not(.mat-mdc-tooltip-disabled) .mat-mdc-paginator-icon,
  ::ng-deep .mat-mdc-paginator-navigation-previous:not(.mat-mdc-tooltip-disabled) .mat-mdc-paginator-icon,
  ::ng-deep .mat-mdc-paginator-navigation-next:not(.mat-mdc-tooltip-disabled) .mat-mdc-paginator-icon,
  ::ng-deep .mat-mdc-paginator-navigation-last:not(.mat-mdc-tooltip-disabled) .mat-mdc-paginator-icon,
  {
    fill: var(--primary-color) !important;
  }
}
