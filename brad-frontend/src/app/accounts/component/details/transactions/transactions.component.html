<main class="container" cdkDropListGroup>
    <div class="table-container">

      <mat-progress-bar mode="indeterminate" *ngIf="isLoading"></mat-progress-bar>
      <table mat-table class="cs-fin-table" matSort [dataSource]="dataSource"  [class.loading]="isLoading"
             (matSortChange)="onSortChange($event)" cdkDropList cdkDropListSortingDisabled>

        <ng-container matColumnDef="inError">
          <th mat-header-cell *matHeaderCellDef></th>
          <td mat-cell *matCellDef="let transaction">
            <div class="warning-icon-div">
              <mat-icon class="warning-icon" *ngIf="transaction.statementIsInError" (click)="onErrorClick()">warning</mat-icon>
             </div>
          </td>
        </ng-container>

        <ng-container matColumnDef="id">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'TRANSACTIONS.FIELDS.ID' | translate }}</th>
          <td mat-cell *matCellDef="let transaction"> {{transaction?.id}} </td>
        </ng-container>


        <ng-container matColumnDef="type">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'TRANSACTIONS.FIELDS.TYPE' | translate }}</th>
          <td mat-cell *matCellDef="let transaction"> {{transaction?.type}} </td>
        </ng-container>

        <ng-container matColumnDef="accountID">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'TRANSACTIONS.FIELDS.ACCOUNT_ID' | translate }}</th>
          <td mat-cell *matCellDef="let transaction"> {{transaction?.accountId}} </td>
        </ng-container>

        <ng-container matColumnDef="currency">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'TRANSACTIONS.FIELDS.CURRENCY' | translate }}</th>
          <td mat-cell *matCellDef="let transaction"> {{transaction?.currency.code}} </td>
        </ng-container>

        <ng-container matColumnDef="valueDate">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'TRANSACTIONS.FIELDS.VALUE_DATE' | translate }}</th>
          <td mat-cell *matCellDef="let transaction"> {{transaction?.valueDate}} </td>
        </ng-container>

        <ng-container matColumnDef="transactionDate">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'TRANSACTIONS.FIELDS.TRANSACTION_DATE' | translate }}</th>
          <td mat-cell *matCellDef="let transaction"> {{transaction?.transactionDate}} </td>
        </ng-container>

        <ng-container matColumnDef="statementDate">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'TRANSACTIONS.FIELDS.STATEMENT_DATE' | translate }}</th>
          <td mat-cell *matCellDef="let transaction"> {{transaction?.statementDate}} </td>
        </ng-container>

        <ng-container matColumnDef="direction">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'TRANSACTIONS.FIELDS.DIRECTION' | translate }}</th>
          <td mat-cell *matCellDef="let transaction"> {{transaction?.direction}} </td>
        </ng-container>

        <ng-container matColumnDef="amount">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'TRANSACTIONS.FIELDS.AMOUNT' | translate }}</th>
          <td mat-cell *matCellDef="let transaction">
            <div class="amountWithUsd">
              {{transaction?.amount | number:'1.2-2'}} {{transaction?.currency.symbol}}
              <div *ngIf="transaction?.amountUsd; else emptyAmount;">
                {{transaction?.amountUsd | number:'1.2-2'}} $
              </div>
            </div>
          </td>
        </ng-container>

        <ng-container matColumnDef="amountLcy">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'TRANSACTIONS.FIELDS.AMOUNT' | translate }} {{this.detailedAccount.country.currency.code}}</th>
          <td mat-cell *matCellDef="let transaction">
            <div *ngIf="transaction?.amountLocalCurrency; else emptyAmount;">
              {{transaction?.amountLocalCurrency | number:'1.2-2'}} {{this.detailedAccount.country.currency.symbol}}
            </div>
          </td>
        </ng-container>

        <ng-container matColumnDef="reference">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'TRANSACTIONS.FIELDS.REFERENCE' | translate }}</th>
          <td mat-cell *matCellDef="let transaction"> {{transaction?.reference}} </td>
        </ng-container>

        <ng-container matColumnDef="description">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'TRANSACTIONS.FIELDS.DESCRIPTION' | translate }}</th>
          <td mat-cell *matCellDef="let transaction"> {{transaction?.description}} </td>
        </ng-container>

        <ng-container matColumnDef="accountStatementID">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'TRANSACTIONS.FIELDS.STATEMENT_ID' | translate }}</th>
          <td mat-cell *matCellDef="let transaction"> {{transaction?.accountStatementID}} </td>
        </ng-container>

        <ng-container matColumnDef="remittanceInformation">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'TRANSACTIONS.FIELDS.REMITTANCE_INFORMATION' | translate }}</th>
          <td mat-cell *matCellDef="let transaction"> {{transaction?.remittanceInformation ?? '-'}} </td>
        </ng-container>

        <ng-container matColumnDef="orderingPartyName">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'TRANSACTIONS.FIELDS.ORDERING_PARTY_NAME' | translate }}</th>
          <td mat-cell *matCellDef="let transaction"> {{transaction?.orderingPartyName ?? '-'}} </td>
        </ng-container>

        <ng-container matColumnDef="createdAt">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'TRANSACTIONS.FIELDS.CREATED_AT' | translate }}</th>
          <td mat-cell *matCellDef="let transaction"> {{transaction?.createdAt}} </td>
        </ng-container>

        <ng-container matColumnDef="createdBy">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'TRANSACTIONS.FIELDS.CREATED_BY' | translate }}</th>
          <td mat-cell *matCellDef="let transaction"> {{transaction?.createdBy}} </td>
        </ng-container>

        <ng-container matColumnDef="updatedAt">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'TRANSACTIONS.FIELDS.UPDATED_AT' | translate }}</th>
          <td mat-cell *matCellDef="let transaction"> {{transaction?.updatedAt}} </td>
        </ng-container>

        <ng-container matColumnDef="updatedBy">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'TRANSACTIONS.FIELDS.UPDATED_BY' | translate }}</th>
          <td mat-cell *matCellDef="let transaction"> {{transaction?.updatedBy}} </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="getDisplayedColumns(); sticky: true"></tr>
        <tr mat-row *matRowDef="let row; columns: getDisplayedColumns();"
            class="with-detail" style="cursor: pointer;"></tr>
      </table>


      <mat-paginator class="table-paginator"
                     [pageSizeOptions]="pagination.pageSizeOptions"
                     [pageSize]="pagination.pageSize"
                     [length]="pagination.totalItems"
                     [pageIndex]="pagination.pageIndex"
                     (page)="onPageChange($event)"
                     showFirstLastButtons>
      </mat-paginator>
    </div>


  <cs-fin-add-remove-columns [triggerOrigin]="triggerOrigin"
                             [isOverlayOpen$]="isOverlayOpen$"
                             [menu]="MENU">
  </cs-fin-add-remove-columns>
</main>


<ng-template #emptyAmount>
  <span [ngStyle]="hideUsd() ? {'display': 'none'} : {}">-</span>
</ng-template>
