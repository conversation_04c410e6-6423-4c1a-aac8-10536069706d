import {
  ChangeDetectorRef,
  Component,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  SimpleChanges,
  ViewEncapsulation
} from '@angular/core';
import {FormControl, FormGroup} from '@angular/forms';
import {ActivatedRoute, Router} from '@angular/router';
import {CsFinActiveFiltersFacade, CsFinApiService, CsFinSidenavService} from '@jumia-cs-fin/common';
import {BehaviorSubject, Observable, Subject, takeUntil} from 'rxjs';
import {TransactionFacade} from 'src/app/accounts/facade/transaction.facade';
import {TransactionFilters} from 'src/app/entities/transaction/transaction-filters';
import {AccountFacade} from "../../../../facade/account.facade";
import {CurrencyApiService} from "../../../../../api/service/currency-api.service";
import {Currency} from "../../../../../entities/currency/currency";
import {DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE} from "@angular/material/core";
import {CustomDateAdapter, MY_DATE_FORMATS} from "../../../../../shared/service/CustomDateAdapter";


@Component({
  selector: 'brad-transactions-header',
  templateUrl: './transactions-header.component.html',
  styleUrls: ['./transactions-header.component.scss'],
  providers: [CsFinActiveFiltersFacade,
    { provide: DateAdapter, useClass: CustomDateAdapter, deps: [MAT_DATE_LOCALE] },
    { provide: MAT_DATE_FORMATS, useValue: MY_DATE_FORMATS }
  ],
  encapsulation:ViewEncapsulation.None
})
export class TransactionsHeaderComponent implements OnInit, OnChanges, OnDestroy {
  @Output() submitFilters = new Subject<TransactionFilters>();
  @Input() filters!: TransactionFilters;
  @Input() accountID!: number;
  @Input() showFilters!: boolean;

  queryParams = {};
  form!: FormGroup;
  directionList:string[] = [];
  filteredDirectionList:string[] = []

  currencyList:Currency[] = [];
  filteredCurrencyList:Currency[] = [];

  isOpen$!: Observable<boolean>;
  isInitializing = false;
  isRefreshing = false;

  private _isOpen!: BehaviorSubject<boolean>;
  private _onDestroy: Subject<void> = new Subject<void>();
  private readonly refreshTimeout = 2000;

  filterTextFormControl!: FormControl;
  typeFormControl!: FormControl;
  accountIdFormControl!: FormControl;
  partitionKeyFormControl!: FormControl;
  currencyFormControl!: FormControl;
  currencySearchFormControl = new FormControl();
  valueDateStartFormControl!: FormControl;
  valueDateEndFormControl!: FormControl;
  transactionDateStartFormControl!: FormControl;
  transactionDateEndFormControl!: FormControl;
  statementDateStartFormControl!: FormControl;
  statementDateEndFormControl!: FormControl;
  amountFormControl!: FormControl;
  referenceFormControl!: FormControl;
  descriptionFormControl!: FormControl;
  accountStatementIDFormControl!: FormControl;
  remittanceInformationFormControl!: FormControl;
  orderingPartyNameFormControl!: FormControl;
  createdAtStartFormControl!: FormControl;
  createdAtEndFormControl!: FormControl;
  directionFormControl!: FormControl;
  directionSearchFormControl = new FormControl();


  constructor(public ref: ChangeDetectorRef,
              public sidenav: CsFinSidenavService,
              private activatedRoute: ActivatedRoute,
              private activeFiltersFacade: CsFinActiveFiltersFacade,
              private router: Router,
              private apiService: CsFinApiService,
              private transactionFacade: TransactionFacade,
              private accountFacade: AccountFacade,
              private currencyApiService: CurrencyApiService) { }


  ngOnInit(){
    this.isInitializing = true;
    this.initFiltersSearch();
    this.initializeFormData(this.filters);
  }

  ngOnChanges(changes: SimpleChanges) {
    if(changes['filters'] && changes['filters'].currentValue && !this.isInitializing) {
      this.initializeFormData(changes['filters'].currentValue);
    }
  }

  ngOnDestroy(): void {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  private initFiltersSearch(): void {
    this.initCurrencySearch();
    this.initDirectionSearch();
  }

  private initializeFormData(params:any): void {
    this.queryParams = params;
    if(!this.form) {
      this.form = new FormGroup({});
      this.initializeFormControlsAndFilters(params);
    }
  }

  private initCurrencySearch(): void {
    this.currencySearchFormControl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe((value: String) => {
        const searchTerm = value.trim();
        if (searchTerm) {
          this.filteredCurrencyList = this.currencyList.filter((currency) => {
            return currency.code.trim().toLowerCase().includes(searchTerm.toLowerCase());
          })
        } else {
          this.filteredCurrencyList = this.currencyList;
        }
      });
  }

  private initDirectionSearch(): void {
    this.directionSearchFormControl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe((value: String) => {
        const searchTerm = value.trim();
        if (searchTerm) {
          this.filteredDirectionList = this.directionList.filter((direction) => {
            return direction.trim().toLowerCase().includes(searchTerm.toLowerCase());
          })
        } else {
          this.filteredDirectionList = this.directionList;
        }
      });
  }

  private setFormControlsToForm(): void {
    this.form.addControl(this.transactionFacade.filterTextKey, this.filterTextFormControl);
    this.form.addControl(this.transactionFacade.typeKey, this.typeFormControl);
    this.form.addControl(this.transactionFacade.accountIDKey, this.accountIdFormControl);
    this.form.addControl(this.transactionFacade.partitionKey, this.partitionKeyFormControl);
    this.form.addControl(this.transactionFacade.currencyKey, this.currencyFormControl);
    this.form.addControl(this.transactionFacade.valueDateStartKey, this.valueDateStartFormControl);
    this.form.addControl(this.transactionFacade.valueDateEndKey, this.valueDateEndFormControl);
    this.form.addControl(this.transactionFacade.transactionDateStartKey, this.transactionDateStartFormControl);
    this.form.addControl(this.transactionFacade.transactionDateEndKey, this.transactionDateEndFormControl);
    this.form.addControl(this.transactionFacade.statementDateStartKey, this.statementDateStartFormControl);
    this.form.addControl(this.transactionFacade.statementDateEndKey, this.statementDateEndFormControl);
    this.form.addControl(this.transactionFacade.directionKey, this.directionFormControl);
    this.form.addControl(this.transactionFacade.amountKey, this.amountFormControl);
    this.form.addControl(this.transactionFacade.referenceKey, this.referenceFormControl);
    this.form.addControl(this.transactionFacade.descriptionKey, this.descriptionFormControl);
    this.form.addControl(this.transactionFacade.accountStatementIDKey, this.accountStatementIDFormControl);
    this.form.addControl(this.transactionFacade.remittanceInformationKey, this.remittanceInformationFormControl);
    this.form.addControl(this.transactionFacade.orderingPartyNameKey, this.orderingPartyNameFormControl);
    this.form.addControl(this.transactionFacade.createdAtStartKey, this.createdAtStartFormControl);
    this.form.addControl(this.transactionFacade.createdAtEndKey, this.createdAtEndFormControl);
  }

  private initializeFormControlsAndFilters(params:any): void {
    const filters: TransactionFilters = {};
    this.filterTextFormControl = new FormControl(params.filterText);
    filters.filterText = params.filterText;

    this.typeFormControl = new FormControl(params.type);
    filters.type = params.type;

    let currencyCodes: string[] = params.currencyCodes === undefined ? [] : params.currencyCodes;
    this.currencyFormControl = new FormControl(currencyCodes);
    filters.currencyCodes = currencyCodes;

    this.accountIdFormControl = new FormControl(params.accountID);
    filters.accountId = params.accountId;

    this.partitionKeyFormControl = new FormControl(params.partitionKey);
    filters.partitionKey = params.partitionKey;

    this.valueDateStartFormControl = new FormControl(params.valueDateStart);
    filters.valueDateStart = params.valueDateStart;

    this.valueDateEndFormControl = new FormControl(params.valueDateEnd);
    filters.valueDateEnd = params.valueDateEnd;

    this.transactionDateStartFormControl = new FormControl(params.transactionDateStart);
    filters.transactionDateStart = params.transactionDateStart;

    this.transactionDateEndFormControl = new FormControl(params.transactionDateEnd);
    filters.transactionDateEnd = params.transactionDateEnd;

    this.statementDateStartFormControl = new FormControl(params.statementDateStart);
    filters.statementDateStart = params.statementDateStart;

    this.statementDateEndFormControl = new FormControl(params.statementDateEnd);
    filters.statementDateEnd = params.statementDateEnd;

    let direction: string[] = params.direction === undefined ? undefined : params.direction;
    this.directionFormControl = new FormControl(direction);
    filters.direction = direction;

    this.amountFormControl = new FormControl(params.amount);
    filters.amount = params.amount;

    this.referenceFormControl = new FormControl(params.reference);
    filters.reference = params.reference;

    this.descriptionFormControl = new FormControl(params.description);
    filters.description = params.description;

    this.accountStatementIDFormControl = new FormControl(params.accountStatementID);
    filters.accountStatementID = params.accountStatementID;

    this.remittanceInformationFormControl = new FormControl(params.remittanceInformation);
    filters.remittanceInformation = params.remittanceInformation;

    this.orderingPartyNameFormControl = new FormControl(params.orderingPartyName);
    filters.orderingPartyName = params.orderingPartyName;

    this.createdAtStartFormControl = new FormControl(params.createdAtStart);
    filters.createdAtStart = params.createdAtStart;

    this.createdAtEndFormControl = new FormControl(params.createdAtEnd);
    filters.createdAtEnd = params.createdAtEnd;

    filters.orderField = params.orderField;
    filters.orderDirection = params.orderDirection;
    filters.page = params.page;
    filters.size = params.size;

    Promise.all([
      this.applyCurrencyFilter(params, filters),
      this.applyDirectionFilter(params, filters),
    ]).then(() => {
      this.setFormControlsToForm();
      this.isInitializing = false;
    });

  }

  private applyCurrencyFilter(params: any, filters: TransactionFilters): Promise<void> {
    return new Promise<void>(async (resolve) => {
      if(!params.currencyCodes) {
        resolve();
        return;
      }

      await this.loadCurrencies();
      this.currencyFormControl.setValue(filters.currencyCodes, {emitEvent: false})
      resolve();
    });
  }

  private applyDirectionFilter(params: any, filters: TransactionFilters): Promise<void> {
    return new Promise<void>(async (resolve) => {

      if(!params.direction) {
        resolve();
        return;
      }

      await this.loadDirection();

      this.directionFormControl.setValue(filters.direction, {emitEvent: false});
      resolve();
    });
  }

  loadCurrencies(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if(this.currencyList.length <= 0) {
        this.currencyApiService.getAll()
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (currencies: Currency[]) => {
              this.currencyList = currencies;
              this.filteredCurrencyList = this.currencyList;
              resolve();
            }, error: (error) => reject(error)
          });
      } else {
        resolve();
      }
    });
  }

  loadDirection(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if(this.directionList.length <= 0) {
        this.transactionFacade.getDirections()
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (directions: string[]) => {
              this.directionList = directions;
              this.filteredDirectionList = this.directionList;
              resolve();
            }, error: (error) => reject(error)
          });
      } else {
        resolve();
      }
    });
  }

  clearFilters(): void {
    this.form.reset('', {emitEvent: false});
    this.transactionFacade.filtersChanged({});
  }

  submit() {
    this.submitFilters.next(this.getFormValues());
  }

  private getFormValues(): TransactionFilters {
    return this.form.value as TransactionFilters;
  }

  noFiltersSelected() {
    return !!this.form.errors || this.isRefreshing;
  }

  compareIdFn(o1: any, o2: any): boolean {
    return o1 && o2 && o1.id === o2.id;
  }

}
