<div class="filters-container">
  <!-- type -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-type-filter">
    <mat-label>{{ 'TRANSACTIONS.FIELDS.TYPE' | translate }}</mat-label>
    <input matInput type="text" [formControl]="typeFormControl">
  </mat-form-field>

  <!-- currency -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-currency-filter"
                  (click)="loadCurrencies()">
    <mat-label>{{'TRANSACTIONS.FIELDS.CURRENCY' | translate}}</mat-label>
    <mat-select [formControl]="currencyFormControl" multiple>
      <ng-container *ngIf="filteredCurrencyList != null; else loadingCurrency">
        <mat-option>
          <ngx-mat-select-search [formControl]="currencySearchFormControl"
                                 [noEntriesFoundLabel]="'GENERAL.FILTERS.NO_MATCHING_RESULTS_FOUND' | translate"
                                 [placeholderLabel]="'GENERAL.FILTERS.SEARCH' | translate">
          </ngx-mat-select-search>
        </mat-option>
        <mat-option *ngFor="let currency of filteredCurrencyList" [value]="currency.code">
          {{currency.code}}
        </mat-option>
        <button mat-button color="primary" class="clear-selection-btn"
                [disabled]="!currencyFormControl.value?.length"
                (click)="currencyFormControl.reset([])">
          {{'GENERAL.FILTERS.ACTIONS.CLEAR_SELECTION' | translate}}
        </button>
      </ng-container>
      <ng-template #loadingCurrency>
        <mat-option disabled>
          <div class="filters-loading-container">
            <span>{{'GENERAL.FILTERS.LOADING' | translate}}</span>
            <mat-spinner diameter="20"></mat-spinner>
          </div>
        </mat-option>
      </ng-template>
    </mat-select>
  </mat-form-field>

  <!-- value date -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-value-date-filter">
    <mat-label>{{'TRANSACTIONS.FIELDS.VALUE_DATE' | translate}}</mat-label>
    <mat-date-range-input [rangePicker]="picker1">
      <input matStartDate [formControl]="valueDateStartFormControl" placeholder="Start date">
      <input matEndDate [formControl]="valueDateEndFormControl" placeholder="End date">
    </mat-date-range-input>
    <mat-datepicker-toggle matIconSuffix [for]="picker1"></mat-datepicker-toggle>
    <mat-date-range-picker #picker1></mat-date-range-picker>
  </mat-form-field>


  <!-- transaction date -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-transaction-date-filter">
    <mat-label>{{'TRANSACTIONS.FIELDS.TRANSACTION_DATE' | translate}}</mat-label>
    <mat-date-range-input [rangePicker]="picker2">
      <input matStartDate [formControl]="transactionDateStartFormControl" placeholder="Start date">
      <input matEndDate [formControl]="transactionDateEndFormControl" placeholder="End date">
    </mat-date-range-input>
    <mat-datepicker-toggle matIconSuffix [for]="picker2"></mat-datepicker-toggle>
    <mat-date-range-picker #picker2></mat-date-range-picker>
  </mat-form-field>

  <!-- statement date -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-statement-date-filter">
    <mat-label>{{'TRANSACTIONS.FIELDS.STATEMENT_DATE' | translate}}</mat-label>
    <mat-date-range-input [rangePicker]="picker3">
      <input matStartDate [formControl]="statementDateStartFormControl" placeholder="Start date">
      <input matEndDate [formControl]="statementDateEndFormControl" placeholder="End date">
    </mat-date-range-input>
    <mat-datepicker-toggle matIconSuffix [for]="picker3"></mat-datepicker-toggle>
    <mat-date-range-picker #picker3></mat-date-range-picker>
  </mat-form-field>

  <!-- direction -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-initial-direction-filter"
                  (click)="loadDirection()">
    <mat-label>{{'TRANSACTIONS.FIELDS.DIRECTION' | translate}}</mat-label>
    <mat-select [formControl]="directionFormControl" multiple>
      <ng-container *ngIf="filteredDirectionList != null; else loadingDirection">
        <mat-option>
          <ngx-mat-select-search [formControl]="directionSearchFormControl"
                                 [noEntriesFoundLabel]="'GENERAL.FILTERS.NO_MATCHING_RESULTS_FOUND' | translate"
                                 [placeholderLabel]="'GENERAL.FILTERS.SEARCH' | translate">
          </ngx-mat-select-search>
        </mat-option>
        <mat-option *ngFor="let direction of filteredDirectionList" [value]="direction">
          {{direction}}
        </mat-option>
        <button mat-button color="primary" class="clear-selection-btn"
                [disabled]="!directionFormControl.value?.length"
                (click)="directionFormControl.reset([])">

          {{'GENERAL.FILTERS.ACTIONS.CLEAR_SELECTION' | translate}}
        </button>
      </ng-container>
      <ng-template #loadingDirection>
        <mat-option disabled>
          <div class="filters-loading-container">
            <span>{{'GENERAL.FILTERS.LOADING' | translate}}</span>
            <mat-spinner diameter="20"></mat-spinner>
          </div>
        </mat-option>
      </ng-template>
    </mat-select>
  </mat-form-field>

  <!-- amount -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-amount-filter">
    <mat-label>{{ 'TRANSACTIONS.FIELDS.AMOUNT' | translate }}</mat-label>
    <input matInput type="number" [formControl]="amountFormControl">
  </mat-form-field>

  <!-- reference -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-reference-filter">
    <mat-label>{{ 'TRANSACTIONS.FIELDS.REFERENCE' | translate }}</mat-label>
    <input matInput type="text" [formControl]="referenceFormControl">
  </mat-form-field>

  <!-- description -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-description-filter">
    <mat-label>{{ 'TRANSACTIONS.FIELDS.DESCRIPTION' | translate }}</mat-label>
    <input matInput type="text" [formControl]="descriptionFormControl">
  </mat-form-field>

  <!-- statement ID -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-statement-id-filter">
    <mat-label>{{ 'TRANSACTIONS.FIELDS.STATEMENT_ID' | translate }}</mat-label>
    <input matInput type="text" [formControl]="accountStatementIDFormControl">
  </mat-form-field>

  <!-- remittance information -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-remittance-information-description-filter">
    <mat-label>{{ 'TRANSACTIONS.FIELDS.REMITTANCE_INFORMATION' | translate }}</mat-label>
    <input matInput type="text" [formControl]="remittanceInformationFormControl">
  </mat-form-field>

  <!-- ordering party name -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-ordering-party-name-filter">
    <mat-label>{{ 'TRANSACTIONS.FIELDS.ORDERING_PARTY_NAME' | translate }}</mat-label>
    <input matInput type="text" [formControl]="orderingPartyNameFormControl">
  </mat-form-field>

  <!-- created at -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-created-at-filter">
    <mat-label>{{'TRANSACTIONS.FIELDS.CREATED_AT' | translate}}</mat-label>
    <mat-date-range-input [rangePicker]="picker4">
      <input matStartDate [formControl]="createdAtStartFormControl" placeholder="Start date">
      <input matEndDate [formControl]="createdAtEndFormControl" placeholder="End date">
    </mat-date-range-input>
    <mat-datepicker-toggle matIconSuffix [for]="picker4"></mat-datepicker-toggle>
    <mat-date-range-picker #picker4></mat-date-range-picker>
  </mat-form-field>

</div>

<div class="filters-actions">
  <button class="flat-white-btn filters-cancel-btn" [disabled]="noFiltersSelected()" mat-flat-button fxShow
          (click)="clearFilters()" [fxShow.xs]="false" [fxShow.sm]="false">
    {{'GENERAL.FILTERS.ACTIONS.CLEAR' | translate}}
  </button>
  <button class="raised-primary-btn filters-apply-btn" [disabled]="noFiltersSelected()" mat-raised-button
          color="primary"
          (click)="submit()">
    {{'GENERAL.FILTERS.ACTIONS.APPLY' | translate}}
  </button>
</div>
