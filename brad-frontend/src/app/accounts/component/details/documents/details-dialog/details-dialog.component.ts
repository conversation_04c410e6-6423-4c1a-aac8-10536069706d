import {Component, Inject, OnDestroy, OnInit} from '@angular/core';
import {Document} from "../../../../../entities/document/document";
import {MAT_DIALOG_DATA} from "@angular/material/dialog";
import {takeUntil} from "rxjs/operators";
import {DocumentFacade} from "../../../../facade/document.facade";
import {Subject} from "rxjs";
import {authParams, bradAuthTarget} from "../../../../../auth/constants/auth.constants";
import {NotificationService} from "../../../../../api/service/notification.service";

@Component({
  selector: 'app-details-dialog',
  templateUrl: './details-dialog.component.html',
  styleUrls: ['./details-dialog.component.scss']
})
export class DetailsDialogComponent implements OnInit, OnDestroy {

  documentID!: number;
  document!: Document;
  isLoading = false;
  downloadUrl!: string | undefined;
  name!: string;
  contentType!: string | undefined;

  private _onDestroy: Subject<void> = new Subject<void>();

  constructor(@Inject(MAT_DIALOG_DATA) public data: {
                documentID: number
              },
              private documentFacade: DocumentFacade,
              private notificationService: NotificationService,) {
  }

  ngOnInit(): void {
    this.isLoading = true;
    this.documentID = this.data.documentID;
    this.loadDocument();
  }

  ngOnDestroy(): void {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  loadDocumentWithUrl(): void {
    this.documentFacade.getURLByID(this.documentID)
      .pipe(takeUntil(this._onDestroy))
      .subscribe((document: Document) => {
        window.open(document.url, '_blank');
      });
    return;
  }

  loadDocumentToPreviewUrl(): void {
    this.documentFacade.getURLByID(this.documentID)
      .pipe(takeUntil(this._onDestroy))
      .subscribe({
        next: (document: Document) => {
          this.downloadUrl = document.url;
          this.getMetadataFromFile(document.file);
        },
        error: (error: any) => {
          this.notificationService.errorWithResponse(error);
        }
      });
  }

  getMetadataFromFile(base64File: any) {
    if (base64File && base64File.startsWith('data:')) {
      this.contentType = base64File.substring(base64File.indexOf(':') + 1, base64File.indexOf(';'));
    }
  }

  loadDocument(): void {
    this.documentFacade.getById(this.documentID)
      .pipe(takeUntil(this._onDestroy))
      .subscribe((document: Document) => {
        this.document = document;
        this.isLoading = false;
        this.loadDocumentToPreviewUrl();
      });
  }

  protected readonly bradAuthTarget = bradAuthTarget;
  protected readonly auth = authParams;
}
