<section class="details-dialog" *ngIf="!isLoading">
 <section mat-dialog-title class="header">
  <span class="title">
      {{'DOCUMENTS.DIALOG.TITLE' | translate}}
    </span>
   <button mat-icon-button color="primary" class="downloadButton" color="primary" (click)="loadDocumentWithUrl()">
     <mat-icon>cloud_download
     </mat-icon>
   </button>
  </section>
  <mat-dialog-content fxLayout="row">
      <div fxLayout="column">
        <section class="info">
          <ul>
            <li>
              <span class="field">{{'GENERAL.FIELDS.ID' | translate}}</span>
              <span class="value">{{document.id}}</span>
            </li>
            <li>
              <span class="field">{{'DOCUMENTS.FIELDS.DOCUMENT_TYPE' | translate}}</span>
              <span class="value">{{document.documentType}}</span>
            </li>
            <li>
              <span class="field">{{'DOCUMENTS.FIELDS.NAME' | translate}}</span>
              <span class="value">{{document.name}}</span>
            </li>
            <li>
              <span class="field">{{'DOCUMENTS.FIELDS.DESCRIPTION' | translate}}</span>
              <span class="value">{{document.description}}</span>
            </li>
          </ul>
          <ul>
            <li>
              <span class="field" data-cy="document-details-field-created-at">{{'GENERAL.FIELDS.CREATED_AT' | translate}}: </span>
              <span class="value" data-cy="document-details-value-created-at">{{document.createdAt | date:'short'}}</span>
            </li>
            <li>
              <span class="field" data-cy="document-details-field-created-by">{{'GENERAL.FIELDS.CREATED_BY' | translate}}: </span>
              <span class="value" data-cy="document-details-value-created-by">{{document.createdBy}}</span>
            </li>
            <li>
              <span class="field" data-cy="document-details-field-updated-at">{{'GENERAL.FIELDS.UPDATED_AT' | translate}}: </span>
              <span class="value" data-cy="document-details-value-updated-at">{{document.updatedAt | date:'short'}}</span>
            </li>
            <li>
              <span class="field" data-cy="document-details-field-updated-by">{{'GENERAL.FIELDS.UPDATED_BY' | translate}}: </span>
              <span class="value" data-cy="document-details-value-updated-by">{{document.updatedBy}}</span>
            </li>
          </ul>
        </section>
      </div>
      <div fxLayout="column">
        <ng-container *ngIf="contentType === 'application/pdf'">
          <ngx-doc-viewer
            [url]="downloadUrl"
            viewer="google"
            disableContent="popout"
            style="width:1000px;height:500vh;">
          </ngx-doc-viewer>
        </ng-container>
        <ng-container *ngIf="contentType === 'image/jpeg' || contentType === 'image/png'">
          <div class="image-container">
            <img [src]="downloadUrl" alt="Document Preview" />
          </div>
        </ng-container>
      </div>
  </mat-dialog-content>
  <mat-dialog-actions align="end">
    <button mat-flat-button mat-dialog-close id="cancel-btn" fxShow [fxShow.xs]="false"
            [fxShow.sm]="false">{{'GENERAL.BUTTONS.TOOLTIPS.CLOSE' | translate}}</button>
  </mat-dialog-actions>
</section>
