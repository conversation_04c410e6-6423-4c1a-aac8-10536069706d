import {Component, Inject, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialog, MatDialogRef} from '@angular/material/dialog'
import {FormControl, FormGroup, Validators} from '@angular/forms';
import {finalize, takeUntil} from 'rxjs/operators';
import {Subject} from "rxjs";
import {CsFinConfirmationDialogComponent, CsFinWritePopupType} from "@jumia-cs-fin/common";
import {DocumentFacade} from "../../../../facade/document.facade";
import {DocumentTypesApiService} from "../../../../../api/service/documents-type-api.service";
import {Document} from "../../../../../entities/document/document";
import {NotificationService} from "../../../../../api/service/notification.service";
import {HttpErrorResponse} from "@angular/common/http";

@Component({
  selector: 'brad-documents-write',
  templateUrl: './documents-write.component.html',
  styleUrls: ['./documents-write.component.scss']
})
export class DocumentsWriteComponent implements OnInit {

  documentForm!: FormGroup;
  documentTypeFormControl!: FormControl;
  nameFormControl!: FormControl;
  descriptionFormControl!: FormControl;
  fileFormControl!: FormControl;
  accountIDFormControl!: FormControl;

  file!: File | null;

  documentTypeSearchFormControl = new FormControl();
  documentTypeList:String[] = [];
  filteredDocumentTypeList:String[] = [];

  isDocumentTypeLoading = true;
  isSavingData = false;
  private _onDestroy:Subject<void> = new Subject<void>();

  constructor(public dialogRef: MatDialogRef<DocumentsWriteComponent>,
              @Inject(MAT_DIALOG_DATA) public data: {
                type: CsFinWritePopupType,
                document: Document | null,
                accountID: number
              },
              private notificationService: NotificationService,
              private documentFacade: DocumentFacade,
              private dialog: MatDialog,
              private documentTypesApiService: DocumentTypesApiService) { }

  ngOnInit(): void {
    this.documentForm = new FormGroup({});
    this.initFiltersSearch();
    this.initFormControls();
    this.setFormControlsToForm();
    this.loadDocumentTypes();
    this.accountIDFormControl.setValue(this.data.accountID);
    this.accountIDFormControl.disable();
    if (this.data.document?.id != null) {
      this.loadDocumentAndSetFormGroup(this.data.document.id);
    }
  }

  ngOnDestroy(): void {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  private initFiltersSearch(): void {
    this.initDocumentTypeSearch();
  }

  private initDocumentTypeSearch(): void {
    this.documentTypeSearchFormControl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .pipe(
        finalize(() => this.isDocumentTypeLoading = false)
      )
      .subscribe((value: string) => {

        const searchTerm = value.trim();
        if (searchTerm) {
          this.filteredDocumentTypeList = this.documentTypeList.filter((documentType) => {
            return documentType.trim().toLowerCase().includes(searchTerm.toLowerCase());
          })
        } else {
          this.filteredDocumentTypeList = this.documentTypeList;
        }
      });
  }

  initFormControls(): void {
    this.documentTypeFormControl = new FormControl('', [Validators.required]);
    this.nameFormControl = new FormControl('', [Validators.required]);
    this.descriptionFormControl = new FormControl('', [Validators.required]);
    this.accountIDFormControl = new FormControl('', [Validators.required]);
    if (this.isCreateView()) {
      this.fileFormControl = new FormControl('', [Validators.required]);
    } else {
      this.fileFormControl = new FormControl('');
    }
  }

  setFormControlsToForm(): void {
    this.documentForm.addControl('documentType', this.documentTypeFormControl);
    this.documentForm.addControl('name', this.nameFormControl);
    this.documentForm.addControl('description', this.descriptionFormControl);
    this.documentForm.addControl('accountID', this.accountIDFormControl);
    this.documentForm.addControl('file', this.fileFormControl);
  }

  loadDocumentAndSetFormGroup(id: number): void {
    this.documentFacade.getById(id)
      .subscribe({
        next: (document) => {
          this.documentTypeFormControl.setValue(document.documentType);
          this.nameFormControl.setValue(document.name);
          this.descriptionFormControl.setValue(document.description);
          this.accountIDFormControl.setValue(document.accountID);
        },
        error: (error:HttpErrorResponse) => {
          this.notificationService.errorWithResponse(error);
        }
      });
  }

  isCreateView(): boolean {
    return this.data.type === CsFinWritePopupType.create;
  }

  isEditView(): boolean {
    return this.data.type === CsFinWritePopupType.update;
  }

  buildDocumentRequest(): Document {
    const documentReq: Document = {
      documentType: this.documentTypeFormControl.value,
      name: this.nameFormControl.value,
      description: this.descriptionFormControl.value,
      accountID: this.accountIDFormControl.value,
      file: this.fileFormControl.value
    };
    if (this.isEditView() && this.data.document) {
      documentReq.id = this.data.document.id;
    }
    return documentReq;
  }

  onSaveClick(): void {

    const documentReq = this.buildDocumentRequest();
    if (this.isCreateView()) {
      this.doCreate(documentReq);
    } else if (this.isEditView()) {
      if (this.file != null) {
        const dialogRef = this.dialog.open(CsFinConfirmationDialogComponent, {
          autoFocus: false,
          width: '400px',
          height: '190px',
          panelClass: 'overflow-hidden-dialog',
          data: {
            titleKey: 'CONFIRMATION_DIALOG.MESSAGES.ABOUT_TO_REPLACE_FILE',
            descriptionKey: 'CONFIRMATION_DIALOG.ACTIONS.ARE_YOU_SURE',
            cancelActionKey: 'CONFIRMATION_DIALOG.BUTTONS.NO',
            confirmActionKey: 'CONFIRMATION_DIALOG.BUTTONS.YES'
          }
        });

        dialogRef.afterClosed().subscribe(response => {
          if (response) {
            this.doUpdate(documentReq);
          }
        });
      } else {
        this.doUpdate(documentReq);
      }


    }

  }

  doCreate(document: Document): void {
    this.isSavingData = true;
    this.documentFacade.create(document)
      .pipe(
        finalize(() => this.isSavingData = false)
      )
      .subscribe({
        next: () => {
          this.notificationService.successTranslated('DOCUMENTS.NOTIFICATIONS.CREATE_DOCUMENT_SUCCESS', {name: document.name});
          this.dialogRef.close(true);
        },
        error: (error:HttpErrorResponse) => {
          this.notificationService.errorWithResponse(error);
        }
      });
  }

  doUpdate(document: Document): void {

    this.isSavingData = true;
    if (this.data.document?.id) {
      this.documentFacade.update(this.data.document.id, document)
        .pipe(
          finalize(() => this.isSavingData = false)
        )
        .subscribe({
          next: () => {
            this.notificationService.successTranslated('DOCUMENTS.NOTIFICATIONS.UPDATE_DOCUMENT_SUCCESS', {name: document.name});
            this.dialogRef.close(true);
          },
          error: (error:HttpErrorResponse) => {
            this.notificationService.errorWithResponse(error);
          }
        });
    }
  }

  loadDocumentTypes(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if(this.documentTypeList.length <= 0) {
        this.documentTypesApiService.getAll()
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (countries: String[]) => {
              this.documentTypeList = countries;
              this.filteredDocumentTypeList = countries;
              resolve();
            }, error: (error) => reject(error)
          });
      } else {
        resolve();
      }
    });
  }

  onSelectFile(event: any) {
    if (event.rejectedFiles.length) {
      return this.notificationService.errorTranslated('UPLOAD.NOTIFICATIONS.MESSAGES.ERROR', {});
    }
    this.file = event.addedFiles[0];
    let reader = new FileReader();
    if (this.file!=null) {
      reader.readAsDataURL(this.file);
      reader.onload = () => {
        this.fileFormControl.setValue(reader.result);
      }
    }
  }

  onRemoveFile() {
    this.file = null;
  }

  getButtonAction() {
    return this.isCreateView() ? 'GENERAL.BUTTONS.LABELS.CREATE' : 'GENERAL.BUTTONS.LABELS.SAVE';
  }

  getTitle() {
    return this.isCreateView() ? 'DOCUMENTS.CREATE.TITLE' : 'DOCUMENTS.EDIT.TITLE';
  }

}
