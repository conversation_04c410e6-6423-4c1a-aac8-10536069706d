<div class="dialog-container">
  <header class="dialog-header">
    <span class="title" mat-dialog-title>{{getTitle() | translate}}</span>
  </header>


  <div class="dialog-content" mat-dialog-content>
    <div class="form-container">
      <!-- documentType -->
      <mat-form-field appearance="outline" data-cy="input-documentType-filter"
                      (click)="loadDocumentTypes()">
        <mat-label>{{ 'DOCUMENTS.FIELDS.DOCUMENT_TYPE' | translate }}</mat-label>

        <mat-select [formControl]="documentTypeFormControl" required>
          <ng-container *ngIf="filteredDocumentTypeList != null; else loadingDocumentType">
            <mat-option>
              <ngx-mat-select-search [formControl]="documentTypeSearchFormControl"
                                     [noEntriesFoundLabel]="'GENERAL.FILTERS.NO_MATCHING_RESULTS_FOUND' | translate"
                                     [placeholderLabel]="'GENERAL.FILTERS.SEARCH' | translate">
              </ngx-mat-select-search>
            </mat-option>
            <mat-option *ngFor="let documentType of filteredDocumentTypeList" [value]="documentType">
              {{documentType}}
            </mat-option>
            <button mat-button color="primary" class="clear-selection-btn"
                    [disabled]="!documentTypeFormControl.value?.length"
                    (click)="documentTypeFormControl.reset([])">
              {{'GENERAL.FILTERS.ACTIONS.CLEAR_SELECTION' | translate}}
            </button>
          </ng-container>
          <ng-template #loadingDocumentType>
            <mat-option disabled>
              <div class="filters-loading-container">
                <span>{{'GENERAL.FILTERS.LOADING' | translate}}</span>
                <mat-spinner diameter="20"></mat-spinner>
              </div>
            </mat-option>
          </ng-template>
        </mat-select>
      </mat-form-field>

      <!-- name -->
      <mat-form-field appearance="outline">
        <mat-label>{{ 'DOCUMENTS.FIELDS.NAME' | translate }}</mat-label>
        <input matInput type="text" [formControl]="nameFormControl" data-cy="input-name" required>
        <mat-error *ngIf="documentForm.get('name')?.errors" data-cy="input-name-errors">
          {{ 'DOCUMENTS.WRITE.ERRORS.NAME_IS_REQUIRED' | translate }}
        </mat-error>
      </mat-form-field>

      <!-- description -->
      <mat-form-field appearance="outline">
        <mat-label>{{ 'DOCUMENTS.FIELDS.DESCRIPTION' | translate }}</mat-label>
        <input matInput type="text" [formControl]="descriptionFormControl" data-cy="input-description" required>
        <mat-error *ngIf="documentForm.get('description')?.errors" data-cy="input-description-errors">
          {{ 'DOCUMENTS.WRITE.ERRORS.DESCRIPTION_IS_REQUIRED' | translate }}
        </mat-error>
      </mat-form-field>

      <!-- accountID -->
      <mat-form-field appearance="outline">
        <mat-label>{{ 'DOCUMENTS.FIELDS.ACCOUNT_ID' | translate }}</mat-label>
        <input matInput type="text" [formControl]="accountIDFormControl" data-cy="input-account-id" required>
        <mat-error *ngIf="documentForm.get('accountID')?.errors" data-cy="input-account-id-errors">
          {{ 'DOCUMENTS.WRITE.ERRORS.ACCOUNT_ID_IS_REQUIRED' | translate }}
        </mat-error>
      </mat-form-field>
      <ngx-dropzone [multiple]="false" data-cy="dropzone" (change)="onSelectFile($event)"
                    [accept]="'image/*, .pdf'" [class.dropzone-disabled]="file" [class.dropzone-files]="true">
        <ngx-dropzone-label>
          <ng-container>
            <mat-icon>cloud_upload</mat-icon>
            <span id="upload-message">{{'UPLOAD.NOTIFICATIONS.MESSAGES.DESKTOPS' | translate}}</span>
          </ng-container>
        </ngx-dropzone-label>
        <ngx-dropzone-preview [removable]="true" (removed)="onRemoveFile()" *ngIf="file">
          <ngx-dropzone-label>{{ file.name }}</ngx-dropzone-label>
        </ngx-dropzone-preview>
      </ngx-dropzone>
    </div>
  </div>


  <div class="dialog-actions" mat-dialog-actions>
    <span fxFlex></span>
    <button mat-flat-button mat-dialog-close id="cancel-btn" [fxShow]="true" [fxHide.xs]="true" [fxHide.sm]="true">
      {{ 'GENERAL.BUTTONS.LABELS.CANCEL' | translate }}
    </button>
    <button mat-flat-button color="primary" id="create-item-btn"
            (click)="onSaveClick()" [disabled]="documentForm.invalid" data-cy="btn-save">
      {{ getButtonAction() | translate }}
    </button>
  </div>

</div>
