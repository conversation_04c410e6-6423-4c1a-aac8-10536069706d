import {Component, Input, On<PERSON><PERSON>es, On<PERSON><PERSON>roy, OnInit} from '@angular/core';
import {Document} from "../../../../entities/document/document";
import {Subject} from "rxjs";
import {DocumentFacade} from "../../../facade/document.facade";
import {finalize, takeUntil} from "rxjs/operators";
import {CsFinConfirmationDialogComponent, CsFinWritePopupType} from "@jumia-cs-fin/common";
import {MatDialog} from "@angular/material/dialog";
import {DocumentsWriteComponent} from "./write/documents-write.component";
import {DetailsDialogComponent} from "./details-dialog/details-dialog.component";
import {
  authParams,
  authParamsCountry,
  bradAuthCountryTarget,
  bradAuthTarget
} from "../../../../auth/constants/auth.constants";
import {NotificationService} from "../../../../api/service/notification.service";
import {HttpErrorResponse} from "@angular/common/http";

@Component({
  selector: 'brad-account-details-document',
  templateUrl: './documents.component.html',
  styleUrls: ['./documents.component.scss', '../../../../../assets/brad-custom.scss']
})
export class DocumentsComponent implements OnInit, OnDestroy, OnChanges  {

  @Input() accountID!: number;

  documentList!: Document[];

  protected readonly bradAuthTarget = bradAuthCountryTarget;
  auth = authParamsCountry;

  isLoading = false;
  private _onDestroy: Subject<void> = new Subject<void>();

  constructor(
    private documentFacade: DocumentFacade,
    private dialog: MatDialog,
    private notificationService: NotificationService
  ) {
  }

  ngOnInit(): void {
    this.isLoading = true;
  }

  ngOnChanges() {
    if(this.accountID){
      this.isLoading = false;
      this.loadDocuments();
    }
  }

  ngOnDestroy(): void {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  loadDocuments(): void {
    this.documentFacade.getAllFromAccount(this.accountID)
      .pipe(takeUntil(this._onDestroy))
      .subscribe((documents: Array<Document>) => {
        this.documentList = documents;
      });
  }

  onCreateClick(): void {
    const dialogRef = this.dialog.open(DocumentsWriteComponent, {
      data: {
        type: CsFinWritePopupType.create,
        accountID: this.accountID
      }
    });

    dialogRef.afterClosed().subscribe(async result => {
      if (result) {
        this.loadDocuments();
      }
    });
  }

  onEditClick(document: Document, event:MouseEvent) {
    event.stopPropagation();
    const dialogRef = this.dialog.open(DocumentsWriteComponent, {
      data: {type: CsFinWritePopupType.update,
        document: document,
        accountID: this.accountID}
    });

    dialogRef.afterClosed().subscribe(async result => {
      if (result) {
        this.loadDocuments();
      }
    });
  }

  onRemoveClick(document: Document, event:MouseEvent) {
    event.stopPropagation();
    const dialogRef = this.dialog.open(CsFinConfirmationDialogComponent, {
      autoFocus: false,
      width: '400px',
      height: '190px',
      panelClass: 'overflow-hidden-dialog',
      data: {
        titleKey: 'CONFIRMATION_DIALOG.MESSAGES.ABOUT_TO_DELETE_DOCUMENT',
        descriptionKey: 'CONFIRMATION_DIALOG.ACTIONS.ARE_YOU_SURE',
        cancelActionKey: 'CONFIRMATION_DIALOG.BUTTONS.NO',
        confirmActionKey: 'CONFIRMATION_DIALOG.BUTTONS.YES'
      }
    });

    dialogRef.afterClosed().subscribe(response => {
      if (response && document.id) {
        this.isLoading = true;
        this.documentFacade.delete(document.id)
          .pipe(
            finalize(() => this.isLoading = false)
          )
          .subscribe({
            next: () => {
              this.loadDocuments();
              this.notificationService.successTranslated('ACCOUNTS.NOTIFICATIONS.DELETE_DOCUMENT_SUCCESS', {document: document.id});
            },
            error: (error:HttpErrorResponse) => {
              this.notificationService.errorWithResponse(error);
            }
          })

      }
    });
  }

  onDocumentCardClick(document: Document, event:MouseEvent): void {
    event.stopPropagation();
    this.dialog.open(DetailsDialogComponent, {
      autoFocus: false,
      minHeight: '370px',
      data: {
        documentID: document.id,
        urlD: document.url,
        name: document.name
      }
    });
  }


}
