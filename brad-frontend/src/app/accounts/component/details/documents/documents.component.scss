.container {
  display: grid;
  gap: 10px 10px;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  margin: 15px;
}

mat-card {
  background-color: white;
  border: darkgray;
  border-radius: 5px;
  display: inline-block;
  position: relative;

  .mat-card-header {
    padding: 13px;
  }



  .btn-edit {
    position: absolute;
    top: 0;
    right: 60px;

    .mat-icon {
      font-size: 20px;
      height: 20px;
      width: 20px;
    }
  }

  .btn-remove {
    position: absolute;
    top: 0;
    right: 20px;

    .mat-icon {
      font-size: 20px;
      height: 20px;
      width: 20px;
    }
  }




  .btn-remove {
    position: absolute;
    top: 0;
    right: 20px;

    .mat-icon {
      font-size: 20px;
      height: 20px;
      width: 20px;
    }
  }

  .btn-add {
    width: 100%;
    height: 100%;

    .mat-icon {
      width: 100%;
      height: 100%;
      font-size: 70px;
      display: flex;
      justify-content: center;
      align-items: center;

    }

  }

  .mat-card-content {
    padding: 10px;
  }

  .field {
    color: darkgray;
    font-size: 12px;
    min-width: 7vw;
    display: inline-block;
    user-select: all;
  }

  .value {
    color: var(--primary-text-color);
    font-weight: bold;
    user-select: all;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
    max-width: 200px;
    overflow: hidden;
  }

  .field-attribute {
    min-width: 7vw;
  }

  .value-attribute {
    max-width: 150px;
  }

  ul {
    list-style-type: none;
    margin: 0;
    padding-left: 0;

    li {
      display: flex;
      flex-direction: row;
    }
  }

  .agent-payment-provider-with-details {
    border-bottom: 1px solid darkgray;
    margin-bottom: 5px;
  }
}

.mat-card:hover {
  background-color: var(--extra-light-color);
  cursor: pointer;
}

.downloadUrl {
  text-decoration: underline;
  cursor: pointer;
}
