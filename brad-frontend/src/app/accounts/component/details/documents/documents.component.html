<main>
  <section class="container">
    <mat-card *ngFor="let document of documentList" (click)="onDocumentCardClick(document, $event)">
      <mat-card-header>

        <button mat-icon-button color="primary" class="btn-edit" (click)="onEditClick(document, $event)"
                aria-label="edit"
                csFinHasPermissionOnAnyTarget
                [authPermissions]="auth.permissions.BRAD_MANAGE_ACCOUNTS"
                [authTarget]="bradAuthTarget"
                [authAction]="auth.actions.DISABLE">
          <mat-icon
                    [matTooltip]="'DOCUMENTS.BUTTONS.LABELS.EDIT' | translate">edit
          </mat-icon>
        </button>
        <button mat-icon-button color="primary" class="btn-remove" aria-label="remove" (click)="onRemoveClick(document, $event)"
                csFinHasPermissionOnAnyTarget
                [authPermissions]="auth.permissions.BRAD_MANAGE_ACCOUNTS"
                [authTarget]="bradAuthTarget"
                [authAction]="auth.actions.DISABLE">
          <mat-icon
            [matTooltip]="'DOCUMENTS.BUTTONS.LABELS.REMOVE' | translate">delete
          </mat-icon>
        </button>
      </mat-card-header>
      <mat-card-content>
        <ul class="document">
          <li>
            <span class="field">{{'DOCUMENTS.FIELDS.DOCUMENT_TYPE' | translate}}</span>
            <span class="value">{{document.documentType}}</span>
          </li>
          <li>
            <span class="field">{{'DOCUMENTS.FIELDS.NAME' | translate}}</span>
            <span class="value">{{document.name}}</span>
          </li>
          <li>
            <span class="field">{{'DOCUMENTS.FIELDS.DESCRIPTION' | translate}}</span>
            <span class="value">{{document.description}}</span>
          </li>
        </ul>
      </mat-card-content>
    </mat-card>
    <mat-card>
        <button mat-icon-button color="primary" class="btn-add" aria-label="add" (click)="onCreateClick()"
                csFinHasPermissionOnAnyTarget
                [authPermissions]="auth.permissions.BRAD_MANAGE_ACCOUNTS"
                [authTarget]="bradAuthTarget"
                [authAction]="auth.actions.DISABLE">
          <mat-icon
                    [matTooltip]="'DOCUMENTS.BUTTONS.LABELS.ADD' | translate">add
          </mat-icon>
        </button>
    </mat-card>
  </section>
</main>
