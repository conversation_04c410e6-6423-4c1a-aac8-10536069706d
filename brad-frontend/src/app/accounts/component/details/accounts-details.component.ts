import {<PERSON><PERSON>iew<PERSON>nit, Component, <PERSON><PERSON><PERSON>roy, OnInit} from '@angular/core';
import {Account} from "../../../entities/account/account";
import {Subject} from "rxjs";
import {ActivatedRoute, Params, Router} from "@angular/router";
import {finalize, takeUntil} from "rxjs/operators";
import {AccountFacade} from "../../facade/account.facade";
import {TransactionFilters} from "../../../entities/transaction/transaction-filters";
import {StatementFilters} from "../../../entities/statement/statement-filters";
import {StatementFacade} from "../../facade/statement.facade";
import {TransactionFacade} from "../../facade/transaction.facade";
import {StatementAdditionalInfoFacade} from "../../facade/statement-additional-info.facade";
import {Statement} from "../../../entities/statement/statement";
import {Currency} from "../../../entities/currency/currency";
import {AccountAdditionalInfo} from "../../../entities/account/account-additional-info";
import {paginationConsts} from "../../../shared/constants/core.constants";
import {HeaderType} from "./header/accounts-detail-header.component";
import {
  authParamsCountry,
  bradAuthCountryTarget
} from "../../../auth/constants/auth.constants";
import {
  activeFiltersSeparator,
  CsFinActiveFilterChip,
  CsFinAddRemoveColumnsFacade,
  CsFinConfirmationDialogComponent,
  CsFinWritePopupType,
} from "@jumia-cs-fin/common";
import {CdkOverlayOrigin} from "@angular/cdk/overlay";
import {
  AddRemoveColumnsAltFacade
} from "../../../shared/component/add-remove-columns-alt/add-remove-columns-alt.facade";
import {NotificationService} from "../../../api/service/notification.service";
import {StatementsWriteComponent} from "./statements/write/statements-write.component";
import {MatDialog} from "@angular/material/dialog";
import {MatTabChangeEvent} from "@angular/material/tabs";

@Component({
  selector: 'brad-account-details',
  templateUrl: './accounts-details.component.html',
  styleUrls: ['./accounts-details.component.scss']
})
export class AccountsDetailsComponent implements OnInit, AfterViewInit, OnDestroy {

  isFullscreen = true;
  isLoading = true;
  accountID!: number;
  detailedAccount!: AccountAdditionalInfo;
  selectedTabIndex: number = 0;
  headerType: HeaderType = HeaderType.TRANSACTION;

  transactionTriggerOrigin!: CdkOverlayOrigin;
  statementTriggerOrigin!: CdkOverlayOrigin;
  firstStatementInError: Statement | null = null;
  readonly auth = authParamsCountry;
  readonly bradAuthTarget = bradAuthCountryTarget;
  readonly HeaderType = HeaderType;
  currentTransactionFilters: TransactionFilters = {};
  currentStatementFilters: StatementFilters = {};

  lastImportedStatement: Statement | null = null;
  showAllUsers = false;
  account!: Account;
  activeTab!: String;

  private _onDestroy: Subject<void> = new Subject<void>();

  transactionActiveFilterChips!: Map<string, CsFinActiveFilterChip>;
  statementActiveFilterChips!: Map<string, CsFinActiveFilterChip>;

  constructor(
    private accountFacade: AccountFacade,
    private router: Router,
    private route: ActivatedRoute,
    private dialog: MatDialog,
    private statementFacade: StatementFacade,
    private transactionFacade: TransactionFacade,
    private statementAdditionalInfoFacade: StatementAdditionalInfoFacade,
    protected addRemoveTransactionColumnsFacade: CsFinAddRemoveColumnsFacade,
    protected addRemoveStatementColumnsFacade: AddRemoveColumnsAltFacade,
    private notificationService: NotificationService
  ) {
  }

  ngOnInit(): void {

    this.route.params
    .pipe(takeUntil(this._onDestroy))
    .subscribe((params: Params) => {
      this.accountID = Number(params['accountID']);
      if (this.accountID != null) {
        this.accountFacade.selectedAccountChangeBehaviorSubject.next(this.accountID);
      }
    });

    this.accountFacade.selectedAccountChangeBehaviorSubject
    .pipe(takeUntil(this._onDestroy))
    .subscribe((accountID: number) => {
      this.accountID = accountID;
      this.loadLastImportedStatement();
      this.loadAccountDetails();
    });

    this.submitStatementFilterChange();
    this.submitTransactionFilterChange();
    this.subscribeTransactionActiveFilterChipsChange();
    this.subscribeStatementActiveFilterChipsChange();
    this.subscribeTransactionActiveFilterChange();
    this.subscribeStatementActiveFilterChange();
  }

  ngAfterViewInit(): void {
    // Force reload to allow table to be displayed correctly
    const reloadCount = localStorage.getItem('reloadCount');

    if (reloadCount != null) {
      let count = parseInt(reloadCount, 10);
      count++;
      localStorage.setItem('reloadCount', count.toString());
    } else {
      localStorage.setItem('reloadCount', '0');
      window.location.reload();
    }
  }

  ngOnDestroy() {
    this._onDestroy.next();
    this._onDestroy.complete();
    this.accountFacade.detailsCloseBehaviorSubject.next(true);
    this.addRemoveTransactionColumnsFacade.isOverlayOpen.next(false);
    this.addRemoveStatementColumnsFacade.isOverlayOpen.next(false);
    localStorage.removeItem('reloadCount');
  }

  private submitStatementFilterChange(): void {
    this.statementFacade.filters$
      .pipe(takeUntil(this._onDestroy))
      .subscribe(async (filters: StatementFilters) => {
        if (filters) {
          this.currentStatementFilters = filters;
        }
      });
  }

  private submitTransactionFilterChange(): void {
    this.transactionFacade.filters$
      .pipe(takeUntil(this._onDestroy))
      .subscribe(async (filters: TransactionFilters) => {
        if (filters) {
          this.currentTransactionFilters = filters;
        }
      });
  }

  private subscribeTransactionActiveFilterChipsChange(): void {
    this.transactionFacade.activeFiltersChips$
      .pipe(takeUntil(this._onDestroy))
      .subscribe((activeFilterChips: Map<string, CsFinActiveFilterChip>) => {
        this.transactionActiveFilterChips = activeFilterChips;
      });
  }

  private subscribeTransactionActiveFilterChange(): void {
    this.transactionFacade.filtersBehaviorSubject
    .pipe(takeUntil(this._onDestroy))
    .subscribe((transactionFilters: TransactionFilters) => {
      this.currentTransactionFilters = transactionFilters;
    });
  }

  private subscribeStatementActiveFilterChipsChange(): void {
    this.statementFacade.activeFiltersChips$
      .pipe(takeUntil(this._onDestroy))
      .subscribe((activeFilterChips: Map<string, CsFinActiveFilterChip>) => {
        this.statementActiveFilterChips = activeFilterChips;
      });
  }

  private subscribeStatementActiveFilterChange(): void {
    this.statementFacade.filtersBehaviorSubject
    .pipe(takeUntil(this._onDestroy))
    .subscribe((statementFilters: StatementFilters) => {
      this.currentStatementFilters = statementFilters;
    });
  }

  triggerTransactionOverlay(trigger: CdkOverlayOrigin): void {
    this.transactionTriggerOrigin = trigger;
    this.addRemoveTransactionColumnsFacade.isOverlayOpen.next(true);
  }

  triggerStatementOverlay(trigger: CdkOverlayOrigin): void {
    this.statementTriggerOrigin = trigger;
    this.addRemoveStatementColumnsFacade.isOverlayOpen.next(true);
  }

  loadAccountDetails(): void {
    if (this.accountID != null) {
      this.isLoading =true;
      this.accountFacade.getById(this.accountID)
        .pipe(takeUntil(this._onDestroy))
        .subscribe((account: Account) => {
          const accountWithAdditionalInfo = {
            ... this.detailedAccount || {},
            ... account
          };
          this.detailedAccount = accountWithAdditionalInfo;
          this.fetchAdditionalInfo();
        });
    }
  }

  fetchAdditionalInfo() {
    if (this.accountID != null) {
      this.accountFacade.getAdditionalInfo(this.accountID)
      .pipe(takeUntil(this._onDestroy))
      .pipe(finalize(() => this.isLoading = false))
      .subscribe((additionalInfo: AccountAdditionalInfo) => {
        const accountWithAdditionalInfo = {
          ...this.detailedAccount,
          ...additionalInfo
        };
        this.detailedAccount = accountWithAdditionalInfo;
      });
    }
  }

  submitTransactionFilters(filters: TransactionFilters) {
    this.currentTransactionFilters = filters;
    this.statementAdditionalInfoFacade.changeTransactionFilters(filters);
    this.transactionFacade.filtersChanged(filters);
  }

  submitStatementFilters(filters: StatementFilters) {
    this.currentStatementFilters = filters;
    this.statementAdditionalInfoFacade.changeStatementFilters(filters);
    this.statementFacade.filtersChanged(filters);
  }

  onRedirectTab(tabIndex: number) {
    this.selectedTabIndex = tabIndex;
    if (tabIndex == 0) {
      this.headerType = HeaderType.TRANSACTION;
    } else {
      this.headerType = HeaderType.STATEMENT;
    }
  }

  loadLastImportedStatement(): void {
    if (this.accountID != null) {
      this.statementFacade.getLastImportedStatement(this.accountID)
      .pipe(takeUntil(this._onDestroy))
      .subscribe({
        next: (statement: Statement) => {
          this.lastImportedStatement = statement;
        }
      });
    }
  }

  onErrorClick() {
    this.router.navigateByUrl(`troubleshooting/statements/${this.detailedAccount.id}`);
  }

  reactStatementSelect(event: Statement) {
    this.currentTransactionFilters.page = 1;
    this.currentTransactionFilters.size = paginationConsts.defaultPageSize;
    this.currentTransactionFilters.accountStatementID = event.id;
    this.currentTransactionFilters.partitionKey = event.accountID;
    this.submitTransactionFilters({...this.currentTransactionFilters});
    this.selectedTabIndex = 0;
  }


  onTransactionActiveFilterRemoveClick(removedChipKey: string): void {
    const filtersToRemove = removedChipKey.split(activeFiltersSeparator);
    if (filtersToRemove) {
      filtersToRemove.forEach((filterKey: string) => {
        // @ts-ignore
        delete this.currentTransactionFilters[filterKey];
      })
    }
    if (filtersToRemove.includes("valueDateStart")) {
      delete this.currentTransactionFilters.valueDateEnd;
    }
    if (filtersToRemove.includes("transactionDateStart")) {
      delete this.currentTransactionFilters.transactionDateEnd;
    }
    if (filtersToRemove.includes("statementDateStart")) {
      delete this.currentTransactionFilters.statementDateEnd;
    }
    if (filtersToRemove.includes("createdAtStart")) {
      delete this.currentTransactionFilters.createdAtEnd;
    }

    this.statementAdditionalInfoFacade.changeTransactionFilters(this.currentTransactionFilters);

  }

  onStatementActiveFilterRemoveClick(removedChipKey: string): void {
    if (removedChipKey === this.statementFacade.accountIDKey) {
      return;
    }
    const filtersToRemove = removedChipKey.split(activeFiltersSeparator);
    if (filtersToRemove) {
      filtersToRemove.forEach((filterKey: string) => {
        // @ts-ignore
        delete this.currentStatementFilters[filterKey];
      })
    }
    if (filtersToRemove.includes("initialDateStart")) {
      delete this.currentStatementFilters.initialDateEnd;
    }
    if (filtersToRemove.includes("finalDateStart")) {
      delete this.currentStatementFilters.finalDateEnd;
    }
    if (filtersToRemove.includes("createdAtStart")) {
      delete this.currentStatementFilters.createdAtEnd;
    }
    this.statementAdditionalInfoFacade.changeStatementFilters(this.currentStatementFilters);
  }

  onDownloadClick(headerType: HeaderType): void {
    if (headerType == HeaderType.TRANSACTION) {
      this.currentTransactionFilters.partitionKey = String(this.detailedAccount.id);
      this.currentTransactionFilters.importedStatementOnly = true;
      this.transactionFacade.download(this.currentTransactionFilters)
        .subscribe({
          next: () => {
            this.notificationService.successTranslated('TRANSACTIONS.NOTIFICATIONS.DOWNLOAD_TRANSACTIONS_SUCCESS', {});
          },
          error: (error) => {
            this.notificationService.errorWithResponse(error);
          }
        });
    } else if (headerType == HeaderType.STATEMENT) {
      this.currentStatementFilters.partitionKey = String(this.detailedAccount.id);
      this.currentStatementFilters.status = ['IMPORTED'];
      this.statementFacade.download(this.currentStatementFilters)
        .subscribe({
          next: () => {
            this.notificationService.successTranslated('STATEMENTS.NOTIFICATIONS.MESSAGES.DOWNLOAD_STATEMENTS_SUCCESS', {});
          },
          error: (error) => {
            this.notificationService.errorWithResponse(error);
          }
        });
    }
  }

  onUploadClick(): void {
    if (this.detailedAccount?.hasError) {
      const dialogRef = this.dialog.open(CsFinConfirmationDialogComponent, {
        autoFocus: false,
        width: '600px',
        panelClass: 'overflow-hidden-dialog',
        data: {
          titleKey: 'CONFIRMATION_DIALOG.MESSAGES.ABOUT_TO_UPLOAD_WITH_STATEMENTS_IN_ERROR',
          descriptionKey: 'CONFIRMATION_DIALOG.ACTIONS.REDIRECT_TROUBLESHOOT',
          cancelActionKey: 'GENERAL.BUTTONS.TOOLTIPS.CLOSE',
          confirmActionKey: 'CONFIRMATION_DIALOG.BUTTONS.REDIRECT'
        }
      });

      dialogRef.afterClosed().subscribe(async response => {
        if (response) {
          if (this.detailedAccount?.hasError) {
            this.statementFacade.getFirstError(this.accountID)
              .subscribe((statement: Statement) => {
                this.firstStatementInError = statement;
                this.router.navigate(['/troubleshooting'], {queryParams: {navReference: this.detailedAccount.navReference}});
              }, error => {
                this.notificationService.error(error);
              });
          } else {
            this.uploadPopup();
          }
        }
      });
    } else {
      this.uploadPopup();
    }
  }

  uploadPopup(): void {

    const dialogRef = this.dialog.open(StatementsWriteComponent, {
      data: {
        type: CsFinWritePopupType.create,
        accountID: this.accountID,
        account: this.detailedAccount,
        nextStatement: this.firstStatementInError,
        previousStatement: this.lastImportedStatement
      }
    });

    let subscription = dialogRef.afterClosed().subscribe(() => {
      this.refreshAdditionalInfoEvent();
    });

    dialogRef.afterClosed().subscribe(async result => {
      if (result) {
        this.submitStatementFilters(this.currentStatementFilters);
      }
      subscription.unsubscribe();
    });
  }


  refreshAdditionalInfoEvent(): void {
    this.fetchAdditionalInfo();
  }

  goToReconciliation() {
    this.router.navigateByUrl(`/reconcile`);
  }
  tabChanged(tabChangeEvent: MatTabChangeEvent): void {
    this.activeTab= tabChangeEvent.tab.textLabel
  }

}
