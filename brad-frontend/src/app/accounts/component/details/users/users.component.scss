.container {
  display: grid;
  gap: 5px 5px;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  margin: 15px;
  cursor: pointer;
}

mat-card {
  background-color: white;
  border: darkgray;
  border-radius: 5px;
  display: inline-block;
  position: relative;

  .mat-card-header {
    padding: 13px;
  }



  .btn-user-edit {
    position: absolute;
    bottom: 0;
    right: 45px;

    .mat-icon {
      font-size: 20px;
      height: 18px;
      width: 18px;
    }
  }

  .btn-user-remove {
    position: absolute;
    bottom: 0;
    right: 5px;

    .mat-icon {
      font-size: 20px;
      height: 18px;
      width: 18px;
    }
  }


  .btn-add {
    width: 100%;
    height: 100%;

    .mat-icon {
      width: 100%;
      height: 100%;
      font-size: 70px;
      display: flex;
      justify-content: center;
      align-items: center;

    }

  }

  .mat-card-content {
    padding: 10px;
  }

  .field {
    color: darkgray;
    font-size: 12px;
    min-width: 7vw;
    display: inline-block;
    user-select: all;
  }

  .value {
    color: var(--primary-text-color);
    font-weight: bold;
    user-select: all;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
    max-width: 200px;
    overflow: hidden;
  }

  .field-attribute {
    min-width: 7vw;
  }

  .value-attribute {
    max-width: 150px;
  }

  ul {
    list-style-type: none;
    margin: 0;
    padding-left: 0;

    li {
      display: flex;
      flex-direction: row;
    }
  }

  .agent-payment-provider-with-details {
    border-bottom: 1px solid darkgray;
    margin-bottom: 5px;
  }
}

.mat-card:hover {
  background-color: var(--extra-light-color);
  cursor: pointer;
}

.full-content {
  display: flex;
  flex-direction: column;
  padding: 0 20px;
}

.green-bg {
  background-color: #CCE8BE;
  border-radius: 2px;
  padding: 1px 3px;
}

.red-bg {
  background-color: #FCBABA;
  border-radius: 2px;
  padding: 1px 3px;
}

.dot-info {
  position: absolute;
  top: 8px;
  right: 18px;
  height: 18px;
  width: 18px;
  border-radius: 45%;
  margin-right: 7px;
}
.dot-details {
  position: absolute;
  top: 23px;
  height: 18px;
  width: 18px;
  border-radius: 50%;
  margin-left: 10px;
}

.green-dot {
  background-color: #4CC897;
}

.red-dot {
  background-color: #EF4F2B;
}
