import {Component, Inject} from '@angular/core';
import {User} from "../../../../../entities/user/user";
import {MAT_DIALOG_DATA} from "@angular/material/dialog";

@Component({
  selector: 'brad-users-details-dialog',
  templateUrl: './users-details-dialog.component.html',
  styleUrls: ['./users-details-dialog.component.scss']
})
export class UsersDetailsDialogComponent {
  user!: User;

  constructor(@Inject(MAT_DIALOG_DATA) public data: {user: User}) { }

  ngOnInit(): void {
    this.user = this.data.user;
  }
}
