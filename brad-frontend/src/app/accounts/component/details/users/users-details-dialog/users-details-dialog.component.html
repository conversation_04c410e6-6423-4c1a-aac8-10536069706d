<section class="details-dialog">
  <section mat-dialog-title class="header">
    <span class="title">
      {{'USERS.DIALOG.TITLE' | translate}}
         <mat-icon class="dot-details"
                   [ngClass]="{'green-dot': user.status ==='ACTIVE',
                     'red-dot' : user.status ==='INACTIVE' }">
      </mat-icon>
    </span>
  </section>
  <mat-dialog-content fxLayout="row">
    <section>
      <section class="info">
        <ul>
          <li>
            <span class="field">{{'USERS.FIELDS.ID' | translate}}</span>
            <span class="value">{{user.id}}</span>
          </li>
          <li>
            <span class="field">{{'USERS.FIELDS.USER_NAME' | translate}}</span>
            <span class="value">{{user.userName}}</span>
          </li>
          <li>
            <span class="field">{{'USERS.FIELDS.EMAIL' | translate}}</span>
            <span class="value">{{user.email}}</span>
          </li>
          <li>
            <span class="field">{{'USERS.FIELDS.HR_ROLE' | translate}}</span>
            <span class="value">{{user.hrRole}}</span>
          </li>
          <li>
            <span class="field">{{'USERS.FIELDS.PERMISSION_TYPE' | translate}}</span>
            <span class="value">{{user.permissionType}}</span>
          </li>
          <li>
            <span class="field">{{'USERS.FIELDS.MOBILE_PHONE_NUMBER' | translate}}</span>
            <span class="value">{{user.mobilePhoneNumber}}</span>
          </li>
        </ul>
        <ul>
          <li>
            <span class="field" data-cy="user-details-field-created-at">{{'GENERAL.FIELDS.CREATED_AT' | translate}}: </span>
            <span class="value" data-cy="user-details-value-created-at">{{user.createdAt | date:'short'}}</span>
          </li>
          <li>
            <span class="field" data-cy="user-details-field-created-by">{{'GENERAL.FIELDS.CREATED_BY' | translate}}: </span>
            <span class="value" data-cy="user-details-value-created-by">{{user.createdBy}}</span>
          </li>
          <li>
            <span class="field" data-cy="user-details-field-updated-at">{{'GENERAL.FIELDS.UPDATED_AT' | translate}}: </span>
            <span class="value" data-cy="user-details-value-updated-at">{{user.updatedAt | date:'short'}}</span>
          </li>
          <li>
            <span class="field" data-cy="user-details-field-updated-by">{{'GENERAL.FIELDS.UPDATED_BY' | translate}}: </span>
            <span class="value" data-cy="user-details-value-updated-by">{{user.updatedBy}}</span>
          </li>
        </ul>
      </section>
    </section>


  </mat-dialog-content>
  <mat-dialog-actions align="end">
    <button mat-flat-button mat-dialog-close id="cancel-btn" color="primary" style="color:white" fxShow [fxShow.xs]="false"
            [fxShow.sm]="false">{{'GENERAL.BUTTONS.TOOLTIPS.CLOSE' | translate}}</button>
  </mat-dialog-actions>
</section>
