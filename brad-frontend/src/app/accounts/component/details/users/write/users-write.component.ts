import {Component, Inject, OnInit} from '@angular/core';
import {FormControl, FormGroup, Validators} from "@angular/forms";
import {Subject} from "rxjs";
import {MAT_DIALOG_DATA, MatDialogRef} from "@angular/material/dialog";
import {UserFacade} from "../../../../facade/user.facade";
import {finalize, takeUntil} from "rxjs/operators";
import {CsFinWritePopupType} from "@jumia-cs-fin/common";
import {User} from "../../../../../entities/user/user";
import {NotificationService} from "../../../../../api/service/notification.service";
import {HttpErrorResponse} from "@angular/common/http";

@Component({
  selector: 'brad-users-write',
  templateUrl: './users-write.component.html',
  styleUrls: ['./users-write.component.scss']
})
export class UsersWriteComponent implements OnInit{

  userForm!: FormGroup;
  userNameFormControl!: FormControl;
  emailFormControl!: FormControl;
  accountIDFormControl!: FormControl;
  hrRoleFormControl!: FormControl;
  permissionTypeFormControl!: FormControl;
  mobilePhoneNumberFormControl!: FormControl;
  statusFormControl!: FormControl;

  userTypeSearchFormControl = new FormControl();
  userTypeList:String[] = [];
  filteredUserTypeList:String[] = [];
  statusList: String[] = ['ACTIVE', 'INACTIVE'];

  isUserTypeLoading = true;
  isSavingData = false;
  private _onDestroy: Subject<void> = new Subject<void>();

  constructor(public dialogRef: MatDialogRef<UsersWriteComponent>,
              @Inject(MAT_DIALOG_DATA) public data: {
                type: CsFinWritePopupType,
                user: User | null,
                accountID: number
              },
              private notificationService: NotificationService,
              private userFacade: UserFacade) { }

  ngOnInit(): void {
    this.userForm = new FormGroup({});
    this.initFiltersSearch();
    this.initFormControls();
    this.setFormControlsToForm();
    this.accountIDFormControl.setValue(this.data.accountID);
    this.accountIDFormControl.disable();
    if (this.data.user?.id != null) {
      this.loadUserAndSetFormGroup(this.data.user.id);
    }
  }

  ngOnDestroy(): void {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  private initFiltersSearch(): void {
    this.initUserTypeSearch();
  }

  private initUserTypeSearch(): void {
    this.userTypeSearchFormControl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .pipe(
        finalize(() => this.isUserTypeLoading = false)
      )
      .subscribe((value: string) => {

        const searchTerm = value.trim();
        if (searchTerm) {
          this.filteredUserTypeList = this.userTypeList.filter((userType) => {
            return userType.trim().toLowerCase().includes(searchTerm.toLowerCase());
          })
        } else {
          this.filteredUserTypeList = this.userTypeList;
        }
      });
  }

  initFormControls(): void {
    this.userNameFormControl = new FormControl('', [Validators.required]);
    this.emailFormControl = new FormControl('', [Validators.required]);
    this.accountIDFormControl = new FormControl('', [Validators.required]);
    this.hrRoleFormControl = new FormControl('', [Validators.required]);
    this.permissionTypeFormControl = new FormControl('', [Validators.required]);
    this.mobilePhoneNumberFormControl = new FormControl('');
    this.statusFormControl = new FormControl('ACTIVE', [Validators.required]);

  }

  setFormControlsToForm(): void {
    this.userForm.addControl('userName', this.userNameFormControl);
    this.userForm.addControl('email', this.emailFormControl);
    this.userForm.addControl('accountID', this.accountIDFormControl);
    this.userForm.addControl('hrRole', this.hrRoleFormControl);
    this.userForm.addControl('permissionType', this.permissionTypeFormControl);
    this.userForm.addControl('mobilePhoneNumber', this.mobilePhoneNumberFormControl);
    this.userForm.addControl('status', this.statusFormControl);
  }

  loadUserAndSetFormGroup(id: number): void {
    this.userFacade.getById(id)
      .subscribe({
        next: (user) => {
          this.userNameFormControl.setValue(user.userName);
          this.emailFormControl.setValue(user.email);
          this.accountIDFormControl.setValue(user.accountID);
          this.hrRoleFormControl.setValue(user.hrRole);
          this.permissionTypeFormControl.setValue(user.permissionType);
          this.mobilePhoneNumberFormControl.setValue(user.mobilePhoneNumber);
          this.statusFormControl.setValue(user.status);
        },
        error: (error:HttpErrorResponse) => {
          this.notificationService.errorWithResponse(error);
        }
      });
  }

  isCreateView(): boolean {
    return this.data.type === CsFinWritePopupType.create;
  }

  isEditView(): boolean {
    return this.data.type === CsFinWritePopupType.update;
  }

  buildUserRequest(): User {
    const userReq: User = {
      userName: this.userNameFormControl.value,
      email: this.emailFormControl.value,
      accountID: this.accountIDFormControl.value,
      hrRole: this.hrRoleFormControl.value,
      permissionType: this.permissionTypeFormControl.value,
      mobilePhoneNumber: this.mobilePhoneNumberFormControl.value,
      status: this.statusFormControl.value
    };
    if (this.isEditView() && this.data.user) {
      userReq.id = this.data.user.id;
    }
    return userReq;
  }

  onSaveClick(): void {

    const userReq = this.buildUserRequest();
    if (this.isCreateView()) {
      this.doCreate(userReq);
    } else if (this.isEditView()) {
      this.doUpdate(userReq);
    }

  }

  doCreate(user: User): void {
    this.isSavingData = true;
    this.userFacade.create(user)
      .pipe(
        finalize(() => this.isSavingData = false)
      )
      .subscribe({
        next: () => {
          this.notificationService.successTranslated('USERS.NOTIFICATIONS.CREATE_USER_SUCCESS', {name: user.userName});
          this.dialogRef.close(true);
        },
        error: (error:HttpErrorResponse) => {
          this.notificationService.errorWithResponse(error);
        }
      });
  }

  doUpdate(user: User): void {

    this.isSavingData = true;
    if (this.data.user?.id) {
      this.userFacade.update(this.data.user.id, user)
        .pipe(
          finalize(() => this.isSavingData = false)
        )
        .subscribe({
          next: () => {
            this.notificationService.successTranslated('USERS.NOTIFICATIONS.UPDATE_USER_SUCCESS', {name: user.userName});
            this.dialogRef.close(true);
          },
          error: (error:HttpErrorResponse) => {
            this.notificationService.errorWithResponse(error);
          }
        });
    }
  }

  getButtonAction() {
    return this.isCreateView() ? 'GENERAL.BUTTONS.LABELS.CREATE' : 'GENERAL.BUTTONS.LABELS.SAVE';
  }

  getTitle() {
    return this.isCreateView() ? 'USERS.CREATE.TITLE' : 'USERS.EDIT.TITLE';
  }

}
