<div class="dialog-container">
  <header class="dialog-header">
    <span class="title" mat-dialog-title>{{getTitle() | translate}}</span>
  </header>


  <div class="dialog-content">
    <div class="form-container">
      <!-- name -->
      <mat-form-field appearance="outline">
        <mat-label>{{ 'USERS.FIELDS.USER_NAME' | translate }}</mat-label>
        <input matInput type="text" [formControl]="userNameFormControl" data-cy="input-user-name" required>
        <mat-error *ngIf="userForm.get('userName')?.errors" data-cy="input-user-name-errors">
          {{ 'USERS.WRITE.ERRORS.USER_NAME_IS_REQUIRED' | translate }}
        </mat-error>
      </mat-form-field>

      <!-- email -->
      <mat-form-field appearance="outline">
        <mat-label>{{ 'USERS.FIELDS.EMAIL' | translate }}</mat-label>
        <input matInput type="text" [formControl]="emailFormControl" data-cy="input-email" required>
        <mat-error *ngIf="userForm.get('email')?.errors" data-cy="input-email-errors">
          {{ 'USERS.WRITE.ERRORS.EMAIL_IS_REQUIRED' | translate }}
        </mat-error>
      </mat-form-field>

      <!-- accountID -->
      <mat-form-field appearance="outline">
        <mat-label>{{ 'USERS.FIELDS.ACCOUNT_ID' | translate }}</mat-label>
        <input matInput type="text" [formControl]="accountIDFormControl" data-cy="input-account-id" required>
        <mat-error *ngIf="userForm.get('accountID')?.errors" data-cy="input-account-id-errors">
          {{ 'USERS.WRITE.ERRORS.ACCOUNT_ID_IS_REQUIRED' | translate }}
        </mat-error>
      </mat-form-field>

      <!-- HR Role -->
      <mat-form-field appearance="outline">
        <mat-label>{{ 'USERS.FIELDS.HR_ROLE' | translate }}</mat-label>
        <input matInput type="text" [formControl]="hrRoleFormControl" data-cy="input-hr-role" required>
        <mat-error *ngIf="userForm.get('hrRole')?.errors" data-cy="input-hr-role-errors">
          {{ 'USERS.WRITE.ERRORS.HR_ROLE_IS_REQUIRED' | translate }}
        </mat-error>
      </mat-form-field>

      <!-- Permission Type -->
      <mat-form-field appearance="outline">
        <mat-label>{{ 'USERS.FIELDS.PERMISSION_TYPE' | translate }}</mat-label>
        <input matInput type="text" [formControl]="permissionTypeFormControl" data-cy="input-permission-type" required>
        <mat-error *ngIf="userForm.get('permissionType')?.errors" data-cy="input-permission-type-errors">
          {{ 'USERS.WRITE.ERRORS.PERMISSION_TYPE_IS_REQUIRED' | translate }}
        </mat-error>
      </mat-form-field>

      <!-- Mobile Phone Number -->
      <mat-form-field appearance="outline">
        <mat-label>{{ 'USERS.FIELDS.MOBILE_PHONE_NUMBER' | translate }}</mat-label>
        <input matInput type="text" [formControl]="mobilePhoneNumberFormControl" data-cy="input-mobile-phone-number">
      </mat-form-field>

      <!-- Status -->
      <mat-form-field appearance="outline" data-cy="input-user_status">
        <mat-label>{{ 'USERS.FIELDS.STATUS' | translate }}</mat-label>

        <mat-select [formControl]="statusFormControl" required>
            <mat-option *ngFor="let status of statusList" [value]="status">
              {{status}}
            </mat-option>
            <button mat-button color="primary" class="clear-selection-btn"
                    [disabled]="!statusFormControl.value?.length"
                    (click)="statusFormControl.reset([])">
              {{'GENERAL.FILTERS.ACTIONS.CLEAR_SELECTION' | translate}}
            </button>
        </mat-select>
      </mat-form-field>
    </div>
  </div>


  <div class="dialog-actions" mat-dialog-actions>
    <span fxFlex></span>
    <button mat-flat-button mat-dialog-close id="cancel-btn" [fxShow]="true" [fxHide.xs]="true" [fxHide.sm]="true">
      {{ 'GENERAL.BUTTONS.LABELS.CANCEL' | translate }}
    </button>
    <button mat-flat-button color="primary" id="create-item-btn"
            (click)="onSaveClick()" [disabled]="userForm.invalid || isSavingData" data-cy="btn-save">
      {{ getButtonAction() | translate }}
    </button>
  </div>

</div>
