import {Component, Input, <PERSON><PERSON><PERSON><PERSON>, On<PERSON><PERSON>roy, OnInit} from '@angular/core';
import {User} from "../../../../entities/user/user";
import {Subject} from "rxjs";
import {UserFacade} from "../../../facade/user.facade";
import {finalize, takeUntil} from "rxjs/operators";
import {CsFinConfirmationDialogComponent, CsFinWritePopupType} from "@jumia-cs-fin/common";
import {MatDialog} from "@angular/material/dialog";
import {UsersWriteComponent} from "./write/users-write.component";
import {UsersDetailsDialogComponent} from "./users-details-dialog/users-details-dialog.component";

import {
  authParamsCountry,
  bradAuthCountryTarget
} from "../../../../auth/constants/auth.constants";
import {NotificationService} from "../../../../api/service/notification.service";
import {HttpErrorResponse} from "@angular/common/http";
import {UserFilters} from "../../../../entities/user/user-filters";
import {PageResponse} from "../../../../entities/page-response";

@Component({
  selector: 'brad-account-details-user',
  templateUrl: './users.component.html',
  styleUrls: ['./users.component.scss', '../../../../../assets/brad-custom.scss']
})
export class UsersComponent implements OnInit, OnDestroy, OnChanges  {

  @Input() accountID!: number;
  @Input() showAllUsers!: boolean;

  userList!: User[];
  private filters: UserFilters = { //todo change this when adding proper pagination
    page:1,
    size:1000
  };

  protected readonly bradAuthTarget = bradAuthCountryTarget;
  auth = authParamsCountry;

  isLoading = false;
  private _onDestroy:Subject<void> = new Subject<void>();


  constructor(
    private userFacade: UserFacade,
    private dialog: MatDialog,
    private notificationService: NotificationService
  ) {
  }

  ngOnInit(): void {
    this.isLoading = true;
  }

  ngOnChanges() {
    this.filters.status = !this.showAllUsers ? 'ACTIVE' : undefined;
    if (this.accountID) {
      this.filters.accountID = this.accountID;
      this.isLoading = false;
      this.loadUsers();
    }
  }

  ngOnDestroy(): void {
    this._onDestroy.next();
    this._onDestroy.complete();
  }


  loadUsers(): void {
    this.userFacade.getAll(this.filters)
      .pipe(takeUntil(this._onDestroy))
      .subscribe((response: PageResponse<User>) => {
        this.userList = response.results;
      });
  }

  onCreateClick(): void {
    const dialogRef = this.dialog.open(UsersWriteComponent, {
      data: {
        type: CsFinWritePopupType.create,
        accountID: this.accountID
      }
    });

    dialogRef.afterClosed().subscribe(async result => {
      if (result) {
        this.loadUsers();
      }
    });
  }

  onEditClick(user: User, event: MouseEvent) {
    event.stopPropagation();
    const dialogRef = this.dialog.open(UsersWriteComponent, {
      data: {
        type: CsFinWritePopupType.update,
        user: user,
        accountID: this.accountID}
    });

    dialogRef.afterClosed().subscribe(async result => {
      if (result) {
        this.loadUsers();
      }
    });
  }

  onRemoveClick(user: User, event:MouseEvent) {
    event.stopPropagation();
    const dialogRef = this.dialog.open(CsFinConfirmationDialogComponent, {
      autoFocus: false,
      width: '400px',
      height: '190px',
      panelClass: 'overflow-hidden-dialog',
      data: {
        titleKey: 'CONFIRMATION_DIALOG.MESSAGES.ABOUT_TO_DELETE_USER',
        descriptionKey: 'CONFIRMATION_DIALOG.ACTIONS.ARE_YOU_SURE',
        cancelActionKey: 'CONFIRMATION_DIALOG.BUTTONS.NO',
        confirmActionKey: 'CONFIRMATION_DIALOG.BUTTONS.YES'
      }
    });

    dialogRef.afterClosed().subscribe(response => {
      if (response && user.id) {
        this.isLoading = true;
        this.userFacade.delete(user.id)
          .pipe(
            finalize(() => this.isLoading = false)
          )
          .subscribe({
            next: () => {
              this.loadUsers();
              this.notificationService.successTranslated('USERS.NOTIFICATIONS.DELETE_USER_SUCCESS', {name: user.userName});
            },
            error: (error:HttpErrorResponse) => {
              this.notificationService.errorWithResponse(error);
            }
          })

      }
    });
  }

  onUserCardClick(user: User, event:MouseEvent): void {
    event.stopPropagation();
    this.dialog.open(UsersDetailsDialogComponent, {
      autoFocus: false,
      minHeight: '370px',
      data: {
        user: user
      }
    });
  }

}
