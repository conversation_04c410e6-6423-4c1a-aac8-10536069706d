<main>
  <section class="container">
    <mat-card *ngFor="let user of userList" (click)="onUserCardClick(user, $event)">
      <mat-card-header>
        <div>
          <mat-icon class="dot-info"
                    [ngClass]="{'green-dot': user.status ==='ACTIVE',
                     'red-dot' : user.status ==='INACTIVE' }">
          </mat-icon>
        </div>
        <button mat-icon-button color="primary" class="btn-user-edit" (click)="onEditClick(user, $event)"
                aria-label="edit" csFinHasPermissionOnAnyTarget
                [authPermissions]="auth.permissions.BRAD_MANAGE_ACCOUNTS"
                [authTarget]="bradAuthTarget"
                [authAction]="auth.actions.DISABLE">
          <mat-icon
            [matTooltip]="'USERS.BUTTONS.LABELS.EDIT' | translate">edit
          </mat-icon>
        </button>
        <button mat-icon-button color="primary" class="btn-user-remove" aria-label="remove" (click)="onRemoveClick(user, $event)"
                csFinHasPermissionOnAnyTarget
                [authPermissions]="auth.permissions.BRAD_MANAGE_ACCOUNTS"
                [authTarget]="bradAuthTarget"
                [authAction]="auth.actions.DISABLE">
          <mat-icon
            [matTooltip]="'USER.BUTTONS.LABELS.REMOVE' | translate">delete
          </mat-icon>
        </button>
      </mat-card-header>
      <mat-card-content>
        <ul class="user">
          <li>
            <span class="field">{{'USERS.FIELDS.USER_NAME' | translate}}</span>
            <span class="value">{{user.userName}}</span>
          </li>
          <li>
            <span class="field">{{'USERS.FIELDS.EMAIL' | translate}}</span>
            <span class="value">{{user.email}}</span>
          </li>
          <li>
            <span class="field">{{'USERS.FIELDS.HR_ROLE' | translate}}</span>
            <span class="value">{{user.hrRole}}</span>
          </li>
          <li>
            <span class="field">{{'USERS.FIELDS.PERMISSION_TYPE' | translate}}</span>
            <span class="value">{{user.permissionType}}</span>
          </li>
        </ul>
      </mat-card-content>
    </mat-card>
    <mat-card>
      <button mat-icon-button color="primary" class="btn-add" aria-label="add" (click)="onCreateClick()"
              csFinHasPermissionOnAnyTarget
              [authPermissions]="auth.permissions.BRAD_MANAGE_ACCOUNTS"
              [authTarget]="bradAuthTarget"
              [authAction]="auth.actions.DISABLE">
        <mat-icon
          [matTooltip]="'USERS.BUTTONS.LABELS.ADD' | translate">add
        </mat-icon>
      </button>
    </mat-card>
  </section>
</main>
