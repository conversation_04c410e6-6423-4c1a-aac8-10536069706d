@import 'node_modules/@jumia-cs-fin/common/assets/styles/details-components';
@import "node_modules/@jumia-cs-fin/common/assets/styles/list-components";

.main{
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 100%;
}

.details {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

mat-card:last-child {
  margin: 10px 0;
}

::ng-deep.mat-mdc-tab-header {
  max-width: 100% !important;
}

::ng-deep.mdc-tab__text-label {
  text-transform: uppercase;
}

.export-btn {
  min-width: fit-content;
  width: fit-content;
  border-color: var(--primary-color);
  margin-left: 12px;

  mat-icon {
    margin: 0 !important;
  }
}

.change-columns-btn {
  min-width: 48px;
  width: 48px;
  padding: 0 8px;
  margin-left: 12px;

  &:not(.mat-mdc-button-disabled) {
    border-color: var(--primary-color);
  }

  mat-icon {
    transform: rotate(90deg);
    margin: auto;
  }
}

.actions .mat-mdc-raised-button{
  color: white;
}

.statement-button {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: row;

  .warning-icon {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    margin-left: 0.5rem;
    color: #f8b27b;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    transition: background-color 0.2s;
  }

  .warning-icon:hover {
    background-color: #fdede2;
    border-radius: 50%;
    transition: background-color 0.2s;
  }

}


