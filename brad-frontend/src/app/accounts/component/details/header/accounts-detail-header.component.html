<mat-toolbar id="header-toolbar">
  <div id="ba-info" *ngIf="detailedAccount">
    <button (click)="return()" class="md-primary return-button" mat-button>
      <mat-icon id="back_bar_icon">keyboard_backspace</mat-icon>
    </button>
    <span class="page-title">{{ detailedAccount.navReference }}</span>
    <mat-card class="status-card" [ngClass]="detailedAccount.status == 'OPEN' ? 'open-status' :
         detailedAccount.status == 'CLOSED' ? 'closed-status' :  detailedAccount.status == 'DORMANT'? 'dormant-status':'to-be-closed-status'">
      <span>{{ detailedAccount.status }}</span>
    </mat-card>
  </div>
  <span fxFlex></span>
  <section class="search-bar" [formGroup]="form">
    <!-- filter text (code) -->
    <mat-form-field id="search" floatLabel="always" class="change-header" appearance="outline" fxShow [fxShow.xs]="false" [fxShow.sm]="false"
                    cdkOverlayOrigin #trigger="cdkOverlayOrigin">
      <mat-icon matPrefix>search</mat-icon>
      <mat-label class="theLabel">{{'RECONCILIATION.DETAILS.SEARCH_BAR_GENERIC' | translate}} {{currentHeader}}</mat-label>
      <input type="text" #input matInput [formControl]="filterTextFormControl"
             (keyup.enter)="submit()">
      <mat-icon matSuffix id="filters" (click)="triggerOverlay()">filter_list</mat-icon>
    </mat-form-field>


    <button mat-raised-button color="primary" id="apply-search-btn" class="raised-primary-btn" fxShow [fxShow.xs]="false"
            [fxShow.sm]="false" (click)="submit()">
      {{ 'GENERAL.BUTTONS.LABELS.SEARCH' | translate }}
    </button>
  </section>
  <span fxFlex></span>

  <ng-template cdkConnectedOverlay class="template"
               [cdkConnectedOverlayHasBackdrop]="true"
               [cdkConnectedOverlayOrigin]="trigger"
               [cdkConnectedOverlayOpen]="(isOpen$ | async) || false">
    <div class="filters-overlay">
      <brad-transactions-header class="headers" *ngIf="currentHeader==HeaderType.TRANSACTION"
                                [accountID]="detailedAccount.id"
                                (submitFilters)="submitTransactionFilters($event)"
                                [filters]="currentTransactionFilters"></brad-transactions-header>
      <brad-statements-header class="headers" *ngIf="currentHeader==HeaderType.STATEMENT"
                              [accountID]="detailedAccount.id"
                              (submitFilters)="submitStatementFilters($event)"
                              [filters]="currentStatementFilters"></brad-statements-header>
    </div>
  </ng-template>
</mat-toolbar>
