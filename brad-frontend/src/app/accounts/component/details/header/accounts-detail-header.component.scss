@import 'node_modules/@jumia-cs-fin/common/assets/styles/header-components';

#header-toolbar #ba-info{
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-content: center;

  .return-button {
    margin: auto;
  }

  #back_bar_icon {
    color: var(--primary-color);
  }

  .page-title {
    margin: auto 0.5rem auto 0;
  }

  .status-card {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    margin: auto;
    border-radius: 0.5rem;
    color:white;
    font-weight: 400;
    padding: 0 0.7rem;
    box-shadow: none;
    width: 7vw;
    font-size: clamp(12px, .8vw, 20px);
    height: 3.5vh;
  }

  .open-status {
    background-color: #4cc897 !important;
  }

  .to-be-closed-status {
    background-color: #c29c4a !important;
  }

  .dormant-status {
    background-color: #6a7a83 !important;
  }

  .closed-status {
    background-color: #b63f3f !important;
  }
}

.search-bar {
  margin: auto auto auto -10%;
}

