import {Component, Input, <PERSON><PERSON><PERSON>roy, OnInit, ViewChild, ViewEncapsulation} from '@angular/core';
import {Account} from 'src/app/entities/account/account';
import {AccountFacade} from 'src/app/accounts/facade/account.facade';
import {Activated<PERSON>oute, Params, Router} from '@angular/router';
import {FormControl, FormGroup} from "@angular/forms";
import {BehaviorSubject, Observable, Subject, tap} from "rxjs";
import {TransactionFacade} from "../../../facade/transaction.facade";
import {StatementFacade} from "../../../facade/statement.facade";
import {CdkConnectedOverlay} from "@angular/cdk/overlay";
import {takeUntil} from "rxjs/operators";
import {
  AccountStatementInformationFilters
} from "../../../../entities/account/account-statement-information-filters";
import {TransactionFilters} from "../../../../entities/transaction/transaction-filters";
import {StatementFilters} from "../../../../entities/statement/statement-filters";
import {StatementAdditionalInfoFacade} from "../../../facade/statement-additional-info.facade";
import * as _ from "lodash";
import {CsFinApiService} from "@jumia-cs-fin/common";
import {DatetimeService} from "../../../../api/service/datetime.service";

export enum HeaderType {
  TRANSACTION = 'Transactions',
  STATEMENT = 'Statements'
}

@Component({
  selector: 'accounts-detail-header',
  templateUrl: './accounts-detail-header.component.html',
  styleUrls: ['./accounts-detail-header.component.scss','../../../../../assets/brad-custom.scss'],
  encapsulation:ViewEncapsulation.None
})
export class AccountsDetailHeaderComponent implements OnInit, OnDestroy {

  @ViewChild(CdkConnectedOverlay, {static: true})
  private connectedOverlay!: CdkConnectedOverlay;

  @Input() detailedAccount!: Account;
  @Input() currentHeader!: HeaderType;
  params: Params = {};
  filterTextFormControl!: FormControl;
  form?: FormGroup;
  queryParams: any = {};
  private _onDestroy:Subject<void> = new Subject<void>();
  private _isOpen!: BehaviorSubject<boolean>;
  isOpen$!: Observable<boolean>;
  currentTransactionFilters: TransactionFilters = {};
  currentStatementFilters: StatementFilters = {};

  constructor(private accountFacade: AccountFacade,
              private router: Router,
              private route: ActivatedRoute,
              private apiService: CsFinApiService,
              private transactionFacade: TransactionFacade,
              private statementFacade: StatementFacade,
              private statementAdditionalInfoFacade: StatementAdditionalInfoFacade) {
  }

  ngOnDestroy(): void {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  ngOnInit(): void {
    this.initializeOverlay();
    this.subscribeFiltersChange();
    this.subscribeUrlParamsChanges();
    this.accountFacade.accountFilterParamsBehaviorSubject
        .subscribe(p => this.params = p);
  }

  private subscribeFiltersChange(): void {
    this.statementAdditionalInfoFacade.filters$
      .pipe(takeUntil(this._onDestroy))
      .subscribe((filters:AccountStatementInformationFilters) => {
        if (!this.isInAccountDetailPage()){
          return;
        }
        this._isOpen.next(false);
        this.updateMissingUrlFilters(filters);
      });
  }

  private subscribeUrlParamsChanges(): void {
    this.route.queryParams
      .pipe(takeUntil(this._onDestroy))
      .subscribe(params => {
        const mutableParams = { ...params };
        if (mutableParams['transactionFilters']) {
          mutableParams['transactionFilters'] = JSON.parse(mutableParams['transactionFilters']);
          this.currentTransactionFilters = mutableParams['transactionFilters'];
          this.currentTransactionFilters.accountId = undefined;
          this.currentTransactionFilters.partitionKey = undefined;
          this.submitTransactionFilters(this.currentTransactionFilters);
        }
        if (mutableParams['statementFilters']) {
          mutableParams['statementFilters'] = JSON.parse(mutableParams['statementFilters']);
          this.currentStatementFilters = mutableParams['statementFilters'];
          this.currentStatementFilters.accountID = undefined;
          this.currentStatementFilters.partitionKey = undefined;
          this.submitStatementFilters(this.currentStatementFilters);
        }
        this.initializeFormData(mutableParams);
        this.updateMissingUrlFilters(mutableParams as AccountStatementInformationFilters)
      });
  }

  private initializeFormData(params:any): void {
    this.queryParams = params;
    if(!this.form) {
      this.form = new FormGroup({});
      this.initializeFormControlsAndFilters();
    }
  }

  private initializeOverlay(): void {
    this._isOpen = new BehaviorSubject<boolean>(false);
    this.connectedOverlay.backdropClass = 'cdk-overlay-transparent-backdrop';
    this.connectedOverlay.panelClass = 'filters-overlay-panel';
    this.connectedOverlay.backdropClick
      .pipe(tap(() => this._isOpen.next(false)))
      .subscribe();
    this.isOpen$ = this._isOpen.asObservable();
  }

  private isInAccountDetailPage(): boolean {
    if (!this.detailedAccount)
      return false;
    return this.router.url.includes('accounts/'+this.detailedAccount.id+'/details');
  }

  return() {
    this.router.navigate(['accounts'], { queryParams: this.params });
  }

  triggerOverlay(): void {
    this._isOpen.next(!this._isOpen.value);
  }

  private initializeFormControlsAndFilters(): void {
    this.filterTextFormControl = new FormControl()
    this.setFormControlsToForm();
  }

  private setFormControlsToForm(): void {
    this.form!.addControl(this.transactionFacade.filterTextKey, this.filterTextFormControl);
    this.form!.addControl(this.statementFacade.filterTextKey, this.filterTextFormControl);
  }

  private updateMissingUrlFilters(filters: AccountStatementInformationFilters): void {
    if (!this.detailedAccount)
      return;
    const queryParams: any = { ...filters };
    if (filters.transactionFilters) {
      let transactionFilters : TransactionFilters = filters.transactionFilters;
      transactionFilters = this.setNullToUndefined(transactionFilters);
      queryParams['transactionFilters'] = JSON.stringify(transactionFilters);
      this.currentTransactionFilters = transactionFilters;
    }

    if (filters.statementFilters) {
      let statementFilters : StatementFilters = filters.statementFilters;
      statementFilters = this.setNullToUndefined(statementFilters);
      queryParams['statementFilters'] = JSON.stringify(statementFilters);
      this.currentStatementFilters = statementFilters;
    }

    const formQueryParams = this.apiService.buildQueryParams(queryParams);
    if (!_.isEqual(this.queryParams, formQueryParams)) {
      this.router.navigate(['accounts/'+this.detailedAccount.id+'/details'], { queryParams: formQueryParams });
    }
  }

  submitTransactionFilters(filters: TransactionFilters) {
    this.currentTransactionFilters = {...filters, transactionDateStart: DatetimeService.formatIso8601Str(filters.transactionDateStart!),
      transactionDateEnd: DatetimeService.formatIso8601Str(filters.transactionDateEnd!),
      statementDateStart: DatetimeService.formatIso8601Str(filters.statementDateStart!),
      statementDateEnd: DatetimeService.formatIso8601Str(filters.statementDateEnd!),
      createdAtStart: DatetimeService.formatIso8601Str(filters.createdAtStart!),
      createdAtEnd: DatetimeService.formatIso8601Str(filters.createdAtEnd!)};
    this.transactionFacade.filtersChanged(this.currentTransactionFilters);
    this.statementAdditionalInfoFacade.changeTransactionFilters(this.currentTransactionFilters);
  }

  submitStatementFilters(filters: StatementFilters) {
    this.currentStatementFilters = {...filters, initialDateStart: DatetimeService.formatIso8601Str(filters.initialDateStart!),
      initialDateEnd: DatetimeService.formatIso8601Str(filters.initialDateEnd!),
      createdAtStart: DatetimeService.formatIso8601Str(filters.createdAtStart!),
      createdAtEnd: DatetimeService.formatIso8601Str(filters.createdAtEnd!)};
    this.statementFacade.filtersChanged(this.currentStatementFilters);
    this.statementAdditionalInfoFacade.changeStatementFilters(this.currentStatementFilters);
  }

  submit() {
    if (this.currentHeader === HeaderType.TRANSACTION) {
      this.currentTransactionFilters.filterText = this.filterTextFormControl.value;
      this.submitTransactionFilters(this.currentTransactionFilters);
    }
    if (this.currentHeader === HeaderType.STATEMENT) {
      this.currentStatementFilters.filterText = this.filterTextFormControl.value;
      this.submitStatementFilters(this.currentStatementFilters);
    }
  }

  private setNullToUndefined(obj: any): any {
    for (const key in obj) {
      if (obj.hasOwnProperty(key) && obj[key] === null) {
        obj[key] = undefined;
      } else if (typeof obj[key] === 'object' && obj[key] !== null) {
        this.setNullToUndefined(obj[key]);
      }
    }
    return obj;
  }

  protected readonly HeaderType = HeaderType;
}
