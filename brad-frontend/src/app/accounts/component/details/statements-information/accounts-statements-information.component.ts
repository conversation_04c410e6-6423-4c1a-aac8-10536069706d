import {Component, Input, OnD<PERSON>roy, OnInit} from '@angular/core';
import {Account} from "../../../../entities/account/account";
import {Subject} from "rxjs";
import {AccountAdditionalInfo} from "../../../../entities/account/account-additional-info";
import {Router} from "@angular/router";
import {AccountTypeConfig} from "../../../../entities/account/account-type-config";

@Component({
  selector: 'brad-account-statements-information',
  templateUrl: './accounts-statements-information.component.html',
  styleUrls: ['./accounts-statements-information.component.scss']
})
export class AccountsStatementsInformationComponent implements OnInit, OnDestroy {

  @Input() detailedAccount!: AccountAdditionalInfo;
  @Input() isLoading !: boolean;
  private _onDestroy: Subject<void> = new Subject<void>();
  protected readonly accountTypeConfigs:any = AccountTypeConfig;
  allFieldsToShow: any[] = []
  fieldsToShowInCollapsedMode: any[] = []
  areDetailsExpanded = false;

  constructor(
    private router: Router
  ) {
  }

  ngOnInit(): void {
    this.initiateFieldsToDisplay();
  }

  private initiateFieldsToDisplay() {
    const accType = this.detailedAccount.type;
    const accSubType = this.detailedAccount.subType;
    this.allFieldsToShow = this.accountTypeConfigs[accType || ""]?.detailsPageOrder.filter((field: any) => 
      !field.subTypes || field.subTypes.includes(accSubType));
    if (this.allFieldsToShow.length > 12) {
      this.fieldsToShowInCollapsedMode = this.allFieldsToShow.slice(0, 12);
    } else {
      this.fieldsToShowInCollapsedMode = this.allFieldsToShow;
    }
  }

  ngOnDestroy(): void {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  toggleDetails() {
    this.areDetailsExpanded = !this.areDetailsExpanded;
  }


}
