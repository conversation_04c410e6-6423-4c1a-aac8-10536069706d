<div class="details-information">
  <div *ngIf="isLoading" class="loading-account-info">
    <mat-progress-spinner mode="indeterminate" diameter="50" strokeWidth="5"></mat-progress-spinner>
    <span class="label">{{ 'GENERAL.DETAILS.LOADING' | translate }} account additional info</span>
  </div>
  <div *ngIf="!isLoading && !detailedAccount" class="detail-failed">
    <mat-icon>report_problem</mat-icon>
    <span class="label">{{ 'ACCOUNTS.DETAILS.ERRORS.UNABLE_TO_FIND_DETAILS' | translate }}</span>
  </div>

  <main *ngIf="!isLoading && detailedAccount">
    <div class="details">
      <div class="expanded-details">
        <div class="expanded-item" *ngFor="let field of areDetailsExpanded? allFieldsToShow: fieldsToShowInCollapsedMode "
             [ngClass]="{'highlight': field.property ==='balance'} ">
          <span class="object-key">{{ 'ACCOUNTS.FIELDS.' + field.name | translate }}</span>
          <span class="separator"> : </span>

          <div [ngSwitch]="field.property">

            <span  *ngSwitchCase="'balance'" >
              <strong>{{ detailedAccount.balance | number:'1.2-2' }}</strong>
              {{ detailedAccount.balance ? detailedAccount.currency.code : '--' }}
            </span>

            <span *ngSwitchCase="'balanceUSD'" >
                {{ detailedAccount.balanceUSD | number:'1.2-2' }}
                {{ detailedAccount.balanceUSD ? 'USD' : '--' }}
            </span>

            <span *ngSwitchCase="'lastStatementDate'">
              {{ detailedAccount.lastStatementDate | date }}
            </span>

            <span *ngSwitchCase="'lastTransactionDate'">
              {{ detailedAccount.lastTransactionDate | date }}
            </span>

            <span *ngSwitchCase="'lastProcessedStatementDate'">
              {{ detailedAccount.lastProcessedStatementDate | date }}
            </span>

            <span *ngSwitchCase="'country'" class="country-cell">
               <div>
                  <cs-fin-flag [countryCode]="detailedAccount.country.code"> </cs-fin-flag>
                 {{ detailedAccount.country.name }}
               </div>
            </span>

            <span *ngSwitchDefault>{{ detailedAccount[field.property]||'--' }}</span>

          </div>
        </div>

      </div>

      <button mat-icon-button (click)="toggleDetails()">
        <mat-icon *ngIf="!areDetailsExpanded">expand_more</mat-icon>
        <mat-icon *ngIf="areDetailsExpanded">expand_less</mat-icon>
      </button>
    </div>
  </main>

</div>
