@import 'node_modules/@jumia-cs-fin/common/assets/styles/details-components';

.details-information {
  padding: 0 10px;
}

ul {
  border-bottom: none !important;
}

.right-column {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
}

.left-column {
  display: flex;
  flex-direction: column;
}

.details-tab-group{
  border-top: 1px solid #EBEBEFFF;
}

.country-cell {
  div {
    display: flex;
    flex-direction: row;
    justify-content: left;
    align-items: center;
  }
  cs-fin-flag {
    margin-right: 5px;
  }
}

.details{
  display: flex;
  button {
    color: var(--primary-color)
  }
}

.highlight{
  border-radius: 2px;
  background-color: var(--lighter-color)!important;
}

::ng-deep .mdc-tab__text-label {
  color: var(--primary-text-color) !important;
}
.regular-text{
  font-weight: normal;
}


.loading-account-info {
  display: flex;
  padding: 50px 0;
  align-items: center;
  flex-direction: column;
  font-style: italic;
}

.expanded-details {
  width: 100%;
  padding: 15px 0;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(20%, 1fr));
  gap: 10px;
}

.expanded-item {
  padding: 0 10px;
  display: grid;
  grid-template-columns:  1fr 30px 1fr;
  gap: 10px;
  background-color: rgba(0, 0, 0, 0.005);
}

.object-key {
  color: #9f9faa;
  font-weight: bold;
}

.separator {
  padding: 0 5px;
}

