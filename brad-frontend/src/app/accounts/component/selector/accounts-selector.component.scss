@import "node_modules/@jumia-cs-fin/common/assets/styles/list-components";

#filters-container {
  margin-top: 30vh;
  display: flex;
  justify-content: space-around;
  align-items: center;
}

#filter-card {
  display: flex;
  justify-content: space-evenly;
  flex-direction: column;
  border: 2px solid #00c5ff;
  border-radius: 10px;
  width: 50%;
  height: 25vh;

  span {
    display: flex;
    justify-content: center;
    align-items: center;
    color: var(--primary-text-color);
    font-size: 20px;
    line-height: 1.44;

  }

}

#filters {
  display: flex;
  justify-content: space-evenly;
  flex-direction: row;

  mat-form-field {
    height: 5vh;
    width: 40%;
  }
}

#card-button {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  margin-top: 2vh;

  button {
    margin: 0 10px;
    width: 40%;
  }
}


.country-name-flag {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  cs-fin-flag {
    margin-left: 5px;
  }
}
