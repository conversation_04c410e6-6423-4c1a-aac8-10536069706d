import {Component, EventEmitter, OnD<PERSON>roy, OnInit, Output} from '@angular/core';
import {FormControl, FormGroup} from "@angular/forms";
import {Country} from "../../../entities/account/country";
import {takeUntil} from "rxjs/operators";
import {Subject} from "rxjs";
import {AccountFilters} from "../../../entities/account/account-filters";
import {CountryApiService} from "../../../api/service/country-api.service";
import {AccountFacade} from "../../facade/account.facade";
import {Account} from "../../../entities/account/account";
import {ActivatedRoute} from "@angular/router";

@Component({
  selector: 'brad-accounts-selector',
  templateUrl: './accounts-selector.component.html',
  styleUrls: ['./accounts-selector.component.scss']
})
export class AccountsSelectorComponent implements OnInit, OnDestroy {

  @Output() account = new EventEmitter<number>;

  form!: FormGroup;
  accountNavReferenceFormControl!: FormControl;
  countryFormControl!: FormControl;

  accountNavReferenceSearchFormControl = new FormControl();
  accountsList:Account[] = [];
  filteredAccountsList:Account[] = [];

  countrySearchFormControl = new FormControl();
  countryList:Country[] = [];
  filteredCountryList:Country[] = [];

  previousAccountNumber: string|null = null;

  private _onDestroy: Subject<void> = new Subject<void>();

  constructor(private countryApiService: CountryApiService,
              private accountFacade: AccountFacade,
              private route: ActivatedRoute) { }

  ngOnInit(): void {
    this.subscribeUrlParams();
    this.initializeFormData();
    this.initFiltersSearch();
    this.populateFieldsDependingOnPage();
  }

  ngOnDestroy(): void {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  private subscribeUrlParams(): void {
    this.route.queryParams
      .pipe(takeUntil(this._onDestroy))
      .subscribe((params: AccountFilters) => {
        if(params.accountID) {
          this.account.emit(params.accountID);
        }
      });
  }

  private initFiltersSearch(): void {
    this.initCountrySearch();
    this.initAccountNavReferenceSearch();
  }

  private getFormValues(): AccountFilters {
    return this.form.value as AccountFilters;
  }

  private initializeFormData(): void {
    if(!this.form) {
      this.form = new FormGroup({});

      this.initializeFormControlsAndFilters();
      this.setFormControlsToForm();
    }
  }

  private setFormControlsToForm(): void {
    this.form.addControl(this.accountFacade.countryKey, this.countryFormControl);
    this.form.addControl(this.accountFacade.navReferenceKey, this.accountNavReferenceFormControl);
  }

  private initializeFormControlsAndFilters(): void {
    const filters: AccountFilters = {};

    this.countryFormControl = new FormControl();
    this.accountNavReferenceFormControl = new FormControl();


    Promise.all([
      this.applyCountryFilter(filters),
      this.applyAccountNavReferenceFilter(filters)
    ]).then(() => {
      this.accountFacade.filtersChanged(filters);
    });

  }

  private initCountrySearch(): void {
    this.countrySearchFormControl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe((value: String) => {
        const searchTerm = value.trim();
        if (searchTerm) {
          this.filteredCountryList = this.countryList.filter((country) => {
            return country.name.trim().toLowerCase().includes(searchTerm.toLowerCase());
          })
        } else {
          this.filteredCountryList = this.countryList;
        }
      });
  }

  private initAccountNavReferenceSearch(): void {
    this.accountNavReferenceSearchFormControl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe((value: String) => {
        const searchTerm = value.trim();
        if (searchTerm) {
          this.filteredAccountsList = this.accountsList.filter((account) => {
            return account.navReference.trim().toLowerCase().includes(searchTerm.toLowerCase());
          })
        } else {
          this.filteredAccountsList = this.accountsList;
        }
      });
  }

  private applyCountryFilter(filters: AccountFilters): Promise<void> {
    return new Promise<void>(async (resolve) => {

      await this.loadCountries();

      this.countryFormControl.setValue(filters.countryCodes, {emitEvent: false})
      resolve();
    });
  }

  loadCountries(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if(this.countryList.length <= 0) {
        this.countryApiService.getAll()
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (countries: Country[]) => {
              this.countryList = countries;
              this.filteredCountryList = this.countryList;
              resolve();
            }, error: (error) => reject(error)
          });
      } else {
        resolve();
      }
    });
  }

  private applyAccountNavReferenceFilter(filters: AccountFilters): Promise<void> {
    return new Promise<void>(async (resolve) => {

      await this.loadAccounts();

      this.accountNavReferenceFormControl.setValue(filters.navReference, {emitEvent: false})
      resolve();
    });
  }

  loadAccounts(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      this.accountFacade.getAllAccountNavReferences(this.getFormValues())
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (accounts: Account[]) => {
            this.accountsList = accounts.filter(account => account.navReference !== null && account.navReference !== '');
            this.filteredAccountsList = this.accountsList;
            resolve();
          }, error: (error) => reject(error)
        });

    });
  }

  submit(): void {
    this.account.emit(this.accountNavReferenceFormControl.value.id);
  }


  compareIdFn(o1: any, o2: any): boolean {
    return o1 && o2 && o1.id === o2.id;
  }

  populateFieldsDependingOnPage(): void {
    if(this.isInTroubleshootingScreen()) {
      this.populateFieldsForTroubleshooting();
    } else if(this.isInReconcileScreen()) {
      this.populateFieldsForReconcile();
    }
  }

  private isInTroubleshootingScreen(): boolean {
    return window.location.href.includes('troubleshooting');
  }

  private isInReconcileScreen(): boolean {
    return window.location.href.includes('reconcile');
  }

  populateFieldsForTroubleshooting(): void {
    this.accountFacade.getAccountByLastUpdatedStatementByUser()
      .pipe(takeUntil(this._onDestroy))
      .subscribe({
        next: (account: Account) => {
          if(account?.accountNumber) {
            this.previousAccountNumber = account.accountNumber;
          }
        }
      });
  }

  populateFieldsForReconcile(): void {

  }


  goToPreviousAccount(){

  }

  selectButtonText(): string {
    if (this.isInTroubleshootingScreen()) {
      return "ACCOUNTS.SELECTOR.LABELS.TROUBLESHOOT"
    }
    return "ACCOUNTS.SELECTOR.LABELS.RECONCILIATION"
  }
}
