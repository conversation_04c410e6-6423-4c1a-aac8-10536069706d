<main class="container" >
  <div id="filters-container">

    <mat-card id="filter-card">

      <span class="filter-title">{{'ACCOUNTS.SELECTOR.TITLE' | translate}}</span>
      <div id="filters">
        <!-- account nav reference -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-account-nav-reference-entity-filter"
                        (click)="loadAccounts()">
          <mat-label>{{'ACCOUNTS.FIELDS.NAV_REFERENCE' | translate}}</mat-label>
          <mat-select [formControl]="accountNavReferenceFormControl" [compareWith]="compareIdFn">
            <ng-container *ngIf="filteredAccountsList != null; else loadingAccounts">
              <mat-option>
                <ngx-mat-select-search [formControl]="accountNavReferenceSearchFormControl"
                                       [noEntriesFoundLabel]="'GENERAL.FILTERS.NO_MATCHING_RESULTS_FOUND' | translate"
                                       [placeholderLabel]="'GENERAL.FILTERS.SEARCH' | translate">
                </ngx-mat-select-search>
              </mat-option>
              <mat-option *ngFor="let account of filteredAccountsList" [value]="account">
                {{account.navReference}} 
              </mat-option>
              <button mat-button color="primary" class="clear-selection-btn"
                      [disabled]="!accountNavReferenceFormControl.value?.length"
                      (click)="accountNavReferenceFormControl.reset([])">
                {{'GENERAL.FILTERS.ACTIONS.CLEAR_SELECTION' | translate}}
              </button>
            </ng-container>
            <ng-template #loadingAccounts>
              <mat-option disabled>
                <div class="filters-loading-container">
                  <span>{{'GENERAL.FILTERS.LOADING' | translate}}</span>
                  <mat-spinner diameter="20"></mat-spinner>
                </div>
              </mat-option>
            </ng-template>
          </mat-select>
        </mat-form-field>


        <!-- country -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-country-entity-filter"
                        (click)="loadCountries()" (change)="loadAccounts()">
          <mat-label>{{'ACCOUNTS.FIELDS.COUNTRY' | translate}}</mat-label>
          <mat-select [formControl]="countryFormControl" [compareWith]="compareIdFn" multiple (openedChange)="loadAccounts()">
            <ng-container *ngIf="filteredCountryList != null; else loadingCountry">
              <mat-option>
                <ngx-mat-select-search [formControl]="countrySearchFormControl"
                                       [noEntriesFoundLabel]="'GENERAL.FILTERS.NO_MATCHING_RESULTS_FOUND' | translate"
                                       [placeholderLabel]="'GENERAL.FILTERS.SEARCH' | translate">
                </ngx-mat-select-search>
              </mat-option>
              <mat-option *ngFor="let country of filteredCountryList" [value]="country">
                <span class="country-name-flag">{{country.name}} <cs-fin-flag [countryCode]="country.code"></cs-fin-flag></span>
              </mat-option>
              <button mat-button color="primary" class="clear-selection-btn"
                      [disabled]="!countryFormControl.value?.length"
                      (click)="countryFormControl.reset([])">
                {{'GENERAL.FILTERS.ACTIONS.CLEAR_SELECTION' | translate}}
              </button>
            </ng-container>
            <ng-template #loadingCountry>
              <mat-option disabled>
                <div class="filters-loading-container">
                  <span>{{'GENERAL.FILTERS.LOADING' | translate}}</span>
                  <mat-spinner diameter="20"></mat-spinner>
                </div>
              </mat-option>
            </ng-template>
          </mat-select>
        </mat-form-field>
      </div>

      <div id="card-button">
        <button mat-raised-button color="primary" (click)="goToPreviousAccount()" [disabled]="this.previousAccountNumber==null">
          {{'ACCOUNTS.SELECTOR.LABELS.PREVIOUS_ACCOUNT' | translate}}
        </button>
        <button mat-raised-button color="primary" [disabled]="accountNavReferenceFormControl.value == null" (click)="submit()">
          {{this.selectButtonText() | translate}}
        </button>
      </div>

    </mat-card>

  </div>

</main>
