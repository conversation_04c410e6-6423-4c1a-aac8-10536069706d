@import "node_modules/@jumia-cs-fin/common/assets/styles/list-components";
@import "node_modules/@jumia-cs-fin/common/assets/styles/tables";
@import "../../accounts.component.scss";

mat-paginator {
  border-top: 1px solid #e0e0e0;
  background-color: var(--mat-table-background-color) !important;
}

.id-column-icon {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.last-statement-date-width {
  width: 170px !important;
}

.cdk-column-id {
  width: 80px !important;
}

.cdk-column-color {
  width: 120px !important;
}

.country-cell {
  div {
    display: flex;
    flex-direction: row;
    justify-content: left;
    align-items: center;
  }

  cs-fin-flag {
    margin-left: 5px;
  }
}

.amountWithUsd {
  display: flex;
  flex-direction: column;

  .usd {
    font-size: 10px;
  }
}

.actions .mat-mdc-raised-button {
  color: white;
  margin: 12px;
}

.warning-icon {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  color: #f8b27b;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  transition: background-color 0.2s;
  font-size: 14px;
}

.unseen {
  display: none;
}

.expanded-details {
  padding: 15px 0;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(30%, 1fr));
  gap: 10px;
}

.expanded-item {
  padding: 0 10px;
  display: grid;
  grid-template-columns:  1fr 30px 1fr;
  gap: 10px;
}

.object-key {
  color: #636363;
  font-weight: bolder;
}

.separator {
  padding: 0 10px;
}

.cdk-cell span:not(:hover),
.cdk-cell span[style*="text-overflow: clip"] {
  pointer-events: none;
}

.force-leftAlign {
  position: relative !important;
  right: 15px;
}

::ng-deep {
  .cdk-table {
    table-layout: fixed;
    width: 100%;

    .cdk-header-cell {
      width: 150px;
      max-width: 150px;
      padding-right: 0 !important;

      .mat-sort-header-container {
        display: flex !important;
        width: 100% !important;

        .mat-sort-header-content {
          text-align: left !important;
          display: block !important;
          overflow: hidden !important;
          text-overflow: ellipsis !important;
          white-space: nowrap !important;
          width: 100% !important;
        }
      }
    }
  }
}

