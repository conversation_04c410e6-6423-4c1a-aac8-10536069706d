@import 'node_modules/@jumia-cs-fin/common/assets/styles/header-components';

.filters-overlay-panel {
  background-color: transparent;
}

.filter-field{
  mat-label {
    color: var(--extra-light-color) !important;
  }
  input {
    color: var(--primary-text-color) !important;
  }

}
.country-name-flag {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  cs-fin-flag {
    margin-left: 5px;
  }
}


.mat-date-range-input-container {
  display: flex;
  justify-content: space-between;
  .mat-date-range-input-wrapper {
    width: 100%;
    input {
      width: 100%;
      text-align: center!important;
    }
    input::placeholder {
      text-align: center !important;
    }
  }
}

.custom-button{
  height: 40px;
  border: .5px solid var(--lighter-color);
  background-color: transparent;
  border-radius: 3px;
  display: flex;
  align-items: center;
  cursor: pointer;
  box-shadow: 0 4px 4px rgba(0, 0, 0, 0.1);

}
.main-text{
  color: #00a7d8;
  font-weight: bold;
  font-size: 12px;
}
.subtext{
  padding-left: 10px;
  color: gray;
  font-size: 12px;

}
.spacer{
  padding: 0 5px;
}
.custom-button:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.display-flex {
  display: flex !important;
  flex-direction: column;

}

.mat-mdc-checkbox{
  display: flex!important;
  align-items: flex-start!important;
}