<mat-toolbar id="header-toolbar">
  <button mat-icon-button (click)="sidenav.toggle()" fxHide [fxShow.xs]="true" [fxShow.sm]="true">
    <mat-icon>menu</mat-icon>
  </button>
  <span class="page-title">{{ 'ACCOUNTS.TITLE' | translate }}</span>

  <section class="search-bar" [formGroup]="form">
    <!-- filter text (code) -->
    <mat-form-field id="search" class="change-header" appearance="outline" fxShow [fxShow.xs]="false" [fxShow.sm]="false"
                    cdkOverlayOrigin #trigger="cdkOverlayOrigin">
      <mat-icon matPrefix>search</mat-icon>
      <mat-label class="theLabel">{{'ACCOUNTS.DETAILS.SEARCH_BAR' | translate}}</mat-label>
      <input type="text" #input matInput [formControl]="filterTextFormControl"
             (keyup.enter)="submit(input)">
      <mat-icon matSuffix id="filters" (click)="triggerOverlay()">filter_list</mat-icon>
    </mat-form-field>

    <button mat-raised-button color="primary" id="apply-search-btn" class="raised-primary-btn" fxShow [fxShow.xs]="false"
            [fxShow.sm]="false" [disabled]="noFiltersSelected()" (click)="submit()">
      {{ 'GENERAL.BUTTONS.LABELS.SEARCH' | translate }}
    </button>

    <!-- Net change filter -->
    <button  class="custom-button" (click)="triggerNetChangeOverlay()"
             cdkOverlayOrigin #NetChangeOverlayTrigger="cdkOverlayOrigin">
      <div class ="main-text">NET CHANGE</div>
      <div class="subtext">{{ isFromBeginning() ? 'Since beginning' : netChangeStartDateFormControl.value | date }}<small
          *ngIf="isFromBeginning() || netChangeStartDateFormControl.value"
          class="spacer">-</small>{{ netChangeEndDateFormControl.value | date }}
      </div>
    </button>
  </section>

  <span fxFlex fxHide [fxShow.xs]="true" [fxShow.sm]="true"></span>
  <button mat-icon-button aria-label="Filter accounts" fxHide [fxShow.xs]="true" [fxShow.sm]="true"
          *ngIf="showFilters" id="show-mobile-filters">
    <mat-icon (click)="triggerOverlay()">filter_list</mat-icon>
  </button>
  <ng-template cdkConnectedOverlay class="template"
               [cdkConnectedOverlayHasBackdrop]="true"
               [cdkConnectedOverlayOrigin]="trigger"
                [cdkConnectedOverlayOpen]="(isOpen$ | async) || false">
    <div class="filters-overlay">
      <div class="filters-header">
        <mat-icon fxHide [fxShow.xs]="true" [fxShow.sm]="true" (click)="closeOverlay()">close</mat-icon>
        <p class="filters-title">{{'GENERAL.FILTERS.TITLE' | translate}}</p>
        <button fxHide class="flat-white-btn filters-cancel-btn" [disabled]="noFiltersSelected()" [fxShow.xs]="true"
                [fxShow.sm]="true"
                mat-flat-button (click)="clearFilters()" id="clear-btn">
          {{ 'GENERAL.BUTTONS.CLEAR' | translate }}
        </button>
      </div>

      <div class="filters-container">

        <!-- companyID -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-companyID-filter">
          <mat-label>{{ 'ACCOUNTS.FIELDS.COMPANY_ID' | translate }}</mat-label>
          <input matInput type="text" [formControl]="companyIDFormControl">
        </mat-form-field>

        <!-- country -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-country-entity-filter"
                        (click)="loadCountries()">
          <mat-label>{{'ACCOUNTS.FIELDS.COUNTRY' | translate}}</mat-label>
          <mat-select [formControl]="countryFormControl" [compareWith]="compareIdFn" multiple>
            <ng-container *ngIf="filteredCountryList != null; else loadingCountry">
              <mat-option>
                <ngx-mat-select-search [formControl]="countrySearchFormControl"
                                       [noEntriesFoundLabel]="'GENERAL.FILTERS.NO_MATCHING_RESULTS_FOUND' | translate"
                                       [placeholderLabel]="'GENERAL.FILTERS.SEARCH' | translate">
                </ngx-mat-select-search>
              </mat-option>
              <mat-option *ngFor="let country of filteredCountryList" [value]="country">
                <span class="country-name-flag">{{country.name}} <cs-fin-flag [countryCode]="country.code"></cs-fin-flag></span>
              </mat-option>
              <button mat-button color="primary" class="clear-selection-btn"
                      [disabled]="!countryFormControl.value?.length"
                      (click)="countryFormControl.reset([])">
                {{'GENERAL.FILTERS.ACTIONS.CLEAR_SELECTION' | translate}}
              </button>
            </ng-container>
            <ng-template #loadingCountry>
              <mat-option disabled>
                <div class="filters-loading-container">
                  <span>{{'GENERAL.FILTERS.LOADING' | translate}}</span>
                  <mat-spinner diameter="20"></mat-spinner>
                </div>
              </mat-option>
            </ng-template>
          </mat-select>
        </mat-form-field>

        <!-- navReference -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-navReference-filter">
          <mat-label>{{ 'ACCOUNTS.FIELDS.NAV_REFERENCE' | translate }}</mat-label>
          <input matInput type="text" [formControl]="navReferenceFormControl">
        </mat-form-field>

        <!-- beneficiaryName -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-beneficiaryName-filter">
          <mat-label>{{ 'ACCOUNTS.FIELDS.BENEFICIARY_NAME' | translate }}</mat-label>
          <input matInput type="text" [formControl]="beneficiaryNameFormControl">
        </mat-form-field>

        <!-- beneficiaryAddress -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-beneficiaryAddress-filter">
          <mat-label>{{ 'ACCOUNTS.FIELDS.BENEFICIARY_ADDRESS' | translate }}</mat-label>
          <input matInput type="text" [formControl]="beneficiaryAddressFormControl">
        </mat-form-field>

        <!-- iban -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-iban-filter">
          <mat-label>{{ 'ACCOUNTS.FIELDS.IBAN' | translate }}</mat-label>
          <input matInput type="text" [formControl]="ibanFormControl">
        </mat-form-field>

        <!-- accountNumber -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-accountNumber-filter">
          <mat-label>{{ 'ACCOUNTS.FIELDS.ACCOUNT_NUMBER' | translate }}</mat-label>
          <input matInput type="text" [formControl]="accountNumberFormControl">
        </mat-form-field>

        <!-- accountName -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-accountName-filter">
          <mat-label>{{ 'ACCOUNTS.FIELDS.ACCOUNT_NAME' | translate }}</mat-label>
          <input matInput type="text" [formControl]="accountNameFormControl">
        </mat-form-field>

        <!-- swiftCode -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-swiftCode-filter">
          <mat-label>{{ 'ACCOUNTS.FIELDS.SWIFT_CODE' | translate }}</mat-label>
          <input matInput type="text" [formControl]="swiftCodeFormControl">
        </mat-form-field>

        <!-- accountRoutingCode -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-accountRoutingCode-filter">
          <mat-label>{{ 'ACCOUNTS.FIELDS.ACCOUNT_ROUTING_CODE' | translate }}</mat-label>
          <input matInput type="text" [formControl]="accountRoutingCodeFormControl">
        </mat-form-field>

        <!-- sortCode -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-sortCode-filter">
          <mat-label>{{ 'ACCOUNTS.FIELDS.SORT_CODE' | translate }}</mat-label>
          <input matInput type="text" [formControl]="sortCodeFormControl">
        </mat-form-field>

        <!-- branchCode -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-branchCode-filter">
          <mat-label>{{ 'ACCOUNTS.FIELDS.BRANCH_CODE' | translate }}</mat-label>
          <input matInput type="text" [formControl]="branchCodeFormControl">
        </mat-form-field>

        <!-- rib -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-rib-filter">
          <mat-label>{{ 'ACCOUNTS.FIELDS.RIB' | translate }}</mat-label>
          <input matInput type="text" [formControl]="ribFormControl">
        </mat-form-field>

        <!-- currency -->

        <mat-form-field class="filter-field" appearance="outline" data-cy="input-country-entity-filter"
                        (click)="loadCurrencies()">
          <mat-label>{{'ACCOUNTS.FIELDS.CURRENCY' | translate}}</mat-label>
          <mat-select [formControl]="currencyFormControl" multiple>
            <ng-container *ngIf="filteredCurrencyList != null; else loadingCurrency">
              <mat-option>
                <ngx-mat-select-search [formControl]="currencySearchFormControl"
                                       [noEntriesFoundLabel]="'GENERAL.FILTERS.NO_MATCHING_RESULTS_FOUND' | translate"
                                       [placeholderLabel]="'GENERAL.FILTERS.SEARCH' | translate">
                </ngx-mat-select-search>
              </mat-option>
              <mat-option *ngFor="let currency of filteredCurrencyList" [value]="currency.code">
                {{currency.code}}
              </mat-option>
              <button mat-button color="primary" class="clear-selection-btn"
                      [disabled]="!currencyFormControl.value?.length"
                      (click)="currencyFormControl.reset([])">
                {{'GENERAL.FILTERS.ACTIONS.CLEAR_SELECTION' | translate}}
              </button>
            </ng-container>
            <ng-template #loadingCurrency>
              <mat-option disabled>
                <div class="filters-loading-container">
                  <span>{{'GENERAL.FILTERS.LOADING' | translate}}</span>
                  <mat-spinner diameter="20"></mat-spinner>
                </div>
              </mat-option>
            </ng-template>
          </mat-select>
        </mat-form-field>

        <!-- Type -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-type-filter">
          <mat-label>{{'ACCOUNTS.FIELDS.TYPE' | translate}}</mat-label>
          <mat-select [formControl]="accountTypeControl" multiple>
            <ng-container *ngIf="accountTypesList.length > 0; else loadingAccountType">
              <mat-option *ngFor="let type of accountTypesList" [value]="type">
                {{type}}
              </mat-option>
              <button mat-button color="primary" class="clear-selection-btn"
                      [disabled]="!accountTypeControl.value?.length"
                      (click)="accountTypeControl.reset([])">
                {{'GENERAL.FILTERS.ACTIONS.CLEAR_SELECTION' | translate}}
              </button>
            </ng-container>
            <ng-template #loadingAccountType>
              <mat-option disabled>
                <div class="filters-loading-container">
                  <span>{{'GENERAL.FILTERS.LOADING' | translate}}</span>
                  <mat-spinner diameter="20"></mat-spinner>
                </div>
              </mat-option>
            </ng-template>
          </mat-select>
        </mat-form-field>

        <!-- SubType -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-sub-type-filter">
          <mat-label>{{'ACCOUNTS.FIELDS.SUB_TYPE' | translate}}</mat-label>
          <mat-select [formControl]="accountSubTypeFormControl" multiple>
            <ng-container *ngIf="accountSubTypesList.length > 0; else loadingAccountSubType">
              <mat-option *ngFor="let subType of accountSubTypesList" [value]="subType">
                {{subType}}
              </mat-option>
              <button mat-button color="primary" class="clear-selection-btn"
                      [disabled]="!accountSubTypeFormControl.value?.length"
                      (click)="accountSubTypeFormControl.reset([])">
                {{'GENERAL.FILTERS.ACTIONS.CLEAR_SELECTION' | translate}}
              </button>
            </ng-container>
            <ng-template #loadingAccountSubType>
              <mat-option disabled>
                <div class="filters-loading-container">
                  <span>{{'GENERAL.FILTERS.LOADING' | translate}}</span>
                  <mat-spinner diameter="20"></mat-spinner>
                </div>
              </mat-option>
            </ng-template>
          </mat-select>
        </mat-form-field>


        <!-- status -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-status-filter" (click)="loadStatuses()">
          <mat-label>{{'ACCOUNTS.FIELDS.STATUS' | translate}}</mat-label>
          <mat-select [formControl]="statusFormControl" multiple>
            <ng-container *ngIf="statusList.length > 0; else loadingStatus">
              <mat-option *ngFor="let status of statusList" [value]="status.id">
                {{status.name}}
              </mat-option>
              <button mat-button color="primary" class="clear-selection-btn"
                      [disabled]="!statusFormControl.value?.length"
                      (click)="statusFormControl.reset([])">
                {{'GENERAL.FILTERS.ACTIONS.CLEAR_SELECTION' | translate}}
              </button>
            </ng-container>
            <ng-template #loadingStatus>
              <mat-option disabled>
                <div class="filters-loading-container">
                  <span>{{'GENERAL.FILTERS.LOADING' | translate}}</span>
                  <mat-spinner diameter="20"></mat-spinner>
                </div>
              </mat-option>
            </ng-template>
          </mat-select>
        </mat-form-field>

        <!-- created at -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-created-at-filter">
          <mat-label>{{'ACCOUNTS.FIELDS.CREATED_AT' | translate}}</mat-label>
          <mat-date-range-input [rangePicker]="picker">
            <input matStartDate [formControl]="createdAtStartFormControl" placeholder="Start date">
            <input matEndDate [formControl]="createdAtEndFormControl" placeholder="End date">
          </mat-date-range-input>
          <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
          <mat-date-range-picker #picker></mat-date-range-picker>
        </mat-form-field>

        <!-- statementSource -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-statement-source-filter">
          <mat-label>{{'ACCOUNTS.FIELDS.STATEMENT_SOURCE' | translate}}</mat-label>
          <mat-select [formControl]="statementSourceFormControl" multiple>
            <ng-container *ngIf="statementSourceList.length > 0; else loadingStatementSource">
              <mat-option *ngFor="let statementSource of statementSourceList" [value]="statementSource">
                {{statementSource}}
              </mat-option>
              <button mat-button color="primary" class="clear-selection-btn"
                      [disabled]="!statementSourceFormControl.value?.length"
                      (click)="statementSourceFormControl.reset([])">
                {{'GENERAL.FILTERS.ACTIONS.CLEAR_SELECTION' | translate}}
              </button>
            </ng-container>
            <ng-template #loadingStatementSource>
              <mat-option disabled>
                <div class="filters-loading-container">
                  <span>{{'GENERAL.FILTERS.LOADING' | translate}}</span>
                  <mat-spinner diameter="20"></mat-spinner>
                </div>
              </mat-option>
            </ng-template>
          </mat-select>
        </mat-form-field>

        <!-- statementPeriodicity -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-statement-periodicity-filter">
          <mat-label>{{'ACCOUNTS.FIELDS.STATEMENT_PERIODICITY' | translate}}</mat-label>
          <mat-select [formControl]="statementPeriodicityFormControl" multiple>
            <ng-container *ngIf="statementPeriodicityList.length > 0; else loadingStatementPeriodicity">
              <mat-option *ngFor="let periodicity of statementPeriodicityList" [value]="periodicity">
                {{periodicity}}
              </mat-option>
              <button mat-button color="primary" class="clear-selection-btn"
                      [disabled]="!statementPeriodicityFormControl.value?.length"
                      (click)="statementPeriodicityFormControl.reset([])">
                {{'GENERAL.FILTERS.ACTIONS.CLEAR_SELECTION' | translate}}
              </button>
            </ng-container>
            <ng-template #loadingStatementPeriodicity>
              <mat-option disabled>
                <div class="filters-loading-container">
                  <span>{{'GENERAL.FILTERS.LOADING' | translate}}</span>
                  <mat-spinner diameter="20"></mat-spinner>
                </div>
              </mat-option>
            </ng-template>
          </mat-select>
        </mat-form-field>

        <!-- Last processed Statement Date -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-last-processed-stmt-date-filter">
          <mat-label>{{'ACCOUNTS.FIELDS.LAST_PROCESSED_STATEMENT_DATE' | translate}}</mat-label>
          <mat-date-range-input [rangePicker]="lastStmtDateRangePicker">
            <input matStartDate [formControl]="lastProcessedStatementDateStartFormControl" placeholder="Start date">
            <input matEndDate [formControl]="lastProcessedStatementDateEndFormControl" placeholder="End date">
          </mat-date-range-input>
          <mat-datepicker-toggle matIconSuffix [for]="lastStmtDateRangePicker"></mat-datepicker-toggle>
          <mat-date-range-picker #lastStmtDateRangePicker></mat-date-range-picker>
        </mat-form-field>

        <!-- partner -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-partner-filter">
          <mat-label>{{ 'ACCOUNTS.FIELDS.PARTNER' | translate }}</mat-label>
          <input matInput type="text" [formControl]="partnerFormControl">
        </mat-form-field>

        <!-- phoneNumber -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-phoneNumber-filter">
          <mat-label>{{ 'ACCOUNTS.FIELDS.PHONE_NUMBER' | translate }}</mat-label>
          <input matInput type="text" [formControl]="phoneNumberFormControl">
        </mat-form-field>

        <!-- investmentId -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-investment-id-filter">
          <mat-label>{{ 'ACCOUNTS.FIELDS.INVESTMENT_ID' | translate }}</mat-label>
          <input matInput type="text" [formControl]="investmentIdFormControl">
        </mat-form-field>

      </div>

      <div class="filters-actions">
        <button class="flat-white-btn filters-cancel-btn" [disabled]="noFiltersSelected()" mat-flat-button fxShow
                (click)="clearFilters()" [fxShow.xs]="false" [fxShow.sm]="false">
          {{'GENERAL.FILTERS.ACTIONS.CLEAR' | translate}}
        </button>
        <button class="raised-primary-btn filters-apply-btn" [disabled]="noFiltersSelected()" mat-raised-button
                color="primary"
                (click)="submit()">
          {{'GENERAL.FILTERS.ACTIONS.APPLY' | translate}}
        </button>
      </div>

    </div>

  </ng-template>

  <ng-template cdkConnectedOverlay class="template"
               #netChangeOverlay="cdkConnectedOverlay"
               [cdkConnectedOverlayHasBackdrop]="true"
               [cdkConnectedOverlayOrigin]="NetChangeOverlayTrigger"
               [cdkConnectedOverlayOpen]="(isNetChangeOverlayOpen$ | async) || false"
               (backdropClick)="onNetChangeOverlayClosedByClickOutside()">
    <div class="filters-overlay">
      <div class="filters-header">
        <p class="filters-title">NET CHANGE</p>
      </div>

      <div class="filters-container display-flex">

        <mat-checkbox (change)="fromBeginningChange()" [formControl]="fromBeginningFormControl"> Since the beginning</mat-checkbox>

        <!-- Net date change range -->
        <mat-form-field *ngIf="isFromBeginning() " class="filter-field" appearance="outline" data-cy="input-created-at-filter">
          <mat-label>End Date</mat-label>
          <input matInput [matDatepicker]="endPicker" [formControl]="netChangeEndDateFormControl" placeholder="End date">
          <mat-datepicker-toggle matIconSuffix [for]="endPicker"></mat-datepicker-toggle>
          <mat-datepicker #endPicker></mat-datepicker>
        </mat-form-field>

        <mat-form-field *ngIf="!isFromBeginning() " class="filter-field" appearance="outline" data-cy="input-created-at-filter">
          <mat-label>Date Range</mat-label>
          <mat-date-range-input [rangePicker]="picker">
            <input matStartDate [formControl]="netChangeStartDateFormControl" placeholder="Start date">
            <input matEndDate [formControl]="netChangeEndDateFormControl" placeholder="End date">
          </mat-date-range-input>
          <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
          <mat-date-range-picker #picker></mat-date-range-picker>
        </mat-form-field>

      </div>

      <div class="filters-actions">
        <button class="flat-white-btn filters-cancel-btn" [disabled]="noNetChangeFiltersSelected()" mat-flat-button fxShow
                (click)="clearNetChangeFilters()" [fxShow.xs]="false" [fxShow.sm]="false">
          {{'GENERAL.FILTERS.ACTIONS.CLEAR' | translate}}
        </button>
        <button class="raised-primary-btn filters-apply-btn" [disabled]="noNetChangeFiltersSelected()" mat-raised-button
                color="primary"
                (click)="submit()">
          {{'GENERAL.FILTERS.ACTIONS.APPLY' | translate}}
        </button>
      </div>

    </div>

  </ng-template>

</mat-toolbar>
