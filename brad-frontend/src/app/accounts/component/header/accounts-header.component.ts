import {ChangeDetectorRef, Component, Input, OnDestroy, OnInit, ViewChild, ViewEncapsulation} from '@angular/core';
import {ActivatedRoute, Router} from "@angular/router";
import {FormControl, FormGroup} from "@angular/forms";
import {BehaviorSubject, filter, Observable, Subject, tap} from "rxjs";
import {CdkConnectedOverlay} from "@angular/cdk/overlay";
import {AccountFilters} from "../../../entities/account/account-filters";
import * as _ from "lodash";
import {takeUntil} from "rxjs/operators";
import {AccountFacade} from "../../facade/account.facade";
import {CountryApiService} from "../../../api/service/country-api.service";
import {Country} from "../../../entities/account/country";
import {CsFinActiveFiltersFacade, CsFinApiService, CsFinSidenavService} from "@jumia-cs-fin/common";
import {Currency} from "../../../entities/currency/currency";
import {CurrencyApiService} from "../../../api/service/currency-api.service";
import {AccountStatus} from "../../../entities/account/account-status";
import {EStatementSource} from "../../../entities/account/account-statement-source-types";
import {AccountTypes, AccountSubTypes} from "../../../entities/account/account-types";
import {NetChangeFilters} from "../../../entities/account/net-change-filters";
import {StatementPeriodicity} from "../../../entities/account/statement-periodicity";


@Component({
  selector: 'brad-accounts-header',
  templateUrl: './accounts-header.component.html',
  styleUrls: ['./accounts-header.component.scss', '../../../../assets/brad-custom.scss'],
  encapsulation: ViewEncapsulation.None
})
export class AccountsHeaderComponent implements OnInit, OnDestroy {
  @Input() showFilters!: boolean;
  filterTextFormControl!: FormControl;
  queryParams = {};
  combinedFilters = {}

  form!: FormGroup;
  companyIDFormControl!: FormControl;
  countryFormControl!: FormControl;
  navReferenceFormControl!: FormControl;
  beneficiaryNameFormControl!: FormControl;
  beneficiaryAddressFormControl!: FormControl;
  ibanFormControl!: FormControl;
  accountNumberFormControl!: FormControl;
  accountNameFormControl!: FormControl;
  swiftCodeFormControl!: FormControl;
  accountRoutingCodeFormControl!: FormControl;
  sortCodeFormControl!: FormControl;
  branchCodeFormControl!: FormControl;
  ribFormControl!: FormControl;
  currencyFormControl!: FormControl;
  createdAtStartFormControl!: FormControl;
  createdAtEndFormControl!: FormControl;
  lastProcessedStatementDateStartFormControl!: FormControl;
  lastProcessedStatementDateEndFormControl!: FormControl;
  accountTypeControl!: FormControl;
  accountSubTypeFormControl!: FormControl;
  statusFormControl!: FormControl;
  statementSourceFormControl!: FormControl;
  statementPeriodicityFormControl!: FormControl;
  partnerFormControl!: FormControl;
  phoneNumberFormControl!: FormControl;
  investmentIdFormControl!: FormControl;

  fromBeginningFormControl!: FormControl;
  netChangeStartDateFormControl!: FormControl;
  netChangeEndDateFormControl!: FormControl;

  isOpen$!: Observable<boolean>;
  isNetChangeOverlayOpen$!: Observable<boolean>;
  isRefreshing = false;
  isInitializing = false;

  currencySearchFormControl = new FormControl();
  currencyList: Currency[] = [];
  filteredCurrencyList: Currency[] = [];

  countrySearchFormControl = new FormControl();
  countryList: Country[] = [];
  filteredCountryList: Country[] = [];

  statusList: AccountStatus[] = [];
  statementSourceList: String[] =  Object.keys(EStatementSource).filter((item) => {
    return isNaN(Number(item));
  });

  accountTypesList: String[] =  Object.keys(AccountTypes).filter((item) => {
    return isNaN(Number(item));
  });

  accountSubTypesList: String[] =  Object.keys(AccountSubTypes).filter((item) => {
    return isNaN(Number(item));
  });

  statementPeriodicityList: String[] =  Object.keys(StatementPeriodicity).filter((item) => {
    return isNaN(Number(item));
  });

  private readonly refreshTimeout = 2000;
  private _onDestroy: Subject<void> = new Subject<void>();
  private _isOpen!: BehaviorSubject<boolean>;
  private _isNetChangeOverlayOpen!: BehaviorSubject<boolean>;
  private _isNetChangeFilterApplied = new BehaviorSubject<boolean>(false);

  @ViewChild(CdkConnectedOverlay, {static: true})
  private connectedOverlay!: CdkConnectedOverlay;

  @ViewChild('netChangeOverlay', {read: CdkConnectedOverlay, static:true})
  private netChangeOverlay!: CdkConnectedOverlay;

  constructor(public ref: ChangeDetectorRef,
              public sidenav: CsFinSidenavService,
              private activatedRoute: ActivatedRoute,
              private activeFiltersFacade: CsFinActiveFiltersFacade,
              private router: Router,
              private apiService: CsFinApiService,
              private accountFacade: AccountFacade,
              private countryApiService: CountryApiService,
              private currencyApiService: CurrencyApiService) {
  }


  async ngOnInit(): Promise<void> {
    this.isInitializing = true;
    this.initializeOverlay();
    this.initFiltersSearch();
    this.subscribeUrlParamsChanges();
    this.subscribeActiveFiltersChange();
    this.subscribeFiltersChange();
    this.subscribeNetChangeFiltersChange();
  }

  ngOnDestroy(): void {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  private subscribeUrlParamsChanges(): void {
    this.activatedRoute.queryParams
      .pipe(takeUntil(this._onDestroy))
      .subscribe(params => this.initializeFormData(params));
  }

  private subscribeActiveFiltersChange(): void {
    this.activeFiltersFacade.openFiltersOverlay
      .pipe(takeUntil(this._onDestroy))
      .subscribe(value => this._isOpen.next(value));
  }

  private subscribeFiltersChange(): void {
    this.accountFacade.filters$
      .pipe(takeUntil(this._onDestroy))
      .subscribe((filters: AccountFilters) => {
        if (!this.isInitializing) {
          this.combinedFilters= {...this.combinedFilters, ...filters}
          this.updateFormData(filters);
          this.updateMissingUrlFilters(filters);
        }
      });
  }

  private subscribeNetChangeFiltersChange(): void {
    this.accountFacade.netChangeFilters$
    .pipe(takeUntil(this._onDestroy))
    .subscribe((filters: NetChangeFilters) => {
      if (!this.isInitializing) {
        this.updateNetChangeFormData(filters);
        filters.partitionKeys=[];
        this.combinedFilters= {...this.combinedFilters, ...filters}
        this.updateNetChangeFormData(filters);
        this.updateMissingUrlFilters(this.combinedFilters);
      }
    });
  }

  private initFiltersSearch(): void {
    this.initCountrySearch();
    this.initCurrencySearch();
  }


  private initializeOverlay(): void {
    this._isOpen = new BehaviorSubject<boolean>(false);
    this._isNetChangeOverlayOpen = new BehaviorSubject<boolean>(false);
    this.connectedOverlay.backdropClass = 'cdk-overlay-transparent-backdrop';
    this.connectedOverlay.panelClass = 'filters-overlay-panel';
    this.connectedOverlay.backdropClick
    .pipe(tap(() => {
      this._isOpen.next(false);
    })).subscribe();

    this.netChangeOverlay.backdropClick
    .pipe(tap(() => {
      this._isNetChangeOverlayOpen.next(false)
    })).subscribe();

    this.isOpen$ = this._isOpen.asObservable();
    this.isNetChangeOverlayOpen$ = this._isNetChangeOverlayOpen.asObservable();
  }

  private initCountrySearch(): void {
    this.countrySearchFormControl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe((value: String) => {
        const searchTerm = value.trim();
        if (searchTerm) {
          this.filteredCountryList = this.countryList.filter((country) => {
            return country.name.trim().toLowerCase().includes(searchTerm.toLowerCase());
          })
        } else {
          this.filteredCountryList = this.countryList;
        }
      });
  }

  private initCurrencySearch(): void {
    this.currencySearchFormControl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe((value: String) => {
        const searchTerm = value.trim();
        if (searchTerm) {
          this.filteredCurrencyList = this.currencyList.filter((currency) => {
            return currency.code.trim().toLowerCase().includes(searchTerm.toLowerCase());
          })
        } else {
          this.filteredCurrencyList = this.currencyList;
        }
      });
  }

  clearFilters(): void {
    this.form.reset('', {emitEvent: false});
    this.accountFacade.filtersChanged({});
  }

  clearNetChangeFilters(): void {
    this.fromBeginningFormControl.reset()
    this.netChangeStartDateFormControl.reset()
    this.netChangeEndDateFormControl.reset()
    this._isNetChangeFilterApplied.next(true);
    this.submit()
  }

  submit(input?: HTMLInputElement) {
    this.accountFacade.filtersChanged(this.getFormValues());
    this.accountFacade.netChangeFiltersChanged(this.getNetChangeValuesFromForm());
    const formQueryParams = this.apiService.buildQueryParams(this.getFormValues());
    if (_.isEqual(this.queryParams, formQueryParams)) {
      this.isRefreshing = true;
      this.ref.markForCheck();
      setTimeout(() => {
        this.isRefreshing = false;
        this.ref.markForCheck();
      }, this.refreshTimeout);
    } else {
      input?.blur();
      this.router.navigate(['/accounts'], {queryParams: formQueryParams});
    }
    this.closeOverlay();
    this.closeNetChangeOverlay();
    this._isNetChangeFilterApplied.next(!this._isNetChangeFilterApplied.getValue());
  }

  triggerOverlay(): void {
    this._isOpen.next(!this._isOpen.value);
  }

  onNetChangeOverlayClosedByClickOutside(){
    if(!this._isNetChangeFilterApplied.getValue()){
      this.fromBeginningFormControl.reset();
      this.netChangeEndDateFormControl.reset();
    }
  }

  triggerNetChangeOverlay(): void {
    this._isNetChangeOverlayOpen.next(!this._isNetChangeOverlayOpen.value);
    this.fromBeginningFormControl.setValue(this._isNetChangeOverlayOpen.value, {emitEvent: false});
    this.netChangeEndDateFormControl.setValue(new Date());
  }

  closeOverlay(): void {
    this._isOpen.next(false);
  }

  closeNetChangeOverlay(): void {
    this._isNetChangeOverlayOpen.next(false);
  }

  private getFormValues(): AccountFilters {
    return this.form.value as AccountFilters;
  }
  private getNetChangeValuesFromForm(): NetChangeFilters {
    return this.form.value as NetChangeFilters;
  }

  private initializeFormData(params: any): void {
    this.queryParams = params;
    if (!this.form) {
      this.form = new FormGroup({});
      this.initializeNetChangeFilters(params);
      this.initializeFormControlsAndFilters(params);
    }
  }

  private initializeFormControlsAndFilters(params: any): void {
    const filters: any = {};

    this.filterTextFormControl = new FormControl(params.filterText);
    filters.filterText = params.filterText;

    this.companyIDFormControl = new FormControl(params.companyID);
    filters.companyID = params.companyID;

    this.navReferenceFormControl = new FormControl(params.navReference);
    filters.navReference = params.navReference;

    this.beneficiaryNameFormControl = new FormControl(params.beneficiaryName);
    filters.beneficiaryName = params.beneficiaryName;

    this.beneficiaryAddressFormControl = new FormControl(params.beneficiaryAddress);
    filters.beneficiaryAddress = params.beneficiaryAddress;

    this.ibanFormControl = new FormControl(params.iban);
    filters.iban = params.iban;

    this.accountNumberFormControl = new FormControl(params.accountNumber);
    filters.accountNumber = params.accountNumber;

    this.accountNameFormControl = new FormControl(params.accountName);
    filters.accountName = params.accountName;

    this.swiftCodeFormControl = new FormControl(params.swiftCode);
    filters.swiftCode = params.swiftCode;

    this.accountRoutingCodeFormControl = new FormControl(params.accountRoutingCode);
    filters.accountRoutingCode = params.accountRoutingCode;

    this.sortCodeFormControl = new FormControl(params.sortCode);
    filters.sortCode = params.sortCode;

    this.branchCodeFormControl = new FormControl(params.branchCode);
    filters.branchCode = params.branchCode;

    this.ribFormControl = new FormControl(params.rib);
    filters.rib = params.rib;

    this.createdAtStartFormControl = new FormControl(params.createdAtStart);
    filters.createdAtStart = params.createdAtStart;

    this.createdAtEndFormControl = new FormControl(params.createdAtEnd);
    filters.createdAtEnd = params.createdAtEnd;

    this.lastProcessedStatementDateStartFormControl = new FormControl(params.lastProcessedStatementDateStart);
    filters.lastProcessedStatementDateStart = params.lastProcessedStatementDateStart;

    this.lastProcessedStatementDateEndFormControl = new FormControl(params.lastProcessedStatementDateEnd);
    filters.lastProcessedStatementDateEnd = params.lastProcessedStatementDateEnd;

    let types: string[] = params.types === undefined ? undefined : params.types.split(',');
    this.accountTypeControl = new FormControl(types);
    filters.types = types;

    let subTypes: string[] = params.subTypes === undefined ? undefined : params.subTypes.split(',');
    this.accountSubTypeFormControl = new FormControl(subTypes);
    filters.subTypes = subTypes;

    let status: string[] = params.status === undefined ? ["OPEN"] : params.status.split(',');
    this.statusFormControl = new FormControl(status);
    filters.status = status;

    this.statementSourceFormControl = new FormControl(params.statementSource);
    filters.statementSource = params.statementSource;

    this.statementPeriodicityFormControl = new FormControl(params.statementPeriodicity)
    filters.statementPeriodicity = params.statementPeriodicity;

    this.partnerFormControl = new FormControl(params.partner);
    filters.partner = params.partner;

    this.phoneNumberFormControl = new FormControl(params.phoneNumber);
    filters.phoneNumber = params.phoneNumber;

    this.investmentIdFormControl = new FormControl(params.investmentId);
    filters.investmentId = params.investmentId;

    filters.orderField = params.orderField;
    filters.orderDirection = params.orderDirection;
    filters.page = params.page;
    filters.size = params.size;

    Promise.all([
      this.applyCountryUrlFilter(params, filters),
      this.applyCurrencyUrlFilter(params, filters),
      this.applyStatusFilter(params, filters),
    ]).then(() => {
      this.setFormControlsToForm();
      this.accountFacade.filtersChanged(filters);
      this.updateMissingUrlFilters(params)
      this.isInitializing = false;
    });

  }

  private initializeNetChangeFilters(params: any): void {
    const netChangeFilters: NetChangeFilters = {};
    this.fromBeginningFormControl = new FormControl(params.fromBeginning);
    netChangeFilters.fromBeginning = params.fromBeginning

    this.netChangeStartDateFormControl = new FormControl(params.startDate);
    netChangeFilters.startDate = params.startDate

    this.netChangeEndDateFormControl = new FormControl(params.endDate);
    netChangeFilters.endDate = params.endDate;

    this.accountFacade.netChangeFiltersChanged(netChangeFilters);
    this.addNetChangeFormControlsToForm();

  }


  private applyCountryUrlFilter(params: any, filters: AccountFilters): Promise<void> {
    return new Promise<void>(async (resolve) => {
      this.countryFormControl = new FormControl();
      if (!params.countryCodes) {
        resolve();
        return;
      }

      await this.loadCountries();
      const filterCountriesIds = params.countryCodes.split(',')?.map((countryId: string) => Number(countryId));
      if (filterCountriesIds) {
        filters.countryCodes = this.countryList.filter((country: Country) => filterCountriesIds.includes(country.id));
        this.countryFormControl = new FormControl(filters.countryCodes);
      }
      resolve();
    });
  }

  private applyCurrencyUrlFilter(params: any, filters: AccountFilters): Promise<void> {
    return new Promise<void>(async (resolve) => {
      this.currencyFormControl = new FormControl();
      if (!params.currencyCodes) {
        resolve();
        return;
      }

      await this.loadCurrencies();
      const filterCurrenciesIds = params.currencyCodes.split(',')?.map((currencyId: string) => Number(currencyId));
      if (filterCurrenciesIds) {
        filters.currencyCodes = this.currencyList.filter((currency: Currency) => filterCurrenciesIds.includes(currency.id));
        this.currencyFormControl = new FormControl(filters.currencyCodes);
      }
      resolve();
    });
  }

  private applyStatusFilter(params: any, filters: AccountFilters): Promise<void> {
    return new Promise<void>(async (resolve) => {

      if (!params.status) {
        resolve();
        return;
      }

      await this.loadStatuses();

      this.statusFormControl.setValue(filters.status, {emitEvent: false})
      resolve();
    });
  }

  private updateFormData(params: AccountFilters): void {
    this.filterTextFormControl.setValue(params.filterText, {emitEvent: false});
    this.companyIDFormControl.setValue(params.companyID, {emitEvent: false});
    this.countryFormControl.setValue(params.countryCodes, {emitEvent: false});
    this.navReferenceFormControl.setValue(params.navReference, {emitEvent: false});
    this.beneficiaryNameFormControl.setValue(params.beneficiaryName, {emitEvent: false});
    this.beneficiaryAddressFormControl.setValue(params.beneficiaryAddress, {emitEvent: false});
    this.ibanFormControl.setValue(params.iban, {emitEvent: false});
    this.accountNumberFormControl.setValue(params.accountNumber, {emitEvent: false});
    this.accountNameFormControl.setValue(params.accountName, {emitEvent: false});
    this.swiftCodeFormControl.setValue(params.swiftCode, {emitEvent: false});
    this.accountRoutingCodeFormControl.setValue(params.accountRoutingCode, {emitEvent: false});
    this.sortCodeFormControl.setValue(params.sortCode, {emitEvent: false});
    this.branchCodeFormControl.setValue(params.branchCode, {emitEvent: false});
    this.ribFormControl.setValue(params.rib, {emitEvent: false});
    this.currencyFormControl.setValue(params.currencyCodes, {emitEvent: false});
    this.createdAtStartFormControl.setValue(params.createdAtStart, {emitEvent: false});
    this.createdAtEndFormControl.setValue(params.createdAtEnd, {emitEvent: false});
    this.lastProcessedStatementDateStartFormControl.setValue(params.lastProcessedStatementDateStart, {emitEvent: false});
    this.lastProcessedStatementDateEndFormControl.setValue(params.lastProcessedStatementDateEnd, {emitEvent: false});
    this.accountTypeControl.setValue(params.types, {emitEvent: false});
    this.accountSubTypeFormControl.setValue(params.subTypes, {emitEvent: false});
    this.statusFormControl.setValue(params.status, {emitEvent: false});
    this.statementSourceFormControl.setValue(params.statementSource, {emitEvent: false});
    this.statementPeriodicityFormControl.setValue(params.statementPeriodicity, {emitEvent: false})
    this.partnerFormControl.setValue(params.partner, {emitEvent: false});
    this.phoneNumberFormControl.setValue(params.phoneNumber, {emitEvent: false});
    this.investmentIdFormControl.setValue(params.investmentId, {emitEvent: false});
  }

  private updateNetChangeFormData(params: NetChangeFilters): void {
    this.fromBeginningFormControl.setValue(params.fromBeginning, {emitEvent: false});
    this.netChangeStartDateFormControl.setValue(params.startDate, {emitEvent: false});
    this.netChangeEndDateFormControl.setValue(params.endDate, {emitEvent: false});

  }

  private updateMissingUrlFilters(filters: any): void {
    const formQueryParams = this.apiService.buildQueryParams(filters);
    if (!_.isEqual(this.queryParams, formQueryParams)) {
      this.router.navigate(['accounts'], {queryParams: formQueryParams});
    }
  }

  private setFormControlsToForm(): void {
    this.form.addControl(this.accountFacade.filterTextKey, this.filterTextFormControl);
    this.form.addControl(this.accountFacade.companyIDKey, this.companyIDFormControl);
    this.form.addControl(this.accountFacade.countryKey, this.countryFormControl);
    this.form.addControl(this.accountFacade.navReferenceKey, this.navReferenceFormControl);
    this.form.addControl(this.accountFacade.beneficiaryNameKey, this.beneficiaryNameFormControl);
    this.form.addControl(this.accountFacade.beneficiaryAddressKey, this.beneficiaryAddressFormControl);
    this.form.addControl(this.accountFacade.ibanKey, this.ibanFormControl);
    this.form.addControl(this.accountFacade.accountNumberKey, this.accountNumberFormControl);
    this.form.addControl(this.accountFacade.accountNameKey, this.accountNameFormControl);
    this.form.addControl(this.accountFacade.swiftCodeKey, this.swiftCodeFormControl);
    this.form.addControl(this.accountFacade.accountRoutingCodeKey, this.accountRoutingCodeFormControl);
    this.form.addControl(this.accountFacade.sortCodeKey, this.sortCodeFormControl);
    this.form.addControl(this.accountFacade.branchCodeKey, this.branchCodeFormControl);
    this.form.addControl(this.accountFacade.ribKey, this.ribFormControl);
    this.form.addControl(this.accountFacade.currencyKey, this.currencyFormControl);
    this.form.addControl(this.accountFacade.createdAtStartKey, this.createdAtStartFormControl);
    this.form.addControl(this.accountFacade.createdAtEndKey, this.createdAtEndFormControl);
    this.form.addControl(this.accountFacade.lastProcessedStmtDateStartKey, this.lastProcessedStatementDateStartFormControl);
    this.form.addControl(this.accountFacade.lastProcessedStmtDateEndKey, this.lastProcessedStatementDateEndFormControl);
    this.form.addControl(this.accountFacade.typeKey, this.accountTypeControl);
    this.form.addControl(this.accountFacade.subTypeKey, this.accountSubTypeFormControl);
    this.form.addControl(this.accountFacade.statusKey, this.statusFormControl);
    this.form.addControl(this.accountFacade.statementSourceKey, this.statementSourceFormControl);
    this.form.addControl(this.accountFacade.statementPeriodicityKey, this.statementPeriodicityFormControl)
    this.form.addControl(this.accountFacade.partnerKey, this.partnerFormControl);
    this.form.addControl(this.accountFacade.phoneNumberKey, this.phoneNumberFormControl);
    this.form.addControl(this.accountFacade.investmentIdKey, this.investmentIdFormControl);
  }

  private addNetChangeFormControlsToForm(): void {
    this.form.addControl(this.accountFacade.fromBeginningKey, this.fromBeginningFormControl);
    this.form.addControl(this.accountFacade.netChangeStartDateKey, this.netChangeStartDateFormControl);
    this.form.addControl(this.accountFacade.netChangeEndDateKey, this.netChangeEndDateFormControl);
  }

  noFiltersSelected() {
    return !!this.form.errors || this.isRefreshing;
  }

  noNetChangeFiltersSelected() {
    return (!this.fromBeginningFormControl.value && !this.netChangeEndDateFormControl.value)
    || (!this.netChangeStartDateFormControl.value && !this.netChangeEndDateFormControl.value)
  }


  isFromBeginning() {
   return this.fromBeginningFormControl.value;
  }

  fromBeginningChange() {
    if(this.fromBeginningFormControl.value==true){
      this.netChangeStartDateFormControl.reset()
    }else{
      this.netChangeStartDateFormControl.reset()
      this.netChangeEndDateFormControl.reset()
    }
  }

  loadCountries(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if (this.countryList.length <= 0) {
        this.countryApiService.getAll()
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (countries: Country[]) => {
              this.countryList = countries;
              this.filteredCountryList = this.countryList;
              resolve();
            }, error: (error) => reject(error)
          });
      } else {
        resolve();
      }
    });
  }

  loadCurrencies(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if (this.currencyList.length <= 0) {
        this.currencyApiService.getAll()
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (currencies: Currency[]) => {
              this.currencyList = currencies;
              this.filteredCurrencyList = this.currencyList;
              resolve();
            }, error: (error) => reject(error)
          });
      } else {
        resolve();
      }
    });
  }

  compareIdFn(o1: any, o2: any): boolean {
    return o1 && o2 && o1.id === o2.id;
  }

  loadStatuses() {
    if (this.statusList.length == 0) {
      new Promise<void>((resolve, reject) => {
        this.accountFacade.getStatuses()
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (accountStatus: AccountStatus[]) => {
              this.statusList = accountStatus;
              resolve();
            },
            error: (error) => reject(error)
          });
      });
    }
  }

  protected readonly filter = filter;
}
