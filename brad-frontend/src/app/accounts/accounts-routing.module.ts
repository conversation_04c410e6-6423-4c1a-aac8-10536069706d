import {RouterModule, Routes} from '@angular/router';
import {NgModule} from '@angular/core';
import {NavigationComponent} from '../navigation/navigation.component';
import {AccountsDetailsComponent} from "./component/details/accounts-details.component";
import {AccountsComponent} from "./accounts.component";
import {
  csFinAuthCanActivateGuard,
  CsFinAuthorizationOnAnyTargetOfTypeService
} from "@jumia-cs-fin/common";
import {bradPermissions} from "../auth/constants/permission.constants";
import {bradAuthCountryTarget} from "../auth/constants/auth.constants";

export const csFinAuthorizationOnAnyTargetOfTypeService =  new CsFinAuthorizationOnAnyTargetOfTypeService();
const routes: Routes = [
  {
    path: 'accounts',
    component: NavigationComponent,
    children: [
      {
        path: '',
        canActivate: [csFinAuthCanActivateGuard],
        data: {
          auth: {
            permissions: bradPermissions.BRAD_ACCESS_ACCOUNTS,
            targets: bradAuthCountryTarget,
            authTypeService: csFinAuthorizationOnAnyTargetOfTypeService,
          }
        },
        component: AccountsComponent
      }
    ]
  },
  {
    path: 'accounts/:accountID/details',
    component: NavigationComponent,
    children: [
      {
        path: '',
        component: AccountsDetailsComponent
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class AccountsRoutingModule { }
