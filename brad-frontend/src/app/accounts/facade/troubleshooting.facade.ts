import {Injectable} from "@angular/core";
import {AccountsApiService} from "../../api/service/accounts-api.service";
import {BehaviorSubject, Observable, of, Subject} from "rxjs";
import {getPropertyKey} from "../../shared/service/ts-utils.service";
import {AccountFilters} from "../../entities/account/account-filters";
import {activeFiltersSeparator, CsFinActiveFilterChip} from "@jumia-cs-fin/common";
import {PageResponse} from "../../entities/page-response";
import {Country} from "../../entities/account/country";

import {TroubleshootingAccount} from "../../entities/account/troubleshooting-account";

@Injectable({providedIn: 'root'})
export class TroubleshootingAccountFacade {

  forceRefresh: Subject<boolean> = new Subject<boolean>();

  public fullscreenChangeBehaviorSubject = new BehaviorSubject<boolean>(true);
  public detailsCloseBehaviorSubject = new BehaviorSubject<boolean>(false);
  public selectedAccountChangeBehaviorSubject = new BehaviorSubject<number>(-1);

  readonly filterTextKey = getPropertyKey<AccountFilters>(p => p.filterText);
  readonly companyIDKey = getPropertyKey<AccountFilters>(p => p.companyID);
  readonly countryKey = getPropertyKey<AccountFilters>(p => p.countryCodes);
  readonly navReferenceKey = getPropertyKey<AccountFilters>(p => p.navReference);
  readonly beneficiaryNameKey = getPropertyKey<AccountFilters>(p => p.beneficiaryName);
  readonly beneficiaryAddressKey = getPropertyKey<AccountFilters>(p => p.beneficiaryAddress);
  readonly ibanKey = getPropertyKey<AccountFilters>(p => p.iban);
  readonly accountNumberKey = getPropertyKey<AccountFilters>(p => p.accountNumber);
  readonly accountNameKey = getPropertyKey<AccountFilters>(p => p.accountName);
  readonly swiftCodeKey = getPropertyKey<AccountFilters>(p => p.swiftCode);
  readonly accountRoutingCodeKey = getPropertyKey<AccountFilters>(p => p.accountRoutingCode);
  readonly sortCodeKey = getPropertyKey<AccountFilters>(p => p.sortCode);
  readonly branchCodeKey = getPropertyKey<AccountFilters>(p => p.branchCode);
  readonly ribKey = getPropertyKey<AccountFilters>(p => p.rib);
  readonly currencyKey = getPropertyKey<AccountFilters>(p => p.currencyCodes);
  readonly createdAtStartKey = getPropertyKey<AccountFilters>(p => p.createdAtStart);
  readonly createdAtEndKey = getPropertyKey<AccountFilters>(p => p.createdAtEnd);
  readonly statusKey = getPropertyKey<AccountFilters>(p => p.status);
  readonly troubleshootingKey = getPropertyKey<AccountFilters>(p => p.troubleshooting);

  private filters!: AccountFilters;

  private readonly activeFiltersConfigMap = new Map<string, any>([
    [
      this.filterTextKey,
      (filterText: string) => {
        return {labelKey: 'GENERAL.FIELDS.FILTER_TEXT', displayText: filterText}
      }
    ],
    [
      this.companyIDKey,
      (companyID: string) => {
        return {labelKey: 'ACCOUNTS.FIELDS.COMPANY_ID', displayText: companyID}
      }
    ],
    [
      this.countryKey,
      (countries: Country[]) => {
        return {
          labelKey: 'ACCOUNTS.FIELDS.COUNTRY',
          displayText: countries.map((country: Country) => country.name).join(activeFiltersSeparator)
        }
      }
    ],
    [
      this.navReferenceKey,
      (navReference: string) => {
        return {labelKey: 'ACCOUNTS.FIELDS.NAV_REFERENCE', displayText: navReference}
      }
    ],
    [
      this.beneficiaryNameKey,
      (beneficiaryName: string) => {
        return {labelKey: 'ACCOUNTS.FIELDS.BENEFICIARY_NAME', displayText: beneficiaryName}
      }
    ],
    [
      this.beneficiaryAddressKey,
      (beneficiaryAddress: string) => {
        return {labelKey: 'ACCOUNTS.FIELDS.BENEFICIARY_ADDRESS', displayText: beneficiaryAddress}
      }
    ],
    [
      this.ibanKey,
      (iban: string) => {
        return {labelKey: 'ACCOUNTS.FIELDS.IBAN', displayText: iban}
      }
    ],
    [
      this.accountNumberKey,
      (accountNumber: string) => {
        return {labelKey: 'ACCOUNTS.FIELDS.ACCOUNT_NUMBER', displayText: accountNumber}
      }
    ],
    [
      this.accountNameKey,
      (accountName: string) => {
        return {labelKey: 'ACCOUNTS.FIELDS.ACCOUNT_NAME', displayText: accountName}
      }
    ],
    [
      this.swiftCodeKey,
      (swiftCode: string) => {
        return {labelKey: 'ACCOUNTS.FIELDS.SWIFT_CODE', displayText: swiftCode}
      }
    ],
    [
      this.accountRoutingCodeKey,
      (accountRoutingCode: string) => {
        return {labelKey: 'ACCOUNTS.FIELDS.ACCOUNT_ROUTING_CODE', displayText: accountRoutingCode}
      }
    ],
    [
      this.sortCodeKey,
      (sortCode: string) => {
        return {labelKey: 'ACCOUNTS.FIELDS.SORT_CODE', displayText: sortCode}
      }
    ],
    [
      this.branchCodeKey,
      (branchCode: string) => {
        return {labelKey: 'ACCOUNTS.FIELDS.BRANCH_CODE', displayText: branchCode}
      }
    ],
    [
      this.ribKey,
      (rib: string) => {
        return {labelKey: 'ACCOUNTS.FIELDS.RIB', displayText: rib}
      }
    ],
    [
      this.currencyKey,
      (currencies: string[]) => {
        return {
          labelKey: 'ACCOUNTS.FIELDS.CURRENCY',
          displayText: currencies.map((currency: string) => currency).join(activeFiltersSeparator)
        }
      }
    ],
    [
      this.createdAtStartKey,
      (createdAt: string) => {
        return {labelKey: 'ACCOUNTS.FIELDS.CREATED_AT', displayText: createdAt}
      }
    ],
    [
      this.statusKey,
      (status: string) => {
        return {labelKey: 'ACCOUNTS.FIELDS.STATUS', displayText: status}
      }
    ],
    [
      this.troubleshootingKey,
      (troubleshooting: string) => {
        return {labelKey: 'TROUBLESHOOTING.FIELDS.TROUBLESHOOTING', displayText: troubleshooting}
      }
    ]
  ]);

  private filtersBehaviorSubject = new BehaviorSubject<AccountFilters>({});
  private activeFilterChips = new Map<string, CsFinActiveFilterChip>();
  public filters$: Observable<AccountFilters> = this.filtersBehaviorSubject.asObservable();
  public activeFiltersChips$: Observable<Map<string, CsFinActiveFilterChip>> = of(this.activeFilterChips);

  constructor(public accountsApiService: AccountsApiService) {
  }

  filtersChanged(filters: AccountFilters) {

    if (this.filters !== filters) {
      this.filters = filters;
    }

    if (!filters.createdAtStart) {
      filters.createdAtStart = undefined;
      filters.createdAtEnd = undefined;
    }
    this.filtersBehaviorSubject.next(filters);
    this.updateActiveFilterChips(filters);
  }

  private updateActiveFilterChips(filters: AccountFilters): void {
    if (!filters) {
      return;
    }
    this.activeFilterChips.clear();

    if(filters.filterText){
      this.activeFilterChips.set(this.filterTextKey, this.activeFiltersConfigMap.get(this.filterTextKey)(filters.filterText));
    }

    if(filters.companyID){
      this.activeFilterChips.set(this.companyIDKey, this.activeFiltersConfigMap.get(this.companyIDKey)(filters.companyID));
    }
    if(filters.countryCodes && filters.countryCodes.length > 0){
      this.activeFilterChips.set(this.countryKey, this.activeFiltersConfigMap.get(this.countryKey)(filters.countryCodes));
    }
    if(filters.navReference){
      this.activeFilterChips.set(this.navReferenceKey, this.activeFiltersConfigMap.get(this.navReferenceKey)(filters.navReference));
    }
    if(filters.beneficiaryName){
      this.activeFilterChips.set(this.beneficiaryNameKey, this.activeFiltersConfigMap.get(this.beneficiaryNameKey)(filters.beneficiaryName));
    }
    if(filters.beneficiaryAddress){
      this.activeFilterChips.set(this.beneficiaryAddressKey, this.activeFiltersConfigMap.get(this.beneficiaryAddressKey)(filters.beneficiaryAddress));
    }
    if(filters.iban){
      this.activeFilterChips.set(this.ibanKey, this.activeFiltersConfigMap.get(this.ibanKey)(filters.iban));
    }
    if(filters.accountNumber){
      this.activeFilterChips.set(this.accountNumberKey, this.activeFiltersConfigMap.get(this.accountNumberKey)(filters.accountNumber));
    }
    if(filters.accountName){
      this.activeFilterChips.set(this.accountNameKey, this.activeFiltersConfigMap.get(this.accountNameKey)(filters.accountName));
    }
    if(filters.swiftCode){
      this.activeFilterChips.set(this.swiftCodeKey, this.activeFiltersConfigMap.get(this.swiftCodeKey)(filters.swiftCode));
    }
    if(filters.accountRoutingCode){
      this.activeFilterChips.set(this.accountRoutingCodeKey, this.activeFiltersConfigMap.get(this.accountRoutingCodeKey)(filters.accountRoutingCode));
    }
    if(filters.sortCode){
      this.activeFilterChips.set(this.sortCodeKey, this.activeFiltersConfigMap.get(this.sortCodeKey)(filters.sortCode));
    }
    if(filters.branchCode){
      this.activeFilterChips.set(this.branchCodeKey, this.activeFiltersConfigMap.get(this.branchCodeKey)(filters.branchCode));
    }
    if(filters.rib){
      this.activeFilterChips.set(this.ribKey, this.activeFiltersConfigMap.get(this.ribKey)(filters.rib));
    }
    if(filters.currencyCodes && filters.currencyCodes.length > 0){
      this.activeFilterChips.set(this.currencyKey, this.activeFiltersConfigMap.get(this.currencyKey)(filters.currencyCodes));
    }
    if(filters.createdAtStart){
      let dateToDisplay = new Date(filters.createdAtStart!).toDateString();
      if (filters.createdAtEnd) {
        dateToDisplay += " - " + new Date(filters.createdAtEnd!).toDateString();
      }
      this.activeFilterChips.set(this.createdAtStartKey, this.activeFiltersConfigMap.get(this.createdAtStartKey)(dateToDisplay));
    }
    if(filters.status && filters.status.length > 0) {
      this.activeFilterChips.set(this.statusKey, this.activeFiltersConfigMap.get(this.statusKey)(filters.status));
    }
    if(filters.troubleshooting ) {
      this.activeFilterChips.set(this.troubleshootingKey, this.activeFiltersConfigMap.get(this.troubleshootingKey)(filters.troubleshooting));
    }
  }

  getTroubleShooting(): Observable<PageResponse<TroubleshootingAccount>> {
    return this.accountsApiService.getTroubleShooting(this.filters);
  }

  download(filters?: AccountFilters)  {
    return this.accountsApiService.download(filters);
  }

}
