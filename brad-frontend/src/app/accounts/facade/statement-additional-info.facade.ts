import {Injectable} from "@angular/core";
import {
  AccountStatementInformationFilters
} from "../../entities/account/account-statement-information-filters";
import {BehaviorSubject, Observable} from "rxjs";
import {TransactionFilters} from "../../entities/transaction/transaction-filters";
import {StatementFilters} from "../../entities/statement/statement-filters";

@Injectable({providedIn: 'root'})
export class StatementAdditionalInfoFacade {
  private filtersBehaviorSubject = new BehaviorSubject<AccountStatementInformationFilters>({});
  public filters$: Observable<AccountStatementInformationFilters> = this.filtersBehaviorSubject.asObservable();

  filtersChanged(filters: AccountStatementInformationFilters) {
    this.filtersBehaviorSubject.next({ ...filters });
  }

  changeTransactionFilters(transactionFilters: TransactionFilters) {
    const filters : AccountStatementInformationFilters = { ...this.filtersBehaviorSubject.getValue() };
    filters.transactionFilters = transactionFilters;
    this.filtersChanged(filters);
  }

  changeStatementFilters(statementFilters: StatementFilters) {
    const filters : AccountStatementInformationFilters = { ...this.filtersBehaviorSubject.getValue() };
    filters.statementFilters = statementFilters;
    this.filtersChanged(filters);
  }

}
