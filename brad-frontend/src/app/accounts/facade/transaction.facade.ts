import {Injectable} from "@angular/core";
import {BehaviorSubject, Observable, of, Subject} from "rxjs";
import {getPropertyKey} from "../../shared/service/ts-utils.service";
import {activeFiltersSeparator, CsFinActiveFilterChip, CsFinColumnDetails} from "@jumia-cs-fin/common";
import {PageResponse} from "../../entities/page-response";
import {TransactionFilters} from "src/app/entities/transaction/transaction-filters";
import {Transaction} from "src/app/entities/transaction/transaction";
import {TransactionsApiService} from "src/app/api/service/transactions-api.service";
import {CsFinGroup} from "../../entities/cs-fin-group";
import {CsFinFilterList, CsFinGroupFacade} from "../../entities/cs-fin-table-group";
import {CsFinGroupedEntityInfo} from "../../entities/cs-fin-grouped-entity";


@Injectable({providedIn: 'root'})
export class TransactionFacade implements CsFinGroupFacade<Transaction, TransactionFilters>{

  public mostRecentStatementIDBehaviorSubject = new BehaviorSubject<number>(-1);
  public hideUnwantedFilters = new BehaviorSubject<boolean>(false);

  forceRefresh: Subject<boolean> = new Subject<boolean>();

  readonly filterTextKey = getPropertyKey<TransactionFilters>(p => p.filterText);
  readonly currencyKey = getPropertyKey<TransactionFilters>(p => p.currencyCodes);
  readonly typeKey = getPropertyKey<TransactionFilters>(p => p.type);
  readonly accountIDKey = getPropertyKey<TransactionFilters>(p => p.accountId);
  readonly partitionKey = getPropertyKey<TransactionFilters>(p => p.partitionKey);
  readonly valueDateStartKey = getPropertyKey<TransactionFilters>(p => p.valueDateStart);
  readonly valueDateEndKey = getPropertyKey<TransactionFilters>(p => p.valueDateEnd);
  readonly transactionDateStartKey = getPropertyKey<TransactionFilters>(p => p.transactionDateStart);
  readonly transactionDateEndKey = getPropertyKey<TransactionFilters>(p => p.transactionDateEnd);
  readonly statementDateStartKey = getPropertyKey<TransactionFilters>(p => p.statementDateStart);
  readonly statementDateEndKey = getPropertyKey<TransactionFilters>(p => p.statementDateEnd);
  readonly directionKey = getPropertyKey<TransactionFilters>(p => p.direction);
  readonly amountKey = getPropertyKey<TransactionFilters>(p => p.amount);
  readonly referenceKey = getPropertyKey<TransactionFilters>(p => p.reference);
  readonly descriptionKey = getPropertyKey<TransactionFilters>(p => p.description);
  readonly accountStatementIDKey = getPropertyKey<TransactionFilters>(p => p.accountStatementID);
  readonly remittanceInformationKey = getPropertyKey<TransactionFilters>(p => p.remittanceInformation);
  readonly orderingPartyNameKey = getPropertyKey<TransactionFilters>(p => p.orderingPartyName);
  readonly createdAtStartKey = getPropertyKey<TransactionFilters>(p => p.createdAtStart);
  readonly createdAtEndKey = getPropertyKey<TransactionFilters>(p => p.createdAtEnd);

  readonly reconciliationIdKey = getPropertyKey<TransactionFilters>(p => p.reconciliationId);
  readonly reconciliationCreatorKey = getPropertyKey<TransactionFilters>(p => p.reconciliationCreator);
  readonly reconciliationCreationDateStartKey = getPropertyKey<TransactionFilters>(p => p.reconciliationCreationDateStart);
  readonly reconciliationCreationDateEndKey = getPropertyKey<TransactionFilters>(p => p.reconciliationCreationDateEnd);
  readonly reconciliationReviewerKey = getPropertyKey<TransactionFilters>(p => p.reconciliationReviewer);
  readonly reconciliationReviewDateStartKey = getPropertyKey<TransactionFilters>(p => p.reconciliationReviewDateStart);
  readonly reconciliationReviewDateEndKey = getPropertyKey<TransactionFilters>(p => p.reconciliationReviewDateEnd);
  readonly reconciliationStatusKey = getPropertyKey<TransactionFilters>(p => p.reconciliationStatus);

  private readonly activeFiltersConfigMap = new Map<string, any>([
    [
      this.filterTextKey,
      (filterText: string) => {
        return {labelKey: 'GENERAL.FIELDS.FILTER_TEXT', displayText: filterText}
      }
    ],
    [
      this.currencyKey,
      (currencies: string[]) => {
        return {
          labelKey: 'TRANSACTIONS.FIELDS.CURRENCY',
          displayText: currencies.map((currency: string) => currency).join(activeFiltersSeparator)
        }
      }
    ],
    [
      this.typeKey,
      (type: string) => {
        return {labelKey: 'TRANSACTIONS.FIELDS.TYPE', displayText: type}
      }
    ],
    [
      this.accountIDKey,
      (accountID: string) => {
        return {labelKey: 'TRANSACTIONS.FIELDS.ACCOUNT_ID', displayText: accountID}
      }
    ],
    [
      this.partitionKey,
      (partitionKey: string) => {
        return {labelKey: 'TRANSACTIONS.FIELDS.PARTITION_KEY', displayText: partitionKey}
      }
    ],
    [
      this.valueDateStartKey,
      (valueDate: string) => {
        return {labelKey: 'TRANSACTIONS.FIELDS.VALUE_DATE', displayText: valueDate}
      }
    ],
    [
      this.transactionDateStartKey,
      (transactionDate: string) => {
        return {labelKey: 'TRANSACTIONS.FIELDS.TRANSACTION_DATE', displayText: transactionDate}
      }
    ],
    [
      this.statementDateStartKey,
      (statementDate: string) => {
        return {labelKey: 'TRANSACTIONS.FIELDS.STATEMENT_DATE', displayText: statementDate}
      }
    ],
    [
      this.directionKey,
      (direction: string[]) => {
        return {
          labelKey: 'TRANSACTIONS.FIELDS.DIRECTION',
          displayText: direction.map((direction: string) => direction).join(activeFiltersSeparator)
        }
      }
    ],
    [
      this.amountKey,
      (amount: string) => {
        return {labelKey: 'TRANSACTIONS.FIELDS.AMOUNT', displayText: amount}
      }
    ],
    [
      this.referenceKey,
      (reference: string) => {
        return {labelKey: 'TRANSACTIONS.FIELDS.REFERENCE', displayText: reference}
      }
    ],
    [
      this.descriptionKey,
      (description: string) => {
        return {labelKey: 'TRANSACTIONS.FIELDS.DESCRIPTION', displayText: description}
      }
    ],
    [
      this.accountStatementIDKey,
      (accountStatementID: string) => {
        return {labelKey: 'TRANSACTIONS.FIELDS.STATEMENT_ID', displayText: accountStatementID}
      }
    ],
    [
      this.remittanceInformationKey,
      (remittanceInformation: string) => {
        return {labelKey: 'TRANSACTIONS.FIELDS.REMITTANCE_INFORMATION', displayText: remittanceInformation}
      }
    ],
    [
      this.orderingPartyNameKey,
      (orderingPartyName: string) => {
        return {labelKey: 'TRANSACTIONS.FIELDS.ORDERING_PARTY_NAME', displayText: orderingPartyName}
      }
    ],
    [
      this.createdAtStartKey,
      (createdAt: string) => {
        return {labelKey: 'TRANSACTIONS.FIELDS.CREATED_AT', displayText: createdAt}
      }
    ],
    [
      this.reconciliationIdKey,
      (reconciliationId: string) => {
        return {labelKey: 'RECONCILIATION.FIELDS.ID', displayText: reconciliationId}
      }
    ],
    [
      this.reconciliationCreatorKey,
      (reconciliationCreator: string) => {
        return {labelKey: 'RECONCILIATION.FIELDS.CREATOR', displayText: reconciliationCreator};
      }
    ],
    [
      this.reconciliationCreationDateStartKey,
      (reconciliationCreationDateStart: string) => {
        return {labelKey: 'RECONCILIATION.FIELDS.CREATION_DATE', displayText: reconciliationCreationDateStart};
      }
    ],
    [
      this.reconciliationReviewerKey,
      (reconciliationReviewer: string) => {
        return {labelKey: 'RECONCILIATION.FIELDS.REVIEWER', displayText: reconciliationReviewer};
      }
    ],
    [
      this.reconciliationReviewDateStartKey,
      (reconciliationReviewDateStart: string) => {
        return {labelKey: 'RECONCILIATION.FIELDS.REVIEW_DATE', displayText: reconciliationReviewDateStart};
      }
    ],
    [
      this.reconciliationStatusKey,
      (reconciliationStatus: string) => {
        return {labelKey: 'RECONCILIATION.FIELDS.STATUS', displayText: reconciliationStatus};
      }
    ]
  ]);

  public filtersBehaviorSubject = new BehaviorSubject<TransactionFilters>({});
  private activeFilterChips = new Map<string, CsFinActiveFilterChip>();
  public filters$: Observable<TransactionFilters> = this.filtersBehaviorSubject.asObservable();
  public activeFiltersChips$: Observable<Map<string, CsFinActiveFilterChip>> = of(this.activeFilterChips);

  constructor(public transactionsApiService: TransactionsApiService) {
  }

  filtersChanged(filters: TransactionFilters) {
    let newFilters : TransactionFilters = {...filters}
    if (!newFilters.valueDateStart) {
      newFilters.valueDateStart = undefined;
      newFilters.valueDateEnd = undefined;
    }
    if (!newFilters.transactionDateStart) {
      newFilters.transactionDateStart = undefined;
      newFilters.transactionDateEnd = undefined;
    }
    if (!newFilters.statementDateStart) {
      newFilters.statementDateStart = undefined;
      newFilters.statementDateEnd = undefined;
    }
    if (!newFilters.createdAtStart) {
      newFilters.createdAtStart = undefined;
      newFilters.createdAtEnd = undefined;
    }
    if (!newFilters.reconciliationCreationDateStart) {
      newFilters.reconciliationCreationDateStart = undefined;
      newFilters.reconciliationCreationDateEnd = undefined;
    }
    if (!newFilters.reconciliationReviewDateStart) {
      newFilters.reconciliationReviewDateStart = undefined;
      newFilters.reconciliationReviewDateEnd = undefined;
    }
    this.filtersBehaviorSubject.next(newFilters);
    this.updateActiveFilterChips(newFilters);
  }

  private updateActiveFilterChips(filters: TransactionFilters): void {
    if (!filters) {
      return;
    }
    this.activeFilterChips.clear();
    if(filters.filterText){
      this.activeFilterChips.set(this.filterTextKey, this.activeFiltersConfigMap.get(this.filterTextKey)(filters.filterText));
    }
    if(filters.currencyCodes && filters.currencyCodes.length > 0){
      this.activeFilterChips.set(this.currencyKey, this.activeFiltersConfigMap.get(this.currencyKey)(filters.currencyCodes));
    }
    if(filters.type){
      this.activeFilterChips.set(this.typeKey, this.activeFiltersConfigMap.get(this.typeKey)(filters.type));
    }
    if(filters.accountId && !this.hideUnwantedFilters.value){
      this.activeFilterChips.set(this.accountIDKey, this.activeFiltersConfigMap.get(this.accountIDKey)(filters.accountId));
    }
    if(filters.partitionKey && !this.hideUnwantedFilters.value){
      this.activeFilterChips.set(this.partitionKey, this.activeFiltersConfigMap.get(this.partitionKey)(filters.partitionKey));
    }
    if(filters.valueDateStart) {
      let dateToDisplay = new Date(filters.valueDateStart!).toDateString();
      if(filters.valueDateEnd){
        dateToDisplay += " - " + new Date(filters.valueDateEnd!).toDateString();
      }
      this.activeFilterChips.set(this.valueDateStartKey, this.activeFiltersConfigMap.get(this.valueDateStartKey)(dateToDisplay));
    }
    if(filters.transactionDateStart){
      let dateToDisplay = new Date(filters.transactionDateStart!).toDateString();
      if (filters.transactionDateEnd) {
        dateToDisplay += " - " + new Date(filters.transactionDateEnd!).toDateString();
      }
      this.activeFilterChips.set(this.transactionDateStartKey, this.activeFiltersConfigMap.get(this.transactionDateStartKey)(dateToDisplay));
    }
    if(filters.statementDateStart){
      let dateToDisplay = new Date(filters.statementDateStart!).toDateString();
      if (filters.statementDateEnd) {
        dateToDisplay += " - " + new Date(filters.statementDateEnd!).toDateString();
      }
      this.activeFilterChips.set(this.statementDateStartKey, this.activeFiltersConfigMap.get(this.statementDateStartKey)(dateToDisplay));
    }
    if(filters.direction && filters.direction.length > 0) {
      this.activeFilterChips.set(this.directionKey, this.activeFiltersConfigMap.get(this.directionKey)(filters.direction));
    }
    if(filters.amount){
      this.activeFilterChips.set(this.amountKey, this.activeFiltersConfigMap.get(this.amountKey)(filters.amount));
    }
    if(filters.reference){
      this.activeFilterChips.set(this.referenceKey, this.activeFiltersConfigMap.get(this.referenceKey)(filters.reference));
    }
    if(filters.description){
      this.activeFilterChips.set(this.descriptionKey, this.activeFiltersConfigMap.get(this.descriptionKey)(filters.description));
    }
    if(filters.accountStatementID){
      this.activeFilterChips.set(this.accountStatementIDKey, this.activeFiltersConfigMap.get(this.accountStatementIDKey)(filters.accountStatementID));
    }
    if(filters.remittanceInformation){
      this.activeFilterChips.set(this.remittanceInformationKey, this.activeFiltersConfigMap.get(this.remittanceInformationKey)(filters.remittanceInformation));
    }
    if(filters.orderingPartyName){
      this.activeFilterChips.set(this.orderingPartyNameKey, this.activeFiltersConfigMap.get(this.orderingPartyNameKey)(filters.orderingPartyName));
    }
    if(filters.createdAtStart){
      let dateToDisplay = new Date(filters.createdAtStart!).toDateString();
      if (filters.createdAtEnd) {
        dateToDisplay += " - " + new Date(filters.createdAtEnd!).toDateString();
      }
      this.activeFilterChips.set(this.createdAtStartKey, this.activeFiltersConfigMap.get(this.createdAtStartKey)(dateToDisplay));
    }
    if(filters.reconciliationId) {
      this.activeFilterChips.set(this.reconciliationIdKey, this.activeFiltersConfigMap.get(this.reconciliationIdKey)(filters.reconciliationId));
    }
    if(filters.reconciliationCreator) {
      this.activeFilterChips.set(this.reconciliationCreatorKey, this.activeFiltersConfigMap.get(this.reconciliationCreatorKey)(filters.reconciliationCreator));
    }
    if(filters.reconciliationCreationDateStart && filters.reconciliationCreationDateEnd) {
      let dateToDisplay = new Date(filters.reconciliationCreationDateStart!).toDateString();
      if(filters.reconciliationCreationDateEnd){
        dateToDisplay += " - " + new Date(filters.reconciliationCreationDateEnd!).toDateString();
      }
      this.activeFilterChips.set(this.reconciliationCreationDateStartKey, this.activeFiltersConfigMap.get(this.reconciliationCreationDateStartKey)(dateToDisplay));
    }
    if(filters.reconciliationReviewer) {
      this.activeFilterChips.set(this.reconciliationReviewerKey, this.activeFiltersConfigMap.get(this.reconciliationReviewerKey)(filters.reconciliationReviewer));
    }
    if(filters.reconciliationReviewDateStart && filters.reconciliationReviewDateEnd) {
      let dateToDisplay = new Date(filters.reconciliationReviewDateStart!).toDateString();
      if(filters.reconciliationReviewDateEnd){
        dateToDisplay += " - " + new Date(filters.reconciliationReviewDateEnd!).toDateString();
      }
      this.activeFilterChips.set(this.reconciliationReviewDateStartKey, this.activeFiltersConfigMap.get(this.reconciliationReviewDateStartKey)(dateToDisplay));
    }
    if(filters.reconciliationStatus && filters.reconciliationStatus.length > 0) {
      this.activeFilterChips.set(this.reconciliationStatusKey, this.activeFiltersConfigMap.get(this.reconciliationStatusKey)(filters.reconciliationStatus));
    }
  }

  getColumnDetails(): CsFinColumnDetails[] {
    return [
      {position: 0, name: 'ID', code: 'id', isActive: false, isRemovable: true, isDefault: false},
      {position: 1, name: 'Type', code: 'type', isActive: true, isRemovable: true, isDefault: true},
      {position: 2, name: 'Account ID', code: 'accountID', isActive: false, isRemovable: true, isDefault: false},
      {position: 3, name: 'Currency', code: 'currency', isActive: true, isRemovable: true, isDefault: true},
      {position: 4, name: 'Value Date', code: 'valueDate', isActive: false, isRemovable: true, isDefault: false},
      {position: 5, name: 'Transaction Date', code: 'transactionDate', isActive: true, isRemovable: true, isDefault: true},
      {position: 6, name: 'Statement Date', code: 'statementDate', isActive: true, isRemovable: true, isDefault: true},
      {position: 7, name: 'Direction', code: 'direction', isActive: false, isRemovable: true, isDefault: false},
      {position: 8, name: 'Amount', code: 'amount', isActive: true, isRemovable: true, isDefault: true},
      {position: 9, name: 'Amount Lcy', code: 'amountLcy', isActive: true, isRemovable: true, isDefault: true},
      {position: 10, name: 'Reference', code: 'reference', isActive: false, isRemovable: true, isDefault: false},
      {position: 11, name: 'Description', code: 'description', isActive: false, isRemovable: true, isDefault: false},
      {position: 12, name: 'Statement ID', code: 'accountStatementID', isActive: false, isRemovable: true, isDefault: false},
      {position: 13, name: 'Remittance Information', code: 'remittanceInformation', isActive: false, isRemovable: true, isDefault: false},
      {position: 14, name: 'Ordering Party Name', code: 'orderingPartyName', isActive: false, isRemovable: true, isDefault: false},
      {position: 15, name: 'Created At', code: 'createdAt', isActive: false, isRemovable: true, isDefault: true},
      {position: 16, name: 'Created By', code: 'createdBy', isActive: false, isRemovable: true, isDefault: false},
      {position: 17, name: 'Updated At', code: 'updatedAt', isActive: false, isRemovable: true, isDefault: false},
      {position: 18, name: 'Updated By', code: 'updatedBy', isActive: false, isRemovable: true, isDefault: false},
    ];
  }

  encodeSortField(field: string): string {
    switch (field) {
      case 'id':
        return 'ID';
      case 'type':
        return 'TYPE';
      case 'accountID':
        return 'ACCOUNT_ID';
      case 'currency':
        return 'CURRENCY';
      case 'valueDate':
        return 'VALUE_DATE';
      case 'transactionDate':
        return 'TRANSACTION_DATE';
      case 'statementDate':
        return 'STATEMENT_DATE';
      case 'direction':
        return 'DIRECTION';
      case 'amount':
        return 'AMOUNT';
      case 'reference':
        return 'REFERENCE';
      case 'description':
        return 'DESCRIPTION';
      case 'finalAmount':
        return 'FINAL_AMOUNT';
      case 'status':
        return 'STATUS';
      case 'statusDescription':
        return 'STATUS_DESCRIPTION';
      case 'accountStatementID':
        return 'STATEMENT_ID';
      case 'remittanceInformation':
        return 'REMITTANCE_INFORMATION';
      case 'orderingPartyName':
        return 'ORDERING_PARTY_NAME';
      case 'createdAt':
        return 'CREATED_AT';
      case 'createdBy':
        return 'CREATED_BY';
      case 'updatedAt':
        return 'UPDATED_AT';
      case 'updatedBy':
        return 'UPDATED_BY';
      default:
        return field.toUpperCase();
    }
  }

  decodeSortField(field: string): string {
    switch (field) {
      case 'ID':
        return 'id';
      case 'TYPE':
        return 'type';
      case 'ACCOUNT_ID':
        return 'accountID';
      case 'CURRENCY':
        return 'currency';
      case 'VALUE_DATE':
        return 'valueDate';
      case 'TRANSACTION_DATE':
        return 'transactionDate';
      case 'STATEMENT_DATE':
        return 'statementDate';
      case 'DIRECTION':
        return 'direction';
      case 'AMOUNT':
        return 'amount';
      case 'REFERENCE':
        return 'reference';
      case 'DESCRIPTION':
        return 'description';
      case 'FINAL_AMOUNT':
        return 'finalAmount';
      case 'STATUS':
        return 'status';
      case 'STATUS_DESCRIPTION':
        return 'statusDescription';
      case 'STATEMENT_ID':
        return 'accountStatementID';
      case 'REMITTANCE_INFORMATION':
        return 'remittanceInformation';
      case 'ORDERING_PARTY_NAME':
        return 'orderingPartyName';
      case 'CREATED_AT':
        return 'createdAt';
      case 'CREATED_BY':
        return 'createdBy';
      case 'UPDATED_AT':
        return 'updatedAt';
      case 'UPDATED_BY':
        return 'updatedBy';
      default:
        return field.toLowerCase();
    }
  }

  convertToFilters(grouping: string, result:string, filter:TransactionFilters): TransactionFilters {
    switch (grouping) {
      case "TYPE":
        filter.type = result;
        break;
      case "CURRENCY":
        let currencyList: string[] = [];
        currencyList.push(result);
        filter.currencyCodes = currencyList;
        break;
      case "VALUE_DATE":
        filter.valueDateStart = result;
        break;
      case "TRANSACTION_DATE":
        filter.transactionDateStart = result;
        break;
      case "STATEMENT_DATE":
        filter.statementDateStart = result;
        break;
      case "DIRECTION":
        let directionList: string[] = [];
        directionList.push(result);
        filter.direction = directionList;
        break;
      case "AMOUNT":
          filter.amount = parseFloat(result);
        break;
      case "REFERENCE":
        filter.reference = result;
        break;
      case "DESCRIPTION":
        filter.description = result;
        break;
      case "STATEMENT":
        filter.accountStatementID = result;
        break;
      case "REMITTANCE_INFORMATION":
        filter.remittanceInformation = result;
        break;
      case "ORDERING_PARTY_NAME":
        filter.orderingPartyName = result;
        break;
      case "RECONCILIATION_CREATOR":
        filter.reconciliationCreator = result;
        break;
      case "RECONCILIATION_CREATION_DATE":
        filter.reconciliationCreationDateStart = result;
    }
    return filter;
  }

  getAll(filters?: TransactionFilters): Observable<PageResponse<Transaction>> {
    return this.transactionsApiService.getAll(filters);
  }

  getDirections(): Observable<string[]> {
    return this.transactionsApiService.getDirections();
  }

  download(filters?: TransactionFilters)  {
    return this.transactionsApiService.download(filters);
  }

  getEntityGroups(filters:string[], defaultFilters?:TransactionFilters): Observable<CsFinGroup> {
    return this.transactionsApiService.getTransactionGroups(filters, defaultFilters);
  }

  getTransactionGroupableFields(): Observable<string[]> {
    return this.transactionsApiService.getTransactionGroupableFields();
  }

  getAllGroupedInfo(filters: CsFinFilterList<TransactionFilters>): Observable<CsFinGroupedEntityInfo[]> {
    return this.transactionsApiService.getAllGroupedInfo(filters);
  }
}
