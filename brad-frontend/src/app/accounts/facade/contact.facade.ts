import {Injectable} from "@angular/core";
import {ContactsApiService} from "../../api/service/contacts-api.service";
import {BehaviorSubject, Observable, of, Subject} from "rxjs";
import {getPropertyKey} from "../../shared/service/ts-utils.service";
import {ContactFilters} from "../../entities/contact/contact-filters";
import {CsFinActiveFilterChip} from "@jumia-cs-fin/common";
import {PageResponse} from "../../entities/page-response";
import {Contact} from "../../entities/contact/contact";
import {AccountFilters} from "../../entities/account/account-filters";
@Injectable({providedIn: 'root'})
export class ContactFacade {

  forceRefresh: Subject<boolean> = new Subject<boolean>();

  public fullscreenChangeBehaviorSubject = new BehaviorSubject<boolean>(false);
  public detailsCloseBehaviorSubject = new BehaviorSubject<boolean>(false);
  public selectedContactChangeBehaviorSubject = new BehaviorSubject<number>(-1);

  readonly filterTextKey = getPropertyKey<ContactFilters>(p => p.filterText);
  readonly contactTypeKey = getPropertyKey<ContactFilters>(p => p.contactType);
  readonly nameKey = getPropertyKey<ContactFilters>(p => p.name);
  readonly emailKey = getPropertyKey<ContactFilters>(p => p.email);
  readonly accountIDKey = getPropertyKey<ContactFilters>(p => p.accountID);
  readonly createdAtKey = getPropertyKey<ContactFilters>(p => p.createdAt);


  private readonly activeFiltersConfigMap = new Map<string, any>([
    [
      this.filterTextKey,
      (filterText: string) => {
        return {labelKey: 'GENERAL.FIELDS.FILTER_TEXT', displayText: filterText}
      }
    ],
    [
      this.contactTypeKey,
      (contactType: string) => {
        return {labelKey: 'CONTACTS.FIELDS.CONTACT_TYPE', displayText: contactType}
      }
    ],
    [
      this.nameKey,
      (name: string) => {
        return {labelKey: 'CONTACTS.FIELDS.NAME', displayText: name}
      }
    ],
    [
      this.emailKey,
      (email: string) => {
        return {labelKey: 'CONTACTS.FIELDS.EMAIL', displayText: email}
      }
    ],
    [
      this.accountIDKey,
      (accountID: string) => {
        return {labelKey: 'CONTACTS.FIELDS.ACCOUNT_ID', displayText: accountID}
      }
    ],
    [
      this.createdAtKey,
      (createdAt: string) => {
        return {labelKey: 'CONTACTS.FIELDS.CREATED_AT', displayText: createdAt}
      }
    ]

  ]);

  private filtersBehaviorSubject = new BehaviorSubject<ContactFilters>({});
  private activeFilterChips = new Map<string, CsFinActiveFilterChip>();
  public filters$: Observable<ContactFilters> = this.filtersBehaviorSubject.asObservable();
  public activeFiltersChips$: Observable<Map<string, CsFinActiveFilterChip>> = of(this.activeFilterChips);

  constructor(public ContactsApiService: ContactsApiService) {
  }

  filtersChanged(filters: ContactFilters) {
    this.filtersBehaviorSubject.next(filters);
    this.updateActiveFilterChips(filters);
  }


  private updateActiveFilterChips(filters: ContactFilters): void {
    if (!filters) {
      return;
    }
    this.activeFilterChips.clear();

    if(filters.filterText){
      this.activeFilterChips.set(this.filterTextKey, this.activeFiltersConfigMap.get(this.filterTextKey)(filters.filterText));
    }
    if(filters.contactType){
      this.activeFilterChips.set(this.contactTypeKey, this.activeFiltersConfigMap.get(this.contactTypeKey)(filters.contactType));
    }
    if(filters.name){
      this.activeFilterChips.set(this.nameKey, this.activeFiltersConfigMap.get(this.nameKey)(filters.name));
    }
    if(filters.email){
      this.activeFilterChips.set(this.emailKey, this.activeFiltersConfigMap.get(this.emailKey)(filters.email));
    }
    if(filters.accountID){
      this.activeFilterChips.set(this.accountIDKey, this.activeFiltersConfigMap.get(this.accountIDKey)(filters.accountID));
    }
    if(filters.createdAt){
      this.activeFilterChips.set(this.createdAtKey, this.activeFiltersConfigMap.get(this.createdAtKey)(filters.createdAt));
    }

  }

  getAll(filters?: ContactFilters): Observable<PageResponse<Contact>> {
    return this.ContactsApiService.getAll(filters);
  }

  getAllFromAccount(id: number): Observable<Array<Contact>> {
    return this.ContactsApiService.getAllFromAccount(id);
  }

  getById(id: number): Observable<Contact> {
    return this.ContactsApiService.getById(id);
  }

  create(field: Contact): Observable<Contact> {
    return this.ContactsApiService.create(field);
  }

  update(id: number, field: Contact): Observable<Contact> {
    return this.ContactsApiService.update(id, field);
  }

  delete(id: number): Observable<void> {
    return this.ContactsApiService.delete(id);
  }

  downloadBasedOnAccountFilters(filters?: AccountFilters)  {
    return this.ContactsApiService.downloadBasedOnAccountFilters(filters);
  }
}
