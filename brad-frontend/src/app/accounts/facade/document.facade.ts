import {Injectable} from "@angular/core";
import {DocumentsApiService} from "../../api/service/documents-api.service";
import {BehaviorSubject, Observable, of, Subject} from "rxjs";
import {getPropertyKey} from "../../shared/service/ts-utils.service";
import {DocumentFilters} from "../../entities/document/document-filters";
import {CsFinActiveFilterChip} from "@jumia-cs-fin/common";
import {PageResponse} from "../../entities/page-response";
import {Document} from "../../entities/document/document";
import {SafeResourceUrl} from "@angular/platform-browser";

@Injectable({providedIn: 'root'})
export class DocumentFacade {

  forceRefresh: Subject<boolean> = new Subject<boolean>();

  public fullscreenChangeBehaviorSubject = new BehaviorSubject<boolean>(false);
  public detailsCloseBehaviorSubject = new BehaviorSubject<boolean>(false);
  public selectedDocumentChangeBehaviorSubject = new BehaviorSubject<number>(-1);

  readonly filterTextKey = getPropertyKey<DocumentFilters>(p => p.filterText);
  readonly documentTypeKey = getPropertyKey<DocumentFilters>(p => p.documentType);
  readonly nameKey = getPropertyKey<DocumentFilters>(p => p.name);
  readonly descriptionKey = getPropertyKey<DocumentFilters>(p => p.description);
  readonly urlKey = getPropertyKey<DocumentFilters>(p => p.url);
  readonly accountIDKey = getPropertyKey<DocumentFilters>(p => p.accountID);
  readonly createdAtKey = getPropertyKey<DocumentFilters>(p => p.createdAt);


  private readonly activeFiltersConfigMap = new Map<string, any>([
    [
      this.filterTextKey,
      (filterText: string) => {
        return {labelKey: 'GENERAL.FIELDS.FILTER_TEXT', displayText: filterText}
      }
    ],
    [
      this.documentTypeKey,
      (documentType: string) => {
        return {labelKey: 'DOCUMENTS.FIELDS.DOCUMENT_TYPE', displayText: documentType}
      }
    ],
    [
      this.nameKey,
      (name: string) => {
        return {labelKey: 'DOCUMENTS.FIELDS.NAME', displayText: name}
      }
    ],
    [
      this.descriptionKey,
      (description: string) => {
        return {labelKey: 'DOCUMENTS.FIELDS.DESCRIPTION', displayText: description}
      }
    ],
    [
      this.urlKey,
      (url: string) => {
        return {labelKey: 'DOCUMENTS.FIELDS.URL', displayText: url}
      }
    ],
    [
      this.accountIDKey,
      (accountID: string) => {
        return {labelKey: 'DOCUMENTS.FIELDS.ACCOUNT_ID', displayText: accountID}
      }
    ],
    [
      this.createdAtKey,
      (createdAt: string) => {
        return {labelKey: 'DOCUMENTS.FIELDS.CREATED_AT', displayText: createdAt}
      }
    ]

  ]);

  private filtersBehaviorSubject = new BehaviorSubject<DocumentFilters>({});
  private activeFilterChips = new Map<string, CsFinActiveFilterChip>();
  public filters$: Observable<DocumentFilters> = this.filtersBehaviorSubject.asObservable();
  public activeFiltersChips$: Observable<Map<string, CsFinActiveFilterChip>> = of(this.activeFilterChips);

  constructor(public DocumentsApiService: DocumentsApiService) {
  }

  filtersChanged(filters: DocumentFilters) {
    this.filtersBehaviorSubject.next(filters);
    this.updateActiveFilterChips(filters);
  }


  private updateActiveFilterChips(filters: DocumentFilters): void {
    if (!filters) {
      return;
    }
    this.activeFilterChips.clear();

    if(filters.filterText){
      this.activeFilterChips.set(this.filterTextKey, this.activeFiltersConfigMap.get(this.filterTextKey)(filters.filterText));
    }
    if(filters.documentType){
      this.activeFilterChips.set(this.documentTypeKey, this.activeFiltersConfigMap.get(this.documentTypeKey)(filters.documentType));
    }
    if(filters.name){
      this.activeFilterChips.set(this.nameKey, this.activeFiltersConfigMap.get(this.nameKey)(filters.name));
    }
    if(filters.description){
      this.activeFilterChips.set(this.descriptionKey, this.activeFiltersConfigMap.get(this.descriptionKey)(filters.description));
    }
    if(filters.url){
      this.activeFilterChips.set(this.urlKey, this.activeFiltersConfigMap.get(this.urlKey)(filters.url));
    }
    if(filters.accountID){
      this.activeFilterChips.set(this.accountIDKey, this.activeFiltersConfigMap.get(this.accountIDKey)(filters.accountID));
    }
    if(filters.createdAt){
      this.activeFilterChips.set(this.createdAtKey, this.activeFiltersConfigMap.get(this.createdAtKey)(filters.createdAt));
    }

  }

  getAll(filters?: DocumentFilters): Observable<PageResponse<Document>> {
    return this.DocumentsApiService.getAll(filters);
  }

  getAllFromAccount(id: number): Observable<Array<Document>> {
    return this.DocumentsApiService.getAllFromAccount(id);
  }

  getById(id: number): Observable<Document> {
    return this.DocumentsApiService.getById(id);
  }

  getURLByID(id: number) {
    return this.DocumentsApiService.getURLByID(id);
  }

  create(field: Document): Observable<Document> {
    return this.DocumentsApiService.create(field);
  }

  update(id: number, field: Document): Observable<Document> {
    return this.DocumentsApiService.update(id, field);
  }
  delete(id: number): Observable<void> {
    return this.DocumentsApiService.delete(id);
  }


}
