import {Injectable} from "@angular/core";
import {AccountsApiService} from "../../api/service/accounts-api.service";
import {BehaviorSubject, Observable, of, Subject} from "rxjs";
import {getPropertyKey} from "../../shared/service/ts-utils.service";
import {AccountFilters} from "../../entities/account/account-filters";
import {activeFiltersSeparator, CsFinActiveFilterChip} from "@jumia-cs-fin/common";
import {PageResponse} from "../../entities/page-response";
import {Account} from "../../entities/account/account";
import {AuditFilters} from "../../entities/audit/audit-filters";
import {AuditResponse} from "../../entities/audit/audit-response";
import {Country} from "../../entities/account/country";
import {AccountRequest} from "../../entities/account/account-request";
import {AccountAdditionalInfo} from "../../entities/account/account-additional-info";
import {AccountStatus} from "../../entities/account/account-status";
import {Params} from "@angular/router";
import {NetChangeFilters} from "../../entities/account/net-change-filters";
import {NetChangeResponse} from "../../entities/account/net-change-response";
import {ExportLog} from "../../entities/export-log/export-log";

@Injectable({providedIn: 'root'})
export class AccountFacade {

  forceRefresh: Subject<boolean> = new Subject<boolean>();

  public fullscreenChangeBehaviorSubject = new BehaviorSubject<boolean>(true);
  public detailsCloseBehaviorSubject = new BehaviorSubject<boolean>(false);
  public selectedAccountChangeBehaviorSubject = new BehaviorSubject<number>(-1);
  public selectedAccountNavReferenceChangeBehaviorSubject = new BehaviorSubject<string>('');
  public accountFilterParamsBehaviorSubject = new BehaviorSubject<Params>({});

  readonly filterTextKey = getPropertyKey<AccountFilters>(p => p.filterText);
  readonly companyIDKey = getPropertyKey<AccountFilters>(p => p.companyID);
  readonly countryKey = getPropertyKey<AccountFilters>(p => p.countryCodes);
  readonly navReferenceKey = getPropertyKey<AccountFilters>(p => p.navReference);
  readonly beneficiaryNameKey = getPropertyKey<AccountFilters>(p => p.beneficiaryName);
  readonly beneficiaryAddressKey = getPropertyKey<AccountFilters>(p => p.beneficiaryAddress);
  readonly ibanKey = getPropertyKey<AccountFilters>(p => p.iban);
  readonly accountNumberKey = getPropertyKey<AccountFilters>(p => p.accountNumber);
  readonly accountNameKey = getPropertyKey<AccountFilters>(p => p.accountName);
  readonly swiftCodeKey = getPropertyKey<AccountFilters>(p => p.swiftCode);
  readonly accountRoutingCodeKey = getPropertyKey<AccountFilters>(p => p.accountRoutingCode);
  readonly sortCodeKey = getPropertyKey<AccountFilters>(p => p.sortCode);
  readonly branchCodeKey = getPropertyKey<AccountFilters>(p => p.branchCode);
  readonly ribKey = getPropertyKey<AccountFilters>(p => p.rib);
  readonly currencyKey = getPropertyKey<AccountFilters>(p => p.currencyCodes);
  readonly createdAtStartKey = getPropertyKey<AccountFilters>(p => p.createdAtStart);
  readonly createdAtEndKey = getPropertyKey<AccountFilters>(p => p.createdAtEnd);
  readonly lastProcessedStmtDateStartKey = getPropertyKey<AccountFilters>(p => p.lastProcessedStatementDateStart);
  readonly lastProcessedStmtDateEndKey = getPropertyKey<AccountFilters>(p => p.lastProcessedStatementDateEnd);
  readonly typeKey = getPropertyKey<AccountFilters>(p => p.types);
  readonly subTypeKey = getPropertyKey<AccountFilters>(p => p.subTypes);
  readonly statusKey = getPropertyKey<AccountFilters>(p => p.status);
  readonly statementSourceKey = getPropertyKey<AccountFilters>(p => p.statementSource);
  readonly statementPeriodicityKey = getPropertyKey<AccountFilters>(p => p.statementPeriodicity);
  readonly troubleshootingKey = getPropertyKey<AccountFilters>(p => p.troubleshooting);
  readonly partnerKey = getPropertyKey<AccountFilters>(p => p.partner);
  readonly phoneNumberKey = getPropertyKey<AccountFilters>(p => p.phoneNumber);
  readonly investmentIdKey = getPropertyKey<AccountFilters>(p => p.investmentId);

  readonly fromBeginningKey = getPropertyKey<NetChangeFilters>(p => p.fromBeginning);
  readonly netChangeStartDateKey = getPropertyKey<NetChangeFilters>(p => p.startDate);
  readonly netChangeEndDateKey = getPropertyKey<NetChangeFilters>(p => p.endDate);

  private readonly activeFiltersConfigMap = new Map<string, any>([
    [
      this.filterTextKey,
      (filterText: string) => {
        return {labelKey: 'GENERAL.FIELDS.FILTER_TEXT', displayText: filterText}
      }
    ],
    [
      this.companyIDKey,
      (companyID: string) => {
        return {labelKey: 'ACCOUNTS.FIELDS.COMPANY_ID', displayText: companyID}
      }
    ],
    [
      this.countryKey,
      (countries: Country[]) => {
        return {
          labelKey: 'ACCOUNTS.FIELDS.COUNTRY',
          displayText: countries.map((country: Country) => country.name).join(activeFiltersSeparator)
        }
      }
    ],
    [
      this.navReferenceKey,
      (navReference: string) => {
        return {labelKey: 'ACCOUNTS.FIELDS.NAV_REFERENCE', displayText: navReference}
      }
    ],
    [
      this.beneficiaryNameKey,
      (beneficiaryName: string) => {
        return {labelKey: 'ACCOUNTS.FIELDS.BENEFICIARY_NAME', displayText: beneficiaryName}
      }
    ],
    [
      this.beneficiaryAddressKey,
      (beneficiaryAddress: string) => {
        return {labelKey: 'ACCOUNTS.FIELDS.BENEFICIARY_ADDRESS', displayText: beneficiaryAddress}
      }
    ],
    [
      this.ibanKey,
      (iban: string) => {
        return {labelKey: 'ACCOUNTS.FIELDS.IBAN', displayText: iban}
      }
    ],
    [
      this.accountNumberKey,
      (accountNumber: string) => {
        return {labelKey: 'ACCOUNTS.FIELDS.ACCOUNT_NUMBER', displayText: accountNumber}
      }
    ],
    [
      this.accountNameKey,
      (accountName: string) => {
        return {labelKey: 'ACCOUNTS.FIELDS.ACCOUNT_NAME', displayText: accountName}
      }
    ],
    [
      this.swiftCodeKey,
      (swiftCode: string) => {
        return {labelKey: 'ACCOUNTS.FIELDS.SWIFT_CODE', displayText: swiftCode}
      }
    ],
    [
      this.accountRoutingCodeKey,
      (accountRoutingCode: string) => {
        return {labelKey: 'ACCOUNTS.FIELDS.ACCOUNT_ROUTING_CODE', displayText: accountRoutingCode}
      }
    ],
    [
      this.sortCodeKey,
      (sortCode: string) => {
        return {labelKey: 'ACCOUNTS.FIELDS.SORT_CODE', displayText: sortCode}
      }
    ],
    [
      this.branchCodeKey,
      (branchCode: string) => {
        return {labelKey: 'ACCOUNTS.FIELDS.BRANCH_CODE', displayText: branchCode}
      }
    ],
    [
      this.ribKey,
      (rib: string) => {
        return {labelKey: 'ACCOUNTS.FIELDS.RIB', displayText: rib}
      }
    ],
    [
      this.currencyKey,
      (currencies: string[]) => {
        return {
          labelKey: 'ACCOUNTS.FIELDS.CURRENCY',
          displayText: currencies.map((currency: string) => currency).join(activeFiltersSeparator)
        }
      }
    ],
    [
      this.createdAtStartKey,
      (createdAt: string) => {
        return {labelKey: 'ACCOUNTS.FIELDS.CREATED_AT', displayText: createdAt}
      }
    ],
    [
      this.typeKey,
      (type: string) => {
        return {labelKey: 'ACCOUNTS.FIELDS.TYPE', displayText: type}
      }
    ],
    [
      this.subTypeKey,
      (subType: string) => {
        return {labelKey: 'ACCOUNTS.FIELDS.SUB_TYPE', displayText: subType}
      }
    ],
    [
      this.statusKey,
      (status: string) => {
        return {labelKey: 'ACCOUNTS.FIELDS.STATUS', displayText: status}
      }
    ],
    [
      this.troubleshootingKey,
      (troubleshooting: string) => {
        return {labelKey: 'TROUBLESHOOTING.FIELDS.TROUBLESHOOTING', displayText: troubleshooting}
      }
    ],
    [
      this.statementSourceKey,
      (statementSource: string) => {
        return {
          labelKey: 'ACCOUNTS.FIELDS.STATEMENT_SOURCE',
          displayText: statementSource}
      }
    ],
    [
      this.partnerKey,
      (partner: string) => {
        return {labelKey: 'ACCOUNTS.FIELDS.PARTNER', displayText: partner}
      }
    ],
    [
      this.phoneNumberKey,
      (phoneNumber: string) => {
        return {labelKey: 'ACCOUNTS.FIELDS.PHONE_NUMBER', displayText: phoneNumber}
      }
    ],
    [
      this.statementPeriodicityKey,
      (statementPeriodicity: string) => {
        return {
          labelKey: 'ACCOUNTS.FIELDS.STATEMENT_PERIODICITY',
          displayText: statementPeriodicity}
      }
    ],
    [
      this.lastProcessedStmtDateStartKey,
      (lastProcessedStmtDateStart: string) => {
        return {labelKey: 'ACCOUNTS.FIELDS.LAST_PROCESSED_STATEMENT_DATE', displayText: lastProcessedStmtDateStart}
      }
    ],
    [
      this.investmentIdKey,
      (investmentId: string) => {
        return {labelKey: 'ACCOUNTS.FIELDS.INVESTMENT_ID', displayText: investmentId}
      }
    ],
  ]);

  private filtersBehaviorSubject = new BehaviorSubject<AccountFilters>({});
  private netChangeFiltersBehaviorSubject = new BehaviorSubject<NetChangeFilters>({});
  private activeFilterChips = new Map<string, CsFinActiveFilterChip>();
  public filters$: Observable<AccountFilters> = this.filtersBehaviorSubject.asObservable();
  public netChangeFilters$: Observable<NetChangeFilters> = this.netChangeFiltersBehaviorSubject.asObservable();
  public activeFiltersChips$: Observable<Map<string, CsFinActiveFilterChip>> = of(this.activeFilterChips);

  constructor(public accountsApiService: AccountsApiService) {
  }

  filtersChanged(filters: AccountFilters) {
    if (!filters.createdAtStart) {
      filters.createdAtStart = undefined;
      filters.createdAtEnd = undefined;
    }
    this.filtersBehaviorSubject.next(filters);
    this.updateActiveFilterChips(filters);
  }

  netChangeFiltersChanged(filters: NetChangeFilters) {
    this.netChangeFiltersBehaviorSubject.next(filters);
  }

  private updateActiveFilterChips(filters: AccountFilters): void {
    if (!filters) {
      return;
    }
    this.activeFilterChips.clear();

    if (filters.filterText) {
      this.activeFilterChips.set(this.filterTextKey, this.activeFiltersConfigMap.get(this.filterTextKey)(filters.filterText));
    }

    if (filters.companyID) {
      this.activeFilterChips.set(this.companyIDKey, this.activeFiltersConfigMap.get(this.companyIDKey)(filters.companyID));
    }
    if (filters.countryCodes && filters.countryCodes.length > 0) {
      this.activeFilterChips.set(this.countryKey, this.activeFiltersConfigMap.get(this.countryKey)(filters.countryCodes));
    }
    if (filters.navReference) {
      this.activeFilterChips.set(this.navReferenceKey, this.activeFiltersConfigMap.get(this.navReferenceKey)(filters.navReference));
    }
    if (filters.beneficiaryName) {
      this.activeFilterChips.set(this.beneficiaryNameKey, this.activeFiltersConfigMap.get(this.beneficiaryNameKey)(filters.beneficiaryName));
    }
    if (filters.beneficiaryAddress) {
      this.activeFilterChips.set(this.beneficiaryAddressKey, this.activeFiltersConfigMap.get(this.beneficiaryAddressKey)(filters.beneficiaryAddress));
    }
    if (filters.iban) {
      this.activeFilterChips.set(this.ibanKey, this.activeFiltersConfigMap.get(this.ibanKey)(filters.iban));
    }
    if (filters.accountNumber) {
      this.activeFilterChips.set(this.accountNumberKey, this.activeFiltersConfigMap.get(this.accountNumberKey)(filters.accountNumber));
    }
    if(filters.accountName){
      this.activeFilterChips.set(this.accountNameKey, this.activeFiltersConfigMap.get(this.accountNameKey)(filters.accountName));
    }
    if (filters.swiftCode) {
      this.activeFilterChips.set(this.swiftCodeKey, this.activeFiltersConfigMap.get(this.swiftCodeKey)(filters.swiftCode));
    }
    if(filters.accountRoutingCode){
      this.activeFilterChips.set(this.accountRoutingCodeKey, this.activeFiltersConfigMap.get(this.accountRoutingCodeKey)(filters.accountRoutingCode));
    }
    if (filters.sortCode) {
      this.activeFilterChips.set(this.sortCodeKey, this.activeFiltersConfigMap.get(this.sortCodeKey)(filters.sortCode));
    }
    if (filters.branchCode) {
      this.activeFilterChips.set(this.branchCodeKey, this.activeFiltersConfigMap.get(this.branchCodeKey)(filters.branchCode));
    }
    if (filters.rib) {
      this.activeFilterChips.set(this.ribKey, this.activeFiltersConfigMap.get(this.ribKey)(filters.rib));
    }
    if (filters.currencyCodes && filters.currencyCodes.length > 0) {
      this.activeFilterChips.set(this.currencyKey, this.activeFiltersConfigMap.get(this.currencyKey)(filters.currencyCodes));
    }
    if (filters.createdAtStart) {
      let dateToDisplay = new Date(filters.createdAtStart!).toDateString();
      if (filters.createdAtEnd) {
        dateToDisplay += " - " + new Date(filters.createdAtEnd!).toDateString();
      }
      this.activeFilterChips.set(this.createdAtStartKey, this.activeFiltersConfigMap.get(this.createdAtStartKey)(dateToDisplay));
    }
    if (filters.lastProcessedStatementDateStart) {
      let dateToDisplay = new Date(filters.lastProcessedStatementDateStart!).toDateString();
      if (filters.lastProcessedStatementDateEnd) {
        dateToDisplay += " - " + new Date(filters.lastProcessedStatementDateEnd!).toDateString();
      }
      this.activeFilterChips.set(this.lastProcessedStmtDateStartKey, this.activeFiltersConfigMap.get(this.lastProcessedStmtDateStartKey)(dateToDisplay));
    }
    if(filters.types && filters.types.length > 0) {
      this.activeFilterChips.set(this.typeKey, this.activeFiltersConfigMap.get(this.typeKey)(filters.types));
    }
    if(filters.subTypes && filters.subTypes.length > 0) {
      this.activeFilterChips.set(this.subTypeKey, this.activeFiltersConfigMap.get(this.subTypeKey)(filters.subTypes));
    }
    if(filters.status && filters.status.length > 0) {
      this.activeFilterChips.set(this.statusKey, this.activeFiltersConfigMap.get(this.statusKey)(filters.status));
    }
    if (filters.troubleshooting) {
      this.activeFilterChips.set(this.troubleshootingKey, this.activeFiltersConfigMap.get(this.troubleshootingKey)(filters.troubleshooting));
    }
    if (filters.statementSource && filters.statementSource.length > 0) {
      this.activeFilterChips.set(this.statementSourceKey, this.activeFiltersConfigMap.get(this.statementSourceKey)(filters.statementSource));
    }
    if (filters.statementPeriodicity && filters.statementPeriodicity.length > 0) {
      this.activeFilterChips.set(this.statementPeriodicityKey, this.activeFiltersConfigMap
        .get(this.statementPeriodicityKey)(filters.statementPeriodicity));
    }
    if (filters.partner){
      this.activeFilterChips.set(this.partnerKey, this.activeFiltersConfigMap.get(this.partnerKey)(filters.partner));
    }
    if (filters.phoneNumber){
      this.activeFilterChips.set(this.phoneNumberKey, this.activeFiltersConfigMap.get(this.phoneNumberKey)(filters.phoneNumber));
    }
    if (filters.investmentId){
      this.activeFilterChips.set(this.investmentIdKey, this.activeFiltersConfigMap.get(this.investmentIdKey)(filters.investmentId));
    }
  }

  getAll(filters?: AccountFilters): Observable<PageResponse<Account>> {
    return this.accountsApiService.getAll(filters);
  }

  getById(id: number): Observable<Account> {
    return this.accountsApiService.getById(id);
  }

  getHistory(id: number, filters?: AuditFilters): Observable<PageResponse<AuditResponse<Account>>> {
    return this.accountsApiService.getHistory(id, filters);
  }

  startDownload(filters?: AccountFilters, netChangeFilters?: NetChangeFilters): Observable<ExportLog> {
    return this.accountsApiService.startDownload(filters, netChangeFilters);
  }

  create(field: AccountRequest): Observable<Account> {
    return this.accountsApiService.create(field);
  }

  update(id: number, field: AccountRequest): Observable<Account> {
    return this.accountsApiService.update(id, field);
  }

  delete(id: number): Observable<void> {
    return this.accountsApiService.delete(id);
  }

  getAdditionalInfo(id: number): Observable<AccountAdditionalInfo> {
    return this.accountsApiService.getAdditionalInfo(id);
  }

  getStatuses(): Observable<AccountStatus[]> {
    return this.accountsApiService.getStatus();
  }

  getNetChange(filters: NetChangeFilters): Observable<NetChangeResponse[]> {
    return this.accountsApiService.getNetChange(filters);
  }

  getAllAccountNavReferences(filters?: AccountFilters): Observable<Account[]> {
    return this.accountsApiService.getAllAccountNavReferences(filters);
  }

  getAccountByLastUpdatedStatementByUser(): Observable<Account> {
    return this.accountsApiService.getAccountByLastUpdatedStatementByUser();
  }
}
