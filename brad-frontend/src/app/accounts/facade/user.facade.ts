import {Injectable} from "@angular/core";
import {UsersApiService} from "../../api/service/users-api.service";
import {BehaviorSubject, Observable, of, Subject} from "rxjs";
import {getPropertyKey} from "../../shared/service/ts-utils.service";
import {UserFilters} from "../../entities/user/user-filters";
import {CsFinActiveFilterChip} from "@jumia-cs-fin/common";
import {PageResponse} from "../../entities/page-response";
import {User} from "../../entities/user/user";
import {AccountFilters} from "../../entities/account/account-filters";

@Injectable({providedIn: 'root'})
export class UserFacade {

  forceRefresh: Subject<boolean> = new Subject<boolean>();

  public fullscreenChangeBehaviorSubject = new BehaviorSubject<boolean>(false);
  public detailsCloseBehaviorSubject = new BehaviorSubject<boolean>(false);
  public selectedUserChangeBehaviorSubject = new BehaviorSubject<number>(-1);

  readonly filterTextKey = getPropertyKey<UserFilters>(p => p.filterText);
  readonly userNameKey = getPropertyKey<UserFilters>(p => p.userName);
  readonly emailKey = getPropertyKey<UserFilters>(p => p.email);
  readonly accountIDKey = getPropertyKey<UserFilters>(p => p.accountID);
  readonly createdAtKey = getPropertyKey<UserFilters>(p => p.createdAt);


  private readonly activeFiltersConfigMap = new Map<string, any>([
    [
      this.filterTextKey,
      (filterText: string) => {
        return {labelKey: 'GENERAL.FIELDS.FILTER_TEXT', displayText: filterText}
      }
    ],
    [
      this.userNameKey,
      (name: string) => {
        return {labelKey: 'USERS.FIELDS.USER_NAME', displayText: name}
      }
    ],
    [
      this.emailKey,
      (email: string) => {
        return {labelKey: 'USERS.FIELDS.EMAIL', displayText: email}
      }
    ],
    [
      this.accountIDKey,
      (accountID: string) => {
        return {labelKey: 'USERS.FIELDS.ACCOUNT_ID', displayText: accountID}
      }
    ],
    [
      this.createdAtKey,
      (createdAt: string) => {
        return {labelKey: 'USERS.FIELDS.CREATED_AT', displayText: createdAt}
      }
    ]

  ]);

  private filtersBehaviorSubject = new BehaviorSubject<UserFilters>({});
  private activeFilterChips = new Map<string, CsFinActiveFilterChip>();
  public filters$: Observable<UserFilters> = this.filtersBehaviorSubject.asObservable();
  public activeFiltersChips$: Observable<Map<string, CsFinActiveFilterChip>> = of(this.activeFilterChips);

  constructor(public usersApiService: UsersApiService) {
  }

  filtersChanged(filters: UserFilters) {
    this.filtersBehaviorSubject.next(filters);
    this.updateActiveFilterChips(filters);
  }


  private updateActiveFilterChips(filters: UserFilters): void {
    if (!filters) {
      return;
    }
    this.activeFilterChips.clear();

    if(filters.filterText){
      this.activeFilterChips.set(this.filterTextKey, this.activeFiltersConfigMap.get(this.filterTextKey)(filters.filterText));
    }
    if(filters.userName){
      this.activeFilterChips.set(this.userNameKey, this.activeFiltersConfigMap.get(this.userNameKey)(filters.userName));
    }
    if(filters.email){
      this.activeFilterChips.set(this.emailKey, this.activeFiltersConfigMap.get(this.emailKey)(filters.email));
    }
    if(filters.accountID){
      this.activeFilterChips.set(this.accountIDKey, this.activeFiltersConfigMap.get(this.accountIDKey)(filters.accountID));
    }
    if(filters.createdAt){
      this.activeFilterChips.set(this.createdAtKey, this.activeFiltersConfigMap.get(this.createdAtKey)(filters.createdAt));
    }

  }

  getAll(filters?: UserFilters): Observable<PageResponse<User>> {
    return this.usersApiService.getAll(filters);
  }

  getAllFromAccount(id: number): Observable<Array<User>> {
    return this.usersApiService.getAllFromAccount(id);

  }

  getById(id: number): Observable<User> {
    return this.usersApiService.getById(id);
  }

  create(field: User): Observable<User> {
    return this.usersApiService.create(field);
  }

  update(id: number, field: User): Observable<User> {
    return this.usersApiService.update(id, field);
  }
  delete(id: number): Observable<void> {
    return this.usersApiService.delete(id);
  }
  downloadBasedOnAccountFilters(filters?: AccountFilters)  {
    return this.usersApiService.downloadBasedOnAccountFilters(filters);
  }
}
