import {Injectable} from "@angular/core";
import {StatementsApiService} from "../../api/service/statements-api.service";
import {BehaviorSubject, Observable, of} from "rxjs";
import {getPropertyKey} from "../../shared/service/ts-utils.service";
import {StatementFilters} from "../../entities/statement/statement-filters";
import {activeFiltersSeparator, CsFinActiveFilterChip, CsFinColumnDetails} from "@jumia-cs-fin/common";
import {PageResponse} from "../../entities/page-response";
import {Statement} from "../../entities/statement/statement";
import {StatementWithFileRequest} from "../../entities/statement/statement-with-file-request";
import {StatementWithTransactionRequest} from "../../entities/statement/statement-with-transaction-request";


@Injectable({providedIn: 'root'})
export class StatementFacade {

  public selectedStatementIdChangeBehaviorSubject = new BehaviorSubject<number>(-1);

  readonly filterTextKey = getPropertyKey<StatementFilters>(p => p.filterText);
  readonly currencyKey = getPropertyKey<StatementFilters>(p => p.currencyCodes);
  readonly statementIdKey = getPropertyKey<StatementFilters>(p => p.statementId);
  readonly previousStatementIDKey = getPropertyKey<StatementFilters>(p => p.previousStatementID);
  readonly initialDateStartKey = getPropertyKey<StatementFilters>(p => p.initialDateStart);
  readonly initialDateEndKey = getPropertyKey<StatementFilters>(p => p.initialDateEnd);
  readonly finalDateStartKey = getPropertyKey<StatementFilters>(p => p.finalDateStart);
  readonly finalDateEndKey = getPropertyKey<StatementFilters>(p => p.finalDateEnd);
  readonly initialDirectionKey = getPropertyKey<StatementFilters>(p => p.initialDirection);
  readonly finalDirectionKey = getPropertyKey<StatementFilters>(p => p.finalDirection);
  readonly initialAmountKey = getPropertyKey<StatementFilters>(p => p.initialAmount);
  readonly finalAmountKey = getPropertyKey<StatementFilters>(p => p.finalAmount);
  readonly statusKey = getPropertyKey<StatementFilters>(p => p.status);
  readonly statusDescriptionKey = getPropertyKey<StatementFilters>(p => p.statusDescription);
  readonly accountIDKey = getPropertyKey<StatementFilters>(p => p.accountID);
  readonly navReferenceKey = getPropertyKey<StatementFilters>(p => p.navReference);
  readonly createdAtStartKey = getPropertyKey<StatementFilters>(p => p.createdAtStart);
  readonly createdAtEndKey = getPropertyKey<StatementFilters>(p => p.createdAtEnd);

  private readonly activeFiltersConfigMap = new Map<string, any>([
    [
      this.filterTextKey,
      (filterText: string) => {
        return {labelKey: 'GENERAL.FIELDS.FILTER_TEXT', displayText: filterText}
      }
    ],
    [
      this.currencyKey,
      (currencyCodes: string[]) => {
        return {
          labelKey: 'STATEMENTS.FIELDS.CURRENCY',
          displayText: currencyCodes.map((currencyCodes: string) => currencyCodes).join(activeFiltersSeparator)
        }
      }
    ],
    [
      this.statementIdKey,
      (statementId: string) => {
        return {labelKey: 'STATEMENTS.FIELDS.STATEMENT_NUMBER', displayText: statementId}
      }
    ],
    [
      this.previousStatementIDKey,
      (previousStatementID: string) => {
        return {labelKey: 'STATEMENTS.FIELDS.PREVIOUS_STATEMENT_NUMBER', displayText: previousStatementID}
      }
    ],
    [
      this.initialDateStartKey,
      (initialDate: string) => {
        return {labelKey: 'STATEMENTS.FIELDS.INITIAL_DATE', displayText: initialDate}
      }
    ],
    [
      this.finalDateStartKey,
      (finalDate: string) => {
        return {labelKey: 'STATEMENTS.FIELDS.FINAL_DATE', displayText: finalDate}
      }
    ],
    [
      this.initialDirectionKey,
      (initialDirection: string[]) => {
        return {
          labelKey: 'STATEMENTS.FIELDS.INITIAL_DIRECTION',
          displayText: initialDirection.map((initialDirection: string) => initialDirection).join(activeFiltersSeparator)
        }
      }
    ],
    [
      this.finalDirectionKey,
      (finalDirection: string[]) => {
        return {
          labelKey: 'STATEMENTS.FIELDS.FINAL_DIRECTION',
          displayText: finalDirection.map((finalDirection: string) => finalDirection).join(activeFiltersSeparator)
        }
      }
    ],
    [
      this.initialAmountKey,
      (initialAmount: string) => {
        return {labelKey: 'STATEMENTS.FIELDS.INITIAL_AMOUNT', displayText: initialAmount}
      }
    ],
    [
      this.finalAmountKey,
      (finalAmount: string) => {
        return {labelKey: 'STATEMENTS.FIELDS.FINAL_AMOUNT', displayText: finalAmount}
      }
    ],
    [
      this.statusKey,
      (status: string[]) => {
        return {
          labelKey: 'STATEMENTS.FIELDS.STATUS',
          displayText: status.map((status: string) => status).join(activeFiltersSeparator)
        }
      }
    ],
    [
      this.statusDescriptionKey,
      (statusDescription: string[]) => {
        return {
          labelKey: 'STATEMENTS.FIELDS.STATUS_DESCRIPTION',
          displayText: statusDescription.map((statusDescription: string) => statusDescription).join(activeFiltersSeparator)
        }
      }
    ],
    [
      this.accountIDKey,
      (accountID: string) => {
        return {labelKey: 'STATEMENTS.FIELDS.ACCOUNT_ID', displayText: accountID}
      }
    ],
    [
      this.navReferenceKey,
      (navReference: string) => {
        return {labelKey: 'STATEMENTS.FIELDS.NAV_REFERENCE', displayText: navReference}
      }
    ],
    [
      this.createdAtStartKey,
      (createdAt: string) => {
        return {labelKey: 'STATEMENTS.FIELDS.CREATED_AT', displayText: createdAt}
      }
    ]

  ]);

  public filtersBehaviorSubject = new BehaviorSubject<StatementFilters>({});
  private activeFilterChips =  new Map<string, CsFinActiveFilterChip>();
  public filters$: Observable<StatementFilters> = this.filtersBehaviorSubject.asObservable();
  public activeFiltersChips$: Observable<Map<string, CsFinActiveFilterChip>> = of(this.activeFilterChips);

  constructor(public statementsApiService: StatementsApiService) {
  }

  filtersChanged(filters: StatementFilters) {
    console.log(JSON.stringify(filters, null, 2));

    if (!filters.initialDateStart) {
      filters.initialDateStart = undefined;
      filters.initialDateEnd = undefined;
    }
    if (!filters.finalDateStart) {
      filters.finalDateStart = undefined;
      filters.finalDateEnd = undefined;
    }
    if (!filters.createdAtStart) {
      filters.createdAtStart = undefined;
      filters.createdAtEnd = undefined;
    }

    this.filtersBehaviorSubject.next(filters);
    this.updateActiveFilterChips(filters);
  }

  getColumnDetails(): CsFinColumnDetails[] {
    return [
      {position: 0, name: 'ID', code: 'id', isActive: true, isRemovable: false, isDefault: true},
      {position: 1, name: 'Account ID', code: 'accountID', isActive: false, isRemovable: true, isDefault: false},
      {position: 2, name: 'Currency', code: 'currency', isActive: false, isRemovable: true, isDefault: false},
      {position: 3, name: 'Statement ID', code: 'statementId', isActive: true, isRemovable: true, isDefault: true},
      {position: 4, name: 'Previous Statement ID', code: 'previousStatementId', isActive: false, isRemovable: true, isDefault: false},
      {position: 5, name: 'Initial Date', code: 'initialDate', isActive: true, isRemovable: true, isDefault: true},
      {position: 6, name: 'Final Date', code: 'finalDate', isActive: true, isRemovable: true, isDefault: true},
      {position: 7, name: 'Initial Direction', code: 'initialDirection', isActive: false, isRemovable: true, isDefault: false},
      {position: 8, name: 'Final Direction', code: 'finalDirection', isActive: false, isRemovable: true, isDefault: false},
      {position: 9, name: 'Initial Amount', code: 'initialAmount', isActive: true, isRemovable: true, isDefault: true},
      {position: 10, name: 'Final Amount', code: 'finalAmount', isActive: true, isRemovable: true, isDefault: true},
      {position: 11, name: 'Status', code: 'status', isActive: true, isRemovable: true, isDefault: true},
      {position: 12, name: 'Status Description', code: 'statusDescription', isActive: false, isRemovable: true, isDefault: false},
      {position: 13, name: 'Created At', code: 'createdAt', isActive: false, isRemovable: true, isDefault: false},
      {position: 14, name: 'Created By', code: 'createdBy', isActive: false, isRemovable: true, isDefault: false},
      {position: 15, name: 'Updated At', code: 'updatedAt', isActive: false, isRemovable: true, isDefault: false},
      {position: 16, name: 'Updated By', code: 'updatedBy', isActive: false, isRemovable: true, isDefault: false},
      {position: 17, name: 'Description', code: 'description', isActive: false, isRemovable: true, isDefault: false}
    ];
  }

  private updateActiveFilterChips(filters: StatementFilters): void {
    if (!filters) {
      return;
    }
    this.activeFilterChips.clear();

    if(filters.filterText){
      this.activeFilterChips.set(this.filterTextKey, this.activeFiltersConfigMap.get(this.filterTextKey)(filters.filterText));
    }
    if(filters.currencyCodes && filters.currencyCodes.length > 0){
      this.activeFilterChips.set(this.currencyKey, this.activeFiltersConfigMap.get(this.currencyKey)(filters.currencyCodes));
    }
    if(filters.statementId){
      this.activeFilterChips.set(this.statementIdKey, this.activeFiltersConfigMap.get(this.statementIdKey)(filters.statementId));
    }
    if(filters.previousStatementID){
      this.activeFilterChips.set(this.previousStatementIDKey, this.activeFiltersConfigMap.get(this.previousStatementIDKey)(filters.previousStatementID));
    }
    if(filters.initialDateStart) {
      let dateToDisplay = new Date(filters.initialDateStart).toDateString();
      if(filters.initialDateEnd){
        dateToDisplay += ' - ' + new Date(filters.initialDateEnd).toDateString();
      }
      this.activeFilterChips.set(this.initialDateStartKey, this.activeFiltersConfigMap.get(this.initialDateStartKey)(dateToDisplay));
    }
    if(filters.finalDateStart) {
      let dateToDisplay = new Date(filters.finalDateStart).toDateString();
      if(filters.finalDateEnd){
        dateToDisplay += ' - ' + new Date(filters.finalDateEnd).toDateString();
      }
      this.activeFilterChips.set(this.finalDateStartKey, this.activeFiltersConfigMap.get(this.finalDateStartKey)(dateToDisplay));
    }
    if(filters.initialDirection && filters.initialDirection.length > 0) {
      this.activeFilterChips.set(this.initialDirectionKey, this.activeFiltersConfigMap.get(this.initialDirectionKey)(filters.initialDirection));
    }
    if(filters.finalDirection && filters.finalDirection.length > 0) {
      this.activeFilterChips.set(this.finalDirectionKey, this.activeFiltersConfigMap.get(this.finalDirectionKey)(filters.finalDirection));
    }
    if(filters.initialAmount){
      this.activeFilterChips.set(this.initialAmountKey, this.activeFiltersConfigMap.get(this.initialAmountKey)(filters.initialAmount));
    }
    if(filters.finalAmount){
      this.activeFilterChips.set(this.finalAmountKey, this.activeFiltersConfigMap.get(this.finalAmountKey)(filters.finalAmount));
    }
    if(filters.status && filters.status.length > 0) {
      this.activeFilterChips.set(this.statusKey, this.activeFiltersConfigMap.get(this.statusKey)(filters.status));
    }
    if(filters.statusDescription && filters.statusDescription.length > 0){
      this.activeFilterChips.set(this.statusDescriptionKey, this.activeFiltersConfigMap.get(this.statusDescriptionKey)(filters.statusDescription));
    }
    if(filters.accountID){
      this.activeFilterChips.set(this.accountIDKey, this.activeFiltersConfigMap.get(this.accountIDKey)(filters.accountID));
    }
    if(filters.navReference){
      this.activeFilterChips.set(this.navReferenceKey, this.activeFiltersConfigMap.get(this.navReferenceKey)(filters.navReference));
    }
    if(filters.createdAtStart){
      let dateToDisplay = new Date(filters.createdAtStart!).toDateString();
      if (filters.createdAtEnd) {
        dateToDisplay += " - " + new Date(filters.createdAtEnd!).toDateString();
      }
      this.activeFilterChips.set(this.createdAtStartKey, this.activeFiltersConfigMap.get(this.createdAtStartKey)(dateToDisplay));
    }

  }

  createWithFile(statement: StatementWithFileRequest): Observable<Statement> {
    return this.statementsApiService.createWithFile(statement);
  }

  create(statement: StatementWithTransactionRequest): Observable<Statement> {
    return this.statementsApiService.create(statement);
  }

  getAll(filters?: StatementFilters): Observable<PageResponse<Statement>> {
    return this.statementsApiService.getAll(filters);
  }

  getAllOrdered(filters?: StatementFilters): Observable<Statement[]> {
    return this.statementsApiService.getAllOrdered(filters);
  }

  getStatusTypes(): Observable<string[]> {
    return this.statementsApiService.getStatusTypes();
  }

  getErrorTypes(): Observable<string[]> {
    return this.statementsApiService.getErrorTypes();
  }

  getDirections(): Observable<string[]> {
    return this.statementsApiService.getDirections();
  }

  retryStatement(statementId: number): Observable<String> {
    return this.statementsApiService.retryStatement(statementId);
  }

  discardStatement(statementId: number): Observable<void> {
    return this.statementsApiService.discardStatement(statementId);
  }

  discardLastImportedStatement(statementId: number): Observable<void> {
    return this.statementsApiService.discardLastImportedStatement(statementId);
  }

  getLastStatement(accountId: number): Observable<Statement> {
    return this.statementsApiService.getLastStatement(accountId);
  }

  getFirstError(statementId: number): Observable<Statement> {
    return this.statementsApiService.getFirstError(statementId);
  }

  getLastImportedStatement(accountId: number): Observable<Statement> {
    return this.statementsApiService.getLastImportedStatement(accountId);
  }

  download(filters?: StatementFilters)  {
    return this.statementsApiService.download(filters);
  }
}
