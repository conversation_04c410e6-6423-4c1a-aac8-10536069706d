import {Component, OnInit} from '@angular/core';
import {Router} from '@angular/router';
import {StatementFacade} from './facade/statement.facade';
import {TransactionFacade} from './facade/transaction.facade';

@Component({
  selector: 'app-accounts',
  templateUrl: './accounts.component.html',
  styleUrls: ['./accounts.component.scss']
})
export class AccountsComponent implements OnInit {

  constructor(private transactionFacade: TransactionFacade,
              private statementFacade: StatementFacade,
              private router: Router) { }

  ngOnInit(): void {
    this.router.navigate(['accounts']);
  }

}
