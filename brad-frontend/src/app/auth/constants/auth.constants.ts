import {CsFinAuthAction, CsFinAuthTarget, CsFinAuthTargetType} from "@jumia-cs-fin/common";
import {bradPermissions} from "./permission.constants";


export const bradAuthTarget: CsFinAuthTarget = {
  id: 'BRAD',
  type: CsFinAuthTargetType.APPLICATION
};

export const authParams = {
  permissions: bradPermissions,
  actions: CsFinAuthAction,
}

export const bradAuthCountryTarget: CsFinAuthTarget = {
  id: 'bradCountry',
  type: CsFinAuthTargetType.COUNTRY
};

export const authParamsCountry = {
  permissions: bradPermissions,
  actions: CsFinAuthAction,
  types: bradAuthCountryTarget
}
