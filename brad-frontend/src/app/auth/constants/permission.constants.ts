import {CsFinAuthTargetType} from "@jumia-cs-fin/common";


export const bradPermissions = {
  BRAD_CAN_ACCESS: {
    code: 'brad_can_access',
    targetType: CsFinAuthTargetType.APPLICATION
  },
  BRAD_ACCESS_ACCOUNTS: {
    code: 'brad_access_bank_accounts',
    targetType: CsFinAuthTargetType.COUNTRY
  },
  BRAD_DOWNLOAD_ACCOUNTS_CSV: {
    code: 'brad_download_bank_accounts_csv',
    targetType: CsFinAuthTargetType.COUNTRY
  },
  BRAD_DOWNLOAD_CONTACTS_CSV: {
    code: 'brad_download_contacts_csv',
    targetType: CsFinAuthTargetType.COUNTRY
  },
  BRAD_DOWNLOAD_USERS_CSV: {
    code: 'brad_download_users_csv',
    targetType: CsFinAuthTargetType.COUNTRY
  },
  BRAD_MANAGE_ACCOUNTS: {
    code: 'brad_manage_bank_accounts',
    targetType: CsFinAuthTargetType.COUNTRY
  },
  BRAD_DELETE_ACCOUNTS: {
    code: 'brad_delete_bank_accounts',
    targetType: CsFinAuthTargetType.COUNTRY
  },
  BRAD_ACCESS_COUNTRIES: {
    code: 'brad_access_countries',
    targetType: CsFinAuthTargetType.COUNTRY
  },
  BRAD_ADMIN_MANAGE_COUNTRIES: {
    code: 'brad_admin_manage_countries',
    targetType: CsFinAuthTargetType.APPLICATION
  },
  BRAD_ACCESS_RECONCILIATION: {
    code: 'brad_access_reconciliation',
    targetType: CsFinAuthTargetType.APPLICATION
  },
  BRAD_MANAGE_RECONCILIATION: {
    code: 'brad_manage_reconciliation',
    targetType: CsFinAuthTargetType.COUNTRY
  },
  BRAD_APPROVE_RECONCILIATIONS: {
    code: 'brad_approve_reconciliations',
    targetType: CsFinAuthTargetType.COUNTRY
  },
  BRAD_UNMATCH_RECONCILIATIONS: {
    code: 'brad_unmatch_reconciliations',
    targetType: CsFinAuthTargetType.COUNTRY
  },
  BRAD_EXPORT_RECONCILIATIONS: {
    code: 'brad_export_reconciliations',
    targetType: CsFinAuthTargetType.COUNTRY
  },
  BRAD_ACCESS_SCHEDULER: {
    code: 'brad_access_scheduler',
    targetType: CsFinAuthTargetType.APPLICATION
  },
  BRAD_MANAGE_SCHEDULER: {
    code: 'brad_manage_scheduler',
    targetType: CsFinAuthTargetType.APPLICATION
  },
  BRAD_ACCESS_API_LOG: {
    code: 'brad_access_api_log',
    targetType: CsFinAuthTargetType.APPLICATION
  },
  BRAD_UPLOAD_STATEMENTS : {
    code: 'brad_upload_statements',
    targetType: CsFinAuthTargetType.COUNTRY
  },
  BRAD_EXPORT_STATEMENTS: {
    code: 'brad_export_statements',
    targetType: CsFinAuthTargetType.COUNTRY
  },
  BRAD_ACCESS_STATEMENTS: {
    code: 'brad_access_statements',
    targetType: CsFinAuthTargetType.COUNTRY
  },
  BRAD_DISCARD_STATEMENTS: {
    code: 'brad_discard_statements',
    targetType: CsFinAuthTargetType.COUNTRY
  },
  BRAD_ACCESS_TROUBLESHOOTING: {
    code: 'brad_access_troubleshooting',
    targetType: CsFinAuthTargetType.COUNTRY
  },
  BRAD_ACCESS_FX_RATES: {
    code: 'brad_access_fx_rates',
    targetType: CsFinAuthTargetType.APPLICATION
  },
  BRAD_ACCESS_EXECUTION_LOG: {
    code: 'brad_access_execution_log',
    targetType: CsFinAuthTargetType.APPLICATION
  },
  BRAD_ACCESS_EXPORT_LOG: {
    code: 'brad_access_export_log',
    targetType: CsFinAuthTargetType.COUNTRY
  },
  BRAD_ACCESS_CURRENCIES: {
    code: 'brad_access_currencies',
    targetType: CsFinAuthTargetType.APPLICATION
  },
  BRAD_ADMIN_MANAGE_CURRENCIES: {
    code: 'brad_admin_manage_currencies',
    targetType: CsFinAuthTargetType.APPLICATION
  },
  BRAD_MANAGE_THRESHOLDS: {
    code: 'brad_manage_thresholds',
    targetType: CsFinAuthTargetType.APPLICATION
  },
  BRAD_SCAN_SFTP_FOLDER:{
    code: 'brad_scan_sftp_folder',
    targetType: CsFinAuthTargetType.APPLICATION
  },
  BRAD_STATEMENT_FILES_ACCESS: {
    code: 'brad_bank_statement_files_access',
    targetType: CsFinAuthTargetType.APPLICATION
  },
  BRAD_DOWNLOAD_STATEMENT_FILE: {
    code: 'brad_download_bank_statement_file',
    targetType: CsFinAuthTargetType.APPLICATION
  },
  BRAD_RETRY_STATEMENT: {
    code: 'brad_retry_statement',
    targetType: CsFinAuthTargetType.COUNTRY
  },

}
