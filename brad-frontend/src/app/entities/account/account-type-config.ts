import { AccountSubTypes } from "./account-types";

export const commonFields = ["id", "companyID", "navReference", "accountNumber", "accountName", "statementSource", "statementPeriodicity", 
  "type", "subType", "status", "country", "currency", "createdAt", "createdBy", "updatedAt", "updatedBy", "lastProcessedStatementDate"];

export const AccountTypeConfig = {
  BANK_ACCOUNT: {
    commonFields: commonFields,
    specificFields: [
      {
        property: "iban",
        name: "IBAN"
      },
      {
        property: "beneficiaryName",
        name: "BENEFICIARY_NAME"
      },
      {
        property: "beneficiaryAddress",
        name: "BENEFICIARY_ADDRESS"
      },
      {
        property: "swiftCode",
        name: "SWIFT_CODE"
      },
      {
        property: "accountRoutingCode",
        name: "ACCOUNT_ROUTING_CODE"
      },
      {
        property: "sortCode",
        name: "SORT_CODE"
      },
      {
        property: "branchCode",
        name: "BRANCH_CODE"
      },
      {
        property: "rib",
        name: "R<PERSON>"
      }
    ],
    detailsPageOrder: [
      {
        property: "balance",
        name: "BALANCE"
      },
      {
        property: "type",
        name: "TYPE"
      },
      {
        property: "accountName",
        name: "ACCOUNT_NAME"
      },
      {
        property: "iban",
        name: "IBAN"
      },
      {
        property: "balanceUSD",
        name: "BALANCE_IN_USD"
      },
      {
        property: "country",
        name: "COUNTRY"
      },
      {
        property: "accountNumber",
        name: "ACCOUNT_NUMBER"
      },
      {
        property: "companyID",
        name: "COMPANY_ID"
      },
      {
        property: "lastStatementDate",
        name: "LAST_STATEMENT_DATE"
      },
      {
        property: "lastTransactionDate",
        name: "LAST_TRANSACTION_DATE"
      },
      {
        property: "beneficiaryName",
        name: "BENEFICIARY_NAME"
      },
      {
        property: "statementSource",
        name: "STATEMENT_SOURCE"
      },
      {
        property: "beneficiaryAddress",
        name: "BENEFICIARY_ADDRESS"
      },
      {
        property: "swiftCode",
        name: "SWIFT_CODE"
      },
      {
        property: "accountRoutingCode",
        name: "ACCOUNT_ROUTING_CODE"
      },
      {
        property: "sortCode",
        name: "SORT_CODE"
      },
      {
        property: "branchCode",
        name: "BRANCH_CODE"
      },
      {
        property: "rib",
        name: "RIB"
      },
      {
        property: "statementPeriodicity",
        name: "STATEMENT_PERIODICITY"
      },
      {
        property: "lastProcessedStatementDate",
        name: "LAST_PROCESSED_STATEMENT_DATE"
      },
    ],
  },
  PSP: {
    commonFields: commonFields,
    specificFields: [
      {
        property: "partner",
        name: "PARTNER"
      },
      {
        property: "phoneNumber",
        name: "PHONE_NUMBER"
      }
    ],
    detailsPageOrder: [
      {
        property: "balance",
        name: "BALANCE"
      },
      {
        property: "type",
        name: "TYPE"
      },
      {
        property: "accountName",
        name: "ACCOUNT_NAME"
      },
      {
        property: "partner",
        name: "PARTNER"
      },
      {
        property: "balanceUSD",
        name: "BALANCE_IN_USD"
      },
      {
        property: "country",
        name: "COUNTRY"
      },
      {
        property: "accountNumber",
        name: "ACCOUNT_NUMBER"
      },
      {
        property: "phoneNumber",
        name: "PHONE_NUMBER"
      },
      {
        property: "lastStatementDate",
        name: "LAST_STATEMENT_DATE"
      },
      {
        property: "lastTransactionDate",
        name: "LAST_TRANSACTION_DATE"
      },
      {
        property: "companyID",
        name: "COMPANY_ID"
      },
      {
        property: "statementSource",
        name: "STATEMENT_SOURCE"
      },
      {
        property: "statementPeriodicity",
        name: "STATEMENT_PERIODICITY"
      },
      {
        property: "lastProcessedStatementDate",
        name: "LAST_PROCESSED_STATEMENT_DATE"
      },

    ],
  },
  MOBILE_MONEY: {
    commonFields: commonFields,
    specificFields: [
      {
        property: "partner",
        name: "PARTNER"
      }, {
        property: "phoneNumber",
        name: "PHONE_NUMBER"
      }
    ],
    detailsPageOrder: [
      {
        property: "balance",
        name: "BALANCE"
      },
      {
        property: "type",
        name: "TYPE"
      },
      {
        property: "accountName",
        name: "ACCOUNT_NAME"
      },
      {
        property: "partner",
        name: "PARTNER"
      },
      {
        property: "balanceUSD",
        name: "BALANCE_IN_USD"
      },
      {
        property: "country",
        name: "COUNTRY"
      },
      {
        property: "accountNumber",
        name: "ACCOUNT_NUMBER"
      },
      {
        property: "phoneNumber",
        name: "PHONE_NUMBER"
      },
      {
        property: "lastStatementDate",
        name: "LAST_STATEMENT_DATE"
      },
      {
        property: "lastTransactionDate",
        name: "LAST_TRANSACTION_DATE"
      },
      {
        property: "companyID",
        name: "COMPANY_ID"
      },
      {
        property: "statementSource",
        name: "STATEMENT_SOURCE"
      },
      {
        property: "statementPeriodicity",
        name: "STATEMENT_PERIODICITY"
      },
      {
        property: "lastProcessedStatementDate",
        name: "LAST_PROCESSED_STATEMENT_DATE"
      },
    ],
  },
  WALLET: {
    commonFields: commonFields,
    specificFields: [
      {
        property: "partner",
        name: "PARTNER"
      }
    ],
    detailsPageOrder: [
      {
        property: "balance",
        name: "BALANCE"
      },
      {
        property: "type",
        name: "TYPE"
      },
      {
        property: "accountName",
        name: "ACCOUNT_NAME"
      },
      {
        property: "partner",
        name: "PARTNER"
      },
      {
        property: "balanceUSD",
        name: "BALANCE_IN_USD"
      },
      {
        property: "country",
        name: "COUNTRY"
      },
      {
        property: "accountNumber",
        name: "ACCOUNT_NUMBER"
      },
      {
        property: "companyID",
        name: "COMPANY_ID"
      },
      {
        property: "lastStatementDate",
        name: "LAST_STATEMENT_DATE"
      },
      {
        property: "lastTransactionDate",
        name: "LAST_TRANSACTION_DATE"
      },
      {
        property: "statementSource",
        name: "STATEMENT_SOURCE"
      },
      {
        property: "statementPeriodicity",
        name: "STATEMENT_PERIODICITY"
      },
      {
        property: "lastProcessedStatementDate",
        name: "LAST_PROCESSED_STATEMENT_DATE"
      },

    ],
  },
  INVESTMENTS: {
    commonFields: commonFields,
    specificFields: [
      {
        property: "isin",
        name: "ISIN",
        subTypes: ["BONDS"]
      },
      {
        property: "contractId",
        name: "CONTRACT_ID",
        subTypes: ["TERM_DEPOSITS", "TLF", "BANK_GUARANTEES"]
      },
      {
        property: "amountDeposited",
        name: "AMOUNT_DEPOSITED",
        subTypes:["TERM_DEPOSITS", "TLF", "BANK_GUARANTEES", "BONDS"]
      },
      {
        property: "maturityDate",
        name: "MATURITY_DATE",
        subTypes: ["TERM_DEPOSITS", "TLF", "BANK_GUARANTEES", "BONDS"]
      },
      {
        property: "nominalAmount",
        name: "NOMINAL_AMOUNT",
        subTypes: ["BONDS"]
      },
      {
        property: "couponPaymentPeriodicity",
        name: "COUPON_PAYMENT_PERIODICITY",
        subTypes: ["BONDS"]
      },
      {
        property: "couponRate",
        name: "COUPON_RATE",
        subTypes: ["BONDS"]
      },
      {
        property: "interest",
        name: "INTEREST",
        subTypes: ["TERM_DEPOSITS", "TLF", "BANK_GUARANTEES"]
      },
    ],
    detailsPageOrder: [
      {
        property: "balance",
        name: "BALANCE"
      },
      {
        property: "type",
        name: "TYPE"
      },
      {
        property: "subType",
        name: "SUB_TYPE"
      },
      {
        property: "accountName",
        name: "ACCOUNT_NAME"
      },
      {
        property: "balanceUSD",
        name: "BALANCE_IN_USD"
      },
      {
        property: "country",
        name: "COUNTRY"
      },
      {
        property: "accountNumber",
        name: "ACCOUNT_NUMBER"
      },
      {
        property: "companyID",
        name: "COMPANY_ID"
      },
      {
        property: "lastStatementDate",
        name: "LAST_STATEMENT_DATE"
      },
      {
        property: "lastTransactionDate",
        name: "LAST_TRANSACTION_DATE"
      },
      {
        property: "statementSource",
        name: "STATEMENT_SOURCE"
      },
      {
        property: "statementPeriodicity",
        name: "STATEMENT_PERIODICITY"
      },
      {
        property: "lastProcessedStatementDate",
        name: "LAST_PROCESSED_STATEMENT_DATE"
      },
      {
        property: "isin",
        name: "ISIN",
        subTypes: ["BONDS"]
      },
      {
        property: "contractId",
        name: "CONTRACT_ID",
        subTypes: ["TERM_DEPOSITS", "TLF", "BANK_GUARANTEES"]
      },
      {
        property: "amountDeposited",
        name: "AMOUNT_DEPOSITED",
        subTypes:["TERM_DEPOSITS", "TLF", "BANK_GUARANTEES", "BONDS"]
      },
      {
        property: "maturityDate",
        name: "MATURITY_DATE",
        subTypes: ["TERM_DEPOSITS", "TLF", "BANK_GUARANTEES", "BONDS"]
      },
      {
        property: "nominalAmount",
        name: "NOMINAL_AMOUNT",
        subTypes: ["BONDS"]
      },
      {
        property: "couponPaymentPeriodicity",
        name: "COUPON_PAYMENT_PERIODICITY",
        subTypes: ["BONDS"]
      },
      {
        property: "couponRate",
        name: "COUPON_RATE",
        subTypes: ["BONDS"]
      },
      {
        property: "interest",
        name: "INTEREST",
        subTypes: ["TERM_DEPOSITS", "TLF", "BANK_GUARANTEES"]
      },
    ]
  }
}
