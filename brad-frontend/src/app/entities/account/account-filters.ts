import {PageFilters} from "../PageFilters";
import {SortFilters} from "../SortFilters";
import {Currency} from "../currency/currency";
import {Country} from "./country";

export interface AccountFilters extends PageFilters, SortFilters {
  accountID?: number;
  filterText?: string;
  companyID?: string;
  countryCodes?: Country[];
  navReference?: string;
  beneficiaryName?: string;
  beneficiaryAddress?: string;
  iban?: string;
  accountNumber?: string;
  accountName?: string;
  swiftCode?: string;
  accountRoutingCode?: string;
  sortCode?: string;
  branchCode?: string;
  rib?: string;
  troubleshooting?: string;
  currencyCodes?: Currency[];
  createdAtStart?: string;
  createdAtEnd?: string;
  types?: string[];
  subTypes?: string[];
  status?: string[];
  selectedFields?: string[];
  statementSource?: string;
  statementPeriodicity?: string;
  partner?: string;
  phoneNumber?: string;
  lastProcessedStatementDateStart?: string;
  lastProcessedStatementDateEnd?: string;
  investmentId?: string;
}
