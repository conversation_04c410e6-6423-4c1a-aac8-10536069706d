export abstract class BaseAccount {
  id?: number;
  companyID!: string;
  navReference!: string;
  beneficiaryName!: string;
  beneficiaryAddress!: string;
  iban!: string;
  accountNumber?: string;
  accountName!: string;
  swiftCode!: string;
  accountRoutingCode!: string;
  sortCode!: string;
  branchCode!: string;
  rib!: string;
  partner!: string
  phoneNumber!: string
  statementSource!: string;
  type?: string;
  subType?: string;
  statementPeriodicity?: string;
  lastProcessedStatementDate?: string;
  isin?: string;
  contractId?: string;
  amountDeposited?: number;
  maturityDate?: string;
  nominalAmount?: number;
  couponPaymentPeriodicity?: string;
  couponRate?: number;
  interest?: number;
}
