
export interface Finrec {
  id?: number;
  type: string;
  accountCode: string;
  cur: string;
  account: string;
  statementDate: Date;
  transactionDate: Date;
  valueDate: Date;
  flowCode: string;
  direction: string;
  amount: number;
  reference: string;
  description: string;
  statementId: string;
  fileName: string;
  skAudInsert: number;
  skAudUpdate: number;
  timestampLastUpdate: Date;
  isReconciled: boolean;
}

