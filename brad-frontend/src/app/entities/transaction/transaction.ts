import {Currency} from "../currency/currency";

export interface Transaction {
  id?: number;
  type: string;
  accountID: string;
  currency: Currency;
  valueDate: string;
  transactionDate: string;
  statementDate: string;
  direction: string;
  amount: number;
  amountLocalCurrency: number;
  localCurrency: Currency;
  amountUsd: number;
  reference: string;
  description: string;
  accountStatementID: string;
  remittanceInformation: string;
  orderingPartyName: string;
  reconcileStatus: string;
  createdAt?: string;
  createdBy?: string;
  updatedAt?: string;
  updatedBy?: string;
  statementIsInError?: boolean;

  // reconciliation fields
  reconciliationId?: number;
  reconciliationCreator?: string;
  reconciliationCreationDate?: string;
  reconciliationReviewer?: string;
  reconciliationReviewDate?: string;
  reconciliationStatus?: string;
}
