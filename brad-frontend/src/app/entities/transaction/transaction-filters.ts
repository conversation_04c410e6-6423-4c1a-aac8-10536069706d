import {PageFilters} from "../PageFilters";
import {SortFilters} from "../SortFilters";

export interface TransactionFilters extends PageFilters, SortFilters {
  filterText?: string;
  type?: string;
  accountId?: string;
  partitionKey?: string;
  currencyCodes?: string[];
  valueDateStart?: string;
  valueDateEnd?: string;
  transactionDateStart?: string;
  transactionDateEnd?: string;
  statementDateStart?: string;
  statementDateEnd?: string;
  direction?: string[];
  amount?: number;
  reference?: string;
  description?: string;
  accountStatementID?: any;
  remittanceInformation?: string;
  orderingPartyName?: string;
  createdAtStart?: string;
  createdAtEnd?: string;
  exactFilters?: boolean;
  reconciliationId?: number;
  reconciliationCreator?: string;
  reconciliationCreationDateStart?: string;
  reconciliationCreationDateEnd?: string;
  reconciliationReviewer?: string;
  reconciliationReviewDateStart?: string;
  reconciliationReviewDateEnd?: string;
  reconciliationStatus?: string[];
  importedStatementOnly?: boolean;

  selectedFields?: string[];
}
