import {PageFilters} from "../PageFilters";
import {SortFilters} from "../SortFilters";

export interface StatementFilters extends PageFilters, SortFilters {
  filterText?: string;
  currencyCodes?: string[];
  statementId?: string;
  previousStatementID?: number;
  initialDateStart?: string;
  initialDateEnd?: string;
  finalDateStart?: string;
  finalDateEnd?: string;
  initialDirection?: string[];
  finalDirection?: string[];
  initialAmount?: number;
  finalAmount?: number;
  status?: string[];
  statusDescription?: string[];
  accountID?: number;
  navReference?: string;
  partitionKey?: string;
  selectedFields?: string[];
  createdAtStart?: string;
  createdAtEnd?: string;

}
