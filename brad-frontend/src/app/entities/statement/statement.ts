import {Currency} from "../currency/currency";

export interface Statement {
  id: number;
  accountID?: string;
  currency?: Currency;
  statementId?: string;
  previousStatementId?: string;
  initialDate?: string;
  finalDate?: string;
  initialDirection?: string;
  finalDirection?: string;
  initialAmount?: number;
  finalAmount?: number;
  initialAmountUsd?: number;
  finalAmountUsd?: number;
  lcy?: Currency;
  initialAmountLcy?: number;
  finalAmountLcy?: number;
  status?: string;
  statusDescription?: string;
  createdAt: string;
  createdBy?: string;
  updatedAt?: string;
  updatedBy?: string;
}
