import {PageFilters} from '../PageFilters';
import {SortFilters} from '../SortFilters';

export interface BaleFilters extends PageFilters, SortFilters {
  filterText?: string;
  idCompany?: string;
  accountNumber?: string;
  entryNo?: number[];
  documentNo?: string;
  documentType?: string;
  postingDateStart?: string;
  postingDateEnd?: string;
  accountPostingGroup?: string;
  description?: string;
  sourceCode?: string;
  reasonCode?: string;
  busLine?: string;
  department?: string;
  direction?: string[];
  amount?: number;
  remainingAmount?: number;
  transactionCurrency?: string[];
  amountLcy?: number;
  balanceAccountType?: string;
  isOpen?: boolean;
  isReversed?: boolean;
  postedBy?: string;
  externalDocumentNo?: string;
  baleTimestamp?: string;
  accountTimestamp?: string;
  exactFilters?: boolean;

  reconciliationId?: number;
  reconciliationCreator?: string;
  reconciliationCreationDateStart?: string;
  reconciliationCreationDateEnd?: string;
  reconciliationReviewer?: string;
  reconciliationReviewDateStart?: string;
  reconciliationReviewDateEnd?: string;
  reconciliationStatus?: string[];

  selectedFields?: string[];
}
