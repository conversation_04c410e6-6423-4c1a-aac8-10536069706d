import {Currency} from "../currency/currency";
import {Account} from "../account/account";

export interface Bale {
  id?: number;
  idCompany: string;
  account: Account;
  entryNo: number;
  documentNo: string;
  documentType: string;
  postingDate: string;
  accountPostingGroup: string;
  description: string;
  sourceCode: string;
  reasonCode: string;
  busLine: string;
  department: string;
  direction: string;
  amount: number;
  remainingAmount: number;
  transactionCurrency: Currency;
  amountLcy: number;
  balanceAccountType: string;
  isOpen: boolean;
  isReversed: boolean;
  postedBy: string;
  externalDocumentNo: string;
  baleTimestamp: string;
  accountTimestamp: string;
  reconcileStatus: string;

  // reconciliation fields
  reconciliationId?: number;
  reconciliationCreator?: string;
  reconciliationCreationDate?: string;
  reconciliationReviewer?: string;
  reconciliationReviewDate?: string;
  reconciliationStatus?: string;
}
