import {CsFinGroup} from "./cs-fin-group";
import {Observable} from "rxjs";
import {CsFinGroupedEntityInfo} from "./cs-fin-grouped-entity";
import {PageResponse} from "./page-response";

export type CsFinGroupElement<E, F> = CsFinGroupEntity<E> | CsFinTableGroup<F> | CsFinTablePaginator;

export type CsFinGroupEntity<E> = E & {
  groupName?: string;
  rowType: CsFinRowType;
}

export interface CsFinTableGroup <F>{
  groupName: string;
  groupFields?: string[];
  rowType: CsFinRowType;
  expanded: boolean;
  groupFilters: CsFinTableGroupFilters<F>,
  totalRecords?: number;
  totalAmount?: number;
  allIds?: number[];
  isStatusShared?: boolean;
  isReconciliationShared?: boolean;
}

export interface CsFinTablePaginator {
  groupName: string;
  rowType: CsFinRowType;
  totalRecords?: number;
  pageSize: number;
  pageIndex: number;
}

export interface CsFinTableGroupFilters <F> {
  filters: F;
  groupName: string;
}

export interface CsFinFilterList <F> {
  filterList: F[];
}

export enum CsFinRowType {
  GROUP = "GROUP",
  PAGINATOR = "PAGINATOR",
  ENTITY = "ENTITY"
}


export interface CsFinGroupFacade<T, F> {
  getEntityGroups(groupByColumns:string[],defaultFilters: F): Observable<CsFinGroup>;
  getAll(filters: F): Observable<PageResponse<T>>;
  convertToFilters(grouping: string, result:string, filter: F): F;
  getAllGroupedInfo(filterList: CsFinFilterList<F>): Observable<CsFinGroupedEntityInfo[]>;
}
