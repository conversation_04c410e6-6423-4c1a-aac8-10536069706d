import {PageFilters} from "../PageFilters";
import {SortFilters} from "../SortFilters";
import {BaleFilters} from "../bale/bale-filters";
import {TransactionFilters} from "../transaction/transaction-filters";

export interface ReconciliationFilters extends PageFilters, SortFilters {
  filterText?: string;
  id?: number;
  status?: string[];
  creator?: string;
  creationDateStart?: string;
  creationDateEnd?: string;
  reviewer?: string;
  reviewedDateStart?: string;
  reviewedDateEnd?: string;
  amountTransaction?: number;
  amountBale?: number;
  amountThreshold?: number;
  transactionID?: number;
  baleID?: number;
  accountID?: number;
  accountNumber?: string;
  navReference?: string;
  transactionFilters?: TransactionFilters;
  baleFilters?: BaleFilters;
}
