import {Currency} from "../currency/currency";
import {Account} from "../account/account";

export interface Statement {
  id: number;
  currency?: Currency;
  statementId?: string;
  previousStatementId?: string;
  initialDate?: string;
  finalDate?: string;
  initialDirection?: string;
  finalDirection?: string;
  initialAmount?: number;
  finalAmount?: number;
  initialAmountUsd?: number;
  finalAmountUsd?: number;
  fxRate?:string;
  status?: string;
  statusDescription?: string;
  account?: Account;
  createdAt: string;
  createdBy?: string;
  updatedAt?: string;
  updatedBy?: string;
  flow?:string;
}
