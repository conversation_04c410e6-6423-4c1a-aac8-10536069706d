import {BrowserModule} from '@angular/platform-browser';
import {HttpClient, HttpClientModule} from '@angular/common/http';
import {NgModule} from '@angular/core';
import {BrowserAnimationsModule} from '@angular/platform-browser/animations';
import {AppRoutingModule} from './app-routing.module';
import {AppComponent} from './app.component';
import {TranslateLoader, TranslateModule} from '@ngx-translate/core';
import {AppConfigurationModule} from './app-configuration/app-configuration.module';
import {
  CsFinLoadingModule,
  CsFinNavigationModule,
  CsFinPageNotFoundModule,
  CsFinTranslateHttpLoader,
  ENVIRONMENT
} from "@jumia-cs-fin/common";
import {ApiModule} from './api/api.module';
import {SharedModule} from './shared/shared.module';
import {LoginResponseModule} from './login-response/login-response.module';
import {NavigationModule} from './navigation/navigation.module';
import {AccountsModule} from './accounts/accounts.module';
import {MatNativeDateModule} from '@angular/material/core';
import {SchedulerModule} from './scheduler/scheduler.module';
import {BaleModule} from "./bale/bale.module";
import {ReconciliationModule} from './reconciliation/reconciliation.module';
import {environment} from "../environments/environment";
import {ApiLogModule} from "./api-log/api-log.module";
import {TroubleshootingModule} from "./troubleshooting/troubleshooting.module";
import {ExecutionLogsModule} from "./execution-logs/execution-logs.module";
import {StatementFilesModule} from "./statement-files/statement-files.module";
import {DashboardModule} from "./dashboard/dashboard.module";
import {ExportLogsModule} from "./export-logs/export-logs.module";


export function HttpLoaderFactory(http: HttpClient) {
  return new CsFinTranslateHttpLoader(http);
}

@NgModule({
  declarations: [
    AppComponent,
  ],
  imports: [
    BrowserModule,
    HttpClientModule,
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpClient]
      }
    }),
    AppConfigurationModule,
    ApiModule,
    SharedModule,
    NavigationModule,
    AccountsModule,
    DashboardModule,
    SchedulerModule,
    BaleModule,
    ReconciliationModule,
    LoginResponseModule,
    AppRoutingModule,
    BrowserAnimationsModule,
    MatNativeDateModule,
    CsFinLoadingModule,
    CsFinNavigationModule,
    CsFinPageNotFoundModule,
    ApiLogModule,
    TroubleshootingModule,
    ExecutionLogsModule,
    ExportLogsModule,
    StatementFilesModule
  ],
  providers: [
    {
      provide: ENVIRONMENT,
      useValue: environment
    }
  ],
  bootstrap: [AppComponent]
})
export class AppModule { }
