import {Component} from "@angular/core";
import {
  CsFinAuthAction,
  CsFinNavMenu,
  CsFinAuthorizationOnTargetService,
  CsFinAuthorizationOnAnyTargetOfTypeService
} from "@jumia-cs-fin/common";
import {bradPermissions} from "../auth/constants/permission.constants";
import {bradAuthTarget, bradAuthCountryTarget} from "../auth/constants/auth.constants";
import {frontendBaseUrls} from '../shared/constants/core.constants';
declare function require(moduleName: string): any;

const {version: appVersion} = require('../../../package.json');

@Component({
  selector: 'brad-navigation',
  templateUrl: './navigation.component.html',
  styleUrls: ['./navigation.component.scss']
})
export class NavigationComponent {

  constructor(private csFinAuthorizationOnAnyTargetOfTypeService: CsFinAuthorizationOnAnyTargetOfTypeService,
              private csFinAuthorizationOnTargetService : CsFinAuthorizationOnTargetService ) { }


  public version: string = appVersion;
  appFullName = "BRAD";
  appLogoPath = "assets/images/app-logo.png";
  menus: CsFinNavMenu[] = [
    {

      group: 'Manage',
      items: [
        {
          name: 'Accounts',
          icon: 'account_balance',
          routerLink: `/${frontendBaseUrls.accounts.list}`,
          auth: {
            permissions: bradPermissions.BRAD_ACCESS_ACCOUNTS,
            targets: bradAuthCountryTarget,
            authTypeService: this.csFinAuthorizationOnAnyTargetOfTypeService,
            action: CsFinAuthAction.HIDE
          }
        }
      ],
    },
    // {
    //   group: 'Reconcile',
    //   items: [
    //     {
    //       name: 'Bale',
    //       icon: 'list',
    //       routerLink: `/${frontendBaseUrls.bale.list}`,
    //       auth: {
    //         permissions: bradPermissions.BRAD_ACCESS_RECONCILIATION,
    //         targets: bradAuthTarget,
    //         targetType: CsFinAuthTargetType.APPLICATION,
    //         type: CsFinAuthType.HAS_PERMISSION_ON_TARGET,
    //         action: CsFinAuthAction.HIDE
    //       }
    //     },
    //     {
    //       name: 'Reconciliation',
    //       icon: 'compare_arrows',
    //       routerLink: '/reconciliation',
    //       auth: {
    //         permissions: bradPermissions.BRAD_ACCESS_RECONCILIATION,
    //         targets: bradAuthTarget,
    //         targetType: CsFinAuthTargetType.APPLICATION,
    //         type: CsFinAuthType.HAS_PERMISSION_ON_TARGET,
    //         action: CsFinAuthAction.HIDE
    //       },
    //       children: [
    //         {
    //           name: 'NAVIGATION.MENUS.RECONCILE',
    //           routerLink: `/${frontendBaseUrls.reconciliation.reconcile}`,
    //           auth: {
    //             permissions: bradPermissions.BRAD_ACCESS_RECONCILIATION,
    //             targets: bradAuthTarget,
    //             targetType: CsFinAuthTargetType.APPLICATION,
    //             type: CsFinAuthType.HAS_PERMISSION_ON_TARGET,
    //             action: CsFinAuthAction.HIDE
    //           }
    //         },
    //         {
    //           name: 'NAVIGATION.MENUS.THRESHOLDS',
    //           routerLink: `/${frontendBaseUrls.reconciliation.thresholds}`,
    //           auth: {
    //             permissions: bradPermissions.BRAD_MANAGE_THRESHOLDS,
    //             targets: bradAuthTarget,
    //             targetType: CsFinAuthTargetType.APPLICATION,
    //             type: CsFinAuthType.HAS_PERMISSION_ON_TARGET,
    //             action: CsFinAuthAction.HIDE
    //           }
    //         },
    //         {
    //           name: 'NAVIGATION.MENUS.RECONCILIATION_AUDIT',
    //           routerLink: `/${frontendBaseUrls.reconciliation.audit}`,
    //           auth: {
    //             permissions: bradPermissions.BRAD_ACCESS_RECONCILIATION,
    //             targets: bradAuthTarget,
    //             targetType: CsFinAuthTargetType.APPLICATION,
    //             type: CsFinAuthType.HAS_PERMISSION_ON_TARGET,
    //             action: CsFinAuthAction.HIDE
    //           }
    //         }
    //       ]
    //     }
    //   ]
    // },
    {
      group: 'ADMIN',
      items: [
        {
          name: 'NAVIGATION.MENUS.SCHEDULER',
          icon: 'schedule',
          routerLink: '/scheduler',
          auth: {
            permissions: bradPermissions.BRAD_ACCESS_SCHEDULER,
            targets: bradAuthTarget,
            action: CsFinAuthAction.HIDE,
            authTypeService: this.csFinAuthorizationOnTargetService
          }
        },
        {
          name: 'Exports',
          icon: 'download',
          routerLink: `/${frontendBaseUrls.exportLogs.list}`,
          auth: {
            permissions: bradPermissions.BRAD_ACCESS_EXPORT_LOG,
            targets: bradAuthTarget,
            authTypeService: this.csFinAuthorizationOnAnyTargetOfTypeService,
            action: CsFinAuthAction.HIDE
          }
        }
      ]
    },
    {
      group: 'Troubleshooting',
      items: [
        {
          name: 'API Log',
          icon: 'list',
          routerLink: `/${frontendBaseUrls.apiLog.list}`,
          auth: {
            permissions: bradPermissions.BRAD_ACCESS_API_LOG,
            targets: bradAuthTarget,
            authTypeService: this.csFinAuthorizationOnTargetService,
            action: CsFinAuthAction.HIDE
          }
        },
        {
          name: 'Troubleshooting',
          icon: 'troubleshoot',
          routerLink: `/${frontendBaseUrls.troubleshooting.list}`,
          auth: {
            permissions: bradPermissions.BRAD_ACCESS_TROUBLESHOOTING,
            targets: bradAuthCountryTarget,
            authTypeService: this.csFinAuthorizationOnAnyTargetOfTypeService,
            action: CsFinAuthAction.HIDE
          }
        },
        {
          name: 'Execution Logs',
          icon: 'playlist_play',
          routerLink: `/${frontendBaseUrls.executionLogs.list}`,
          auth: {
            permissions: bradPermissions.BRAD_ACCESS_EXECUTION_LOG,
            targets: bradAuthTarget,
            authTypeService: this.csFinAuthorizationOnTargetService,
            action: CsFinAuthAction.HIDE
          }
        },
        {
          name: 'Statements Files',
          icon: 'troubleshoot',
          routerLink: `/${frontendBaseUrls.accountStatementFiles.list}`,
          auth: {
            permissions: bradPermissions.BRAD_STATEMENT_FILES_ACCESS,
            targets: bradAuthTarget,
            authTypeService: this.csFinAuthorizationOnTargetService,
            action: CsFinAuthAction.HIDE
          }
        },
      ]
    }
  ];
}
