import {Injectable} from "@angular/core";
import {BehaviorSubject, Observable, of, Subject} from "rxjs";
import {getPropertyKey} from "../../shared/service/ts-utils.service";
import {activeFiltersSeparator, CsFinActiveFilterChip} from "@jumia-cs-fin/common";

import {PageResponse} from "../../entities/page-response";
import {ExecutionLogFilters} from "../../entities/execution-log/execution-log-filters";
import {ExecutionLogApiService} from "../../api/service/execution-log-api.service";
import {ExecutionLog} from "../../entities/execution-log/execution-log";

@Injectable({providedIn: 'root'})
export class ExecutionLogFacade {

  forceRefresh: Subject<boolean> = new Subject<boolean>();

  public fullscreenChangeBehaviorSubject = new BehaviorSubject<boolean>(true);
  public detailsCloseBehaviorSubject = new BehaviorSubject<boolean>(false);
  public selectedExecutionLogChangeBehaviorSubject = new BehaviorSubject<number>(-1);

  readonly filterTextKey = getPropertyKey<ExecutionLogFilters>(p => p.filterText);
  readonly recordsAmountKey = getPropertyKey<ExecutionLogFilters>(p => p.recordsAmount);
  readonly executionStartTimeKey = getPropertyKey<ExecutionLogFilters>(p => p.executionStartTime);
  readonly executionEndTimeKey = getPropertyKey<ExecutionLogFilters>(p => p.executionEndTime);
  readonly logTypesKey = getPropertyKey<ExecutionLogFilters>(p => p.logTypes);
  readonly logStatusesKey = getPropertyKey<ExecutionLogFilters>(p => p.logStatuses);
  readonly relatedEntityKey = getPropertyKey<ExecutionLogFilters>(p => p.relatedEntity);


  private readonly activeFiltersConfigMap = new Map<string, any>([
    [
      this.filterTextKey,
      (filterText: string) => {
        return {labelKey: 'GENERAL.FIELDS.FILTER_TEXT', displayText: filterText}
      }
    ],
    [
      this.recordsAmountKey,
      (recordsAmount: number) => {
        return {labelKey: 'EXECUTION_LOGS.FIELDS.RECORDS_AMOUNT', displayText: recordsAmount.toString()}
      }
    ],
    [
      this.executionStartTimeKey,
      (executionStartTime: string) => {
        return {labelKey: 'EXECUTION_LOGS.FIELDS.EXECUTION_START_TIME', displayText: executionStartTime}
      }
    ],
    [
      this.executionEndTimeKey,
      (executionEndTime: string) => {
        return {labelKey: 'EXECUTION_LOGS.FIELDS.EXECUTION_END_TIME', displayText: executionEndTime}
      }
    ],
    [
      this.logTypesKey,
      (logTypes: string[]) => {
        return {
          labelKey: 'EXECUTION_LOGS.FIELDS.TYPE',
          displayText: logTypes.map((type: string) => type).join(activeFiltersSeparator)
        }
      }
    ],
    [
      this.logStatusesKey,
      (logStatuses: string[]) => {
        return {
          labelKey: 'EXECUTION_LOGS.FIELDS.STATUS',
          displayText: logStatuses.map((status: string) => status).join(activeFiltersSeparator)
        }
      }
    ],
    [
      this.relatedEntityKey,
      (relatedEntity: string) => {
        return {labelKey: 'EXECUTION_LOGS.FIELDS.ENTITY', displayText: relatedEntity}
      }
    ]
  ]);

  private filtersBehaviorSubject = new BehaviorSubject<ExecutionLogFilters>({});
  private activeFilterChips = new Map<string, CsFinActiveFilterChip>();
  public filters$: Observable<ExecutionLogFilters> = this.filtersBehaviorSubject.asObservable();
  public activeFiltersChips$: Observable<Map<string, CsFinActiveFilterChip>> = of(this.activeFilterChips);

  constructor(public executionLogApiService: ExecutionLogApiService) {
  }

  filtersChanged(filters: ExecutionLogFilters) {
    this.filtersBehaviorSubject.next(filters);
    this.updateActiveFilterChips(filters);
  }


  private updateActiveFilterChips(filters: ExecutionLogFilters): void {
    if (!filters) {
      return;
    }
    this.activeFilterChips.clear();

    if (filters.filterText) {
      this.activeFilterChips.set(this.filterTextKey, this.activeFiltersConfigMap.get(this.filterTextKey)(filters.filterText));
    }
    if (filters.recordsAmount) {
      this.activeFilterChips.set(this.recordsAmountKey, this.activeFiltersConfigMap.get(this.recordsAmountKey)(filters.recordsAmount));
    }
    if (filters.executionStartTime) {
      this.activeFilterChips.set(this.executionStartTimeKey, this.activeFiltersConfigMap.get(this.executionStartTimeKey)(filters.executionStartTime));
    }
    if (filters.executionEndTime) {
      this.activeFilterChips.set(this.executionEndTimeKey, this.activeFiltersConfigMap.get(this.executionEndTimeKey)(filters.executionEndTime));
    }
    if (filters.logTypes && filters.logTypes.length > 0) {
      this.activeFilterChips.set(this.logTypesKey, this.activeFiltersConfigMap.get(this.logTypesKey)(filters.logTypes));
    }
    if (filters.logStatuses && filters.logStatuses.length > 0) {
      this.activeFilterChips.set(this.logStatusesKey, this.activeFiltersConfigMap.get(this.logStatusesKey)(filters.logStatuses));
    }
    if (filters.relatedEntity) {
      this.activeFilterChips.set(this.relatedEntityKey, this.activeFiltersConfigMap.get(this.relatedEntityKey)(filters.relatedEntity));
    }

  }

  getAll(filters?: ExecutionLogFilters): Observable<PageResponse<ExecutionLog>> {
    return this.executionLogApiService.getAll(filters);
  }

  getById(id: number): Observable<ExecutionLog> {
    return this.executionLogApiService.getById(id);
  }

  getLogTypes(): Observable<string[]> {
    return this.executionLogApiService.getLogTypes();
  }

  getLogStatus(): Observable<string[]> {
    return this.executionLogApiService.getLogStatus();
  }



}
