import {ChangeDetectorRef, Component, Input, OnD<PERSON>roy, OnInit, ViewChild, ViewEncapsulation} from '@angular/core';
import {FormControl, FormGroup} from "@angular/forms";
import {BehaviorSubject, Observable, Subject, tap} from "rxjs";
import {CdkConnectedOverlay} from "@angular/cdk/overlay";
import {CsFinActiveFiltersFacade, CsFinApiService, CsFinSidenavService} from "@jumia-cs-fin/common";
import {ActivatedRoute, Router} from "@angular/router";
import {takeUntil} from "rxjs/operators";
import * as _ from "lodash";
import {ExecutionLogFacade} from "../../facade/execution-log-facade";
import {ExecutionLogFilters} from "../../../entities/execution-log/execution-log-filters";

@Component({
  selector: 'brad-execution-logs-header',
  templateUrl: './execution-logs-header.component.html',
  styleUrls: ['./execution-logs-header.component.scss','../../../../assets/brad-custom.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ExecutionLogsHeaderComponent implements OnInit, OnDestroy {
  @Input() showFilters!: boolean;
  filterTextFormControl!: FormControl;
  queryParams = {};

  form!: FormGroup;

  recordsAmountFormControl!: FormControl;
  executionStartTimeFormControl!: FormControl;
  executionEndTimeFormControl!: FormControl;
  logStatusesFormControl!: FormControl;
  logTypesFormControl!: FormControl;
  relatedEntityFormControl!: FormControl;

  logStatusesSearchFormControl = new FormControl();
  logStatusesList:string[] = [];
  filteredLogStatusesList:string[] = [];

  logTypesSearchFormControl = new FormControl();
  logTypesList:string[] = [];
  filteredLogTypesList:string[] = [];


  isOpen$!: Observable<boolean>;
  isRefreshing = false;
  isInitializing = false;


  private readonly refreshTimeout = 2000;
  private _onDestroy: Subject<void> = new Subject<void>();
  private _isOpen!: BehaviorSubject<boolean>;

  @ViewChild(CdkConnectedOverlay, {static: true})
  private connectedOverlay!: CdkConnectedOverlay;

  constructor(public ref: ChangeDetectorRef,
              public sidenav: CsFinSidenavService,
              private activatedRoute: ActivatedRoute,
              private activeFiltersFacade: CsFinActiveFiltersFacade,
              private router: Router,
              private apiService: CsFinApiService,
              private executionLogFacade: ExecutionLogFacade) { }

  async ngOnInit(): Promise<void> {
    this.isInitializing = true;
    this.initializeOverlay();
    this.initFiltersSearch();
    this.subscribeUrlParamsChanges();
    this.subscribeActiveFiltersChange();
    this.subscribeFiltersChange();
  }

  ngOnDestroy(): void {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  private subscribeUrlParamsChanges(): void {
    this.activatedRoute.queryParams
      .pipe(takeUntil(this._onDestroy))
      .subscribe(params => this.initializeFormData(params));
  }

  private subscribeActiveFiltersChange(): void {
    this.activeFiltersFacade.openFiltersOverlay
      .pipe(takeUntil(this._onDestroy))
      .subscribe(value => this._isOpen.next(value));
  }

  private subscribeFiltersChange(): void {
    this.executionLogFacade.filters$
      .pipe(takeUntil(this._onDestroy))
      .subscribe((filters:ExecutionLogFilters) => {
        if(!this.isInitializing) {
          this.updateFormData(filters);
          this.updateMissingUrlFilters(filters);
        }
      });
  }

  private initFiltersSearch(): void {
    this.initLogTypesSearch();
    this.initLogStatusesSearch();
  }


  private initializeOverlay(): void {
    this._isOpen = new BehaviorSubject<boolean>(false);
    this.connectedOverlay.backdropClass = 'cdk-overlay-transparent-backdrop';
    this.connectedOverlay.panelClass = 'filters-overlay-panel';
    this.connectedOverlay.backdropClick
      .pipe(tap(() => this._isOpen.next(false)))
      .subscribe();
    this.isOpen$ = this._isOpen.asObservable();
  }

  private initLogTypesSearch(): void {
    this.logTypesSearchFormControl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe((value: string) => {
        const searchTerm = value.trim();
        if (searchTerm) {
          this.filteredLogTypesList = this.logTypesList.filter((type) => {
            return type.toLowerCase().includes(searchTerm.toLowerCase());
          })
        } else {
          this.filteredLogTypesList = this.logTypesList;
        }
      });
  }

  private initLogStatusesSearch(): void {
    this.logStatusesSearchFormControl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe((value: string) => {
        const searchTerm = value.trim();
        if (searchTerm) {
          this.filteredLogStatusesList = this.logStatusesList.filter((status) => {
            return status.toLowerCase().includes(searchTerm.toLowerCase());
          })
        } else {
          this.filteredLogStatusesList = this.logStatusesList;
        }
      });
  }

  clearFilters(): void {
    this.form.reset('', {emitEvent: false});
    this.executionLogFacade.filtersChanged({});
  }

  submit(input?: HTMLInputElement) {
    this.executionLogFacade.filtersChanged(this.getFormValues());
    const formQueryParams = this.apiService.buildQueryParams(this.getFormValues());
    if (_.isEqual(this.queryParams, formQueryParams)) {
      this.isRefreshing = true;
      this.ref.markForCheck();
      setTimeout(() => {
        this.isRefreshing = false;
        this.ref.markForCheck();
      }, this.refreshTimeout);
    } else {
      input?.blur();
      this.router.navigate(['/execution-logs'], {queryParams: formQueryParams});
    }
    this.closeOverlay();

  }

  triggerOverlay(): void {
    this._isOpen.next(!this._isOpen.value);
  }

  closeOverlay(): void {
    this._isOpen.next(false);
  }

  private getFormValues(): ExecutionLogFilters {
    return this.form.value as ExecutionLogFilters;
  }

  private initializeFormData(params:any): void {
    this.queryParams = params;
    if(!this.form) {
      this.form = new FormGroup({});
      this.initializeFormControlsAndFilters(params);
    }
  }

  private initializeFormControlsAndFilters(params:any): void {
    const filters: ExecutionLogFilters = {};

    this.filterTextFormControl = new FormControl(params.filterText);
    filters.filterText = params.filterText;

    this.recordsAmountFormControl = new FormControl(params.recordsAmount);
    filters.recordsAmount = params.recordsAmount;

    this.executionStartTimeFormControl = new FormControl(params.executionStartTime);
    filters.executionStartTime = params.executionStartTime;

    this.executionEndTimeFormControl = new FormControl(params.executionEndTime);
    filters.executionEndTime = params.executionEndTime;

    let logTypes: string[] = params.logType === undefined ? [] : params.logType.split(',');
    this.logTypesFormControl = new FormControl(logTypes);
    filters.logTypes = logTypes;

    let logStatuses: string[] = params.logStatus === undefined ? [] : params.logStatus.split(',');
    this.logStatusesFormControl = new FormControl(logStatuses);
    filters.logStatuses = logStatuses;

    this.relatedEntityFormControl = new FormControl(params.relatedEntity);
    filters.relatedEntity = params.relatedEntity;


    filters.orderField = params.orderField;
    filters.orderDirection = params.orderDirection;
    filters.page = params.page;
    filters.size = params.size;

    Promise.all([
      this.applyLogTypesUrlFilter(params, filters),
      this.applyLogStatusesUrlFilter(params, filters)
    ]).then(() => {
      this.setFormControlsToForm();
      this.executionLogFacade.filtersChanged(filters);
      this.updateMissingUrlFilters(filters);
      this.isInitializing = false;
    });

  }

  private applyLogTypesUrlFilter(params: any, filters: ExecutionLogFilters): Promise<void> {
    return new Promise<void>(async (resolve) => {
      this.logTypesFormControl = new FormControl();
      if(!params.logTypes) {
        resolve();
        return;
      }

      await this.loadLogTypes();
      let logTypesArray = Array.isArray(params.logTypes?.split(',')) ?
        params.logTypes?.split(',') :
        [params.logTypes];
      filters.logTypes = this.logTypesList.filter((type: string) => logTypesArray.includes(type));
      this.logTypesFormControl.setValue(filters.logTypes)
      resolve();
    });
  }

  private applyLogStatusesUrlFilter(params: any, filters: ExecutionLogFilters): Promise<void> {
    return new Promise<void>(async (resolve) => {
      this.logStatusesFormControl = new FormControl();

      if(!params.logStatuses) {
        resolve();
        return;
      }

      await this.loadLogStatuses();
      let logStatusesArray = Array.isArray(params.logStatuses?.split(',')) ?
        params.logStatuses?.split(',') :
        [params.logStatuses];
      filters.logStatuses = this.logStatusesList.filter((status: string) => logStatusesArray.includes(status));
      this.logStatusesFormControl.setValue(filters.logStatuses)
      resolve();
    });
  }

  loadLogTypes(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if(this.logTypesList.length === 0) {
        this.executionLogFacade.getLogTypes()
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (types: string[]) => {
              this.logTypesList = types;
              this.filteredLogTypesList = this.logTypesList;
              resolve();
            }, error: (error) => reject(error)
          });
      } else {
        resolve();
      }
    });
  }

  loadLogStatuses(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if(this.logStatusesList.length === 0) {
        this.executionLogFacade.getLogStatus()
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (status: string[]) => {
              this.logStatusesList = status;
              this.filteredLogStatusesList = this.logStatusesList;
              resolve();
            }, error: (error) => reject(error)
          });
      } else {
        resolve();
      }
    });
  }

  private updateFormData (params: ExecutionLogFilters): void {
    this.filterTextFormControl.setValue(params.filterText, {emitEvent: false});
    this.recordsAmountFormControl.setValue(params.recordsAmount, {emitEvent: false});
    this.executionStartTimeFormControl.setValue(params.executionStartTime, {emitEvent: false});
    this.executionEndTimeFormControl.setValue(params.executionEndTime, {emitEvent: false});
    this.logTypesFormControl.setValue(params.logTypes, {emitEvent: false});
    this.logStatusesFormControl.setValue(params.logStatuses, {emitEvent: false});
    this.relatedEntityFormControl.setValue(params.relatedEntity, {emitEvent: false});
  }

  private updateMissingUrlFilters(filters: ExecutionLogFilters): void {
    const formQueryParams = this.apiService.buildQueryParams(filters);
    if (!_.isEqual(this.queryParams, formQueryParams)) {
      this.router.navigate(['execution-logs'], {queryParams: formQueryParams});
    }
  }

  private setFormControlsToForm(): void {
    this.form.addControl(this.executionLogFacade.filterTextKey, this.filterTextFormControl);
    this.form.addControl(this.executionLogFacade.recordsAmountKey, this.recordsAmountFormControl);
    this.form.addControl(this.executionLogFacade.executionStartTimeKey, this.executionStartTimeFormControl);
    this.form.addControl(this.executionLogFacade.executionEndTimeKey, this.executionEndTimeFormControl);
    this.form.addControl(this.executionLogFacade.logTypesKey, this.logTypesFormControl);
    this.form.addControl(this.executionLogFacade.logStatusesKey, this.logStatusesFormControl);
    this.form.addControl(this.executionLogFacade.relatedEntityKey, this.relatedEntityFormControl);
  }

  noFiltersSelected() {
    return !!this.form.errors || this.isRefreshing;
  }

}
