<mat-toolbar id="header-toolbar">
  <button mat-icon-button (click)="sidenav.toggle()" fxHide [fxShow.xs]="true" [fxShow.sm]="true">
    <mat-icon>menu</mat-icon>
  </button>
  <span class="page-title">{{ 'EXECUTION_LOGS.TITLE' | translate }}</span>

  <section class="search-bar" [formGroup]="form">
    <!-- filter text (code) -->
    <mat-form-field id="search" class="change-header" appearance="outline" fxShow [fxShow.xs]="false" [fxShow.sm]="false"
                    cdkOverlayOrigin #trigger="cdkOverlayOrigin">
      <mat-icon matPrefix>search</mat-icon>
      <mat-label class="theLabel">{{'EXECUTION_LOGS.DETAILS.SEARCH_BAR' | translate}}</mat-label>
      <input type="text" #input matInput [formControl]="filterTextFormControl"
             (keyup.enter)="submit(input)">
      <mat-icon matSuffix id="filters" (click)="triggerOverlay()">filter_list</mat-icon>
    </mat-form-field>

    <button mat-raised-button color="primary" id="apply-search-btn" class="raised-primary-btn" fxShow [fxShow.xs]="false"
            [fxShow.sm]="false" [disabled]="noFiltersSelected()" (click)="submit()">
      {{ 'GENERAL.BUTTONS.LABELS.SEARCH' | translate }}
    </button>
  </section>

  <span fxFlex fxHide [fxShow.xs]="true" [fxShow.sm]="true"></span>
  <button mat-icon-button aria-label="Filter accounts" fxHide [fxShow.xs]="true" [fxShow.sm]="true"
          *ngIf="showFilters" id="show-mobile-filters">
    <mat-icon (click)="triggerOverlay()">filter_list</mat-icon>
  </button>
  <ng-template cdkConnectedOverlay class="template"
               [cdkConnectedOverlayHasBackdrop]="true"
               [cdkConnectedOverlayOrigin]="trigger"
               [cdkConnectedOverlayOpen]="(isOpen$ | async) || false">
    <div class="filters-overlay">
      <div class="filters-header">
        <mat-icon fxHide [fxShow.xs]="true" [fxShow.sm]="true" (click)="closeOverlay()">close</mat-icon>
        <p class="filters-title">{{'GENERAL.FILTERS.TITLE' | translate}}</p>
        <button fxHide class="flat-white-btn filters-cancel-btn" [disabled]="noFiltersSelected()" [fxShow.xs]="true"
                [fxShow.sm]="true"
                mat-flat-button (click)="clearFilters()" id="clear-btn">
          {{ 'GENERAL.BUTTONS.CLEAR' | translate }}
        </button>
      </div>

      <div class="filters-container">

        <!-- log type -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-type-entity-filter"
                        (click)="loadLogTypes()">
          <mat-label>{{'EXECUTION_LOGS.FIELDS.TYPE' | translate}}</mat-label>
          <mat-select [formControl]="logTypesFormControl" multiple>
            <ng-container *ngIf="filteredLogTypesList != null; else loadingLogType">
              <mat-option>
                <ngx-mat-select-search [formControl]="logTypesSearchFormControl"
                                       [noEntriesFoundLabel]="'GENERAL.FILTERS.NO_MATCHING_RESULTS_FOUND' | translate"
                                       [placeholderLabel]="'GENERAL.FILTERS.SEARCH' | translate">
                </ngx-mat-select-search>
              </mat-option>
              <mat-option *ngFor="let type of filteredLogTypesList" [value]="type">
                {{type}}
              </mat-option>
              <button mat-button color="primary" class="clear-selection-btn"
                      [disabled]="!logTypesFormControl.value?.length"
                      (click)="logTypesFormControl.reset([])">
                {{'GENERAL.FILTERS.ACTIONS.CLEAR_SELECTION' | translate}}
              </button>
            </ng-container>
            <ng-template #loadingLogType>
              <mat-option disabled>
                <div class="filters-loading-container">
                  <span>{{'GENERAL.FILTERS.LOADING' | translate}}</span>
                  <mat-spinner diameter="20"></mat-spinner>
                </div>
              </mat-option>
            </ng-template>
          </mat-select>
        </mat-form-field>

        <!-- log status -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-status-entity-filter"
                        (click)="loadLogStatuses()">
          <mat-label>{{'EXECUTION_LOGS.FIELDS.STATUS' | translate}}</mat-label>
          <mat-select [formControl]="logStatusesFormControl" multiple>
            <ng-container *ngIf="filteredLogStatusesList != null; else loadingLogStatus">
              <mat-option>
                <ngx-mat-select-search [formControl]="logStatusesSearchFormControl"
                                       [noEntriesFoundLabel]="'GENERAL.FILTERS.NO_MATCHING_RESULTS_FOUND' | translate"
                                       [placeholderLabel]="'GENERAL.FILTERS.SEARCH' | translate">
                </ngx-mat-select-search>
              </mat-option>
              <mat-option *ngFor="let status of filteredLogStatusesList" [value]="status">
                {{status}}
              </mat-option>
              <button mat-button color="primary" class="clear-selection-btn"
                      [disabled]="!logStatusesFormControl.value?.length"
                      (click)="logStatusesFormControl.reset([])">
                {{'GENERAL.FILTERS.ACTIONS.CLEAR_SELECTION' | translate}}
              </button>
            </ng-container>
            <ng-template #loadingLogStatus>
              <mat-option disabled>
                <div class="filters-loading-container">
                  <span>{{'GENERAL.FILTERS.LOADING' | translate}}</span>
                  <mat-spinner diameter="20"></mat-spinner>
                </div>
              </mat-option>
            </ng-template>
          </mat-select>
        </mat-form-field>

        <!-- records amount -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-records-amount-filter">
          <mat-label>{{'EXECUTION_LOGS.FIELDS.RECORDS_AMOUNT' | translate}}</mat-label>
          <input matInput type="number" [formControl]="recordsAmountFormControl">
        </mat-form-field>


        <!-- execution start time -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-execution-start-time-filter">
          <mat-label>{{'EXECUTION_LOGS.FIELDS.EXECUTION_START_TIME' | translate}}</mat-label>
          <input matInput [matDatepicker]="picker1" [formControl]="executionStartTimeFormControl">
          <mat-datepicker-toggle matSuffix [for]="picker1"></mat-datepicker-toggle>
          <mat-datepicker #picker1></mat-datepicker>
        </mat-form-field>

        <!-- execution end time -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-execution-end-time-filter">
          <mat-label>{{'EXECUTION_LOGS.FIELDS.EXECUTION_END_TIME' | translate}}</mat-label>
          <input matInput [matDatepicker]="picker2" [formControl]="executionEndTimeFormControl">
          <mat-datepicker-toggle matSuffix [for]="picker2"></mat-datepicker-toggle>
          <mat-datepicker #picker2></mat-datepicker>
        </mat-form-field>

        <!-- relatedEntity -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-related-entity-filter">
          <mat-label>{{'EXECUTION_LOGS.FIELDS.ENTITY' | translate}}</mat-label>
          <input matInput [formControl]="relatedEntityFormControl">
        </mat-form-field>
      </div>

      <div class="filters-actions">
        <button class="flat-white-btn filters-cancel-btn" [disabled]="noFiltersSelected()" mat-flat-button fxShow
                (click)="clearFilters()" [fxShow.xs]="false" [fxShow.sm]="false">
          {{'GENERAL.FILTERS.ACTIONS.CLEAR' | translate}}
        </button>
        <button class="raised-primary-btn filters-apply-btn" [disabled]="noFiltersSelected()" mat-raised-button
                color="primary"
                (click)="submit()">
          {{'GENERAL.FILTERS.ACTIONS.APPLY' | translate}}
        </button>
      </div>

    </div>

  </ng-template>

</mat-toolbar>
