<main class="container" cdkDropListGroup>
  <section class="filters" *ngIf="dataSource">
    <cs-fin-active-filters [chipsMap]="activeFilterChips"
                           (onRemove)="onActiveFilterRemoveClick($event)">
    </cs-fin-active-filters>
    <span fxFlex></span>
    <span class="actions">
      <button mat-stroked-button id="change-columns-btn" color="primary" fxShow [fxHide.xs]="true" [fxHide.sm]="true"
              [disabled]="executionLogDetailsOpened" cdkOverlayOrigin #trigger="cdkOverlayOrigin"
              (click)="triggerOverlay(trigger)">
        <mat-icon>table_rows</mat-icon>
      </button>
    </span>

  </section>
  <mat-progress-bar mode="indeterminate" *ngIf="isLoading"></mat-progress-bar>

  <section class="table-detail responsive-table"
           *ngIf="!isLoading"
           [class.detail-opened]="executionLogDetailsOpened"
           [class.fullscreen]="isExecutionLogDetailsInFullscreen">

    <div class="table-container" [class.detail-opened]="executionLogDetailsOpened"
         [class.fullscreen]="isExecutionLogDetailsInFullscreen">

      <mat-progress-bar mode="indeterminate" *ngIf="isLoading"></mat-progress-bar>

      <table mat-table class="cs-fin-table" matSort [dataSource]="dataSource"  [class.loading]="isLoading"
             (matSortChange)="onSortChange($event)" cdkDropList cdkDropListSortingDisabled>

        <ng-container matColumnDef="id">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'EXECUTION_LOGS.FIELDS.ID' | translate }}</th>
          <td mat-cell *matCellDef="let executionLog"> {{executionLog?.id}} </td>
        </ng-container>

        <ng-container matColumnDef="status">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'EXECUTION_LOGS.FIELDS.STATUS' | translate }}</th>
          <td mat-cell *matCellDef="let executionLog"> {{executionLog?.logStatus}} </td>
        </ng-container>

        <ng-container matColumnDef="type">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'EXECUTION_LOGS.FIELDS.TYPE' | translate }}</th>
          <td mat-cell *matCellDef="let executionLog"> {{executionLog?.logType}} </td>
        </ng-container>

        <ng-container matColumnDef="recordsAmount">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'EXECUTION_LOGS.FIELDS.RECORDS_AMOUNT' | translate }}</th>
          <td mat-cell *matCellDef="let executionLog"> {{executionLog?.recordsAmount}} </td>
        </ng-container>

        <ng-container matColumnDef="executionStartTime">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'EXECUTION_LOGS.FIELDS.EXECUTION_START_TIME' | translate }}</th>
          <td mat-cell *matCellDef="let executionLog"> {{executionLog?.executionStartTime | date: 'medium'}} </td>
        </ng-container>

        <ng-container matColumnDef="executionEndTime">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'EXECUTION_LOGS.FIELDS.EXECUTION_END_TIME' | translate }}</th>
          <td mat-cell *matCellDef="let executionLog"> {{executionLog?.executionEndTime | date: 'medium'}} </td>
        </ng-container>


        <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"
            (click)="onOpenItemDetailsClick(row.id)"
            [class.item-opened]="isItemDetailsOpened(row.id)"
            class="with-detail" style="cursor: pointer;"></tr>
      </table>

      <mat-paginator [pageSizeOptions]="pagination.pageSizeOptions"
                     [pageSize]="pagination.pageSize"
                     [length]="pagination.totalItems"
                     [pageIndex]="pagination.pageIndex"
                     (page)="onPageChange($event)"
                     showFirstLastButtons>
      </mat-paginator>

      <span id="empty-table" *ngIf="!isLoading && !dataSource.data.length">
        {{'GENERAL.TABLE.NO_RESULTS_FOUND' | translate}}
      </span>
    </div>
    <div class="{{isExecutionLogDetailsInFullscreen ? 'details-full-screen' : 'details-container'}}"
         *ngIf="executionLogDetailsOpened">
      <router-outlet></router-outlet>
    </div>
  </section>

  <cs-fin-add-remove-columns [triggerOrigin]="triggerOrigin"
                             [isOverlayOpen$]="isOverlayOpen$"
                             [menu]="MENU">
  </cs-fin-add-remove-columns>
</main>
