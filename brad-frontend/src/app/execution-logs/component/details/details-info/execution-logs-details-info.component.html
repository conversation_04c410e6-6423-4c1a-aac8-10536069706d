<div class="details-information">
  <div *ngIf="isLoading" class="loading">
    <mat-progress-spinner mode="indeterminate" diameter="75" strokeWidth="5"></mat-progress-spinner>
    <span class="label">{{'GENERAL.DETAILS.LOADING' | translate}}</span>
  </div>
  <div *ngIf="!isLoading && !detailedExecutionLog" class="detail-failed">
    <mat-icon>report_problem</mat-icon>
    <span class="label">{{'EXECUTION_LOGS.DETAILS.ERRORS.UNABLE_TO_FIND_DETAILS' | translate}}</span>
  </div>

  <main *ngIf="!isLoading && detailedExecutionLog">
    <section class="header">
      <button mat-mini-fab disabled>
        <mat-icon>compare</mat-icon>
      </button>
      <span class="item-name">{{detailedExecutionLog.id}}</span>
    </section>

    <ul class="description">
      <li>
        <span class="field" data-cy="execution-log-details-field-records_amount">{{'EXECUTION_LOGS.FIELDS.RECORDS_AMOUNT' | translate}}: </span>
        <span class="value" data-cy="execution-log-details-value-records_amount">{{detailedExecutionLog.recordsAmount}}</span>
      </li>
      <li>
        <span class="field" data-cy="execution-log-details-field-status">{{'EXECUTION_LOGS.FIELDS.STATUS' | translate}}: </span>
        <span class="value" data-cy="execution-log-details-value-status">{{detailedExecutionLog.logStatus}}</span>
      </li>
      <li>
        <span class="field" data-cy="execution-log-details-field-type">{{'EXECUTION_LOGS.FIELDS.TYPE' | translate}}: </span>
        <span class="value" data-cy="execution-log-details-value-type">{{detailedExecutionLog.logType}}</span>
      </li>
      <li>
        <span class="field" data-cy="execution-log-details-field-execution-start-time">{{'EXECUTION_LOGS.FIELDS.EXECUTION_START_TIME' | translate}}: </span>
        <span class="value" data-cy="execution-log-details-value-execution-start-time">{{detailedExecutionLog.executionStartTime | date:'medium'}}</span>
      </li>
      <li>
        <span class="field" data-cy="execution-log-details-field-execution-end-time">{{'EXECUTION_LOGS.FIELDS.EXECUTION_END_TIME' | translate}}: </span>
        <span class="value" data-cy="execution-log-details-value-execution-end-time">{{detailedExecutionLog.executionEndTime | date:'medium'}}</span>
      </li>
    </ul>

    <div class="payloads">
      <mat-expansion-panel *ngIf="detailedExecutionLog.appliedFilters">
        <mat-expansion-panel-header>
          <mat-panel-title>
            <div>
              <span class="page-title">{{'EXECUTION_LOGS.FIELDS.APPLIED_FILTERS' | translate}}:</span>
            </div>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <div class="code-action-card">
          <pre data-cy="execution-log-details-value-request">{{detailedExecutionLog.appliedFilters | jsonFormatter}}</pre>
        </div>
      </mat-expansion-panel><br>
      <mat-expansion-panel *ngIf="detailedExecutionLog.query" class="container">
        <mat-expansion-panel-header>
          <mat-panel-title>

            <div class="section-header-text" id="details_header_container">
              <span class="page-title">{{'EXECUTION_LOGS.FIELDS.QUERY' | translate}}:</span>
            </div>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <div class="code-action-card">
          <pre data-cy="execution-log-details-value-request">{{detailedExecutionLog.query | jsonFormatter}}</pre>
        </div>
      </mat-expansion-panel>
    </div>
  </main>
</div>
