import {Component, Input, On<PERSON>hanges, OnD<PERSON>roy, OnInit} from '@angular/core';
import {Subject} from "rxjs";
import {ExecutionLog} from "../../../../entities/execution-log/execution-log";

@Component({
  selector: 'brad-execution-logs-details-info',
  templateUrl: './execution-logs-details-info.component.html',
  styleUrls: ['./execution-logs-details-info.component.scss']
})
export class ExecutionLogsDetailsInfoComponent implements OnInit, OnDestroy, OnChanges {

  @Input() detailedExecutionLog!: ExecutionLog;


  isLoading = false;
  private _onDestroy: Subject<void> = new Subject<void>();

  ngOnInit(): void {
    this.isLoading = true;
  }

  ngOnChanges() {
    if(this.detailedExecutionLog){
      this.isLoading = false;
    }
  }

  ngOnDestroy(): void {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

}
