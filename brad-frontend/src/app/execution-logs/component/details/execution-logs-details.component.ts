import {Component, OnDestroy, OnInit} from '@angular/core';
import {Subject} from "rxjs";
import {ActivatedRoute, Params, Router} from "@angular/router";
import {takeUntil} from "rxjs/operators";
import {ExecutionLog} from "../../../entities/execution-log/execution-log";
import {ExecutionLogFacade} from "../../facade/execution-log-facade";

@Component({
  selector: 'brad-execution-logs-details',
  templateUrl: './execution-logs-details.component.html',
  styleUrls: ['./execution-logs-details.component.scss']
})
export class ExecutionLogsDetailsComponent  implements OnInit, OnDestroy {

  isFullscreen = true;
  executionLogID!: number;
  executionLog!: ExecutionLog;

  private _onDestroy:Subject<void> = new Subject<void>();

  constructor(
    private executionLogFacade: ExecutionLogFacade,
    private router: Router,
    private route: ActivatedRoute,
  ) {
  }
  ngOnInit(): void {
    this.route.params
      .pipe(takeUntil(this._onDestroy))
      .subscribe((params: Params) => {
        this.executionLogID = Number(params['executionLogID']);
        if (this.executionLogID != null) {
          this.executionLogFacade.selectedExecutionLogChangeBehaviorSubject.next(this.executionLogID);
        }
      });

    this.executionLogFacade.selectedExecutionLogChangeBehaviorSubject
      .pipe(takeUntil(this._onDestroy))
      .subscribe((executionLogID: number) => {
        this.executionLogID = executionLogID;
        this.loadExecutionLogDetails();
      });
  }

  ngOnDestroy() {
    this._onDestroy.next();
    this._onDestroy.complete();
    this.executionLogFacade.detailsCloseBehaviorSubject.next(true);
  }

  onFullscreenClick(): void {
    this.isFullscreen = !this.isFullscreen;
    this.executionLogFacade.fullscreenChangeBehaviorSubject.next(this.isFullscreen);
  }

  onCloseClick(): void {
    this.executionLogFacade.detailsCloseBehaviorSubject.next(true);
    this.router.navigate(['execution-logs'], {queryParams: this.route.snapshot.queryParams});
  }

  loadExecutionLogDetails(): void {
    if (this.executionLogID != null) {
      this.executionLogFacade.getById(this.executionLogID)
        .pipe(takeUntil(this._onDestroy))
        .subscribe((executionLog: ExecutionLog) => {
          this.executionLog = executionLog;
        });
    }

  }


}
