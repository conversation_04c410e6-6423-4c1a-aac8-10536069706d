import {Component, Input, <PERSON><PERSON><PERSON><PERSON>, OnD<PERSON>roy, OnInit} from '@angular/core';
import {ExecutionLog} from "../../../../entities/execution-log/execution-log";
import {Subject} from "rxjs";

@Component({
  selector: 'brad-execution-logs-errors',
  templateUrl: './execution-logs-errors.component.html',
  styleUrls: ['./execution-logs-errors.component.scss']
})
export class ExecutionLogsErrorsComponent implements OnInit, OnDestroy, OnChanges {

  @Input() detailedExecutionLog!: ExecutionLog;

  isLoading = false;
  private _onDestroy: Subject<void> = new Subject<void>();

  ngOnInit(): void {
    if(!this.detailedExecutionLog) {
      this.isLoading = true;
    }
  }

  ngOnChanges() {
    if(this.detailedExecutionLog){
      this.isLoading = false;
    }
  }

  ngOnDestroy(): void {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

}
