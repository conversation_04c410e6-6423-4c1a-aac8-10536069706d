<mat-card class="details-wrapper main">
  <span class="actions">
    <span fxFlex></span>
    <mat-icon id="fullscreen-toggle" (click)="onFullscreenClick()" fxShow [fxShow.xs]="false" [fxShow.sm]="false" data-cy="toggle-details-fullscreen-btn">
      {{isFullscreen ? 'fullscreen_exit' : 'fullscreen'}}
    </mat-icon>
    <mat-icon (click)="onCloseClick()" data-cy="close-details-btn">close</mat-icon>
  </span>
  <mat-tab-group class="details-tab-group" fitInkBarToContent="false">

    <!-- details -->
    <mat-tab [label]="'EXECUTION_LOGS.DETAILS.TABS.LABELS.EXECUTION_LOG_DETAILS' | translate">
      <brad-execution-logs-details-info [detailedExecutionLog]="executionLog"></brad-execution-logs-details-info>
    </mat-tab>
    <mat-tab *ngIf="executionLog?.logStatus=='ERROR'" [label]="'EXECUTION_LOGS.DETAILS.TABS.LABELS.EXECUTION_LOG_ERRORS' | translate">
      <brad-execution-logs-errors [detailedExecutionLog]="executionLog"></brad-execution-logs-errors>
    </mat-tab>

  </mat-tab-group>

</mat-card>
