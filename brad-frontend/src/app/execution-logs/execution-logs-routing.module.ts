import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {NavigationComponent} from "../navigation/navigation.component";
import {csFinAuthCanActivateGuard} from "@jumia-cs-fin/common";
import {bradPermissions} from "../auth/constants/permission.constants";
import {bradAuthTarget} from "../auth/constants/auth.constants";
import {ExecutionLogsComponent} from "./execution-logs.component";
import {ExecutionLogsDetailsComponent} from "./component/details/execution-logs-details.component";
import {csFinAuthorizationOnTargetService} from "../app-routing.module";

const routes: Routes = [
  {
    path: '',
    component: NavigationComponent,
    children: [
      {
        path: '',
        canActivate: [csFinAuthCanActivateGuard],
        data: {
          auth: {
            permissions: bradPermissions.BRAD_ACCESS_EXECUTION_LOG,
            targets: bradAuthTarget,
            authTypeService: csFinAuthorizationOnTargetService,
          }
        },
        component: ExecutionLogsComponent,
        children: [
          {
            path: ':executionLogID',
            component: ExecutionLogsDetailsComponent
          }
        ]
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ExecutionLogsRoutingModule { }
