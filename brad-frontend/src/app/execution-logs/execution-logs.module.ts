import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';

import {ExecutionLogsRoutingModule} from './execution-logs-routing.module';
import {ExecutionLogsListComponent} from './component/list/execution-logs-list.component';
import {ExecutionLogsDetailsComponent} from './component/details/execution-logs-details.component';
import {ExecutionLogsComponent} from "./execution-logs.component";
import {ExecutionLogsHeaderComponent} from './component/header/execution-logs-header.component';
import {CdkDropList, CdkDropListGroup} from "@angular/cdk/drag-drop";
import {CdkConnectedOverlay, CdkOverlayOrigin} from "@angular/cdk/overlay";
import {CsFinActiveFiltersModule, CsFinAddRemoveColumnsModule} from "@jumia-cs-fin/common";
import {ExtendedModule, FlexModule} from "@angular/flex-layout";
import {MatButtonModule} from "@angular/material/button";
import {MatIconModule} from "@angular/material/icon";
import {MatPaginatorModule} from "@angular/material/paginator";
import {MatProgressBarModule} from "@angular/material/progress-bar";
import {MatSortModule} from "@angular/material/sort";
import {MatTableModule} from "@angular/material/table";
import {TranslateModule} from "@ngx-translate/core";
import {MatDatepickerModule} from "@angular/material/datepicker";
import {MatFormFieldModule} from "@angular/material/form-field";
import {MatInputModule} from "@angular/material/input";
import {MatOptionModule} from "@angular/material/core";
import {MatProgressSpinnerModule} from "@angular/material/progress-spinner";
import {MatSelectModule} from "@angular/material/select";
import {MatToolbarModule} from "@angular/material/toolbar";
import {NgxMatSelectSearchModule} from "ngx-mat-select-search";
import {ReactiveFormsModule} from "@angular/forms";
import {
  ExecutionLogsDetailsInfoComponent
} from './component/details/details-info/execution-logs-details-info.component';
import {JsonFormatter} from "../shared/helpers/json-formatter";
import {MatExpansionModule} from "@angular/material/expansion";
import {MatCardModule} from "@angular/material/card";
import {MatTabsModule} from "@angular/material/tabs";
import {ExecutionLogsErrorsComponent} from './component/details/errors/execution-logs-errors.component';


@NgModule({
  declarations: [
    ExecutionLogsListComponent,
    ExecutionLogsDetailsComponent,
    ExecutionLogsComponent,
    ExecutionLogsHeaderComponent,
    ExecutionLogsDetailsInfoComponent,
    ExecutionLogsErrorsComponent
  ],
  imports: [
    CommonModule,
    ExecutionLogsRoutingModule,
    CdkDropList,
    CdkDropListGroup,
    CdkOverlayOrigin,
    CsFinActiveFiltersModule,
    CsFinAddRemoveColumnsModule,
    ExtendedModule,
    FlexModule,
    MatButtonModule,
    MatIconModule,
    MatPaginatorModule,
    MatProgressBarModule,
    MatSortModule,
    MatTableModule,
    TranslateModule,
    CdkConnectedOverlay,
    MatDatepickerModule,
    MatFormFieldModule,
    MatInputModule,
    MatOptionModule,
    MatProgressSpinnerModule,
    MatSelectModule,
    MatToolbarModule,
    NgxMatSelectSearchModule,
    ReactiveFormsModule,
    JsonFormatter,
    MatExpansionModule,
    MatCardModule,
    MatTabsModule
  ]
})
export class ExecutionLogsModule { }
