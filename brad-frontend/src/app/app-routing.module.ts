import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {NavigationComponent} from './navigation/navigation.component';
import {
  csFinAuthCanActivateGuard,
  CsFinAuthorizationOnTargetService,
  CsFinPageNotFoundComponent
} from '@jumia-cs-fin/common';
import {bradPermissions} from './auth/constants/permission.constants';
import {bradAuthTarget} from './auth/constants/auth.constants';
import {LoginResponseComponent} from "./login-response/component/login-response/login-response.component";

export const csFinAuthorizationOnTargetService =  new CsFinAuthorizationOnTargetService();

const routes: Routes = [
  {
    path: '',
    component: NavigationComponent,
    canActivate: [csFinAuthCanActivateGuard],
    pathMatch: 'full',
    data: {
      auth: {
        permissions: bradPermissions.BRAD_CAN_ACCESS,
        targets: bradAuthTarget,
        authTypeService: csFinAuthorizationOnTargetService
      }
    }
  },
  {
    path: 'accounts',
    loadChildren: () => import('./accounts/accounts-routing.module').then(m => m.AccountsRoutingModule)
  },
  {
    path: 'bale',
    loadChildren: () => import('./bale/bale-routing.module').then(m => m.BaleRoutingModule)
  },
  // {
  //   path: 'reconcile',
  //   loadChildren: () => import('./reconciliation/reconciliation-routing.module').then(m => m.ReconciliationRoutingModule)
  // },
  {
    path: 'api-log',
    loadChildren: () => import('./api-log/api-log-routing.module').then(m => m.ApiLogRoutingModule)
  },
  {
    path: 'troubleshooting',
    loadChildren: () => import('./troubleshooting/troubleshooting-routing.module').then(m => m.TroubleshootingRoutingModule)
  },
  {
    path: 'execution-logs',
    loadChildren: () => import('./execution-logs/execution-logs-routing.module').then(m => m.ExecutionLogsRoutingModule)
  },
  {
    path: 'export-logs',
    loadChildren: () => import('./export-logs/export-logs-routing.module').then(m => m.ExportLogsRoutingModule)
  },
  {
    path: 'statement-files',
    loadChildren: () => import('./statement-files/statement-files-routing.module').then(m => m.StatementFilesRoutingModule)
  },
  {
    path: 'login/response',
    component: LoginResponseComponent
  },
  {
    path: '**',
    component: NavigationComponent,
    canActivate: [csFinAuthCanActivateGuard],
    data: {
      auth: {
        permissions: bradPermissions.BRAD_CAN_ACCESS,
        targets: bradAuthTarget,
        authTypeService: csFinAuthorizationOnTargetService

      }
    },
    children: [
      {
        path: '',
        component: CsFinPageNotFoundComponent
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
