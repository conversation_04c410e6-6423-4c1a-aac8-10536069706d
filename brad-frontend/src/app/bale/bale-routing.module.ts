import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {NavigationComponent} from "../navigation/navigation.component";
import {BaleDetailsComponent} from "./component/details/bale-details.component";
import {BaleComponent} from "./bale.component";
import {csFinAuthCanActivateGuard} from "@jumia-cs-fin/common";
import {bradPermissions} from "../auth/constants/permission.constants";
import {bradAuthTarget} from "../auth/constants/auth.constants";
import {csFinAuthorizationOnTargetService} from "../app-routing.module";

const routes: Routes = [
  {
    path: 'bale',
    component: NavigationComponent,
    children: [
      {
        path: '',
        canActivate: [csFinAuthCanActivateGuard],
        data: {
          auth: {
            permissions: bradPermissions.BRAD_ACCESS_RECONCILIATION,
            targets: bradAuthTarget,
            authTypeService: csFinAuthorizationOnTargetService,
          }
        },
        component: BaleComponent,
        children: [
          {
            path: ':baleEntryNo',
            component: BaleDetailsComponent
          }
        ]
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class BaleRoutingModule { }
