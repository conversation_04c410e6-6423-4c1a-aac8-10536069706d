import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';

import {BaleRoutingModule} from './bale-routing.module';
import {BaleListComponent} from './component/bale-list/bale-list.component';
import {MatButtonModule} from "@angular/material/button";
import {MatCheckboxModule} from "@angular/material/checkbox";
import {MatFormFieldModule} from "@angular/material/form-field";
import {MatIconModule} from "@angular/material/icon";
import {MatInputModule} from "@angular/material/input";
import {MatMenuModule} from "@angular/material/menu";
import {MatPaginatorModule} from "@angular/material/paginator";
import {MatProgressBarModule} from "@angular/material/progress-bar";
import {MatSortModule} from "@angular/material/sort";
import {MatTableModule} from "@angular/material/table";
import {MatToolbarModule} from "@angular/material/toolbar";
import {MatTooltipModule} from "@angular/material/tooltip";
import {TranslateModule} from "@ngx-translate/core";
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {BaleDetailsComponent} from './component/details/bale-details.component';
import {MatCardModule} from "@angular/material/card";
import {MatDividerModule} from "@angular/material/divider";
import {MatListModule} from "@angular/material/list";
import {MatTabsModule} from "@angular/material/tabs";
import {BaleHeaderComponent} from './component/header/bale-header.component';
import {ExtendedModule, FlexModule} from "@angular/flex-layout";
import {MatOptionModule} from "@angular/material/core";
import {MatProgressSpinnerModule} from "@angular/material/progress-spinner";
import {MatSelectModule} from "@angular/material/select";
import {NgxMatSelectSearchModule} from "ngx-mat-select-search";
import {OverlayModule} from "@angular/cdk/overlay";
import {
  BaleDetailsInformationComponent
} from './component/details/details-information/bale-details-information.component';
import {DragDropModule} from "@angular/cdk/drag-drop";
import {SharedModule} from "../shared/shared.module";
import {BaleComponent} from './bale.component';
import {BaleHeaderFormComponent} from './component/header/header-form/header-form.component';
import {CsFinActiveFiltersModule, CsFinAddRemoveColumnsModule} from "@jumia-cs-fin/common";
import {MatDatepickerModule} from "@angular/material/datepicker";


@NgModule({
  declarations: [
    BaleListComponent,
    BaleDetailsComponent,
    BaleHeaderComponent,
    BaleDetailsInformationComponent,
    BaleComponent,
    BaleHeaderFormComponent,
  ],
    exports: [
        BaleListComponent,
        BaleHeaderComponent,
        BaleHeaderFormComponent
    ],
  imports: [
    CommonModule,
    BaleRoutingModule,
    MatButtonModule,
    MatCheckboxModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatMenuModule,
    MatPaginatorModule,
    MatProgressBarModule,
    MatSortModule,
    MatTableModule,
    MatToolbarModule,
    MatTooltipModule,
    TranslateModule,
    FormsModule,
    MatCardModule,
    MatDividerModule,
    MatListModule,
    MatTabsModule,
    ExtendedModule,
    FlexModule,
    MatOptionModule,
    MatProgressSpinnerModule,
    MatSelectModule,
    NgxMatSelectSearchModule,
    OverlayModule,
    ReactiveFormsModule,
    DragDropModule,
    SharedModule,
    CsFinActiveFiltersModule,
    CsFinAddRemoveColumnsModule,
    MatDatepickerModule
  ]
})
export class BaleModule { }
