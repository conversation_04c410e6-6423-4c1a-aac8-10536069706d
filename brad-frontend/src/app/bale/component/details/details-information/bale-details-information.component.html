<div class="details-information">
    <div *ngIf="isLoading" class="loading">
        <mat-progress-spinner mode="indeterminate" diameter="75" strokeWidth="5"></mat-progress-spinner>
        <span class="label">{{'GENERAL.DETAILS.LOADING' | translate}}</span>
    </div>
    <div *ngIf="!isLoading && !detailedBale" class="detail-failed">
        <mat-icon>report_problem</mat-icon>
        <span class="label">{{'ACCOUNTS.DETAILS.ERRORS.UNABLE_TO_FIND_DETAILS' | translate}}</span>
    </div>

    <main *ngIf="!isLoading && detailedBale">
        <section class="header">
            <button mat-mini-fab disabled>
                <mat-icon>compare</mat-icon>
            </button>
            <span class="item-name">{{detailedBale.account.accountNumber}}</span>
        </section>

        <ul class="description">
            <li>
                <span class="field" data-cy="bale-details-field-id-company">{{'BALE.FIELDS.ID_COMPANY' | translate}}: </span>
                <span class="value" data-cy="bale-account-details-value-id-company">{{detailedBale.idCompany}}</span>
            </li>
            <li>
              <span class="field" data-cy="bale-details-field-entry-no">{{'BALE.FIELDS.ENTRY_NO' | translate}}: </span>
              <span class="value" data-cy="bale-account-details-value-entry-no">{{detailedBale.entryNo}}</span>
            </li>
            <li>
                <span class="field" data-cy="bale-details-field-document-no">{{'BALE.FIELDS.DOCUMENT_NO' | translate}}: </span>
                <span class="value" data-cy="bale-account-details-value-document-no">{{detailedBale.documentNo}}</span>
            </li>
            <li>
                <span class="field" data-cy="bale-details-field-document-type">{{'BALE.FIELDS.DOCUMENT_TYPE' | translate}}: </span>
                <span class="value" data-cy="bale-account-details-value-document-type">{{detailedBale.documentType}}</span>
            </li>
            <li>
                <span class="field" data-cy="bale-details-field-posting-date">{{'BALE.FIELDS.POSTING_DATE' | translate}}: </span>
                <span class="value" data-cy="bale-account-details-value-posting-date">{{detailedBale.postingDate}}</span>
            </li>
            <li>
                <span class="field" data-cy="bale-details-field-account-posting-group">{{'BALE.FIELDS.ACCOUNT_POSTING_GROUP' | translate}}: </span>
                <span class="value" data-cy="bale-account-details-value-account-posting-group">{{detailedBale.accountPostingGroup}}</span>
            </li>
            <li>
                <span class="field" data-cy="bale-details-field-description">{{'BALE.FIELDS.DESCRIPTION' | translate}}: </span>
                <span class="value" data-cy="bale-account-details-value-description">{{detailedBale.description}}</span>
            </li>
            <li>
                <span class="field" data-cy="bale-details-field-source-code">{{'BALE.FIELDS.SOURCE_CODE' | translate}}: </span>
                <span class="value" data-cy="bale-account-details-value-source-code">{{detailedBale.sourceCode}}</span>
            </li>
            <li>
                <span class="field" data-cy="bale-details-field-reason-code">{{'BALE.FIELDS.REASON_CODE' | translate}}: </span>
                <span class="value" data-cy="bale-account-details-value-reason-code">{{detailedBale.reasonCode}}</span>
            </li>
            <li>
                <span class="field" data-cy="bale-details-field-bus-line">{{'BALE.FIELDS.BUS_LINE' | translate}}: </span>
                <span class="value" data-cy="bale-account-details-value-bus-line">{{detailedBale.busLine}}</span>
            </li>
            <li>
                <span class="field" data-cy="bale-details-field-department">{{'BALE.FIELDS.DEPARTMENT' | translate}}: </span>
                <span class="value" data-cy="bale-account-details-value-department">{{detailedBale.department}}</span>
            </li>
            <li>
                <span class="field" data-cy="bale-details-field-amount">{{'BALE.FIELDS.AMOUNT' | translate}}: </span>
                <span class="value" data-cy="bale-account-details-value-amount">{{detailedBale.amount}}</span>
            </li>
            <li>
                <span class="field" data-cy="bale-details-field-remaining-amount">{{'BALE.FIELDS.REMAINING_AMOUNT' | translate}}: </span>
                <span class="value" data-cy="bale-account-details-value-remaining-amount">{{detailedBale.remainingAmount}}</span>
            </li>
            <li>
                <span class="field" data-cy="bale-details-field-currency">{{'BALE.FIELDS.TRANSACTION_CURRENCY' | translate}}: </span>
                <span class="value" data-cy="bale-account-details-value-currency">{{detailedBale.transactionCurrency.code}}</span>
            </li>
            <li>
                <span class="field" data-cy="bale-details-field-amount-lcy">{{'BALE.FIELDS.AMOUNT_LCY' | translate}}: </span>
                <span class="value" data-cy="bale-account-details-value-amount-lcy">{{detailedBale.amountLcy}}</span>
            </li>
            <li>
                <span class="field" data-cy="bale-details-field-bal-account-type">{{'BALE.FIELDS.BALANCE_ACCOUNT_TYPE' | translate}}: </span>
                <span class="value" data-cy="bale-account-details-value-bal-account-type">{{detailedBale.balanceAccountType}}</span>
            </li>
            <li>
                <span class="field" data-cy="bale-details-field-is-open">{{'BALE.FIELDS.IS_OPEN' | translate}}: </span>
                <span class="value" data-cy="bale-account-details-value-is-open">{{detailedBale.isOpen}}</span>
            </li>
            <li>
                <span class="field" data-cy="bale-details-field-reversed">{{'BALE.FIELDS.IS_REVERSED' | translate}}: </span>
                <span class="value" data-cy="bale-account-details-value-reversed">{{detailedBale.isReversed}}</span>
            </li>
            <li>
                <span class="field" data-cy="bale-details-field-posted-by">{{'BALE.FIELDS.POSTED_BY' | translate}}: </span>
                <span class="value" data-cy="bale-account-details-value-posted-by">{{detailedBale.postedBy}}</span>
            </li>
            <li>
                <span class="field" data-cy="bale-details-field-external-document-no">{{'BALE.FIELDS.EXTERNAL_DOCUMENT_NO' | translate}}: </span>
                <span class="value" data-cy="bale-account-details-value-external-document-no">{{detailedBale.externalDocumentNo}}</span>
            </li>
            <li>
              <span class="field" data-cy="bale-details-field-bale-timestamp">{{'BALE.FIELDS.BALE_TIMESTAMP' | translate}}: </span>
              <span class="value" data-cy="bale-account-details-value-bale-timestamp">{{detailedBale.baleTimestamp}}</span>
            </li>
            <li>
              <span class="field" data-cy="bale-details-field-ba-timestamp">{{'BALE.FIELDS.ACCOUNT_TIMESTAMP' | translate}}: </span>
              <span class="value" data-cy="bale-account-details-value-ba-timestamp">{{detailedBale.accountTimestamp}}</span>
            </li>

        </ul>

    </main>

</div>
