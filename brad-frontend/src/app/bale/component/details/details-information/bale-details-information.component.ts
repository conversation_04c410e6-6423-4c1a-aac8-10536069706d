import {Component, Input, OnChanges, OnDestroy, OnInit} from '@angular/core';
import {Subject} from "rxjs";
import {Bale} from "../../../../entities/bale/bale";

@Component({
  selector: 'brad-bale-details-information',
  templateUrl: './bale-details-information.component.html',
  styleUrls: ['./bale-details-information.component.scss']
})
export class BaleDetailsInformationComponent implements OnInit, OnChanges, OnDestroy {

  @Input() detailedBale!: Bale;


  isLoading = false;
  private _onDestroy:Subject<void> = new Subject<void>();

  ngOnInit(): void {
    this.isLoading = true;
  }

  ngOnChanges() {
    if(this.detailedBale){
      this.isLoading = false;
    }
  }

  ngOnDestroy(): void {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

}
