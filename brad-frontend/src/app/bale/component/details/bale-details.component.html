<mat-card class="details-wrapper main">
    <span class="actions">
      <span fxFlex></span>
      <mat-icon id="fullscreen-toggle" (click)="onFullscreenClick()" fxShow [fxShow.xs]="false" [fxShow.sm]="false" data-cy="toggle-details-fullscreen-btn">
        {{isFullscreen ? 'fullscreen_exit' : 'fullscreen'}}
      </mat-icon>
      <mat-icon (click)="onCloseClick()" data-cy="close-details-btn">close</mat-icon>
    </span>
  <mat-tab-group class="details-tab-group" fitInkBarToContent="false">

    <!-- bale details -->
    <mat-tab [label]="'BALE.DETAILS.TABS.LABELS.BALE_DETAILS' | translate">
      <brad-bale-details-information [detailedBale]="bale"></brad-bale-details-information>
    </mat-tab>


  </mat-tab-group>

</mat-card>
