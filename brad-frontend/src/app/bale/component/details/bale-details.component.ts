import {Component, OnDestroy, OnInit} from '@angular/core';
import {Bale} from "../../../entities/bale/bale";
import {Subject} from "rxjs";
import {ActivatedRoute, Params, Router} from "@angular/router";
import {takeUntil} from "rxjs/operators";
import {BaleFacade} from "../../facade/bale.facade";

@Component({
  selector: 'brad-bale-details',
  templateUrl: './bale-details.component.html',
  styleUrls: ['./bale-details.component.scss']
})
export class BaleDetailsComponent implements OnInit, OnDestroy {

  isFullscreen = false;
  baleId!: number;
  bale!: Bale;

  private _onDestroy:Subject<void>= new Subject<void>();

  constructor(
      private baleFacade: BaleFacade,
      private router: Router,
      private route: ActivatedRoute,
  ) {
  }
  ngOnInit(): void {
    this.route.params
        .pipe(takeUntil(this._onDestroy))
        .subscribe((params: Params) => {
          this.baleId = Number(params['baleEntryNo']);
          this.baleFacade.selectedBaleChangeBehaviorSubject.next(this.baleId);
        });

    this.baleFacade.selectedBaleChangeBehaviorSubject
        .pipe(takeUntil(this._onDestroy))
        .subscribe((bale: number) => {
          this.baleId = bale;
          this.loadBaleDetails();
        });
  }

  ngOnDestroy() {
    this._onDestroy.next();
    this._onDestroy.complete();
    this.baleFacade.detailsCloseBehaviorSubject.next(true);
  }

  onFullscreenClick(): void {
    this.isFullscreen = !this.isFullscreen;
    this.baleFacade.fullscreenChangeBehaviorSubject.next(this.isFullscreen);
  }

  onCloseClick(): void {
    this.baleFacade.detailsCloseBehaviorSubject.next(true);
    this.router.navigate(['bale'], {queryParams: this.route.snapshot.queryParams});
  }

  loadBaleDetails(): void {
    this.baleFacade.getById(this.baleId!)
        .pipe(takeUntil(this._onDestroy))
        .subscribe((bale: Bale) => {
          this.bale = bale;
        });

  }

}
