<mat-toolbar id="header-toolbar" fxLayout="row" fxLayoutAlign="start center" fxLayoutGap="8px">
  <button mat-icon-button (click)="sidenav.toggle()" fxHide [fxShow.xs]="true" [fxShow.sm]="true">
    <mat-icon>menu</mat-icon>
  </button>
  <span class="page-title">{{ 'BALE.TITLE' | translate }}</span>
  <span fxFlex></span>
  <brad-bale-header-form></brad-bale-header-form>
  <span fxFlex></span>

</mat-toolbar>
