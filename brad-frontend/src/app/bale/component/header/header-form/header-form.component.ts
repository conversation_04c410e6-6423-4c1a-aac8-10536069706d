import {ChangeDetectorRef, Component, Input, OnDestroy, OnInit, ViewChild, ViewEncapsulation} from '@angular/core';
import {FormControl, FormGroup} from "@angular/forms";
import {BehaviorSubject, Observable, Subject, tap} from "rxjs";
import {CdkConnectedOverlay} from "@angular/cdk/overlay";
import {CsFinApiService, CsFinSidenavService} from "@jumia-cs-fin/common";
import {ActivatedRoute, Router} from "@angular/router";
import * as _ from "lodash";
import {IsReconciledStatusApiService} from "../../../../api/service/is-reconciled-status-api.service";
import {takeUntil} from "rxjs/operators";
import {BaleFilters} from "../../../../entities/bale/bale-filters";
import {BaleFacade} from "../../../facade/bale.facade";
import {Currency} from "../../../../entities/currency/currency";
import {TransactionFilters} from "../../../../entities/transaction/transaction-filters";
import {CurrencyApiService} from "../../../../api/service/currency-api.service";

@Component({
  selector: 'brad-bale-header-form',
  templateUrl: './header-form.component.html',
  styleUrls: ['./header-form.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class BaleHeaderFormComponent implements OnInit, OnDestroy {

  @Input() showFilters!: boolean;

  queryParams = {};
  filterTextFormControl!: FormControl;
  form!: FormGroup;

  idCompanyFormControl!: FormControl;
  accountFormControl!: FormControl;
  entryNoFormControl!: FormControl;
  documentNoFormControl!: FormControl;
  documentTypeFormControl!: FormControl;
  postingDateStartFormControl!: FormControl;
  postingDateEndFormControl!: FormControl;
  accountPostingGroupFormControl!: FormControl;
  descriptionFormControl!: FormControl;
  sourceCodeFormControl!: FormControl;
  reasonCodeFormControl!: FormControl;
  busLineFormControl!: FormControl;
  departmentFormControl!: FormControl;
  directionFormControl!: FormControl;
  directionSearchFormControl = new FormControl();
  amountFormControl!: FormControl;
  remainingAmountFormControl!: FormControl;
  transactionCurrencyFormControl!: FormControl;
  transactionCurrencySearchFormControl = new FormControl();
  amountLcyFormControl!: FormControl;
  balanceAccountTypeFormControl!: FormControl;
  isOpenFormControl!: FormControl;
  isReversedFormControl!: FormControl;
  postedByFormControl!: FormControl;
  externalDocumentNoFormControl!: FormControl;
  baleTimestampFormControl!: FormControl;
  accountTimestampFormControl!: FormControl;

  isReconciledSearchFormControl = new FormControl();
  isReconciledList: String[] = [];
  filteredIsReconciledList: String[] = [];

  directionList:string[] = [];
  filteredDirectionList:string[] = []

  currencyList:Currency[] = [];
  filteredCurrencyList:Currency[] = [];


  isOpen$!: Observable<boolean>;
  isRefreshing = false;
  isInitializing = false;

  private readonly refreshTimeout = 2000;
  private _onDestroy:Subject<void> = new Subject<void>();
  private _isOpen!: BehaviorSubject<boolean>;

  @ViewChild(CdkConnectedOverlay, {static: true})
  private connectedOverlay!: CdkConnectedOverlay;

  constructor(public ref: ChangeDetectorRef,
              public sidenav: CsFinSidenavService,
              private activatedRoute: ActivatedRoute,
              private router: Router,
              private apiService: CsFinApiService,
              private baleFacade: BaleFacade,
              private isReconciledService: IsReconciledStatusApiService,
              private currencyApiService: CurrencyApiService
  ) { }


  async ngOnInit(): Promise<void> {
    this.isInitializing = true;
    this.initializeOverlay();
    this.initFiltersSearch();
    this.subscribeUrlParamsChanges();
    this.subscribeActiveFiltersChange();
    this.subscribeFiltersChange();
  }

  private subscribeUrlParamsChanges(): void {
    this.activatedRoute.queryParams
      .pipe(takeUntil(this._onDestroy))
      .subscribe(params => this.initializeFormData(params));
  }

  private subscribeActiveFiltersChange(): void {
    this.baleFacade.filtersChanged(this.getFormValues());
  }

  private subscribeFiltersChange(): void {
    this.baleFacade.filters$
      .pipe(takeUntil(this._onDestroy))
      .subscribe((filters:BaleFilters) => {
        if(!this.isInitializing) {
          this.updateFormData(filters);
          this.updateMissingUrlFilters(filters);
        }
      });
  }

  ngOnDestroy(): void {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  private initializeOverlay(): void {
    this._isOpen = new BehaviorSubject<boolean>(false);
    this.connectedOverlay.backdropClass = 'cdk-overlay-transparent-backdrop';
    this.connectedOverlay.panelClass = 'filters-overlay-panel';
    this.connectedOverlay.backdropClick
      .pipe(tap(() => this._isOpen.next(false)))
      .subscribe();
    this.isOpen$ = this._isOpen.asObservable();
  }

  private initFiltersSearch(): void {
    this.initCurrencySearch();
    this.initDirectionSearch();
    this.initIsReconciledSearch();
  }


  private initDirectionSearch(): void {
    this.directionSearchFormControl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe((value: String) => {
        const searchTerm = value.trim();
        if (searchTerm) {
          this.filteredDirectionList = this.directionList.filter((direction) => {
            return direction.trim().toLowerCase().includes(searchTerm.toLowerCase());
          })
        } else {
          this.filteredDirectionList = this.directionList;
        }
      });
  }

  private initCurrencySearch(): void {
    this.transactionCurrencySearchFormControl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe((value: String) => {
        const searchTerm = value.trim();
        if (searchTerm) {
          this.filteredCurrencyList = this.currencyList.filter((currency) => {
            return currency.code.trim().toLowerCase().includes(searchTerm.toLowerCase());
          })
        } else {
          this.filteredCurrencyList = this.currencyList;
        }
      });
  }

  private initIsReconciledSearch(): void {
    this.isReconciledSearchFormControl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe((value: string) => {
        const searchTerm = value.trim();
        if (searchTerm) {
          this.filteredIsReconciledList = this.isReconciledList.filter((status) => {
            return status.trim().toLowerCase().includes(searchTerm.toLowerCase());
          })
        } else {
          this.filteredIsReconciledList = this.isReconciledList;
        }
      });
  }

  clearFilters(): void {
    this.form.reset('', {emitEvent: false});
    this.baleFacade.filtersChanged({});
  }

  submit(input?: HTMLInputElement) {
    this.baleFacade.filtersChanged(this.getFormValues());
    const formQueryParams = this.apiService.buildQueryParams(this.getFormValues());
    if (_.isEqual(this.queryParams, formQueryParams)) {
      this.isRefreshing = true;
      this.ref.markForCheck();
      setTimeout(() => {
        this.isRefreshing = false;
        this.ref.markForCheck();
      }, this.refreshTimeout);
    } else {
      input?.blur();
      if (this.router.url.includes('reconcile')) {
        this.router.navigate(['/reconcile'], {queryParams: formQueryParams});
      }
      else {
        this.router.navigate(['/bale'], {queryParams: formQueryParams});
      }
    }
    this.closeOverlay();

  }

  triggerOverlay(): void {
    this._isOpen.next(!this._isOpen.value);
  }

  closeOverlay(): void {
    this._isOpen.next(false);
  }

  private getFormValues(): BaleFilters {
    return this.form.value as BaleFilters;
  }

  private initializeFormData(params:any): void {
    this.queryParams = params;
    if(!this.form) {
      this.form = new FormGroup({});
      this.initializeFormControlsAndFilters(params);
      this.setFormControlsToForm();
    }
  }

  private initializeFormControlsAndFilters(params:any): void {
    const filters: BaleFilters = {};

    this.filterTextFormControl = new FormControl(params.filterText);
    filters.filterText = params.filterText;

    this.idCompanyFormControl = new FormControl(params.idCompany);
    filters.idCompany = params.idCompany;

    this.accountFormControl = new FormControl(params.account);
    filters.accountNumber = params.account;

    this.entryNoFormControl = new FormControl(params.entryNo);
    filters.entryNo = params.entryNo;

    this.documentNoFormControl = new FormControl(params.documentNo);
    filters.documentNo = params.documentNo;

    this.documentTypeFormControl = new FormControl(params.documentType);
    filters.documentType = params.documentType;

    this.postingDateStartFormControl = new FormControl(params.postingDateStart);
    filters.postingDateStart = params.postingDateStart;

    this.postingDateEndFormControl = new FormControl(params.postingDateEnd);
    filters.postingDateEnd = params.postingDateEnd;

    this.accountPostingGroupFormControl = new FormControl(params.accountPostingGroup);
    filters.accountPostingGroup = params.accountPostingGroup;

    this.descriptionFormControl = new FormControl(params.description);
    filters.description = params.description;

    this.sourceCodeFormControl = new FormControl(params.sourceCode);
    filters.sourceCode = params.sourceCode;

    this.reasonCodeFormControl = new FormControl(params.reasonCode);
    filters.reasonCode = params.reasonCode;

    this.busLineFormControl = new FormControl(params.busLine);
    filters.busLine = params.busLine;

    this.departmentFormControl = new FormControl(params.department);
    filters.department = params.department;

    let direction: string[] = params.direction === undefined ? undefined : params.direction.split(',');
    this.directionFormControl = new FormControl(direction);
    filters.direction = direction;

    let transactionCurrency: string[] = params.transactionCurrency === undefined ? [] : params.transactionCurrency.split(',');
    this.transactionCurrencyFormControl = new FormControl(transactionCurrency);
    filters.transactionCurrency = transactionCurrency;

    this.amountFormControl = new FormControl(params.amount);
    filters.amount = params.amount;

    this.remainingAmountFormControl = new FormControl(params.remainingAmount);
    filters.remainingAmount = params.remainingAmount;

    this.transactionCurrencyFormControl = new FormControl(params.transactionCurrency);
    filters.transactionCurrency = params.transactionCurrency;

    this.amountLcyFormControl = new FormControl(params.amountLcy);
    filters.amountLcy = params.amountLcy;

    this.balanceAccountTypeFormControl = new FormControl(params.balanceAccountType);
    filters.balanceAccountType = params.balanceAccountType;

    this.isOpenFormControl = new FormControl(params.isOpen);
    filters.isOpen = params.isOpen;

    this.isReversedFormControl = new FormControl(params.isReversed);
    filters.isReversed = params.isReversed;

    this.postedByFormControl = new FormControl(params.postedBy);
    filters.postedBy = params.postedBy;

    this.externalDocumentNoFormControl = new FormControl(params.externalDocumentNo);
    filters.externalDocumentNo = params.externalDocumentNo;

    this.baleTimestampFormControl = new FormControl(params.baleTimestamp);
    filters.baleTimestamp = params.baleTimestamp;

    this.accountTimestampFormControl = new FormControl(params.accountTimestamp);
    filters.accountTimestamp = params.accountTimestamp;


    filters.orderField = params.orderField;
    filters.orderDirection = params.orderDirection;
    filters.page = params.page;
    filters.size = params.size;

    Promise.all([
      this.applyCurrencyFilter(params, filters),
      this.applyDirectionFilter(params, filters)
    ]).then(() => {
      this.baleFacade.filtersChanged(filters);
      this.isInitializing = false;
    });

  }

  private applyDirectionFilter(params: any, filters: TransactionFilters): Promise<void> {
    return new Promise<void>(async (resolve) => {

      if(!params.direction) {
        resolve();
        return;
      }

      await this.loadDirection();

      this.directionFormControl.setValue(filters.direction, {emitEvent: false});
      resolve();
    });
  }


  private applyCurrencyFilter(params: any, filters: BaleFilters): Promise<void> {
    return new Promise<void>(async (resolve) => {

      if(!params.transactionCurrency) {
        resolve();
        return;
      }

      await this.loadCurrencies();
      this.transactionCurrencyFormControl.setValue(filters.transactionCurrency, {emitEvent: false});
      resolve();
    });
  }


  loadDirection(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if(this.directionList.length <= 0) {
        this.baleFacade.getDirections()
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (directions: string[]) => {
              this.directionList = directions;
              this.filteredDirectionList = this.directionList;
              resolve();
            }, error: (error) => reject(error)
          });
      } else {
        resolve();
      }
    });
  }

  loadCurrencies(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if(this.currencyList.length <= 0) {
        this.currencyApiService.getAll()
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (currencies: Currency[]) => {
              this.currencyList = currencies;
              this.filteredCurrencyList = this.currencyList;
              resolve();
            }, error: (error) => reject(error)
          });
      } else {
        resolve();
      }
    });
  }

  loadIsReconciled(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if(this.isReconciledList.length <= 0) {
        this.isReconciledService.getAll()
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (isReconciledStatuses: String[]) => {
              this.isReconciledList = isReconciledStatuses;
              this.filteredIsReconciledList = isReconciledStatuses;
              resolve();
            }, error: (error) => reject(error)
          });
      } else {
        resolve();
      }
    });
  }


  private updateFormData (params:BaleFilters): void {
    this.filterTextFormControl.setValue(params.filterText, {emitEvent: false});
    this.idCompanyFormControl.setValue(params.idCompany, {emitEvent: false});
    this.accountFormControl.setValue(params.accountNumber, {emitEvent: false});
    this.entryNoFormControl.setValue(params.entryNo, {emitEvent: false});
    this.documentNoFormControl.setValue(params.documentNo, {emitEvent: false});
    this.documentTypeFormControl.setValue(params.documentType, {emitEvent: false});
    this.postingDateStartFormControl.setValue(params.postingDateStart, {emitEvent: false});
    this.postingDateEndFormControl.setValue(params.postingDateEnd, {emitEvent: false});
    this.accountPostingGroupFormControl.setValue(params.accountPostingGroup, {emitEvent: false});
    this.descriptionFormControl.setValue(params.description, {emitEvent: false});
    this.sourceCodeFormControl.setValue(params.sourceCode, {emitEvent: false});
    this.reasonCodeFormControl.setValue(params.reasonCode, {emitEvent: false});
    this.busLineFormControl.setValue(params.busLine, {emitEvent: false});
    this.departmentFormControl.setValue(params.department, {emitEvent: false});
    this.directionFormControl.setValue(params.direction, {emitEvent: false});
    this.amountFormControl.setValue(params.amount, {emitEvent: false});
    this.remainingAmountFormControl.setValue(params.remainingAmount, {emitEvent: false});
    this.transactionCurrencyFormControl.setValue(params.transactionCurrency, {emitEvent: false});
    this.amountLcyFormControl.setValue(params.amountLcy, {emitEvent: false});
    this.balanceAccountTypeFormControl.setValue(params.balanceAccountType, {emitEvent: false});
    this.isOpenFormControl.setValue(params.isOpen, {emitEvent: false});
    this.isReversedFormControl.setValue(params.isReversed, {emitEvent: false});
    this.postedByFormControl.setValue(params.postedBy, {emitEvent: false});
    this.externalDocumentNoFormControl.setValue(params.externalDocumentNo, {emitEvent: false});
    this.baleTimestampFormControl.setValue(params.baleTimestamp, {emitEvent: false});
    this.accountTimestampFormControl.setValue(params.accountTimestamp, {emitEvent: false});

  }

  private updateMissingUrlFilters(filters: BaleFilters): void {
    const formQueryParams = this.apiService.buildQueryParams(filters);
    if (!_.isEqual(this.queryParams, formQueryParams)) {
      if (this.router.url.includes('reconcile')) {
        this.router.navigate(['reconcile'], {queryParams: formQueryParams});
      }
      else {
        this.router.navigate(['bale'], {queryParams: formQueryParams});
      }

    }
  }

  private setFormControlsToForm(): void {
    this.form.addControl(this.baleFacade.filterTextKey, this.filterTextFormControl);
    this.form.addControl(this.baleFacade.idCompanyKey, this.idCompanyFormControl);
    this.form.addControl(this.baleFacade.accountKey, this.accountFormControl);
    this.form.addControl(this.baleFacade.entryNoKey, this.entryNoFormControl);
    this.form.addControl(this.baleFacade.documentNoKey, this.documentNoFormControl);
    this.form.addControl(this.baleFacade.documentTypeKey, this.documentTypeFormControl);
    this.form.addControl(this.baleFacade.postingDateStartKey, this.postingDateStartFormControl);
    this.form.addControl(this.baleFacade.postingDateEndKey, this.postingDateEndFormControl);
    this.form.addControl(this.baleFacade.accountPostingGroupKey, this.accountPostingGroupFormControl);
    this.form.addControl(this.baleFacade.descriptionKey, this.descriptionFormControl);
    this.form.addControl(this.baleFacade.sourceCodeKey, this.sourceCodeFormControl);
    this.form.addControl(this.baleFacade.reasonCodeKey, this.reasonCodeFormControl);
    this.form.addControl(this.baleFacade.busLineKey, this.busLineFormControl);
    this.form.addControl(this.baleFacade.departmentKey, this.departmentFormControl);
    this.form.addControl(this.baleFacade.directionKey, this.directionFormControl);
    this.form.addControl(this.baleFacade.amountKey, this.amountFormControl);
    this.form.addControl(this.baleFacade.remainingAmountKey, this.remainingAmountFormControl);
    this.form.addControl(this.baleFacade.transactionCurrencyKey, this.transactionCurrencyFormControl);
    this.form.addControl(this.baleFacade.amountLcyKey, this.amountLcyFormControl);
    this.form.addControl(this.baleFacade.balanceAccountTypeKey, this.balanceAccountTypeFormControl);
    this.form.addControl(this.baleFacade.isOpenKey, this.isOpenFormControl);
    this.form.addControl(this.baleFacade.isReversedKey, this.isReversedFormControl);
    this.form.addControl(this.baleFacade.postedByKey, this.postedByFormControl);
    this.form.addControl(this.baleFacade.externalDocumentNoKey, this.externalDocumentNoFormControl);
    this.form.addControl(this.baleFacade.baleTimestampKey, this.baleTimestampFormControl);
    this.form.addControl(this.baleFacade.accountTimestampKey, this.accountTimestampFormControl);
  }

  noFiltersSelected() {
    return !!this.form.errors || this.isRefreshing;
  }

  compareIdFn(o1: any, o2: any): boolean {
    return o1 && o2 && o1.id === o2.id;
  }

}
