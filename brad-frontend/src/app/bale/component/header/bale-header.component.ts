import {Component, <PERSON><PERSON><PERSON>roy, OnInit, ViewEncapsulation} from '@angular/core';
import {CsFinSidenavService} from "@jumia-cs-fin/common";

@Component({
  selector: 'brad-bale-header',
  templateUrl: './bale-header.component.html',
  styleUrls: ['./bale-header.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class BaleHeaderComponent implements OnInit, OnDestroy {

  constructor(public sidenav: CsFinSidenavService) {
  }

  ngOnDestroy(): void {
  }

  ngOnInit(): void {
  }



}
