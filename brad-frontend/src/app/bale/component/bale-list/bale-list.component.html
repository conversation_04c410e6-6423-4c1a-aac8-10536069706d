<main class="container" cdkDropListGroup>
  <section class="filters" *ngIf="dataSource">
    <cs-fin-active-filters [chipsMap]="activeFilterChips"
                           (onRemove)="onActiveFilterRemoveClick($event)">
    </cs-fin-active-filters>
    <span fxFlex></span>
    <button mat-stroked-button id="change-columns-btn" color="primary" fxShow [fxHide.xs]="true" [fxHide.sm]="true"
            [disabled]="filterButtonActive" cdkOverlayOrigin #trigger="cdkOverlayOrigin"
            (click)="triggerOverlay(trigger)">
      <mat-icon>table_rows</mat-icon>
    </button>
  </section>

  <section class="table-detail responsive-table"
           *ngIf="!isLoading"
           [class.detail-opened]="baleDetailsOpened"
           [class.fullscreen]="isBaleDetailsInFullscreen">

    <div class="table-container" [class.detail-opened]="baleDetailsOpened"
         [class.fullscreen]="isBaleDetailsInFullscreen">

      <mat-progress-bar mode="indeterminate" *ngIf="isLoading"></mat-progress-bar>

      <table mat-table class="cs-fin-table" matSort [dataSource]="dataSource"  [class.loading]="isLoading"
             (matSortChange)="onSortChange($event)" cdkDropList cdkDropListSortingDisabled>

        <ng-container matColumnDef="idCompany">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'BALE.FIELDS.ID_COMPANY' | translate }}</th>
          <td mat-cell *matCellDef="let bale"> {{bale.idCompany}} </td>
        </ng-container>

        <ng-container matColumnDef="account">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'BALE.FIELDS.ACCOUNT' | translate }}</th>
          <td mat-cell *matCellDef="let bale"> {{bale.account.accountNumber}} </td>
        </ng-container>

        <ng-container matColumnDef="entryNo">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'BALE.FIELDS.ENTRY_NO' | translate }}</th>
          <td mat-cell *matCellDef="let bale"> {{bale.entryNo}} </td>
        </ng-container>


        <ng-container matColumnDef="documentNo">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'BALE.FIELDS.DOCUMENT_NO' | translate }}</th>
          <td mat-cell *matCellDef="let bale"> {{bale.documentNo}} </td>
        </ng-container>

        <ng-container matColumnDef="documentType">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'BALE.FIELDS.DOCUMENT_TYPE' | translate }}</th>
          <td mat-cell *matCellDef="let bale"> {{bale.documentType}} </td>
        </ng-container>

        <ng-container matColumnDef="postingDate">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'BALE.FIELDS.POSTING_DATE' | translate }}</th>
          <td mat-cell *matCellDef="let bale"> {{bale.postingDate}} </td>
        </ng-container>

        <ng-container matColumnDef="accountPostingGroup">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'BALE.FIELDS.ACCOUNT_POSTING_GROUP' | translate }}</th>
          <td mat-cell *matCellDef="let bale"> {{bale.accountPostingGroup}} </td>
        </ng-container>

        <ng-container matColumnDef="description">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'BALE.FIELDS.DESCRIPTION' | translate }}</th>
          <td mat-cell *matCellDef="let bale"> {{bale.description}} </td>
        </ng-container>

        <ng-container matColumnDef="sourceCode">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'BALE.FIELDS.SOURCE_CODE' | translate }}</th>
          <td mat-cell *matCellDef="let bale"> {{bale.sourceCode}} </td>
        </ng-container>

        <ng-container matColumnDef="reasonCode">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'BALE.FIELDS.REASON_CODE' | translate }}</th>
          <td mat-cell *matCellDef="let bale"> {{bale.reasonCode}} </td>
        </ng-container>

        <ng-container matColumnDef="busLine">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'BALE.FIELDS.BUS_LINE' | translate }}</th>
          <td mat-cell *matCellDef="let bale"> {{bale.busLine}} </td>
        </ng-container>

        <ng-container matColumnDef="department">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'BALE.FIELDS.DEPARTMENT' | translate }}</th>
          <td mat-cell *matCellDef="let bale"> {{bale.department}} </td>
        </ng-container>

        <ng-container matColumnDef="amount">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'BALE.FIELDS.AMOUNT' | translate }}</th>
          <td mat-cell *matCellDef="let bale"> {{bale.amount}} </td>
        </ng-container>

        <ng-container matColumnDef="remainingAmount">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'BALE.FIELDS.REMAINING_AMOUNT' | translate }}</th>
          <td mat-cell *matCellDef="let bale"> {{bale.remainingAmount}} </td>
        </ng-container>

        <ng-container matColumnDef="currency">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'BALE.FIELDS.TRANSACTION_CURRENCY' | translate }}</th>
          <td mat-cell *matCellDef="let bale"> {{bale.transactionCurrency.code}} </td>
        </ng-container>

        <ng-container matColumnDef="amountLcy">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'BALE.FIELDS.AMOUNT_LCY' | translate }}</th>
          <td mat-cell *matCellDef="let bale"> {{bale.amountLcy}} </td>
        </ng-container>

        <ng-container matColumnDef="balanceAccountType">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'BALE.FIELDS.BALANCE_ACCOUNT_TYPE' | translate }}</th>
          <td mat-cell *matCellDef="let bale"> {{bale.balanceAccountType}} </td>
        </ng-container>

        <ng-container matColumnDef="isOpen">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'BALE.FIELDS.IS_OPEN' | translate }}</th>
          <td mat-cell *matCellDef="let bale"> {{bale.isOpen}} </td>
        </ng-container>

        <ng-container matColumnDef="isReversed">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'BALE.FIELDS.IS_REVERSED' | translate }}</th>
          <td mat-cell *matCellDef="let bale"> {{bale.isReversed}} </td>
        </ng-container>

        <ng-container matColumnDef="postedBy">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'BALE.FIELDS.POSTED_BY' | translate }}</th>
          <td mat-cell *matCellDef="let bale"> {{bale.postedBy}} </td>
        </ng-container>

        <ng-container matColumnDef="externalDocumentNo">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'BALE.FIELDS.EXTERNAL_DOCUMENT_NO' | translate }}</th>
          <td mat-cell *matCellDef="let bale"> {{bale.externalDocumentNo}} </td>
        </ng-container>

        <ng-container matColumnDef="baleTimestamp">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'BALE.FIELDS.BALE_TIMESTAMP' | translate }}</th>
          <td mat-cell *matCellDef="let bale"> {{bale.baleTimestamp}} </td>
        </ng-container>

        <ng-container matColumnDef="baTimestamp">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'BALE.FIELDS.ACCOUNT_TIMESTAMP' | translate }}</th>
          <td mat-cell *matCellDef="let bale"> {{bale.accountTimestamp}} </td>
        </ng-container>


        <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"
            (click)="onOpenItemDetailsClick(row.id)"
            [class.item-opened]="isItemDetailsOpened(row.id)"
            class="with-detail" style="cursor: pointer;"></tr>
      </table>


      <mat-paginator [pageSizeOptions]="pagination.pageSizeOptions"
                     [pageSize]="pagination.pageSize"
                     [length]="pagination.totalItems"
                     [pageIndex]="pagination.pageIndex"
                     (page)="onPageChange($event)"
                     showFirstLastButtons>
      </mat-paginator>
      <span id="empty-table" *ngIf="!isLoading && !dataSource.data.length">
        {{'GENERAL.TABLE.NO_RESULTS_FOUND' | translate}}
      </span>
    </div>
    <div class="{{isBaleDetailsInFullscreen ? 'details-full-screen' : 'details-container'}}"
         *ngIf="baleDetailsOpened">
      <router-outlet></router-outlet>
    </div>
  </section>
  <cs-fin-add-remove-columns [triggerOrigin]="triggerOrigin"
                             [isOverlayOpen$]="isOverlayOpen$"
                             [menu]="MENU">
  </cs-fin-add-remove-columns>
</main>

