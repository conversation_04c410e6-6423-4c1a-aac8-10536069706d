import {ChangeDete<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy, OnInit, ViewChild} from '@angular/core';
import {MatSort, SortDirection} from "@angular/material/sort";
import {paginationConsts} from "../../../shared/constants/core.constants";
import {
  activeFiltersSeparator,
  CsFinActiveFilterChip,
  CsFinAddRemoveColumns,
  CsFinAddRemoveColumnsFacade,
  CsFinColumnDetails,
  CsFinLastUsedColumns,
  CsFinPagination
} from "@jumia-cs-fin/common";
import {Observable, Subject} from "rxjs";
import {ActivatedRoute, Router} from "@angular/router";
import {BaleFilters} from "../../../entities/bale/bale-filters";
import {finalize, takeUntil} from "rxjs/operators";
import {PageResponse} from "../../../entities/page-response";
import {MatTableDataSource} from "@angular/material/table";
import {<PERSON><PERSON>} from "../../../entities/bale/bale";
import {SortFilters} from "../../../entities/SortFilters";
import * as _ from 'lodash';
import {MatPaginator} from "@angular/material/paginator";
import {MenusConsts} from "../../../shared/constants/menus.constants";
import {CdkOverlayOrigin} from "@angular/cdk/overlay";
import {MediaMatcher} from "@angular/cdk/layout";
import {BaleFacade} from "../../facade/bale.facade";
import {authParams} from "../../../auth/constants/auth.constants";
import {HttpErrorResponse} from "@angular/common/http";
import {NotificationService} from "../../../api/service/notification.service";

@Component({
  selector: 'brad-bale-list',
  templateUrl: './bale-list.component.html',
  styleUrls: ['./bale-list.component.scss']
})
export class BaleListComponent implements OnInit, OnDestroy {

  @ViewChild(MatPaginator, {static: true}) paginator!: MatPaginator;
  @ViewChild(MatSort, {static: true}) sort!: MatSort;

  readonly MENU = MenusConsts.bale;
  auth = authParams;

  isBaleDetailsInFullscreen = false;
  lastOpenedBaleDetailsId!: number | null;


  private displayedColumnsOnDetailsMode = ['entryNo', 'idCompany', 'documentNo', 'account',  'amount'];
  private displayedColumnsOnFullscreenMode = ['entryNo', 'idCompany', 'account', 'amount'];

  isLoading = true;
  dataSource: MatTableDataSource<Bale> = new MatTableDataSource<Bale>([]);
  displayedColumns: string[] = [];
  mobileQuery: MediaQueryList;
  pagination:CsFinPagination = {
    pageSizeOptions: paginationConsts.pageSizeOptions,
    pageSize: paginationConsts.defaultPageSize,
    pageIndex: 0,
    totalItems: 0
  };
  activeFilterChips!: Map<string, CsFinActiveFilterChip>;

  baleDetailsOpened = false;

  isOverlayOpen$!: Observable<boolean>;
  triggerOrigin!: CdkOverlayOrigin;
  localStorageDetailSelectedTabVariableName = 'baleDetailsTabGroupSelectedIndex';
  private _mobileQueryListener: () => void;
  private _onDestroy:Subject<void> = new Subject<void>();

  private filters: BaleFilters = {
    page: 1,
    size: paginationConsts.defaultPageSize
  };
  private lastDisplayedColumns!: string[];

  filterButtonActive = false;

  constructor(
      public ref: ChangeDetectorRef,
      public media: MediaMatcher,
      private route: ActivatedRoute,
      private router: Router,
      private baleFacade: BaleFacade,
      private addRemoveColumnsFacade: CsFinAddRemoveColumnsFacade,
      private notificationService: NotificationService
  ) {
    this.mobileQuery = media.matchMedia('(max-width: 960px)');
    this._mobileQueryListener = () => ref.detectChanges();
    this.mobileQuery?.addEventListener('change', this._mobileQueryListener);

    CsFinLastUsedColumns.getInstance().initColumns(this.MENU, this.getColumnDetails());
  }

  ngOnInit(): void {
    this.isLoading = true;
    this.subscribeFiltersChange();
    this.subscribeActiveFilterChipsChange();
    this.subscribeDisplayedColumnsChange();
    this.subscribeFullscreenChange();
    this.subscribeSelectedBaleChange();
    this.subscribeDetailsCloseChange();

    this.displayedColumns = this.displayedColumnsOnFullscreenMode;
  }

  ngOnDestroy(): void {
    this.baleFacade.filtersChanged({});
    this.mobileQuery?.removeEventListener('change', this._mobileQueryListener);
    this._onDestroy.next();
    this._onDestroy.complete();
    this.addRemoveColumnsFacade.isOverlayOpen.next(false);
    this.baleFacade.selectedBaleChangeBehaviorSubject.next(-1);
  }

  private subscribeFiltersChange(): void {
    this.baleFacade.filters$
        .pipe(takeUntil(this._onDestroy))
        .subscribe(async (filters: BaleFilters) => {
          if (filters && this.isInBaleScreen()) {
            this.closeDetailsSliderIfOpened();
            this.filters = filters;
            this.loadBales();
          }
        });
  }

  private closeDetailsSliderIfOpened(): void {
    if (this.lastOpenedBaleDetailsId) {
      this.closeBaleDetails();
    }
  }

  private isInBaleScreen(): boolean {
    return window.location.href.includes('bale');
  }

  private subscribeActiveFilterChipsChange(): void {
    this.baleFacade.activeFiltersChips$
        .pipe(takeUntil(this._onDestroy))
        .subscribe((activeFilterChips: Map<string, CsFinActiveFilterChip>) => {
          this.activeFilterChips = activeFilterChips;
        });
  }


  private subscribeDisplayedColumnsChange(): void {
    this.addRemoveColumnsFacade.fetchDisplayedColumns
        .pipe(takeUntil(this._onDestroy))
        .subscribe((columns: CsFinAddRemoveColumns) => {
          setTimeout(() => {
                if (columns) {
                  this.lastDisplayedColumns = this.displayedColumns;
                  this.displayedColumns = columns.displayedColumns ? columns.displayedColumns : [];
                }
              }
              , 0);
        });
  }

  private subscribeFullscreenChange(): void {
    this.baleFacade.fullscreenChangeBehaviorSubject
        .pipe(takeUntil(this._onDestroy))
        .subscribe((isFullscreen: boolean) => {
          this.handleFullscreenChange(isFullscreen);
        });
  }

  private subscribeSelectedBaleChange(): void {
    this.baleFacade.selectedBaleChangeBehaviorSubject
        .pipe(takeUntil(this._onDestroy))
        .subscribe((baleId: number) => {
          if(baleId>0 && this.lastOpenedBaleDetailsId != baleId){
            setTimeout(() => {
              this.onOpenItemDetailsClick(baleId);
              this.ref.markForCheck();
            }, 0);
          }
        });
  }

  private subscribeDetailsCloseChange(): void {
    this.baleFacade.detailsCloseBehaviorSubject
        .pipe(takeUntil(this._onDestroy))
        .subscribe((isClosed: boolean) => {
          if(isClosed){
            this.closeBaleDetails();
          }
        });
  }

  getColumnDetails(): CsFinColumnDetails[] {
    return [
      {position: 1, name: 'ID Company', code: 'idCompany', isActive: true, isRemovable: true, isDefault: true},
      {position: 2, name: 'Account', code: 'account', isActive: true, isRemovable: true, isDefault: true},
      {position: 3, name: 'Entry No', code: 'entryNo', isActive: true, isRemovable: false, isDefault: true},
      {position: 4, name: 'DocumentNo', code: 'documentNo', isActive: true, isRemovable: true, isDefault: true},
      {position: 5, name: 'Document Type', code: 'documentType', isActive: false, isRemovable: true, isDefault: false},
      {position: 6, name: 'Posting Date', code: 'postingDate', isActive: false, isRemovable: true, isDefault: false},
      {position: 7, name: 'Account Posting Group', code: 'accountPostingGroup', isActive: false, isRemovable: true, isDefault: false},
      {position: 8, name: 'Description', code: 'description', isActive: false, isRemovable: true, isDefault: false},
      {position: 9, name: 'Source Code', code: 'sourceCode', isActive: false, isRemovable: true, isDefault: false},
      {position: 10, name: 'Reason Code', code: 'reasonCode', isActive: false, isRemovable: true, isDefault: false},
      {position: 11, name: 'Bus Line', code: 'busLine', isActive: false, isRemovable: true, isDefault: false},
      {position: 12, name: 'Department', code: 'department', isActive: false, isRemovable: true, isDefault: false},
      {position: 13, name: 'Amount', code: 'amount', isActive: true, isRemovable: true, isDefault: true},
      {position: 14, name: 'Remaining Amount', code: 'remainingAmount', isActive: true, isRemovable: true, isDefault: true},
      {position: 15, name: 'Currency', code: 'currency', isActive: true, isRemovable: true, isDefault: true},
      {position: 16, name: 'Amount Lcy', code: 'amountLcy', isActive: false, isRemovable: true, isDefault: false},
      {position: 18, name: 'Bal Account Type', code: 'balanceAccountType', isActive: false, isRemovable: true, isDefault: false},
      {position: 19, name: 'Is Open', code: 'isOpen', isActive: false, isRemovable: true, isDefault: false},
      {position: 20, name: 'Is Reversed', code: 'isReversed', isActive: false, isRemovable: true, isDefault: false},
      {position: 21, name: 'Posted By', code: 'postedBy', isActive: false, isRemovable: true, isDefault: false},
      {position: 22, name: 'External DocumentNo', code: 'externalDocumentNo', isActive: false, isRemovable: true, isDefault: false},
      {position: 23, name: 'Bale Timestamp', code: 'baleTimestamp', isActive: false, isRemovable: true, isDefault: false},
      {position: 24, name: 'Account Timestamp', code: 'baTimestamp', isActive: false, isRemovable: true, isDefault: false},
    ];
  }


  onActiveFilterRemoveClick(removedChipKey: string): void {
    const filtersToRemove = removedChipKey.split(activeFiltersSeparator);
    if (filtersToRemove) {
      filtersToRemove.forEach((filterKey: string) => {
        // @ts-ignore
        delete this.filters[filterKey];
      })
    }
    this.baleFacade.filtersChanged(this.filters);
  }

  loadBales() {
    this.baleFacade.getAll(this.filters)
      .pipe(takeUntil(this._onDestroy))
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: (result: PageResponse<Bale>) => {
          this.dataSource = new MatTableDataSource<Bale>(result.results);
          this.pagination.totalItems = result.total;
          this.pagination.pageSize = result.size;
          this.pagination.pageIndex = result.page - 1;
          this.setSort();
        },
        error: (error:HttpErrorResponse) => {
          this.notificationService.errorWithResponse(error);
        }
      });

  }

  triggerOverlay(trigger: CdkOverlayOrigin): void {
    this.triggerOrigin = trigger;
    this.addRemoveColumnsFacade.isOverlayOpen.next(true);
  }

  private handleFullscreenChange(isFullscreen: boolean): void {
    this.isBaleDetailsInFullscreen = isFullscreen;
    if(this.isBaleDetailsInFullscreen){
      this.displayedColumns = this.displayedColumnsOnFullscreenMode
    } else {
      this.displayedColumns = this.displayedColumnsOnDetailsMode;
    }
    this.ref.markForCheck();
  }

  private setSort(): void {
    if (!this.sort) {
      return;
    }

    const previousSort = {
      active: this.sort.active,
      direction: this.sort.direction
    };
    if (this.filters.orderField) {
      this.sort.active = this.decodeSortField(this.filters.orderField);
    }
    if (this.filters.orderDirection) {
      this.sort.direction = this.filters.orderDirection.toLowerCase() as SortDirection;
    }

    const sort = {
      active: this.sort.active,
      direction: this.sort.direction
    };
    if (!_.isEqual(previousSort, sort)) {
      this.sort.sortChange.emit(this.sort);
    }
  }
  encodeSortField(field: string): string {
    return this.baleFacade.encodeSortField(field);
  }

  decodeSortField(field: string): string {
    return this.baleFacade.decodeSortField(field);
  }

  onPageChange(event: any) {
    if (event) {
      this.filters.page = event.pageIndex + 1;
      this.filters.size = event.pageSize;
    }
    this.baleFacade.filtersChanged(this.filters);
  }

  onSortChange(event: any): void {
    const sortFiltersBefore = {
      orderDirection: this.filters.orderDirection,
      orderField: this.filters.orderField
    };

    this.filters.orderDirection = event.direction?.toUpperCase();
    this.filters.orderField = this.encodeSortField(event.active);

    if(!_.isEqual(sortFiltersBefore, this.filters as SortFilters)){
      this.baleFacade.filtersChanged(this.filters);
    }

  }

  onOpenItemDetailsClick(baleId:number):void {
    if(this.baleDetailsOpened){
      this.resetSelectedDetailsTab();
    }

    if(!this.baleDetailsOpened){
      this.baleDetailsOpened = true;
      this.lastDisplayedColumns = this.displayedColumns;
      this.displayedColumns = this.displayedColumnsOnDetailsMode;

    } else if(this.lastOpenedBaleDetailsId === baleId){
      this.router.navigate(['.'], {queryParamsHandling: 'preserve', relativeTo: this.route});
      this.closeBaleDetails();
      return;
    }
    this.lastOpenedBaleDetailsId = baleId;
    this.router.navigate([`./${baleId}`], {queryParamsHandling: 'preserve', relativeTo: this.route});
  }

  resetSelectedDetailsTab(): void {
    const detailTabGroupSelectedIndex = localStorage.getItem(this.localStorageDetailSelectedTabVariableName);
    if(detailTabGroupSelectedIndex === '1')
      localStorage.setItem(this.localStorageDetailSelectedTabVariableName, '0');
  }

  closeBaleDetails(): void {
    this.baleDetailsOpened = false;
    this.displayedColumns = this.lastDisplayedColumns;
    this.lastOpenedBaleDetailsId = null;
    this.isBaleDetailsInFullscreen = false;
  }

  isItemDetailsOpened(baleId: number): boolean {
    return baleId === this.lastOpenedBaleDetailsId;
  }


}
