import {Injectable} from "@angular/core";
import {BaleApiService} from "../../api/service/bale-api.service";
import {BehaviorSubject, Observable, of} from "rxjs";
import {getPropertyKey} from "../../shared/service/ts-utils.service";
import {BaleFilters} from "../../entities/bale/bale-filters";
import {activeFiltersSeparator, CsFinActiveFilterChip} from "@jumia-cs-fin/common";
import {PageResponse} from "../../entities/page-response";
import {Bale} from "../../entities/bale/bale";
import {CsFinGroup} from "../../entities/cs-fin-group";
import {CsFinFilterList, CsFinGroupFacade} from "../../entities/cs-fin-table-group";
import {CsFinGroupedEntityInfo} from "../../entities/cs-fin-grouped-entity";
import {TransactionsApiService} from "../../api/service/transactions-api.service";

@Injectable({providedIn: 'root'})
export class BaleFacade implements CsFinGroupFacade<Bale, BaleFilters>{
  constructor(
    private baleApiService: BaleApiService,
    public transactionsApiService: TransactionsApiService
  ) {}


  public fullscreenChangeBehaviorSubject = new BehaviorSubject<boolean>(false);
  public detailsCloseBehaviorSubject = new BehaviorSubject<boolean>(false);
  public selectedBaleChangeBehaviorSubject = new BehaviorSubject<number>(-1);
  public hideUnwantedReconciliationFilters = new BehaviorSubject<boolean>(false);


  readonly filterTextKey = getPropertyKey<BaleFilters>(p => p.filterText);
  readonly idCompanyKey = getPropertyKey<BaleFilters>(p => p.idCompany);
  readonly accountKey = getPropertyKey<BaleFilters>(p => p.accountNumber);
  readonly entryNoKey = getPropertyKey<BaleFilters>(p => p.entryNo);
  readonly documentNoKey = getPropertyKey<BaleFilters>(p => p.documentNo);
  readonly documentTypeKey = getPropertyKey<BaleFilters>(p => p.documentType);
  readonly postingDateStartKey = getPropertyKey<BaleFilters>(p => p.postingDateStart);
  readonly postingDateEndKey = getPropertyKey<BaleFilters>(p => p.postingDateEnd);
  readonly accountPostingGroupKey = getPropertyKey<BaleFilters>(p => p.accountPostingGroup);
  readonly descriptionKey = getPropertyKey<BaleFilters>(p => p.description);
  readonly sourceCodeKey = getPropertyKey<BaleFilters>(p => p.sourceCode);
  readonly reasonCodeKey = getPropertyKey<BaleFilters>(p => p.reasonCode);
  readonly busLineKey = getPropertyKey<BaleFilters>(p => p.busLine);
  readonly departmentKey = getPropertyKey<BaleFilters>(p => p.department);
  readonly directionKey = getPropertyKey<BaleFilters>(p => p.direction);
  readonly amountKey = getPropertyKey<BaleFilters>(p => p.amount);
  readonly remainingAmountKey = getPropertyKey<BaleFilters>(p => p.remainingAmount);
  readonly transactionCurrencyKey = getPropertyKey<BaleFilters>(p => p.transactionCurrency);
  readonly amountLcyKey = getPropertyKey<BaleFilters>(p => p.amountLcy);
  readonly balanceAccountTypeKey = getPropertyKey<BaleFilters>(p => p.balanceAccountType);
  readonly isOpenKey = getPropertyKey<BaleFilters>(p => p.isOpen);
  readonly isReversedKey = getPropertyKey<BaleFilters>(p => p.isReversed);
  readonly postedByKey = getPropertyKey<BaleFilters>(p => p.postedBy);
  readonly externalDocumentNoKey = getPropertyKey<BaleFilters>(p => p.externalDocumentNo);
  readonly baleTimestampKey = getPropertyKey<BaleFilters>(p => p.baleTimestamp);
  readonly accountTimestampKey = getPropertyKey<BaleFilters>(p => p.accountTimestamp);

  readonly reconciliationIdKey = getPropertyKey<BaleFilters>(p => p.reconciliationId);
  readonly reconciliationCreatorKey = getPropertyKey<BaleFilters>(p => p.reconciliationCreator);
  readonly reconciliationCreationDateStartKey = getPropertyKey<BaleFilters>(p => p.reconciliationCreationDateStart);
  readonly reconciliationCreationDateEndKey = getPropertyKey<BaleFilters>(p => p.reconciliationCreationDateEnd);
  readonly reconciliationReviewerKey = getPropertyKey<BaleFilters>(p => p.reconciliationReviewer);
  readonly reconciliationReviewDateStartKey = getPropertyKey<BaleFilters>(p => p.reconciliationReviewDateStart);
  readonly reconciliationReviewDateEndKey = getPropertyKey<BaleFilters>(p => p.reconciliationReviewDateEnd);
  readonly reconciliationStatusKey = getPropertyKey<BaleFilters>(p => p.reconciliationStatus);



  private readonly activeFiltersConfigMap = new Map<string, any>([
    [
      this.filterTextKey,
      (filterText: string) => {
        return {labelKey: 'GENERAL.FIELDS.FILTER_TEXT', displayText: filterText};
      }
    ],
    [
      this.idCompanyKey,
      (idCompany: string) => {
        return {labelKey: 'BALE.FIELDS.ID_COMPANY', displayText: idCompany};
      }
    ],
    [
      this.accountKey,
      (account: string) => {
        return {labelKey: 'BALE.FIELDS.ACCOUNT', displayText: account};
      }
    ],
    [
      this.entryNoKey,
      (entryNo: number) => {
        return {labelKey: 'BALE.FIELDS.ENTRY_NO', displayText: entryNo.toString()};
      }
    ],
    [
      this.documentNoKey,
      (documentNo: string) => {
        return {labelKey: 'BALE.FIELDS.DOCUMENT_NO', displayText: documentNo};
      }
    ],
    [
      this.documentTypeKey,
      (documentType: string) => {
        return {labelKey: 'BALE.FIELDS.DOCUMENT_TYPE', displayText: documentType};
      }
    ],
    [
      this.postingDateStartKey,
      (postingDate: string) => {
        return {labelKey: 'BALE.FIELDS.POSTING_DATE', displayText: postingDate};
      }
    ],
    [
      this.accountPostingGroupKey,
      (accountPostingGroup: string) => {
        return {labelKey: 'BALE.FIELDS.ACCOUNT_POSTING_GROUP', displayText: accountPostingGroup};
      }
    ],
    [
      this.descriptionKey,
      (description: string) => {
        return {labelKey: 'BALE.FIELDS.DESCRIPTION', displayText: description};
      }
    ],
    [
      this.sourceCodeKey,
      (sourceCode: string) => {
        return {labelKey: 'BALE.FIELDS.SOURCE_CODE', displayText: sourceCode};
      }
    ],
    [
      this.reasonCodeKey,
      (reasonCode: string) => {
        return {labelKey: 'BALE.FIELDS.REASON_CODE', displayText: reasonCode};
      }
    ],
    [
      this.busLineKey,
      (busLine: string) => {
        return {labelKey: 'BALE.FIELDS.BUS_LINE', displayText: busLine};
      }
    ],
    [
      this.departmentKey,
      (department: string) => {
        return {labelKey: 'BALE.FIELDS.DEPARTMENT', displayText: department};
      }
    ],
    [
      this.amountKey,
      (amount: number) => {
        return {labelKey: 'BALE.FIELDS.AMOUNT', displayText: amount.toString()};
      }
    ],
    [
      this.remainingAmountKey,
      (remainingAmount: number) => {
        return {labelKey: 'BALE.FIELDS.REMAINING_AMOUNT', displayText: remainingAmount.toString()};
      }
    ],
    [
      this.transactionCurrencyKey,
      (currencies: string[]) => {
        return {
          labelKey: 'BALE.FIELDS.TRANSACTION_CURRENCY',
          displayText: currencies.map((currency: string) => currency).join(activeFiltersSeparator)
        }
      }
    ],
    [
      this.directionKey,
      (direction: string[]) => {
        return {labelKey: 'BALE.FIELDS.DIRECTION',
          displayText: direction.map((dir: string) => dir).join(activeFiltersSeparator)};
      }
    ],
    [
      this.amountLcyKey,
      (amountLcy: number) => {
        return {labelKey: 'BALE.FIELDS.AMOUNT_LCY', displayText: amountLcy.toString()};
      }
    ],
    [
      this.balanceAccountTypeKey,
      (balAccountType: string) => {
        return {labelKey: 'BALE.FIELDS.BAL_ACCOUNT_TYPE', displayText: balAccountType};
      }
    ],
    [
      this.isOpenKey,
      (isOpen: boolean) => {
        return {labelKey: 'BALE.FIELDS.IS_OPEN', displayText: isOpen.toString()};
      }
    ],
    [
      this.isReversedKey,
      (isReversed: boolean) => {
        return {labelKey: 'BALE.FIELDS.IS_REVERSED', displayText: isReversed.toString()};
      }
    ],
    [
      this.postedByKey,
      (postedBy: string) => {
        return {labelKey: 'BALE.FIELDS.POSTED_BY', displayText: postedBy};
      }
    ],
    [
      this.externalDocumentNoKey,
      (externalDocumentNo: string) => {
        return {labelKey: 'BALE.FIELDS.EXTERNAL_DOCUMENT_NO', displayText: externalDocumentNo};
      }
    ],
    [
      this.baleTimestampKey,
      (baleTimestamp: string) => {
        return {labelKey: 'BALE.FIELDS.BALE_TIMESTAMP', displayText: baleTimestamp};
      }
    ],
    [
      this.accountTimestampKey,
      (accountTimestamp: string) => {
        return {labelKey: 'BALE.FIELDS.ACCOUNT_TIMESTAMP', displayText: accountTimestamp};
      }
    ],
    [
      this.reconciliationIdKey,
      (reconciliationId: string) => {
        return {labelKey: 'RECONCILIATION.FIELDS.ID', displayText: reconciliationId}
      }
    ],
    [
      this.reconciliationCreatorKey,
      (reconciliationCreator: string) => {
        return {labelKey: 'RECONCILIATION.FIELDS.CREATOR', displayText: reconciliationCreator};
      }
    ],
    [
      this.reconciliationCreationDateStartKey,
      (reconciliationCreationDateStart: string) => {
        return {labelKey: 'RECONCILIATION.FIELDS.CREATION_DATE', displayText: reconciliationCreationDateStart};
      }
    ],
    [
      this.reconciliationReviewerKey,
      (reconciliationReviewer: string) => {
        return {labelKey: 'RECONCILIATION.FIELDS.REVIEWER', displayText: reconciliationReviewer};
      }
    ],
    [
      this.reconciliationReviewDateStartKey,
      (reconciliationReviewDateStart: string) => {
        return {labelKey: 'RECONCILIATION.FIELDS.REVIEW_DATE', displayText: reconciliationReviewDateStart};
      }
    ],
    [
      this.reconciliationStatusKey,
      (reconciliationStatus: string) => {
        return {labelKey: 'RECONCILIATION.FIELDS.STATUS', displayText: reconciliationStatus};
      }
    ]
  ]);

  private filtersBehaviorSubject = new BehaviorSubject<BaleFilters>({});
  private activeFilterChips = new Map<string, CsFinActiveFilterChip>();
  public filters$: Observable<BaleFilters> = this.filtersBehaviorSubject.asObservable();
  public activeFiltersChips$: Observable<Map<string, CsFinActiveFilterChip>> = of(this.activeFilterChips);

  filtersChanged(filters: BaleFilters) {
    let newFilters : BaleFilters = {...filters}
    if (!newFilters.postingDateStart) {
      newFilters.postingDateStart = undefined;
      newFilters.postingDateEnd = undefined;
    }
    if (!newFilters.reconciliationCreationDateStart) {
      newFilters.reconciliationCreationDateStart = undefined;
      newFilters.reconciliationCreationDateEnd = undefined;
    }
    if (!newFilters.reconciliationReviewDateStart) {
      newFilters.reconciliationReviewDateStart = undefined;
      newFilters.reconciliationReviewDateEnd = undefined;
    }
    this.filtersBehaviorSubject.next(newFilters);
    this.updateActiveFilterChips(newFilters);
  }

  private updateActiveFilterChips(filters: BaleFilters): void {
    if (!filters) {
      return;
    }
    this.activeFilterChips.clear();

    if(filters.filterText) {
      this.activeFilterChips.set(this.filterTextKey, this.activeFiltersConfigMap.get(this.filterTextKey)(filters.filterText));
    }
    if(filters.idCompany && !this.hideUnwantedReconciliationFilters.value) {
      this.activeFilterChips.set(this.idCompanyKey, this.activeFiltersConfigMap.get(this.idCompanyKey)(filters.idCompany));
    }
    if(filters.accountNumber && !this.hideUnwantedReconciliationFilters.value) {
      this.activeFilterChips.set(this.accountKey, this.activeFiltersConfigMap.get(this.accountKey)(filters.accountNumber));
    }
    if(filters.entryNo) {
      this.activeFilterChips.set(this.entryNoKey, this.activeFiltersConfigMap.get(this.entryNoKey)(filters.entryNo));
    }
    if(filters.documentNo) {
      this.activeFilterChips.set(this.documentNoKey, this.activeFiltersConfigMap.get(this.documentNoKey)(filters.documentNo));
    }
    if(filters.documentType) {
      this.activeFilterChips.set(this.documentTypeKey, this.activeFiltersConfigMap.get(this.documentTypeKey)(filters.documentType));
    }
    if(filters.postingDateStart) {
      let dateToDisplay = new Date(filters.postingDateStart!).toDateString();
      if(filters.postingDateEnd){
        dateToDisplay += " - " + new Date(filters.postingDateEnd!).toDateString();
      }
      this.activeFilterChips.set(this.postingDateStartKey, this.activeFiltersConfigMap.get(this.postingDateStartKey)(dateToDisplay));
    }
    if(filters.accountPostingGroup) {
      this.activeFilterChips.set(this.accountPostingGroupKey, this.activeFiltersConfigMap.get(this.accountPostingGroupKey)(filters.accountPostingGroup));
    }
    if(filters.description) {
      this.activeFilterChips.set(this.descriptionKey, this.activeFiltersConfigMap.get(this.descriptionKey)(filters.description));
    }
    if(filters.sourceCode) {
      this.activeFilterChips.set(this.sourceCodeKey, this.activeFiltersConfigMap.get(this.sourceCodeKey)(filters.sourceCode));
    }
    if(filters.reasonCode) {
      this.activeFilterChips.set(this.reasonCodeKey, this.activeFiltersConfigMap.get(this.reasonCodeKey)(filters.reasonCode));
    }
    if(filters.busLine) {
      this.activeFilterChips.set(this.busLineKey, this.activeFiltersConfigMap.get(this.busLineKey)(filters.busLine));
    }
    if(filters.department) {
      this.activeFilterChips.set(this.departmentKey, this.activeFiltersConfigMap.get(this.departmentKey)(filters.department));
    }
    if(filters.direction && filters.direction.length > 0) {
      this.activeFilterChips.set(this.directionKey, this.activeFiltersConfigMap.get(this.directionKey)(filters.direction));
    }
    if(filters.amount) {
      this.activeFilterChips.set(this.amountKey, this.activeFiltersConfigMap.get(this.amountKey)(filters.amount));
    }
    if(filters.remainingAmount) {
      this.activeFilterChips.set(this.remainingAmountKey, this.activeFiltersConfigMap.get(this.remainingAmountKey)(filters.remainingAmount));
    }
    if(filters.transactionCurrency && filters.transactionCurrency.length > 0) {
      this.activeFilterChips.set(this.transactionCurrencyKey, this.activeFiltersConfigMap.get(this.transactionCurrencyKey)(filters.transactionCurrency));
    }
    if(filters.amountLcy) {
      this.activeFilterChips.set(this.amountLcyKey, this.activeFiltersConfigMap.get(this.amountLcyKey)(filters.amountLcy));
    }
    if(filters.balanceAccountType) {
      this.activeFilterChips.set(this.balanceAccountTypeKey, this.activeFiltersConfigMap.get(this.balanceAccountTypeKey)(filters.balanceAccountType));
    }
    if(filters.isOpen) {
      this.activeFilterChips.set(this.isOpenKey, this.activeFiltersConfigMap.get(this.isOpenKey)(filters.isOpen));
    }
    if(filters.isReversed) {
      this.activeFilterChips.set(this.isReversedKey, this.activeFiltersConfigMap.get(this.isReversedKey)(filters.isReversed));
    }
    if(filters.postedBy) {
      this.activeFilterChips.set(this.postedByKey, this.activeFiltersConfigMap.get(this.postedByKey)(filters.postedBy));
    }
    if(filters.externalDocumentNo) {
      this.activeFilterChips.set(this.externalDocumentNoKey, this.activeFiltersConfigMap.get(this.externalDocumentNoKey)(filters.externalDocumentNo));
    }
    if(filters.baleTimestamp) {
      this.activeFilterChips.set(this.baleTimestampKey, this.activeFiltersConfigMap.get(this.baleTimestampKey)(filters.baleTimestamp));
    }
    if(filters.accountTimestamp) {
      this.activeFilterChips.set(this.accountTimestampKey, this.activeFiltersConfigMap.get(this.accountTimestampKey)(filters.accountTimestamp));
    }
    if(filters.reconciliationId) {
      this.activeFilterChips.set(this.reconciliationIdKey, this.activeFiltersConfigMap.get(this.reconciliationIdKey)(filters.reconciliationId));
    }
    if(filters.reconciliationCreator) {
      this.activeFilterChips.set(this.reconciliationCreatorKey, this.activeFiltersConfigMap.get(this.reconciliationCreatorKey)(filters.reconciliationCreator));
    }
    if(filters.reconciliationCreationDateStart && filters.reconciliationCreationDateEnd) {
      let dateToDisplay = new Date(filters.reconciliationCreationDateStart!).toDateString();
      if(filters.reconciliationCreationDateEnd){
        dateToDisplay += " - " + new Date(filters.reconciliationCreationDateEnd!).toDateString();
      }
      this.activeFilterChips.set(this.reconciliationCreationDateStartKey, this.activeFiltersConfigMap.get(this.reconciliationCreationDateStartKey)(dateToDisplay));
    }
    if(filters.reconciliationReviewer) {
      this.activeFilterChips.set(this.reconciliationReviewerKey, this.activeFiltersConfigMap.get(this.reconciliationReviewerKey)(filters.reconciliationReviewer));
    }
    if(filters.reconciliationReviewDateStart && filters.reconciliationReviewDateEnd) {
      let dateToDisplay = new Date(filters.reconciliationReviewDateStart!).toDateString();
      if(filters.reconciliationReviewDateEnd){
        dateToDisplay += " - " + new Date(filters.reconciliationReviewDateEnd!).toDateString();
      }
      this.activeFilterChips.set(this.reconciliationReviewDateStartKey, this.activeFiltersConfigMap.get(this.reconciliationReviewDateStartKey)(dateToDisplay));
    }
    if(filters.reconciliationStatus && filters.reconciliationStatus.length > 0) {
      this.activeFilterChips.set(this.reconciliationStatusKey, this.activeFiltersConfigMap.get(this.reconciliationStatusKey)(filters.reconciliationStatus));
    }
  }

  encodeSortField(field: string): string {
    switch (field) {
      case 'idCompany':
        return 'ID_COMPANY';
      case 'account':
        return 'ACCOUNT';
      case 'entryNo':
        return 'ENTRY_NO';
      case 'documentNo':
        return 'DOCUMENT_NO';
      case 'documentType':
        return 'DOCUMENT_TYPE';
      case 'postingDate':
        return 'POSTING_DATE';
      case 'accountPostingGroup':
        return 'ACCOUNT_POSTING_GROUP';
      case 'description':
        return 'DESCRIPTION';
      case 'sourceCode':
        return 'SOURCE_CODE';
      case 'reasonCode':
        return 'REASON_CODE';
      case 'busLine':
        return 'BUS_LINE';
      case 'department':
        return 'DEPARTMENT';
      case 'direction':
        return 'DIRECTION';
      case 'amount':
        return 'AMOUNT';
      case 'remainingAmount':
        return 'REMAINING_AMOUNT';
      case 'transactionCurrency':
        return 'TRANSACTION_CURRENCY';
      case 'amountLcy':
        return 'AMOUNT_LCY';
      case 'balanceAccountType':
        return 'BALANCE_ACCOUNT_TYPE';
      case 'isOpen':
        return 'IS_OPEN';
      case 'isReversed':
        return 'IS_REVERSED';
      case 'postedBy':
        return 'POSTED_BY';
      case 'externalDocumentNo':
        return 'EXTERNAL_DOCUMENT_NO';
      case 'baleTimestamp':
        return 'BALE_TIMESTAMP';
      case 'accountTimestamp':
        return 'ACCOUNT_TIMESTAMP';
      default:
        return field.toUpperCase();
    }
  }

  decodeSortField(field: string): string {
    switch (field) {
      case 'ID_COMPANY':
        return 'idCompany';
      case 'ACCOUNT':
        return 'account';
      case 'ENTRY_NO':
        return 'entryNo';
      case 'DOCUMENT_NO':
        return 'documentNo';
      case 'DOCUMENT_TYPE':
        return 'documentType';
      case 'POSTING_DATE':
        return 'postingDate';
      case 'ACCOUNT_POSTING_GROUP':
        return 'accountPostingGroup';
      case 'DESCRIPTION':
        return 'description';
      case 'SOURCE_CODE':
        return 'sourceCode';
      case 'REASON_CODE':
        return 'reasonCode';
      case 'BUS_LINE':
        return 'busLine';
      case 'DEPARTMENT':
        return 'department';
      case 'DIRECTION':
        return 'direction';
      case 'AMOUNT':
        return 'amount';
      case 'REMAINING_AMOUNT':
        return 'remainingAmount';
      case 'TRANSACTION_CURRENCY':
        return 'transactionCurrency';
      case 'AMOUNT_LCY':
        return 'amountLcy';
      case 'BALANCE_ACCOUNT_TYPE':
        return 'balanceAccountType';
      case 'IS_OPEN':
        return 'isOpen';
      case 'IS_REVERSED':
        return 'isReversed';
      case 'POSTED_BY':
        return 'postedBy';
      case 'EXTERNAL_DOCUMENT_NO':
        return 'externalDocumentNo';
      case 'BALE_TIMESTAMP':
        return 'baleTimestamp';
      case 'ACCOUNT_TIMESTAMP':
        return 'accountTimestamp';
      default:
        return field;
    }
  }

  convertToFilters(grouping: string, result:string, filter:BaleFilters): BaleFilters {
    switch (grouping) {
      case 'ID_COMPANY':
        filter.idCompany = result;
        break;
      case 'ACCOUNT':
        filter.accountNumber = result;
        break;
      case 'ENTRY_NO':
        if (!filter.entryNo) {
          filter.entryNo = [];
        }
        filter.entryNo.push(Number(result));
        break;
      case 'DOCUMENT_NO':
        filter.documentNo = result;
        break;
      case 'DOCUMENT_TYPE':
        filter.documentType = result;
        break;
      case 'POSTING_DATE':
        filter.postingDateStart = result;
        break;
      case 'ACCOUNT_POSTING_GROUP':
        filter.accountPostingGroup = result;
        break;
      case 'DESCRIPTION':
        filter.description = result;
        break;
      case 'SOURCE_CODE':
        filter.sourceCode = result;
        break;
      case 'REASON_CODE':
        filter.reasonCode = result;
        break;
      case 'BUS_LINE':
        filter.busLine = result;
        break;
      case 'DEPARTMENT':
        filter.department = result;
        break;
      case 'DIRECTION':
        let directionList: string[] = [];
        directionList.push(result);
        filter.direction = directionList;
        break;
      case 'AMOUNT':
        filter.amount = Number(result);
        break;
      case 'REMAINING_AMOUNT':
        filter.remainingAmount = Number(result);
        break;
      case 'TRANSACTION_CURRENCY':
        let currencyList: string[] = [];
        currencyList.push(result);
        filter.transactionCurrency = currencyList;
        break;
      case 'AMOUNT_LCY':
        filter.amountLcy = Number(result);
        break;
      case 'BALANCE_ACCOUNT_TYPE':
        filter.balanceAccountType = result;
        break;
      case 'IS_OPEN':
        filter.isOpen = result === 'true';
        break;
      case 'IS_REVERSED':
        filter.isReversed = result === 'true';
        break;
      case 'POSTED_BY':
        filter.postedBy = result;
        break;
      case 'EXTERNAL_DOCUMENT_NO':
        filter.externalDocumentNo = result;
        break;
      case 'BALE_TIMESTAMP':
        filter.baleTimestamp = result;
        break;
      case 'ACCOUNT_TIMESTAMP':
        filter.accountTimestamp = result;
        break;
      case "RECONCILIATION_CREATOR":
        filter.reconciliationCreator = result;
        break;
      case "RECONCILIATION_CREATION_DATE":
        filter.reconciliationCreationDateStart = result;
    }
    return filter;
  }



  getAll(baleFilters:BaleFilters): Observable<PageResponse<Bale>> {
    return this.baleApiService.getAll(baleFilters);
  }

  getById(id: number): Observable<Bale> {
    return this.baleApiService.getById(id);
  }

  getEntityGroups(filters:string[], defaultFilters?: BaleFilters): Observable<CsFinGroup> {
    return this.baleApiService.getBaleGroups(filters, defaultFilters);
  }

  getDirections(): Observable<string[]> {
    return this.transactionsApiService.getDirections();
  }

  getBaleGroupableFields(): Observable<string[]> {
    return this.baleApiService.getBaleGroupableFields();
  }

  syncByBaleViewIds(baleViewIds: number[]): Observable<void> {
    return this.baleApiService.syncByBaleViewIds(baleViewIds);
  }

  getAllGroupedInfo(filters: CsFinFilterList<BaleFilters>): Observable<CsFinGroupedEntityInfo[]> {
    return this.baleApiService.getAllGroupedInfo(filters);
  }

}
