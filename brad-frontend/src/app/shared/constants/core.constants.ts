export const frontendBaseUrls = {
  loginResponse: 'login/response',
  tags: {
    details: 'tags/details',
    list: 'tags'
  },
  accounts: {
    details: 'accounts/details',
    list: 'accounts'
  },
  scheduler: {
    details: 'scheduler/details',
    list: 'scheduler'
  },
  finrec: {
    list: 'finrec'
  },
  bale: {
    list: 'bale'
  },
  reconciliation: {
    reconcile: 'reconcile',
    thresholds: 'thresholds',
    audit: 'audit'
  },
  apiLog: {
    details: 'api-log/details',
    list: 'api-log'
  },
  troubleshooting: {
    list: 'troubleshooting'
  },
  executionLogs: {
    details: 'execution-logs/details',
    list: 'execution-logs'
  },
  exportLogs: {
    details: 'export-logs/details',
    list: 'export-logs'
  },
  accountStatementFiles: {
    details: 'statement-files/details',
    list: 'statement-files'
  },
};

export const localStorageKeys = {
  satellizerToken: 'satellizerToken',
  userPermissions: 'userPermissions',
  jwtToken: 'jwtToken',
  lastUsedColumns: 'lastUsedColumns',
  lastUsedColumnsAlt: 'lastUsedColumnsAlt',
};

export const mouseButtons = {
  left: 0,
  middle: 1,
  right: 2
};

export const paginationConsts = {
  defaultPage: 1,
  defaultCardPageSize: 5,
  defaultListPageSize: 10,
  cardPageSizeOptions: [5, 10, 15, 20, 50],
  listPageSizeOptions: [10, 20, 50, 100],
  defaultPageSize: 10,
  pageSizeOptions: [10, 20, 50, 100],
  auditTimelineDefaultPageSize: 5,
  auditTimelinePageSizeOptions: [5, 10, 15, 50, 100]
};

export const sortingDirections = {
  asc: 'asc',
  desc: 'desc'
};
