import {ColumnDetails} from 'src/app/entities/column-details';
import {MenuColumnsAlt} from './entities/menu-columns-alt';
import {localStorageKeys} from "../../constants/core.constants";

export class LastUsedColumnsAlt {

  private static instance: LastUsedColumnsAlt;
  private columns: MenuColumnsAlt = {};


  static getInstance(): LastUsedColumnsAlt {

    if (!LastUsedColumnsAlt.instance) {
      LastUsedColumnsAlt.instance = new LastUsedColumnsAlt();
    }

    return LastUsedColumnsAlt.instance;
  }

  initColumns(menu: string, columnDetails: ColumnDetails[]): void {
    const columnsSaved = localStorage.getItem(localStorageKeys.lastUsedColumnsAlt);

    if (columnsSaved) {
      this.columns = JSON.parse(columnsSaved);

      if (!this.columns.hasOwnProperty(menu)) {
        this.set(menu, columnDetails);
      } else {
        this.refreshColumns(menu, columnDetails);
      }
    } else {
      this.set(menu, columnDetails);
    }
  }

  getDefaultColumns(menu: string): string[] {
    return this.get(menu).filter(column => column.isActive).map(column => column.code);
  }

  get = (key: string): ColumnDetails[] => this.columns[key];

  set(key: string, value: ColumnDetails[]): void {

    this.columns[key] = value;
    this.hydrate();
  }

  reset(): void {
    localStorage.removeItem(localStorageKeys.lastUsedColumnsAlt);
  }

  private hydrate() {
    localStorage.setItem(localStorageKeys.lastUsedColumnsAlt, JSON.stringify(this.columns));
  }

  private refreshColumns(menu: string, columns: ColumnDetails[]) {
    let savedColumns = this.columns[menu];
    let refreshedColumns: ColumnDetails[] = []

    columns.forEach(
      column => {
        let savedColumn = savedColumns.find(savedColumn => savedColumn.code == column.code);

        // new column
        if (savedColumn == undefined) {
          refreshedColumns.push(column);
          return;
        }

        // update existing column
        let updatedColumn: ColumnDetails = {
          position: column.position,
          name: column.name,
          code: column.code,
          isActive: (column.isDefault && !savedColumn.isDefault) ? column.isActive : savedColumn.isActive,
          isRemovable: column.isRemovable,
          isDefault: column.isDefault
        }
        refreshedColumns.push(updatedColumn);
      }
    )
    this.set(menu, refreshedColumns);
  }


}
