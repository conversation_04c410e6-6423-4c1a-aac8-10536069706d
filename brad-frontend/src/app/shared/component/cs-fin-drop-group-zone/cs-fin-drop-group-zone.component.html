<section cdkDropList cdkDropListOrientation="horizontal" class="drop-area" (cdkDropListDropped)="groupBy($event)">
  <div class="label" *ngIf="groupByColumns.length == 0">Drag to group</div>
  <div class="group" *ngFor="let group of groupByColumns" cdkDrag [cdkDragData]="group">
    <mat-chip class="group-chip">
      <div class="group-element">
        <span>{{nameByGroupByColumnGroup(group)}}</span>
        <mat-icon (click)="unGroupBy(group)">close</mat-icon>
      </div>
    </mat-chip>
    <span class="line"></span>
  </div>
  <span fxFlex></span>
</section>
