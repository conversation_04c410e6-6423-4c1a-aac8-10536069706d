import {Component, EventEmitter, Input, Output} from '@angular/core';

@Component({
  selector: 'cs-fin-drop-group-zone',
  templateUrl: './cs-fin-drop-group-zone.component.html',
  styleUrls: ['./cs-fin-drop-group-zone.component.scss']
})
export class CsFinDropGroupZoneComponent {

  @Input() groupByColumnDetails: Map<any, any> = new Map();
  @Input() displayedColumns: string[] = [];
  @Input() groupByColumns: string[] = [];
  @Input() expandedGroupNames: string[] = [];
  @Output() groupOnClick: EventEmitter<any> = new EventEmitter();
  removedColumns: Map<string, number> = new Map();

  groupBy($event: any) {
    let groupCode = this.groupByColumnDetails.get($event.item.data)?.code;
    if (!groupCode) {
      return;
    }
    if (this.existsInGroupByColumns($event.item.data)) {
      this.reOrderGroupByColumns($event.item.data, $event.previousIndex, $event.currentIndex);
    }
    this.checkGroupByColumn($event.item.data, true);
    const index = this.displayedColumns.indexOf(groupCode);
    if (index > -1) {
      this.removedColumns.set(groupCode, index);
      this.displayedColumns.splice(index, 1);
    }

    this.groupOnClick.emit()
    this.expandedGroupNames = [];
  }

  unGroupBy($event: any) {
    let groupCode = this.groupByColumnDetails.get($event)?.code;
    if (!groupCode) {
      return;
    }
    this.checkGroupByColumn($event, false);

    const index = this.removedColumns.get(groupCode)!;
    this.displayedColumns.splice(index, 0, groupCode);
    this.removedColumns.delete(groupCode)
    this.groupOnClick.emit()
    this.expandedGroupNames = [];
  }

  existsInGroupByColumns(column: string): boolean {
    return this.groupByColumns.indexOf(column) !== -1;
  }

  reOrderGroupByColumns(existingItem:string, oldIndex:number, newIndex:number) {
    this.groupByColumns.splice(oldIndex, 1);
    this.groupByColumns.splice(newIndex, 0, existingItem);
    this.groupOnClick.emit()
  }

  checkGroupByColumn(field: any, add: boolean): void {
    let found: any = null;
    for (const column of this.groupByColumns) {
      if (column === field) {
        found = this.groupByColumns.indexOf(column, 0);
      }
    }
    if (found != null && found >= 0) {
      if (!add) {
        this.groupByColumns.splice(found, 1);
      }
    } else {
      if (add) {
        this.groupByColumns.push(field);
      }
    }
  }

  nameByGroupByColumnGroup(groupByColumn: any): string {
    return this.groupByColumnDetails.get(groupByColumn)?.name;
  }

}
