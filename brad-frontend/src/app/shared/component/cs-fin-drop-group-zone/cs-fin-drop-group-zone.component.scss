.drop-area {
  display: block;
  max-height: 40px;
  height: 40px;
  border: 1px dashed var(--primary-color);
  border-radius: 5px;
  background-color: #f8f8f8;

  .label {
    font-weight: 300;
    font-size: 12px;
    line-height: 40px;
    text-transform: uppercase;
    color: var(--primary-color);
    padding-left: 24px;
  }

  .group {
    display: flex;
    align-items: center;

    .line {
      display: inline-block;
      width: 24px;
      height: 4px;
      margin-top: 4px;
      border-top: 1px dashed #7D97F4;
    }

    &:nth-child(1) {
      mat-chip {
        margin-left: 24px;
      }
    }

    &:last-of-type {
      .line {
        display: none;
      }
    }
    .group-chip {
      border: 1px solid var(--primary-color);
      background-color: #f3f3f3;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      .group-element {
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

  }
}
