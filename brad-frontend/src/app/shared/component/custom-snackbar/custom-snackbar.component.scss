.snackbar-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  color: white;
  background-color: #EE6666;

  div {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
  }

  button {
    color: white;
  }
}

::ng-deep {
  .mdc-snackbar__surface {
    padding: 0;
  }
  .mat-mdc-snack-bar-label {
    background-color: #EE6666;
    padding: 5px 15px;
  }
}
