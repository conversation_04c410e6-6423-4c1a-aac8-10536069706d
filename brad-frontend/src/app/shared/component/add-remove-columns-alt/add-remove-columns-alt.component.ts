import {CdkConnectedOverlay, CdkOverlayOrigin} from '@angular/cdk/overlay';
import {Component, Input, OnInit, ViewChild} from '@angular/core';
import {BehaviorSubject, Observable, Subject, takeUntil, tap} from 'rxjs';
import {AddRemoveColumnsAltFacade} from './add-remove-columns-alt.facade';
import {LastUsedColumnsAlt} from "../../util/last-used-columns-alt/last-used-columns-alt";
import {ColumnDetails} from "../../../entities/column-details";

@Component({
  selector: 'shared-add-remove-columns-alt',
  templateUrl: './add-remove-columns-alt.component.html',
  styleUrls: ['./add-remove-columns-alt.component.scss']
})
export class AddRemoveColumnsAltComponent implements OnInit {

  @ViewChild(CdkConnectedOverlay, {static: true})
  private connectedOverlay!: CdkConnectedOverlay;

  @Input() columnDetails: Map<string, string> = new Map([]);
  @Input() isOverlayOpen$!: Observable<boolean>;
  @Input() triggerOrigin!: CdkOverlayOrigin;
  @Input() menu!: string;

  _isOverlayOpen!: BehaviorSubject<boolean>;
  columnInformation: ColumnDetails[] = [];

  private displayedColumns: string[] = [];
  private lastSelectedColumns!: string[];
  private _onDestroy = new Subject<void>();

  constructor (private addRemoveColumnsFacade: AddRemoveColumnsAltFacade) {
  }

  ngOnInit(): void {
    this.initializeOverlay();
    this.validateDisplayedColumns();
    this.addRemoveColumnsFacade.isOverlayOpen
      .pipe(takeUntil(this._onDestroy))
      .subscribe((value) => this._isOverlayOpen.next(value));
  }

  ngOnDestroy() {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  initializeOverlay() {
    this._isOverlayOpen = new BehaviorSubject<boolean>(false);
    this.connectedOverlay.backdropClass = 'cdk-overlay-transparent-backdrop';
    this.connectedOverlay.panelClass = 'columns-dropdown';
    this.connectedOverlay.backdropClick
      .pipe(tap(() => {
        this._isOverlayOpen.next(false);
        this.validateSelectedColumns();
      }))
      .subscribe();

    this.isOverlayOpen$ = this._isOverlayOpen.asObservable();
  }

  private validateDisplayedColumns() {
    this.columnInformation = LastUsedColumnsAlt.getInstance().get(this.menu);

    this.displayedColumns.push(...this.getActiveColumns());
    this.lastSelectedColumns = this.displayedColumns;
    this.addRemoveColumnsFacade.fetchDisplayedColumns.next({displayedColumns: this.displayedColumns});
  }

  private getActiveColumns(): string[] {
    return this.columnInformation.filter(column => column.isActive).map(column => column.code);
  }

  toggleColumn(column: ColumnDetails){
    column.isActive = !column.isActive;
  }

  restoreDefaultColumns() {
    this.columnInformation.forEach(element => element.isActive = element.isDefault);
    this.applyColumns();
    this.addRemoveColumnsFacade.fetchDisplayedColumns.next({displayedColumns: this.displayedColumns});
  }

  applyColumns() {
    const activeColumns = this.getActiveColumns();
    const columnsAdded = activeColumns.filter(column => !this.displayedColumns.includes(column));

    this.displayedColumns.splice(0, this.displayedColumns.length, ...activeColumns);
    this.lastSelectedColumns = this.displayedColumns;

    if (columnsAdded.length) {
      const columnDetailsKeys = [...this.columnDetails.keys()];
      const additionalDetails = columnDetailsKeys.some(element => columnsAdded.includes(element));

      if (additionalDetails) {
        this.addRemoveColumnsFacade.fetchRequestData.next({lastSelectedColumns: this.lastSelectedColumns});
      }
    }

    LastUsedColumnsAlt.getInstance().set(this.menu, this.columnInformation);
    this.addRemoveColumnsFacade.fetchDisplayedColumns.next({displayedColumns: this.displayedColumns});
    this._isOverlayOpen.next(false);
  }

  validateSelectedColumns () {
    this.columnInformation.forEach(element => element.isActive = <boolean>this.displayedColumns?.includes(element.code));
  }
}
