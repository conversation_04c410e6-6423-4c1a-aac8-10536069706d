import {Injectable} from '@angular/core';
import {BehaviorSubject} from 'rxjs';
import {AddRemoveColumnsAlt} from './entities/add-remove-columns-alt';

@Injectable({providedIn: 'root'})
export class AddRemoveColumnsAltFacade {

  fetchDisplayedColumns = new BehaviorSubject<AddRemoveColumnsAlt>({});
  fetchRequestData = new BehaviorSubject<AddRemoveColumnsAlt>({});
  isOverlayOpen = new BehaviorSubject<boolean>(false);

}
