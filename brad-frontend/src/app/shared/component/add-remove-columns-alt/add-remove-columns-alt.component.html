<ng-template cdkConnectedOverlay cdkConnectedOverlayHasBackdrop [cdkConnectedOverlayOrigin]="triggerOrigin"
             [cdkConnectedOverlayOpen]="isOverlayOpen$ | async">
  <div id="column-overlay-container">
    <div class="column-overlay-actions">
      <button class="flat-white-btn" mat-flat-button (click)="restoreDefaultColumns()">Restore</button>
      <button class="raised-primary-btn" mat-raised-button color="primary" (click)="applyColumns()">Apply</button>
    </div>
    <div class="column-overlay-content">
      <button [disabled]="!column.isRemovable" class="column-overlay-item" *ngFor="let column of columnInformation"
              (click)="toggleColumn(column)">
        <mat-checkbox color="primary" [disabled]="!column.isRemovable" [checked]="column.isActive"
                      (change)="toggleColumn(column)" (click)="$event.stopPropagation()" class="no-ripple-checkbox">
        </mat-checkbox>
        {{column.name | translate}}
        <br>
      </button>
    </div>
  </div>
</ng-template>
