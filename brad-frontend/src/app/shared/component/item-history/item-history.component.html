<div class="container">
  <div class="history">
    <mat-accordion>


      <div class="timeline-item" *ngFor="let item of items; let idxItem = index; last as isLast"
           [ngClass]="{'is-last': isLast}">
        <div class="timeline-icon">
          <mat-icon>{{getAuditIcon(item)}}</mat-icon>
        </div>
        <div class="timeline-label">
          <mat-expansion-panel hideToggle class="mat-elevation-z0">
            <mat-expansion-panel-header>
              <div class="timeline-label-container">
                <div class="timeline-main-text">{{item.mainText}}</div>
                <div class="timeline-revision-container">
                  <div class="timeline-revision-date">
                    <mat-icon>calendar_month</mat-icon>
                    <span>{{item.auditedEntity.revision.datetime}}</span>
                  </div>
                  <div class="timeline-revision-user">
                    <mat-icon>person</mat-icon>
                    <span>{{item.auditedEntity.revision.username}}</span>
                  </div>
                </div>
              </div>
            </mat-expansion-panel-header>
            <div class="timeline-item-full-content-container">
              <div class="timeline-item-full-content-item"
                   *ngFor="let fullContentItem of item.decodedEntity; let idxAuditField = index"
                   [class.timeline-item-highlighted]="shouldHightlightValue(fullContentItem, idxItem, idxAuditField)">
                <span class="timeline-full-content-key">
                  {{fullContentItem.key | translate}}
                </span>
                <span class="timeline-full-content-value" *ngIf="fullContentItem.isDate">
                  {{fullContentItem.value}}</span>
                <span class="timeline-full-content-value" *ngIf="!fullContentItem.isDate">
                  {{fullContentItem.value}}
                </span>
              </div>
            </div>
          </mat-expansion-panel>
        </div>
      </div>

    </mat-accordion>
    <div class="timeline-paginator-background"></div>
    <mat-paginator *ngIf="items"
                   [pageSizeOptions]="pagination.pageSizeOptions"
                   [pageSize]="pagination.pageSize"
                   [length]="pagination.totalItems"
                   [pageIndex]="pagination.pageIndex"
                   (page)="onAuditPageChange($event)"
                   showFirstLastButtons>
    </mat-paginator>

  </div>

</div>
