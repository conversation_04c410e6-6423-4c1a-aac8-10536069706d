import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {DecodedAuditEntity} from '../../../entities/audit/decoded-audit-entity';
import {AuditTimelineItem} from '../../../entities/audit/audit-timeline-item';
import {CsFinPagination} from '@jumia-cs-fin/common';

@Component({
  selector: 'brad-item-history',
  templateUrl: './item-history.component.html',
  styleUrls: ['./item-history.component.scss']
})
export class ItemHistoryComponent implements OnInit {

  private readonly HIGHLIGHT_BLACKLIST_FIELD_KEYS: string[] = [
    'GENERAL.FIELDS.UPDATED_AT',
    'GENERAL.FIELDS.UPDATED_BY'
  ];

  @Input() items: AuditTimelineItem[] = [];
  @Input() pagination!: CsFinPagination;
  @Output() onPageChange = new EventEmitter();

  getAuditIcon(item: AuditTimelineItem): string {
    switch (item?.auditedEntity?.operation) {
      case 'CREATE':
        return 'add';
      case 'UPDATE':
        return 'edit';
      case 'DELETE':
        return 'delete';
      default:
        return 'question_mark';
    }
  }

  onAuditPageChange(event: any): void {
    this.onPageChange.emit(event);
  }

  shouldHightlightValue(currentItem: DecodedAuditEntity, idxItem: number, idxAuditField: number): boolean {
    if (idxItem === this.items.length - 1) {
      return false;
    }

    const nextItemField = this.items[idxItem + 1].decodedEntity.find(item => item.key === currentItem.key);

    if (!nextItemField) {
      return false;
    }

    return currentItem.value !== nextItemField.value
      && this.HIGHLIGHT_BLACKLIST_FIELD_KEYS.indexOf(currentItem.key) < 0;
  }

  ngOnInit(): void {
  }

}
