@import "node_modules/@jumia-cs-fin/common/assets/styles/tables";

$brad-timeline-border-size: 6px;
$brad-timeline-icon-size: 35px;
$brad-timeline-element-space: 50px;
$brad-timeline-arrow-size: 8px;
$brad-timeline-label-padding: 10px;
$brad-timeline-element-size: $brad-timeline-icon-size + ($brad-timeline-border-size * 2);
$brad-button-color: #C7ECF8;
$brad-button-color-darker: #D8F6FF;
$brad-text-color: #1A2950;

::ng-deep.mat-paginator-container {
  background: white !important;
  z-index: 10 !important;
}

.container{
  max-height: 71vh;
  overflow-y: auto;
}

.history {
  overflow-x: hidden;
  margin: 15px;
  padding: 0;
  list-style: none;
  position: relative;
}


.history::before {
  z-index: -1;
  content: '';
  position: absolute;
  top: 10px;
  bottom: 0;
  left: ($brad-timeline-icon-size/ 2) + ($brad-timeline-border-size / 2);
  width: 6px;
  background: $brad-button-color;
}

.timeline-paginator-background {
  z-index: 10;
  background-color: white;
  margin-top: 0;
}



.timeline-item {
  margin-top: 10px ;
  position: relative;
  margin-bottom: $brad-timeline-element-space;

  .timeline-icon {
    width: $brad-timeline-icon-size;
    height: $brad-timeline-icon-size;
    line-height: $brad-timeline-icon-size + 4px;
    position: absolute;
    color: $brad-text-color;
    font-weight: bold;
    background-color: white;
    border-radius: 50%;
    box-shadow: 0 0 0 $brad-timeline-border-size $brad-button-color;
    text-align: center;
    left: $brad-timeline-border-size;
    top: $brad-timeline-border-size;
    text-transform: uppercase;
    font-size: 1.5rem;
  }

  .timeline-label {
    margin: 0 0 0 $brad-timeline-element-size + $brad-timeline-arrow-size + 5px;
    background-color: $brad-button-color;
    color: $brad-text-color;
    padding: $brad-timeline-label-padding;
    position: relative;
    border-radius: 5px;
    min-height: $brad-timeline-icon-size + ($brad-timeline-border-size * 2);
    outline: none;

    mat-expansion-panel {
      background-color: $brad-button-color;
      color: $brad-text-color;
      margin: 0 0 0 0;


      mat-expansion-panel-header {
        background-color: $brad-button-color;
        min-height: 48px;
        height: auto;
      }

      .timeline-label-container {
        display: flex;
        flex-direction: column;
        gap: 10px;

        .timeline-main-text {
          font-size: 1.1em;
          font-weight: 550;
        }

        .timeline-revision-container {
          display: flex;
          flex-direction: row;
          gap: 15px;

          .timeline-revision-date, .timeline-revision-user {
            display: flex;
            flex-direction: row;
            align-items: center;

            .mat-icon {
              font-size: 20px;
            }
          }
        }
      }

      .timeline-item-full-content-container {
        position: relative;
        background-color: white;
        padding: 10px;
        border-radius: 5px;
        width: 100%!important;
        box-sizing: border-box;

        .timeline-item-full-content-item {
          display: grid;
          grid-template-columns: 1fr 3fr;

          .timeline-full-content-key {
            font-weight: 600;
          }
        }
      }
    }
  }

  .timeline-label::after {
    right: 100%;
    border: $brad-timeline-arrow-size solid transparent;
    border-right-color: $brad-button-color;
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
    top: ($brad-timeline-icon-size / 2) - ($brad-timeline-arrow-size / 2);
  }
}

.timeline-item:last-child,
.timeline-item.is-last {
  margin: 0;
  background-color: white;
}


::-webkit-scrollbar {
  width: 0;
}


.timeline-item-highlighted {
  font-weight: 600 !important;
  text-decoration: underline;
  background-color: $brad-button-color-darker;
  border-radius: 5px;
}






