import {<PERSON>pe} from '@angular/core';

@Pipe({
  standalone: true,
  name: 'json<PERSON>ormatter'
})
export class Json<PERSON>ormatter {

  /**
   * parses and beautifies Json String.
   * if the input is not a Json object, returns plain string value
   *
   * @param text input string to parse
   */
  transform(text: string): string {
    // if text is not null, tries to parse
    if (text != null) {
      try {
        // if text is parseable, formats
        return JSON.stringify(JSON.parse(text), null, 2);
      } catch (e) {
        // if text is not parseable, returns plain string
        return text;
      }
    }
    return text;
  }
}
