import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'thousandsFormatter'
})
export class ThousandsFormatterPipe implements PipeTransform {

  transform(value: number | string | null | undefined): string | null {
    if (value == null) {
      return null;
    }

    let num: number;

    if (typeof value === 'string') {
      // Attempt to convert string to number, handle potential errors
      const parsedValue = Number(value);
      if (isNaN(parsedValue)) {
        return value; // Return original value if parsing fails
      }
      num = parsedValue;
    } else {
      num = value;
    }


    return num.toLocaleString('en-US'); // Use toLocaleString for comma formatting
  }

}
