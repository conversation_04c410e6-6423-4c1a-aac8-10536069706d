import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {TranslateModule} from '@ngx-translate/core';
import {FormsModule} from '@angular/forms';
import {MatIconModule} from '@angular/material/icon';
import {MatSnackBarModule} from '@angular/material/snack-bar';
import {MatDialogModule} from '@angular/material/dialog';
import {MatButtonModule} from '@angular/material/button';
import {ItemHistoryComponent} from './component/item-history/item-history.component';
import {MatExpansionModule} from "@angular/material/expansion";
import {MatPaginatorModule} from "@angular/material/paginator";
import {FlexLayoutModule} from "@angular/flex-layout";
import {MatChipsModule} from "@angular/material/chips";
import {OverlayModule} from "@angular/cdk/overlay";
import {MatCheckboxModule} from "@angular/material/checkbox";
import {JsonFormatter} from "./helpers/json-formatter";
import {ThousandsFormatterPipe} from "./helpers/thousands-formatter";
import {UnderscoreToSpacePipe} from "./helpers/underscore-to-space";
import {MatTableModule} from "@angular/material/table";
import {CsFinDropGroupZoneComponent} from './component/cs-fin-drop-group-zone/cs-fin-drop-group-zone.component';
import {CdkDrag, CdkDropList} from "@angular/cdk/drag-drop";
import {AddRemoveColumnsAltComponent} from "./component/add-remove-columns-alt/add-remove-columns-alt.component";
import { CustomSnackbarComponent } from './component/custom-snackbar/custom-snackbar.component';

@NgModule({
  declarations: [
    ItemHistoryComponent,
    CsFinDropGroupZoneComponent,
    AddRemoveColumnsAltComponent,
    ThousandsFormatterPipe,
    UnderscoreToSpacePipe,
    CustomSnackbarComponent,
  ],
  exports: [
    ItemHistoryComponent,
    JsonFormatter,
    CsFinDropGroupZoneComponent,
    AddRemoveColumnsAltComponent,
    ThousandsFormatterPipe,
    UnderscoreToSpacePipe,
    CustomSnackbarComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,
    MatSnackBarModule,
    MatIconModule,
    MatDialogModule,
    MatButtonModule,
    MatExpansionModule,
    MatPaginatorModule,
    FlexLayoutModule,
    MatChipsModule,
    OverlayModule,
    MatCheckboxModule,
    JsonFormatter,
    MatTableModule,
    CdkDrag,
    CdkDropList
  ]
})
export class SharedModule { }
