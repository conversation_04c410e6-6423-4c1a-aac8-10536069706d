import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {NavigationComponent} from "../navigation/navigation.component";
import {ReconcileComponent} from "./component/reconcile/reconcile.component";
import {csFinAuthCanActivateGuard, CsFinAuthTargetType, CsFinAuthType} from "@jumia-cs-fin/common";
import {bradPermissions} from "../auth/constants/permission.constants";
import {bradAuthTarget} from "../auth/constants/auth.constants";
import {ThresholdsComponent} from "./component/thresholds/thresholds.component";
import {AuditComponent} from "./component/audit/audit.component";

const routes: Routes = [
  // {
  //   path: 'reconcile',
  //   component: NavigationComponent,
  //   children: [
  //     {
  //       path: '',
  //       canActivate: [csFinAuthCanActivateGuard],
  //       data: {
  //         auth: {
  //           permissions: bradPermissions.BRAD_ACCESS_RECONCILIATION,
  //           targets: bradAuthTarget,
  //           targetType: CsFinAuthTargetType.APPLICATION,
  //           type: CsFinAuthType.HAS_PERMISSION_ON_TARGET
  //         }
  //       },
  //       component: ReconcileComponent,
  //       pathMatch: 'full'
  //     }
  //   ]
  // },
  // {
  //   path: 'thresholds',
  //   component: NavigationComponent,
  //   children: [
  //     {
  //       path: '',
  //       canActivate: [csFinAuthCanActivateGuard],
  //       component: ThresholdsComponent,
  //       data: {
  //         auth: {
  //           permissions: bradPermissions.BRAD_MANAGE_THRESHOLDS,
  //           targets: bradAuthTarget,
  //           targetType: CsFinAuthTargetType.APPLICATION,
  //           type: CsFinAuthType.HAS_PERMISSION_ON_TARGET
  //         }
  //       }
  //     }
  //   ]
  // },
  // {
  //   path: 'audit',
  //   component: NavigationComponent,
  //   children: [
  //     {
  //       path: '',
  //       canActivate: [csFinAuthCanActivateGuard],
  //       component: AuditComponent,
  //       data: {
  //         auth: {
  //           permissions: bradPermissions.BRAD_ACCESS_RECONCILIATION,
  //           targets: bradAuthTarget,
  //           targetType: CsFinAuthTargetType.APPLICATION,
  //           type: CsFinAuthType.HAS_PERMISSION_ON_TARGET
  //         }
  //       }
  //     }
  //   ]
  // }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ReconciliationRoutingModule { }
