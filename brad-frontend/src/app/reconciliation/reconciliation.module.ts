import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';

import {ReconciliationRoutingModule} from './reconciliation-routing.module';
import {ReconcileComponent} from './component/reconcile/reconcile.component';
import {MatButtonModule} from "@angular/material/button";
import {MatFormFieldModule} from "@angular/material/form-field";
import {MatIconModule} from "@angular/material/icon";
import {MatInputModule} from "@angular/material/input";
import {MatProgressBarModule} from "@angular/material/progress-bar";
import {MatToolbarModule} from "@angular/material/toolbar";
import {TranslateModule} from "@ngx-translate/core";
import {BaleModule} from "../bale/bale.module";
import {MatCheckboxModule} from "@angular/material/checkbox";
import {MatMenuModule} from "@angular/material/menu";
import {MatPaginatorModule} from "@angular/material/paginator";
import {MatSortModule} from "@angular/material/sort";
import {MatTableModule} from "@angular/material/table";
import {MatTooltipModule} from "@angular/material/tooltip";
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {MatCardModule} from "@angular/material/card";
import {MatDividerModule} from "@angular/material/divider";
import {MatListModule} from "@angular/material/list";
import {MatTabsModule} from "@angular/material/tabs";
import {MatExpansionModule} from "@angular/material/expansion";
import {ReconcileHeaderComponent} from './component/reconcile/header/reconcile-header.component';
import {ExtendedModule, FlexModule} from "@angular/flex-layout";
import {MatOptionModule} from "@angular/material/core";
import {MatProgressSpinnerModule} from "@angular/material/progress-spinner";
import {MatSelectModule} from "@angular/material/select";
import {NgxMatSelectSearchModule} from "ngx-mat-select-search";
import {OverlayModule} from "@angular/cdk/overlay";
import {ListBaleComponent} from './component/reconcile/list-bale/list-bale.component';
import {ListTransactionComponent} from './component/reconcile/list-transaction/list-transaction.component';
import {DragDropModule} from "@angular/cdk/drag-drop";
import {SharedModule} from "../shared/shared.module";
import {MatSlideToggleModule} from "@angular/material/slide-toggle";
import {
  CsFinActiveFiltersModule,
  CsFinAddRemoveColumnsModule,
  CsFinAuthModule,
  CsFinFlagModule
} from "@jumia-cs-fin/common";
import {AccountsModule} from "../accounts/accounts.module";
import {MatChipsModule} from "@angular/material/chips";
import {ThresholdsHeaderComponent} from './component/thresholds/header/thresholds-header.component';
import {ListThresholdsComponent} from "./component/thresholds/list-thresholds/list-thresholds.component";
import {ThresholdsComponent} from './component/thresholds/thresholds.component';
import {MatDatepickerModule} from "@angular/material/datepicker";
import {ThresholdsWriteComponent} from './component/thresholds/write/thresholds-write.component';
import {MatDialogModule} from "@angular/material/dialog";
import {HeaderTransactionComponent} from './component/reconcile/header/header-transaction/header-transaction.component';
import {HeaderBaleComponent} from './component/reconcile/header/header-bale/header-bale.component';
import {HeaderAllComponent} from './component/reconcile/header/header-all/header-all.component';
import {MatButtonToggleModule} from "@angular/material/button-toggle";
import {AuditComponent} from './component/audit/audit.component';
import {ListAuditComponent} from './component/audit/list-audit/list-audit.component';
import {AuditHeaderComponent} from './component/audit/header/audit-header.component';


@NgModule({
  declarations: [
    ReconcileComponent,
    ReconcileHeaderComponent,
    ListBaleComponent,
    ListTransactionComponent,
    ListThresholdsComponent,
    ThresholdsHeaderComponent,
    ThresholdsComponent,
    ThresholdsWriteComponent,
    HeaderTransactionComponent,
    HeaderBaleComponent,
    HeaderAllComponent,
    AuditComponent,
    ListAuditComponent,
    AuditHeaderComponent
  ],
    imports: [
        CommonModule,
        ReconciliationRoutingModule,
        MatButtonModule,
        MatFormFieldModule,
        MatIconModule,
        MatInputModule,
        MatProgressBarModule,
        MatToolbarModule,
        TranslateModule,
        BaleModule,
        MatCheckboxModule,
        MatMenuModule,
        MatPaginatorModule,
        MatSortModule,
        MatTableModule,
        MatTooltipModule,
        FormsModule,
        MatCardModule,
        MatDividerModule,
        MatListModule,
        MatTabsModule,
        MatExpansionModule,
        ExtendedModule,
        FlexModule,
        MatOptionModule,
        MatProgressSpinnerModule,
        MatSelectModule,
        NgxMatSelectSearchModule,
        OverlayModule,
        ReactiveFormsModule,
        DragDropModule,
        SharedModule,
        MatSlideToggleModule,
        CsFinActiveFiltersModule,
        CsFinAddRemoveColumnsModule,
        CsFinAuthModule,
        AccountsModule,
        MatChipsModule,
        CsFinFlagModule,
        MatDatepickerModule,
        MatDialogModule,
        MatButtonToggleModule
    ]
})
export class ReconciliationModule { }
