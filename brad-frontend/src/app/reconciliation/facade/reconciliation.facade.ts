import {Injectable} from "@angular/core";
import {SelectionModel} from "@angular/cdk/collections";
import {BehaviorSubject, Observable, of, Subject} from "rxjs";
import {ReconciliationApiService} from "../../api/service/reconciliation-api.service";
import {SelectedIds} from "../../entities/reconciliation/selectedIds";
import {getPropertyKey} from "../../shared/service/ts-utils.service";
import {ReconciliationFilters} from "../../entities/reconciliation/reconciliation-filters";
import {CsFinActiveFilterChip} from "@jumia-cs-fin/common";
import {PageResponse} from "../../entities/page-response";
import {Reconciliation} from "../../entities/reconciliation/reconciliation";
import {SelectedAmountDifference} from "../../entities/reconciliation/selectedAmountDifference";
import {TransactionFilters} from "../../entities/transaction/transaction-filters";
import {BaleFilters} from "../../entities/bale/bale-filters";
import {ReconciliationStatusApiService} from "../../api/service/reconciliation-status-api.service";

@Injectable({providedIn: 'root'})
export class ReconciliationFacade {

  isBaleLoadingSetSubject: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);
  isTransactionLoadingSetSubject: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);

  selectionBale = new SelectionModel<any>(true, []);
  selectionBaleCache: Record<string, boolean> = {};
  private selectionBaleCacheSubject = new Subject<Record<string, boolean>>();

  selectionTransaction = new SelectionModel<any>(true, []);
  selectionTransactionCache: Record<string, boolean> = {};
  private selectionTransactionCacheSubject = new Subject<Record<string, boolean>>();


  public fullscreenChangeBehaviorSubject = new BehaviorSubject<boolean>(false);
  public detailsCloseBehaviorSubject = new BehaviorSubject<boolean>(false);

  readonly filterTextKey = getPropertyKey<ReconciliationFilters>(p => p.filterText);
  readonly idKey = getPropertyKey<ReconciliationFilters>(p=>p.id);
  readonly accountNumberKey = getPropertyKey<ReconciliationFilters>(p=>p.accountNumber);
  readonly statusKey = getPropertyKey<ReconciliationFilters>(p=>p.status);
  readonly creatorKey = getPropertyKey<ReconciliationFilters>(p=>p.creator);
  readonly creationDateStartKey = getPropertyKey<ReconciliationFilters>(p=>p.creationDateStart);
  readonly creationDateEndKey = getPropertyKey<ReconciliationFilters>(p=>p.creationDateEnd);
  readonly reviewerKey = getPropertyKey<ReconciliationFilters>(p=>p.reviewer);
  readonly reviewedDateStartKey = getPropertyKey<ReconciliationFilters>(p=>p.reviewedDateStart);
  readonly reviewedDateEndKey = getPropertyKey<ReconciliationFilters>(p=>p.reviewedDateEnd);
  readonly amountTransactionKey = getPropertyKey<ReconciliationFilters>(p=>p.amountTransaction);
  readonly amountBaleKey = getPropertyKey<ReconciliationFilters>(p=>p.amountBale);
  readonly amountThresholdKey = getPropertyKey<ReconciliationFilters>(p=>p.amountThreshold);
  readonly transactionIDKey = getPropertyKey<ReconciliationFilters>(p=>p.transactionID);
  readonly baleIDKey = getPropertyKey<ReconciliationFilters>(p=>p.baleID);


  private readonly activeFiltersConfigMap = new Map<string, any>([
    [
      this.filterTextKey,
      (filterText: string) => {
        return {labelKey: 'GENERAL.FIELDS.FILTER_TEXT', displayText: filterText};
      }
    ],
    [
      this.idKey,
      (id: number) => {
        return {labelKey: 'RECONCILIATION.FIELDS.ID', displayText: id};
      }
    ],
    [
      this.accountNumberKey,
      (accountNumber: string) => {
        return {labelKey: 'RECONCILIATION.FIELDS.ACCOUNT_NUMBER', displayText: accountNumber};
      }
    ],
    [
      this.statusKey,
      (status: string) => {
        return {labelKey: 'RECONCILIATION.FIELDS.STATUS', displayText: status};
      }
    ],
    [
      this.creatorKey,
      (creator: string) => {
        return {labelKey: 'RECONCILIATION.FIELDS.CREATOR', displayText: creator};
      }
    ],
    [
      this.creationDateStartKey,
      (creationDateStart: string) => {
        return {labelKey: 'RECONCILIATION.FIELDS.CREATION_DATE', displayText: creationDateStart};
      }
    ],
    [
      this.creationDateEndKey,
      (creationDateEnd: string) => {
        return {labelKey: 'RECONCILIATION.FIELDS.CREATION_DATE', displayText: creationDateEnd};
      }
    ],
    [
      this.reviewerKey,
      (reviewer: string) => {
        return {labelKey: 'RECONCILIATION.FIELDS.REVIEWER', displayText: reviewer};
      }
    ],
    [
      this.reviewedDateStartKey,
      (reviewedDateStart: string) => {
        return {labelKey: 'RECONCILIATION.FIELDS.REVIEWED_DATE', displayText: reviewedDateStart};
      }
    ],
    [
      this.reviewedDateEndKey,
      (reviewedDateEnd: string) => {
        return {labelKey: 'RECONCILIATION.FIELDS.REVIEWED_DATE', displayText: reviewedDateEnd};
      }
    ],
    [
      this.amountTransactionKey,
      (amountTransaction: number) => {
        return {labelKey: 'RECONCILIATION.FIELDS.AMOUNT_TRANSACTION', displayText: amountTransaction};
      }
    ],
    [
      this.amountBaleKey,
      (amountBale: number) => {
        return {labelKey: 'RECONCILIATION.FIELDS.AMOUNT_BALE', displayText: amountBale};
      }
    ],
    [
      this.amountThresholdKey,
      (amountThreshold: number) => {
        return {labelKey: 'RECONCILIATION.FIELDS.AMOUNT_THRESHOLD', displayText: amountThreshold};
      }
    ],
    [
      this.statusKey,
      (status: string) => {
        return {labelKey: 'RECONCILIATION.FIELDS.STATUS', displayText: status};
      }
    ],
    [
      this.transactionIDKey,
      (transactionIds: number[]) => {
        return {labelKey: 'RECONCILIATION.FIELDS.TRANSACTION_IDS', displayText: transactionIds};
      }
    ],
    [
      this.baleIDKey,
      (baleIds: number[]) => {
        return {labelKey: 'RECONCILIATION.FIELDS.BALE_IDS', displayText: baleIds};
      }
    ]

  ]);

  private filtersBehaviorSubject = new BehaviorSubject<ReconciliationFilters>({});
  private activeFilterChips = new Map<string, CsFinActiveFilterChip>();
  public filters$: Observable<ReconciliationFilters> = this.filtersBehaviorSubject.asObservable();
  public activeFiltersChips$: Observable<Map<string, CsFinActiveFilterChip>> = of(this.activeFilterChips);

  constructor(
    public reconciliationApiService: ReconciliationApiService,
    public reconciliationStatusApiService: ReconciliationStatusApiService
  ) {}

  filtersChanged(filters: ReconciliationFilters) {
    if (!filters.creationDateStart) {
      filters.creationDateStart = undefined;
      filters.creationDateEnd = undefined;
    }
    if (!filters.reviewedDateStart) {
      filters.reviewedDateStart = undefined;
      filters.reviewedDateEnd = undefined;
    }
    this.filtersBehaviorSubject.next({ ...filters });
    this.updateActiveFiltersChips(filters);
  }

  changeTransactionFilters(transactionFilters: TransactionFilters) {
    const filters : ReconciliationFilters = { ...this.filtersBehaviorSubject.getValue() };
    filters.transactionFilters = transactionFilters;
    this.filtersChanged(filters);
  }

  changeBaleFilters(baleFilters: BaleFilters) {
    const filters : ReconciliationFilters = { ...this.filtersBehaviorSubject.getValue() };
    filters.baleFilters = baleFilters;
    this.filtersChanged(filters);
  }

  private updateActiveFiltersChips(filters: ReconciliationFilters): void {
    if (!filters) {
      return;
    }
    this.activeFilterChips.clear();

    if(filters.filterText) {
      this.activeFilterChips.set(this.filterTextKey, this.activeFiltersConfigMap.get(this.filterTextKey)(filters.filterText));
    }
    if(filters.id) {
      this.activeFilterChips.set(this.idKey, this.activeFiltersConfigMap.get(this.idKey)(filters.id));
    }
    if(filters.accountNumber) {
      this.activeFilterChips.set(this.accountNumberKey, this.activeFiltersConfigMap.get(this.accountNumberKey)(filters.accountNumber));
    }
    if(filters.status && filters.status.length > 0) {
      this.activeFilterChips.set(this.statusKey, this.activeFiltersConfigMap.get(this.statusKey)(filters.status));
    }
    if(filters.creator) {
      this.activeFilterChips.set(this.creatorKey, this.activeFiltersConfigMap.get(this.creatorKey)(filters.creator));
    }
    if(filters.creationDateStart){
      let dateToDisplay = new Date(filters.creationDateStart!).toDateString();
      if (filters.creationDateEnd) {
        dateToDisplay += " - " + new Date(filters.creationDateEnd!).toDateString();
      }
      this.activeFilterChips.set(this.creationDateStartKey, this.activeFiltersConfigMap.get(this.creationDateStartKey)(dateToDisplay));
    }
    if(filters.reviewer) {
      this.activeFilterChips.set(this.reviewerKey, this.activeFiltersConfigMap.get(this.reviewerKey)(filters.reviewer));
    }
    if(filters.reviewedDateStart){
      let dateToDisplay = new Date(filters.reviewedDateStart!).toDateString();
      if (filters.reviewedDateEnd) {
        dateToDisplay += " - " + new Date(filters.reviewedDateEnd!).toDateString();
      }
      this.activeFilterChips.set(this.reviewedDateStartKey, this.activeFiltersConfigMap.get(this.reviewedDateStartKey)(dateToDisplay));
    }
    if(filters.amountTransaction) {
      this.activeFilterChips.set(this.amountTransactionKey, this.activeFiltersConfigMap.get(this.amountTransactionKey)(filters.amountTransaction));
    }
    if(filters.amountBale) {
      this.activeFilterChips.set(this.amountBaleKey, this.activeFiltersConfigMap.get(this.amountBaleKey)(filters.amountBale));
    }
    if(filters.amountThreshold) {
      this.activeFilterChips.set(this.amountThresholdKey, this.activeFiltersConfigMap.get(this.amountThresholdKey)(filters.amountThreshold));
    }
    if(filters.transactionID) {
      this.activeFilterChips.set(this.transactionIDKey, this.activeFiltersConfigMap.get(this.transactionIDKey)(filters.transactionID));
    }
    if(filters.baleID) {
      this.activeFilterChips.set(this.baleIDKey, this.activeFiltersConfigMap.get(this.baleIDKey)(filters.baleID));
    }
  }

  updateSelectionBaleCache(cache: Record<string, boolean>) {
    this.selectionBaleCache = cache;
    this.selectionBaleCacheSubject.next(this.selectionBaleCache);
  }

  updateSelectionTransactionCache(cache: Record<string, boolean>) {
    this.selectionTransactionCache = cache;
    this.selectionTransactionCacheSubject.next(this.selectionTransactionCache);
  }

  getSelectionBaleCacheUpdates() {
    return this.selectionBaleCacheSubject.asObservable();
  }

  getSelectionTransactionCacheUpdates() {
    return this.selectionTransactionCacheSubject.asObservable();
  }

  getAmountDifference(ids: SelectedIds): Observable<SelectedAmountDifference> {
    return this.reconciliationApiService.getAmountDifference(ids);
  }

  reconcile(ids: SelectedIds) {
    return this.reconciliationApiService.createReconciliation(ids);
  }

  approveReconciliationBySelectedIds(ids: SelectedIds): Observable<void> {
    return this.reconciliationApiService.approveReconciliation(ids);
  }

  unmatchReconciliationBySelectedIds(ids: SelectedIds): Observable<void> {
    return this.reconciliationApiService.unmatchReconciliation(ids);
  }

  getAllReconciliations(filters?: ReconciliationFilters): Observable<PageResponse<Reconciliation>> {
    return this.reconciliationApiService.getAllReconciliations(filters);
  }

  getReconciliation(id: Number): Observable<Reconciliation> {
    return this.reconciliationApiService.getReconciliation(id);
  }

  fetchReconciliationByBaleId(id: Number): Observable<Reconciliation> {
    return this.reconciliationApiService.fetchReconciliationByBaleId(id);
  }

  fetchReconciliationByTransactionId(id: Number): Observable<Reconciliation> {
    return this.reconciliationApiService.fetchReconciliationByTransactionId(id);
  }

  getAllReconciliationStatus():Observable<String[]>{
    return this.reconciliationStatusApiService.getAll();
  }

}
