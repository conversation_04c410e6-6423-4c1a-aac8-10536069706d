<main class="container" cdkDropListGroup>
  <mat-card class="table-cards">
    <section class="title-expansion">
      <mat-accordion>
        <mat-expansion-panel>
          <mat-expansion-panel-header>
            <mat-panel-title class="title-panel">
              <span>{{"RECONCILIATION.TRANSACTIONS_TITLE" | translate }}</span>
              <div class="amountsAndQuantities">
                <div>
                  <span class="bold-title">Nº of Transactions: </span>
                  <span>{{transactionQuantity}} Transactions</span>
                </div>
                <span class="divider"> / </span>
                <div>
                  <span class="bold-title">Total Amount: </span>
                  <span>{{transactionAmount | number:'1.2-2'}} {{account.currency.code}}</span>
                </div>
              </div>
            </mat-panel-title>
          </mat-expansion-panel-header>
          <cs-fin-active-filters [chipsMap]="activeFilterChips"
                                 (onRemove)="onActiveFilterRemoveClick($event)">
          </cs-fin-active-filters>
        </mat-expansion-panel>
      </mat-accordion>
    </section>
    <section class="filter-dropdown filters" *ngIf="dataSource">
      <cs-fin-drop-group-zone
        [groupByColumnDetails]="groupByColumnDetails"
        [displayedColumns]="displayedColumns"
        [groupByColumns]="groupByColumns"
        [expandedGroupNames]="expandedGroupNames"
        (groupOnClick)="groupOnClick()"></cs-fin-drop-group-zone>
      <button mat-stroked-button id="change-columns-btn" color="primary" fxShow [fxHide.xs]="true" [fxHide.sm]="true"
              [disabled]="isGroupedView" cdkOverlayOrigin #trigger="cdkOverlayOrigin"
              (click)="triggerOverlay(trigger)">
        <mat-icon>table_rows</mat-icon>
      </button>
    </section>

    <section class="table-detail responsive-table">
      <mat-progress-bar mode="indeterminate" *ngIf="isLoading"></mat-progress-bar>

      <div class="table-container" *ngIf="!isLoading">

        <table mat-table class="cs-fin-table" matSort multiTemplateDataRows [dataSource]="dataSource" [class.loading]="isLoading"
               (matSortChange)="onSortChange($event)" cdkDropList cdkDropListSortingDisabled>

          <ng-container matColumnDef="select">
            <th mat-header-cell *matHeaderCellDef></th>
            <td mat-cell *matCellDef="let transaction" [ngClass]="{'select-styling': isGroupedView}">
              <mat-checkbox [checked]="reconciliationFacade.selectionTransaction.isSelected(transaction.id)"
                            [aria-label]="checkboxLabel(transaction)"
                            [disabled]="!sameStatusAsCurrentSelection(transaction)"
                            (click)="onRowClick($event, transaction)">
              </mat-checkbox>
            </td>
          </ng-container>

          <ng-container matColumnDef="id">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              {{ 'TRANSACTIONS.FIELDS.ID' | translate }}
            </th>
            <td mat-cell *matCellDef="let transaction">
                {{transaction?.type}}{{transaction?.id}}
            </td>
          </ng-container>

          <ng-container matColumnDef="accountID">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag [cdkDragData]="'ACCOUNT_ID'"
                [cdkDragDisabled]="!groupKeyExists('ACCOUNT_ID')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('ACCOUNT_ID')">drag_indicator</mat-icon>
              {{ 'TRANSACTIONS.FIELDS.ACCOUNT_ID' | translate }}

              <mat-chip *cdkDragPreview>{{ 'TRANSACTIONS.FIELDS.ACCOUNT_ID' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let transaction">
                {{transaction?.accountID}}
            </td>
          </ng-container>

          <ng-container matColumnDef="currency">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag [cdkDragData]="'CURRENCY'"
                [cdkDragDisabled]="!groupKeyExists('CURRENCY')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('CURRENCY')">drag_indicator</mat-icon>
              {{ 'TRANSACTIONS.FIELDS.CURRENCY' | translate }}

              <mat-chip *cdkDragPreview>{{ 'TRANSACTIONS.FIELDS.CURRENCY' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let transaction">
                {{transaction?.currency.code}}
            </td>
          </ng-container>

          <ng-container matColumnDef="valueDate">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag class="header-wrap" [cdkDragData]="'VALUE_DATE'"
                [cdkDragDisabled]="!groupKeyExists('VALUE_DATE')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('VALUE_DATE')">drag_indicator</mat-icon>
              {{ 'TRANSACTIONS.FIELDS.VALUE_DATE' | translate }}

              <mat-chip *cdkDragPreview>{{ 'TRANSACTIONS.FIELDS.VALUE_DATE' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let transaction">
                {{transaction?.valueDate | date:'dd/MM/yyyy'}}
            </td>
          </ng-container>

          <ng-container matColumnDef="transactionDate">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag class="header-wrap" [cdkDragData]="'TRANSACTION_DATE'"
                [cdkDragDisabled]="!groupKeyExists('TRANSACTION_DATE')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('TRANSACTION_DATE')">drag_indicator</mat-icon>
              {{ 'TRANSACTIONS.FIELDS.TRANSACTION_DATE' | translate }}

              <mat-chip *cdkDragPreview>{{ 'TRANSACTIONS.FIELDS.TRANSACTION_DATE' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let transaction">
                {{transaction?.transactionDate | date:'dd/MM/yyyy'}}
            </td>
          </ng-container>

          <ng-container matColumnDef="statementDate">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag class="header-wrap" [cdkDragData]="'STATEMENT_DATE'"
                [cdkDragDisabled]="!groupKeyExists('STATEMENT_DATE')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('STATEMENT_DATE')">drag_indicator</mat-icon>
              {{ 'TRANSACTIONS.FIELDS.STATEMENT_DATE' | translate }}

              <mat-chip *cdkDragPreview>{{ 'TRANSACTIONS.FIELDS.STATEMENT_DATE' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let transaction">
                {{transaction?.statementDate | date:'dd/MM/yyyy'}}
            </td>
          </ng-container>

          <ng-container matColumnDef="direction">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag [cdkDragData]="'DIRECTION'"
              [cdkDragDisabled]="!groupKeyExists('DIRECTION')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('DIRECTION')">drag_indicator</mat-icon>
              {{ 'TRANSACTIONS.FIELDS.DIRECTION' | translate }}

              <mat-chip *cdkDragPreview>{{ 'TRANSACTIONS.FIELDS.DIRECTION' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let transaction">
                {{transaction?.direction}}
            </td>
          </ng-container>

          <ng-container matColumnDef="amount">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag [cdkDragData]="'AMOUNT'"
                [cdkDragDisabled]="!groupKeyExists('AMOUNT')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('AMOUNT')">drag_indicator</mat-icon>
              {{ 'TRANSACTIONS.FIELDS.AMOUNT' | translate }}

              <mat-chip *cdkDragPreview>{{ 'TRANSACTIONS.FIELDS.AMOUNT' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let transaction" matTooltip="{{transaction?.amount | number:'1.2-2'}}">
              <div class="amountWithUsd">
                {{transaction?.amount | number:'1.2-2'}} {{transaction?.currency.symbol}}
                <div *ngIf="transaction?.amountUsd else emptyAmount;">
                  {{transaction?.amountUsd | number:'1.2-2'}} $
                </div>
              </div>
            </td>
          </ng-container>

          <ng-container matColumnDef="reference">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag [cdkDragData]="'REFERENCE'"
                [cdkDragDisabled]="!groupKeyExists('REFERENCE')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('REFERENCE')">drag_indicator</mat-icon>
              {{ 'TRANSACTIONS.FIELDS.REFERENCE' | translate }}

              <mat-chip *cdkDragPreview>{{ 'TRANSACTIONS.FIELDS.REFERENCE' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let transaction" matTooltip="{{transaction?.reference}}">
                {{transaction?.reference}}
            </td>
          </ng-container>

          <ng-container matColumnDef="description">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag [cdkDragData]="'DESCRIPTION'"
                [cdkDragDisabled]="!groupKeyExists('DESCRIPTION')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('DESCRIPTION')">drag_indicator</mat-icon>
              {{ 'TRANSACTIONS.FIELDS.DESCRIPTION' | translate }}

              <mat-chip *cdkDragPreview>{{ 'TRANSACTIONS.FIELDS.DESCRIPTION' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let transaction" matTooltip="{{transaction?.description}}">
              <div>{{transaction?.description}}</div>
            </td>
          </ng-container>

          <ng-container matColumnDef="accountStatementID">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag class="header-wrap" [cdkDragData]="'STATEMENT'"
                [cdkDragDisabled]="!groupKeyExists('STATEMENT')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('STATEMENT')">drag_indicator</mat-icon>
              {{ 'TRANSACTIONS.FIELDS.STATEMENT_ID' | translate }}

              <mat-chip *cdkDragPreview>{{ 'TRANSACTIONS.FIELDS.STATEMENT_ID' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let transaction">
                {{transaction?.accountStatementID}}
            </td>
          </ng-container>

          <ng-container matColumnDef="reconcileStatus">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag [cdkDragData]="'reconcileStatus'"
                [cdkDragDisabled]="!groupKeyExists('RECONCILE_STATUS')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('RECONCILE_STATUS')">drag_indicator</mat-icon>
              {{ 'TRANSACTIONS.FIELDS.RECONCILE_STATUS' | translate }}

              <mat-chip *cdkDragPreview>{{ 'TRANSACTIONS.FIELDS.RECONCILE_STATUS' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let transaction" matTooltip="{{transaction.reconcileStatus}}">
              {{transaction.reconcileStatus}}
            </td>
          </ng-container>

          <ng-container matColumnDef="createdAt">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag class="header-wrap" [cdkDragData]="'CREATED_AT'"
                [cdkDragDisabled]="!groupKeyExists('CREATED_AT')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('CREATED_AT')">drag_indicator</mat-icon>
              {{ 'TRANSACTIONS.FIELDS.CREATED_AT' | translate }}

              <mat-chip *cdkDragPreview>{{ 'TRANSACTIONS.FIELDS.CREATED_AT' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let transaction">
                {{transaction?.createdAt | date:'medium'}}
            </td>
          </ng-container>

          <ng-container matColumnDef="createdBy">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag class="header-wrap" [cdkDragData]="'CREATED_BY'"
                [cdkDragDisabled]="!groupKeyExists('CREATED_BY')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('CREATED_BY')">drag_indicator</mat-icon>
              {{ 'TRANSACTIONS.FIELDS.CREATED_BY' | translate }}

              <mat-chip *cdkDragPreview>{{ 'TRANSACTIONS.FIELDS.CREATED_BY' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let transaction">
                {{transaction?.createdBy}}
            </td>
          </ng-container>

          <ng-container matColumnDef="updatedAt">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag class="header-wrap" [cdkDragData]="'UPDATED_AT'"
                [cdkDragDisabled]="!groupKeyExists('UPDATED_AT')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('UPDATED_AT')">drag_indicator</mat-icon>
              {{ 'TRANSACTIONS.FIELDS.UPDATED_AT' | translate }}

              <mat-chip *cdkDragPreview>{{ 'TRANSACTIONS.FIELDS.UPDATED_AT' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let transaction">
                {{transaction?.updatedAt | date:'medium'}}
            </td>
          </ng-container>

          <ng-container matColumnDef="updatedBy">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag class="header-wrap" [cdkDragData]="'UPDATED_BY'"
                [cdkDragDisabled]="!groupKeyExists('UPDATED_BY')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('UPDATED_BY')">drag_indicator</mat-icon>
              {{ 'TRANSACTIONS.FIELDS.UPDATED_BY' | translate }}

              <mat-chip *cdkDragPreview>{{ 'TRANSACTIONS.FIELDS.UPDATED_BY' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let transaction">
                {{transaction?.updatedBy}}
            </td>
          </ng-container>

          <ng-container matColumnDef="reconciliationId">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag class="header-wrap" [cdkDragData]="'RECONCILIATION_ID'"
                [cdkDragDisabled]="!groupKeyExists('RECONCILIATION_ID')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('RECONCILIATION_ID')">drag_indicator</mat-icon>
              {{ 'RECONCILIATION.FIELDS.RECONCILIATION_ID' | translate }}

              <mat-chip *cdkDragPreview>{{ 'RECONCILIATION.FIELDS.ID' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let transaction">
              {{transaction?.reconciliationId}}
            </td>
          </ng-container>

          <ng-container matColumnDef="reconciliationCreator">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag class="header-wrap" [cdkDragData]="'RECONCILIATION_CREATOR'"
                [cdkDragDisabled]="!groupKeyExists('RECONCILIATION_CREATOR')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('RECONCILIATION_CREATOR')">drag_indicator</mat-icon>
              {{ 'RECONCILIATION.FIELDS.CREATOR' | translate }}

              <mat-chip *cdkDragPreview>{{ 'RECONCILIATION.FIELDS.CREATOR' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let transaction">
              {{transaction?.reconciliationCreator}}
            </td>
          </ng-container>

          <ng-container matColumnDef="reconciliationCreationDate">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag class="header-wrap" [cdkDragData]="'RECONCILIATION_CREATION_DATE'"
                [cdkDragDisabled]="!groupKeyExists('RECONCILIATION_CREATION_DATE')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('RECONCILIATION_CREATION_DATE')">drag_indicator</mat-icon>
              {{ 'RECONCILIATION.FIELDS.RECONCILIATION_CREATION_DATE' | translate }}

              <mat-chip *cdkDragPreview>{{ 'RECONCILIATION.FIELDS.CREATION_DATE' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let transaction">
              {{transaction?.reconciliationCreationDate}}
            </td>
          </ng-container>

          <ng-container matColumnDef="reconciliationReviewer">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag class="header-wrap" [cdkDragData]="'RECONCILIATION_REVIEWER'"
                [cdkDragDisabled]="!groupKeyExists('RECONCILIATION_REVIEWER')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('RECONCILIATION_REVIEWER')">drag_indicator</mat-icon>
              {{ 'RECONCILIATION.FIELDS.REVIEWER' | translate }}

              <mat-chip *cdkDragPreview>{{ 'RECONCILIATION.FIELDS.REVIEWER' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let transaction">
              {{transaction?.reconciliationReviewer}}
            </td>
          </ng-container>

          <ng-container matColumnDef="reconciliationReviewDate">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag class="header-wrap" [cdkDragData]="'RECONCILIATION_REVIEW_DATE'"
                [cdkDragDisabled]="!groupKeyExists('RECONCILIATION_REVIEW_DATE')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('RECONCILIATION_REVIEW_DATE')">drag_indicator</mat-icon>
              {{ 'RECONCILIATION.FIELDS.REVIEW_DATE' | translate }}

              <mat-chip *cdkDragPreview>{{ 'RECONCILIATION.FIELDS.REVIEW_DATE' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let transaction">
              {{transaction?.reconciliationReviewDate}}
            </td>
          </ng-container>

          <ng-container matColumnDef="expandedDetail">
            <td mat-cell *matCellDef="let row" [attr.colspan]="displayedColumns.length">
              <div [@detailExpand]="row == expandedElement ? 'expanded' : 'collapsed'" class="expanded-details">
                <div class="left-column">
                  <div class="attribute">
                    <span class="attribute-title">{{ 'TRANSACTIONS.FIELDS.CURRENCY' | translate }}</span>
                    <span class="attribute-value">{{row.currency?.code}}</span>
                  </div>
                  <div class="attribute">
                    <span class="attribute-title">{{ 'TRANSACTIONS.FIELDS.STATEMENT_DATE' | translate }}</span>
                    <span class="attribute-value">{{row.statementDate}}</span>
                  </div>
                </div>
                <div class="right-column">
                  <div class="attribute">
                    <span class="attribute-title">{{ 'TRANSACTIONS.FIELDS.STATEMENT_ID' | translate }}</span>
                    <span class="attribute-value">{{row.accountStatementID}}</span>
                  </div>
                  <div class="attribute">
                    <span class="attribute-title">{{ 'TRANSACTIONS.FIELDS.RECONCILE_STATUS' | translate }}</span>
                    <span class="attribute-value">{{row.reconcileStatus}}</span>
                  </div>
                </div>
              </div>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns when:csFinGroupService.notGroupOrPaginator"
              [ngClass]="getRowClass(row)"
              class="with-details"
              [class.expanded-row]="expandedElement === row"
              [class.disabled]="!sameStatusAsCurrentSelection(row) || !belongsToReconciliation(row)"
              [class.enabled]="sameStatusAsCurrentSelection(row) && belongsToReconciliation(row)"
              (click)="expandedElement = expandedElement === row ? null : row">
          </tr>
          <tr mat-row *matRowDef="let row; columns: ['expandedDetail']" class="expanded-row"></tr>



          <ng-container matColumnDef="groupHeader">
            <td mat-cell *matCellDef="let group" (click)="expandGroup(group)" [colSpan]="getMaxSpan()">
              <div class="group-row">
                <mat-checkbox id="group-checkbox"
                              [indeterminate]="indeterminate(group)"
                              (change)="onCheckboxSelectAll($event, group)"
                              (click)="$event.stopPropagation()"
                              [checked]="isGroupSelected(group)"
                              [disabled]="disableGroup(group)"
                              [ngClass]="{'indeterminate-checkbox': indeterminate(group),
                                          'disabled-checkbox':disableGroup(group)}">
                </mat-checkbox>
                <button mat-icon-button id="expand-button">
                  <mat-icon *ngIf="group.expanded">expand_less</mat-icon>
                  <mat-icon *ngIf="!group.expanded">expand_more</mat-icon>
                </button>
                <div class="group-name-elements">
                  <ng-container *ngFor="let name of getGroupNameTrimmed(group.groupFields)">
                    <span class="name-square" [title]="name.full">{{name.trimmed}}</span>
                  </ng-container>
                </div>
                <div class="group-information">
                  <span class="group-count">
                    <span class="group-info-title">Nº of Transactions: </span>
                    <span>{{group.totalRecords}}</span>
                  </span>
                  <span class="line">
                    <span class="group-info-title">Total Amount: </span>
                    <span>{{group.totalAmount | number:'1.2-2'}} {{account.currency.symbol}}</span>
                  </span>
                </div>
              </div>
            </td>
          </ng-container>

          <ng-container matColumnDef="groupPaginator">
            <td mat-cell *matCellDef="let paginator" [colSpan]="getMaxSpan()" class="cs-fin-paginator">
              <mat-paginator
                [pageSizeOptions]="pagination.pageSizeOptions"
                [pageSize]="paginator.pageSize"
                [length]="paginator.totalRecords"
                [pageIndex]="paginator.pageIndex"
                (page)="onGroupPageChange($event, paginator)"
                showFirstLastButtons>
              </mat-paginator>
            </td>
          </ng-container>

          <tr mat-row class="group-mat-row" *matRowDef="let row; columns: ['groupHeader'] when:csFinGroupService.isGroup"></tr>
          <tr mat-row *matRowDef="let paginator; columns: ['groupPaginator'] when:csFinGroupService.isPaginator"></tr>


        </table>
        <span fxFill></span>
        <mat-paginator class="table-paginator-reconciliation"
                       *ngIf="!isGroupedView"
                       [pageSizeOptions]="pagination.pageSizeOptions"
                       [pageSize]="pagination.pageSize"
                       [length]="pagination.totalItems"
                       [pageIndex]="pagination.pageIndex"
                       (page)="onPageChange($event)"
                       showFirstLastButtons>
        </mat-paginator>
        <span id="empty-table" *ngIf="!isLoading && !dataSource.data.length">
          {{'GENERAL.TABLE.NO_RESULTS_FOUND' | translate}}
        </span>
      </div>
    </section>
    <cs-fin-add-remove-columns [triggerOrigin]="triggerOrigin"
                               [isOverlayOpen$]="isOverlayOpen$"
                               [menu]="MENU">
    </cs-fin-add-remove-columns>
  </mat-card>
</main>
<ng-template #emptyAmount>
</ng-template>
