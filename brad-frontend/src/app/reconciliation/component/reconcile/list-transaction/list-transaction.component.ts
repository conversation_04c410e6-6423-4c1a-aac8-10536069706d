import {ChangeDetectorRef, Component, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild} from '@angular/core';
import {MatPaginator} from "@angular/material/paginator";
import {MatSort, SortDirection} from "@angular/material/sort";
import {
  activeFiltersSeparator,
  CsFinActiveFilterChip,
  CsFinAddRemoveColumns,
  CsFinAddRemoveColumnsFacade,
  CsFinColumnDetails,
  CsFinLastUsedColumns,
  CsFinPagination
} from "@jumia-cs-fin/common";
import {MenusConsts} from "../../../../shared/constants/menus.constants";
import {MatTableDataSource} from "@angular/material/table";
import {paginationConsts} from "../../../../shared/constants/core.constants";
import {Observable, Subject, takeWhile} from "rxjs";
import {CdkOverlayOrigin} from "@angular/cdk/overlay";
import {MediaMatcher} from "@angular/cdk/layout";
import {ActivatedRoute, Router} from "@angular/router";
import * as _ from 'lodash';
import {finalize, takeUntil} from "rxjs/operators";
import {PageResponse} from "../../../../entities/page-response";
import {SortFilters} from "../../../../entities/SortFilters";
import {Transaction} from "../../../../entities/transaction/transaction";
import {ReconciliationFacade} from "../../../facade/reconciliation.facade";
import {authParams} from "../../../../auth/constants/auth.constants";
import {TransactionFacade} from "../../../../accounts/facade/transaction.facade";
import {TransactionFilters} from "../../../../entities/transaction/transaction-filters";
import {Account} from "../../../../entities/account/account";
import {
  CsFinGroupElement,
  CsFinGroupEntity,
  CsFinRowType,
  CsFinTableGroup,
  CsFinTablePaginator
} from "../../../../entities/cs-fin-table-group";
import {MatCheckboxChange} from "@angular/material/checkbox";
import {CsFinGroupService} from "../../../../api/service/cs-fin-group.service";
import {trigger, state, style, transition, animate} from "@angular/animations";

@Component({
  selector: 'brad-reconciliation-reconcile-list-transaction',
  templateUrl: './list-transaction.component.html',
  styleUrls: ['./list-transaction.component.scss'],
  providers: [CsFinAddRemoveColumnsFacade],
  animations: [
    trigger('detailExpand', [
      state('collapsed,void', style({height: '0px', minHeight: '0'})),
      state('expanded', style({height: '*'})),
      transition('expanded <=> collapsed', animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)')),
    ]),
  ],
})
export class ListTransactionComponent implements OnInit, OnDestroy {
  @ViewChild(MatPaginator, {static: true}) paginator!: MatPaginator;
  @ViewChild(MatSort, {static: true}) sort!: MatSort;

  @Input() account!: Account;
  @Input() selectedStatus?: string;
  @Input() transactionAmount!: number;
  @Input() transactionQuantity!: number;
  @Output() selectedStatusChange = new EventEmitter<string>();
  @Output() selectedReconciliationTransaction = new EventEmitter<{row: Transaction, selected: boolean, isPending:boolean}>();

  readonly MENU = MenusConsts.transactionAlt;
  auth = authParams;

  selectingReconciliation: boolean = false;

  isGroupedView: boolean = false;
  isLoading = true;
  dataSource: MatTableDataSource<CsFinGroupElement<Transaction, TransactionFilters>> = new MatTableDataSource<CsFinGroupElement<Transaction, TransactionFilters>>([]);
  expandedElement!: CsFinGroupElement<Transaction, TransactionFilters> | null;
  displayedColumns: string[] = [];
  defaultDisplayedColumns: string[] = [];
  groupByColumnDetails: Map<string, CsFinColumnDetails> = new Map<string, CsFinColumnDetails>();
  mobileQuery: MediaQueryList;
  pagination: CsFinPagination = {
    pageSizeOptions: paginationConsts.pageSizeOptions,
    pageSize: paginationConsts.defaultPageSize,
    pageIndex: 0,
    totalItems: 0
  };
  activeFilterChips!: Map<string, CsFinActiveFilterChip>;
  groupByColumns: string[] = [];

  isOverlayOpen$!: Observable<boolean>;
  triggerOrigin!: CdkOverlayOrigin;
  private _mobileQueryListener: () => void;
  private _onDestroy:Subject<void> = new Subject<void>();

  private filters: TransactionFilters = {
    page: 1,
    size: paginationConsts.defaultPageSize
  };

  amountSelected = 0;
  transactionGroupableFields: string[] = [];
  expandedGroupNames: string[] = [];

  constructor(
    public ref: ChangeDetectorRef,
    public media: MediaMatcher,
    private route: ActivatedRoute,
    private router: Router,
    private transactionFacade: TransactionFacade,
    private addRemoveColumnsFacade: CsFinAddRemoveColumnsFacade,
    public reconciliationFacade: ReconciliationFacade,
    public csFinGroupService: CsFinGroupService
  ) {
    this.mobileQuery = media.matchMedia('(max-width: 960px)');
    this._mobileQueryListener = () => ref.detectChanges();
    this.mobileQuery?.addEventListener('change', this._mobileQueryListener);

    CsFinLastUsedColumns.getInstance().initColumns(this.MENU, this.getColumnDetails());
  }

  ngOnInit(): void {
    this.isLoading = true;
    this.transactionFacade.hideUnwantedFilters.next(true);
    this.subscribeDisplayedColumnsChange();
    this.subscribeFiltersChange();
    this.subscribeActiveFilterChipsChange();
    this.loadTransactionGroupableFields();
    this.subscribeAmountSelectedChanges();
    this.setupGroupByColumnDetails();
  }

  ngOnDestroy(): void {
    this.transactionFacade.filtersChanged({});
    this.mobileQuery?.removeEventListener('change', this._mobileQueryListener);
    this._onDestroy.next();
    this._onDestroy.complete();
    this.addRemoveColumnsFacade.isOverlayOpen.next(false);
  }

  updateSelectionCache(row: any, selected: boolean) {
    if (this.selectedStatus && selected && this.selectedStatus !== row.reconcileStatus) {
      return;
    }
    this.selectedStatus = row.reconcileStatus;
    this.selectedStatusChange.emit(row.reconcileStatus);
    const key = row.id;

    this.reconciliationFacade.updateSelectionTransactionCache({
      ...this.reconciliationFacade.selectionTransactionCache,
      [key]: selected
    })

  }

  bulkUpdateSelectionCache(rowIds: any[], selected: boolean) {
    let updatedCache: Record<string, boolean> = {};
    rowIds.forEach((rowId: any) => {
      updatedCache[rowId] = selected;
    });
    this.reconciliationFacade.updateSelectionTransactionCache({
      ...this.reconciliationFacade.selectionTransactionCache,
      ...updatedCache
    });
  }

  private subscribeFiltersChange(): void {
    this.transactionFacade.filters$
      .pipe(takeUntil(this._onDestroy))
      .subscribe(async (filters: TransactionFilters) => {
        if (filters) {
          this.filters = filters;
          if (this.isGroupedView) {
            this.loadTransactionGroups();
          } else {
            this.loadTransactions();
          }
        }
      });
  }


  private subscribeActiveFilterChipsChange(): void {
    this.transactionFacade.activeFiltersChips$
      .pipe(takeUntil(this._onDestroy))
      .subscribe((activeFilterChips: Map<string, CsFinActiveFilterChip>) => {
        this.activeFilterChips = activeFilterChips;
      });
  }

  private subscribeDisplayedColumnsChange(): void {

    this.addRemoveColumnsFacade.fetchDisplayedColumns
      .pipe(takeUntil(this._onDestroy))
      .subscribe((columns: CsFinAddRemoveColumns) => {
        setTimeout(() => {
            if (columns) {
              this.displayedColumns = columns.displayedColumns ? columns.displayedColumns : [];
              this.defaultDisplayedColumns = this.displayedColumns;
              this.loadTransactions();
            }
          }
          , 0);
      });
  }


  setupGroupByColumnDetails() {
    let columnDetails = this.getColumnDetails();
    this.groupByColumnDetails.set('ACCOUNT_ID', columnDetails.filter((column: CsFinColumnDetails) => column.code === 'accountID')[0]);
    this.groupByColumnDetails.set('CURRENCY', columnDetails.filter((column: CsFinColumnDetails) => column.code === 'currency')[0]);
    this.groupByColumnDetails.set('VALUE_DATE', columnDetails.filter((column: CsFinColumnDetails) => column.code === 'valueDate')[0]);
    this.groupByColumnDetails.set('TRANSACTION_DATE', columnDetails.filter((column: CsFinColumnDetails) => column.code === 'transactionDate')[0]);
    this.groupByColumnDetails.set('STATEMENT_DATE', columnDetails.filter((column: CsFinColumnDetails) => column.code === 'statementDate')[0]);
    this.groupByColumnDetails.set('DIRECTION', columnDetails.filter((column: CsFinColumnDetails) => column.code === 'direction')[0]);
    this.groupByColumnDetails.set('AMOUNT', columnDetails.filter((column: CsFinColumnDetails) => column.code === 'amount')[0]);
    this.groupByColumnDetails.set('REFERENCE', columnDetails.filter((column: CsFinColumnDetails) => column.code === 'reference')[0]);
    this.groupByColumnDetails.set('DESCRIPTION', columnDetails.filter((column: CsFinColumnDetails) => column.code === 'description')[0]);
    this.groupByColumnDetails.set('STATEMENT_ID', columnDetails.filter((column: CsFinColumnDetails) => column.code === 'accountStatementID')[0]);
    this.groupByColumnDetails.set('CREATED_AT', columnDetails.filter((column: CsFinColumnDetails) => column.code === 'createdAt')[0]);
    this.groupByColumnDetails.set('CREATED_BY', columnDetails.filter((column: CsFinColumnDetails) => column.code === 'createdBy')[0]);
    this.groupByColumnDetails.set('UPDATED_AT', columnDetails.filter((column: CsFinColumnDetails) => column.code === 'updatedAt')[0]);
    this.groupByColumnDetails.set('UPDATED_BY', columnDetails.filter((column: CsFinColumnDetails) => column.code === 'updatedBy')[0]);
    this.groupByColumnDetails.set('RECONCILE_STATUS', columnDetails.filter((column: CsFinColumnDetails) => column.code === 'reconcileStatus')[0]);
    this.groupByColumnDetails.set('RECONCILIATION_ID', columnDetails.filter((column: CsFinColumnDetails) => column.code === 'reconciliationId')[0]);
    this.groupByColumnDetails.set('RECONCILIATION_CREATOR', columnDetails.filter((column: CsFinColumnDetails) => column.code === 'reconciliationCreator')[0]);
    this.groupByColumnDetails.set('RECONCILIATION_CREATION_DATE', columnDetails.filter((column: CsFinColumnDetails) => column.code === 'reconciliationCreationDate')[0]);
    this.groupByColumnDetails.set('RECONCILIATION_REVIEWER', columnDetails.filter((column: CsFinColumnDetails) => column.code === 'reconciliationReviewer')[0]);
    this.groupByColumnDetails.set('RECONCILIATION_REVIEW_DATE', columnDetails.filter((column: CsFinColumnDetails) => column.code === 'reconciliationReviewDate')[0]);
  }

  getColumnDetails(): CsFinColumnDetails[] {
    return [
      {position: 0, name: 'Select', code: 'select', isActive: true, isRemovable: false, isDefault: true},
      {position: 1, name: 'ID', code: 'id', isActive: true, isRemovable: true, isDefault: true},
      {position: 2, name: 'Transaction Date', code: 'transactionDate', isActive: true, isRemovable: true, isDefault: true},
      {position: 3, name: 'Description', code: 'description', isActive: true, isRemovable: true, isDefault: true},
      {position: 4, name: 'Direction', code: 'direction', isActive: true, isRemovable: true, isDefault: true},
      {position: 5, name: 'Amount', code: 'amount', isActive: true, isRemovable: true, isDefault: true},
      {position: 6, name: 'Reference', code: 'reference', isActive: true, isRemovable: true, isDefault: true},
      {position: 7, name: 'Account ID', code: 'accountID', isActive: false, isRemovable: true, isDefault: false},
      {position: 8, name: 'Currency', code: 'currency', isActive: false, isRemovable: true, isDefault: false},
      {position: 9, name: 'Value Date', code: 'valueDate', isActive: true, isRemovable: true, isDefault: true},
      {position: 10, name: 'Statement Date', code: 'statementDate', isActive: false, isRemovable: true, isDefault: false},
      {position: 11, name: 'Statement ID', code: 'accountStatementID', isActive: false, isRemovable: true, isDefault: false},
      {position: 12, name: 'Reconcile Status', code: 'reconcileStatus', isActive: false, isRemovable: true, isDefault: false},
      {position: 13, name: 'Created At', code: 'createdAt', isActive: false, isRemovable: true, isDefault: false},
      {position: 14, name: 'Created By', code: 'createdBy', isActive: false, isRemovable: true, isDefault: false},
      {position: 15, name: 'Updated At', code: 'updatedAt', isActive: false, isRemovable: true, isDefault: false},
      {position: 16, name: 'Updated By', code: 'updatedBy', isActive: false, isRemovable: true, isDefault: false},
      {position: 17, name: 'Reconciliation ID', code: 'reconciliationId', isActive: false, isRemovable: true, isDefault: false},
      {position: 18, name: 'Reconciliation Creator', code: 'reconciliationCreator', isActive: false, isRemovable: true, isDefault: false},
      {position: 19, name: 'Reconciliation Creation Date', code: 'reconciliationCreationDate', isActive: false, isRemovable: true, isDefault: false},
      {position: 20, name: 'Reconciliation Reviewer', code: 'reconciliationReviewer', isActive: false, isRemovable: true, isDefault: false},
      {position: 21, name: 'Reconciliation Review Date', code: 'reconciliationReviewDate', isActive: false, isRemovable: true, isDefault: false}
    ];
  }

  onActiveFilterRemoveClick(removedChipKey: string): void {
    const filtersToRemove = removedChipKey.split(activeFiltersSeparator);
    if (filtersToRemove) {
      filtersToRemove.forEach((filterKey: string) => {
        // @ts-ignore
        delete this.filters[filterKey];
      })
    }
    if (filtersToRemove.includes("valueDateStart")) {
      delete this.filters.valueDateEnd;
    }
    if (filtersToRemove.includes("transactionDateStart")) {
      delete this.filters.transactionDateEnd;
    }
    if (filtersToRemove.includes("statementDateStart")) {
      delete this.filters.statementDateEnd;
    }
    if (filtersToRemove.includes("createdAtStart")) {
      delete this.filters.createdAtEnd;
    }
    this.reconciliationFacade.changeTransactionFilters(this.filters);
  }

  loadTransactionsOrGroups() {
    if (this.isGroupedView) {
      this.loadTransactionGroups();
    } else {
      this.loadTransactions();
    }
  }

  loadTransactions() {
    this.filters.accountId = this.account.accountNumber;
    this.filters.importedStatementOnly = true;
    this.transactionFacade.getAll(this.filters)
      .pipe(takeUntil(this._onDestroy))
      .pipe(
        finalize(() => {
          this.isLoading = false;
          this.reconciliationFacade.isTransactionLoadingSetSubject.next(false);
        })
      )
      .subscribe((result: PageResponse<Transaction>) => {
        let elements: CsFinGroupEntity<Transaction>[] = [];
        result.results.forEach((transaction: Transaction) => {
          elements.push(
            {
              ...transaction,
              rowType: CsFinRowType.ENTITY
            } as CsFinGroupEntity<Transaction>
          );
        });
        this.dataSource = new MatTableDataSource<CsFinGroupElement<Transaction, TransactionFilters>>(elements);
        this.pagination.totalItems = result.total;
        this.pagination.pageSize = result.size;
        this.pagination.pageIndex = result.page - 1;
        this.setSort();
      });

  }

  loadTransactionGroupableFields() {
    this.transactionFacade.getTransactionGroupableFields()
      .pipe(takeUntil(this._onDestroy))
      .subscribe((result: string[]) => {
        this.transactionGroupableFields = result;
      });
  }

  loadTransactionGroups(sortFilters?: SortFilters) {
    let filters: TransactionFilters = {
      ...this.filters,
      accountId: this.account.accountNumber,
      exactFilters: true
    }

    if (sortFilters) {
      filters.orderField = sortFilters.orderField;
      filters.orderDirection = sortFilters.orderDirection;
    }

    this.csFinGroupService.loadGroups(
      this.groupByColumns,
      this.dataSource,
      true,
      this.transactionFacade,
      filters
    ).pipe(
      takeWhile(() => this.isGroupedView),
      finalize(() => {
      this.loadInfo();
    }))
    .subscribe((newGroups:CsFinGroupElement<Transaction, TransactionFilters>[]) => {
      this.dataSource.data = newGroups;
    });

  }

  loadInfo(): void {
    const allGroups: CsFinTableGroup<TransactionFilters>[] = this.dataSource.data
      .filter((element: CsFinGroupElement<Transaction, TransactionFilters>) =>
        element.rowType === CsFinRowType.GROUP) as CsFinTableGroup<TransactionFilters>[];

    this.csFinGroupService.loadAllGroupInfo(allGroups, this.transactionFacade)
      .subscribe((groupedInfo: CsFinTableGroup<TransactionFilters>[]) => {
        this.dataSource.data = groupedInfo;
      });

  }

  triggerOverlay(trigger: CdkOverlayOrigin): void {
    this.triggerOrigin = trigger;
    this.addRemoveColumnsFacade.isOverlayOpen.next(true);
  }

  private setSort(): void {
    if (!this.sort) {
      return;
    }

    const previousSort = {
      active: this.sort.active,
      direction: this.sort.direction
    };
    if (this.filters.orderField) {
      this.sort.active = this.decodeSortField(this.filters.orderField);
    }
    if (this.filters.orderDirection) {
      this.sort.direction = this.filters.orderDirection.toLowerCase() as SortDirection;
    }

    const sort = {
      active: this.sort.active,
      direction: this.sort.direction
    };
    if (!_.isEqual(previousSort, sort)) {
      this.sort.sortChange.emit(this.sort);
    }
  }
  encodeSortField(field: string): string {
    return this.transactionFacade.encodeSortField(field);
  }

  decodeSortField(field: string): string {
    return this.transactionFacade.decodeSortField(field);
  }

  onPageChange(event: any) {
    if (event) {
      this.filters.page = event.pageIndex + 1;
      this.filters.size = event.pageSize;
      this.reconciliationFacade.changeTransactionFilters(this.filters)
    }
  }

  onSortChange(event: any): void {
    const sortFiltersBefore = {
      orderDirection: this.filters.orderDirection,
      orderField: this.filters.orderField
    };

    this.filters.orderDirection = event.direction?.toUpperCase();
    this.filters.orderField = this.encodeSortField(event.active);

    if (this.isGroupedView) {
      this.groupedSort(this.filters as SortFilters);
      return;
    }

    if(!_.isEqual(sortFiltersBefore, this.filters as SortFilters)){
      this.reconciliationFacade.changeTransactionFilters(this.filters);
    }
  }

  groupedSort(sortFilters: SortFilters) {
    this.loadTransactionGroups(sortFilters);
  }

  checkboxLabel(row?: any): string {
    return `${this.reconciliationFacade.selectionTransaction.isSelected(row) ? 'deselect' : 'select'} row ${row.id}`;
  }

  onRowClick(event: MouseEvent, row: any) {
    event.stopPropagation();
    if (!this.sameStatusAsCurrentSelection(row)){
      return;
    }
    if (row.reconcileStatus !== 'NOT_RECONCILED') {
      this.selectedStatus = row.reconcileStatus;
      this.selectedStatusChange.emit(row.reconcileStatus);
      this.managePendingSelection(row, this.reconciliationFacade.selectionTransaction.isSelected(row.id));
      return;
    }

    this.reconciliationFacade.selectionTransaction.toggle(row.id);

    const selected = this.reconciliationFacade.selectionTransaction.isSelected(row.id);
    this.updateSelectionCache(row, selected);
  }

  managePendingSelection(row: Transaction, selected: boolean) {
    this.selectedReconciliationTransaction.emit({row, selected, isPending: row.reconcileStatus === 'PENDING_APPROVAL'});
  }

  subscribeAmountSelectedChanges(): void {
    this.reconciliationFacade.getSelectionTransactionCacheUpdates()
      .pipe(takeUntil(this._onDestroy))
      .subscribe((selectionTransactionCache: Record<string, boolean>) => {
        this.amountSelected = Object.values(selectionTransactionCache).filter((value: boolean) => value).length;
      });

  }

  groupKeyExists(groupKey: string): boolean {
    return !!this.transactionGroupableFields.find((field: string) => field.toLowerCase() === groupKey.toLowerCase())
  }

  groupOnClick() {
    this.isGroupedView = this.groupByColumns.length != 0;
    this.expandedElement = null;
    if (this.isGroupedView) {
      this.loadTransactionGroups();
    } else {
      this.loadTransactions();
    }
  }

  expandGroup(group: CsFinTableGroup<TransactionFilters>) {
    group.expanded = !group.expanded;
    if (group.expanded) {
      this.expandedGroupNames.push(group.groupName);
      let filters: TransactionFilters = {
        ...this.filters,
        accountId: this.account.accountNumber,
        exactFilters: true,
        // selectedFields: this.displayedColumns
      }
      this.csFinGroupService.loadGroupEntities(group, this.transactionFacade, filters)
        .pipe(takeUntil(this._onDestroy))
        .subscribe((newElements: CsFinGroupElement<Transaction, TransactionFilters>[]) => {
          let newDataSource : CsFinGroupElement<Transaction, TransactionFilters>[] = [];
          this.dataSource.data.map((element: CsFinGroupElement<Transaction, TransactionFilters>) => {
            if (!(element.rowType === CsFinRowType.GROUP && element.groupName === group.groupName)) {
              newDataSource.push(element);
            } else {
              newDataSource.push(...newElements);
            }
          });
          this.dataSource.data = newDataSource;
        });
    } else {
      this.expandedGroupNames = this.expandedGroupNames.filter((groupName: string) => groupName !== group.groupName);
      this.dataSource.data = this.dataSource.data.filter((element: CsFinGroupElement<Transaction, TransactionFilters>) => {
        return !(element.rowType !== CsFinRowType.GROUP && element.groupName === group.groupName);
      });
    }
  }

  onCheckboxSelectAll(event: MatCheckboxChange, group: CsFinTableGroup<TransactionFilters>) {
    let allIds: number[] = group.allIds || [];
    if (event.checked) {
      this.bulkUpdateSelectionCache(allIds, true);
      allIds.forEach((row: any) => this.reconciliationFacade.selectionTransaction.select(row));
    } else {
      this.bulkUpdateSelectionCache(allIds, false);
      allIds.forEach((row: any) => this.reconciliationFacade.selectionTransaction.deselect(row));
    }
  }

  onGroupPageChange(event: any, paginator: CsFinTablePaginator) {
    this.csFinGroupService.onGroupPageChange(event, paginator, this.dataSource, this.transactionFacade)
      .subscribe((elements:CsFinGroupElement<Transaction, TransactionFilters>[]) => {
      this.dataSource.data = elements;
    });
  }

  isGroupSelected(group: CsFinTableGroup<Transaction>): boolean {
    let allIds: number[] = group.allIds || [];
    return allIds.some((row: any) => this.reconciliationFacade.selectionTransaction.isSelected(row));
  }

  indeterminate(group:CsFinTableGroup<Transaction>): boolean{
    let allIds: number[] = group.allIds || [];
    let selectedIds: number[] = [];
    allIds.forEach((row: any) => {
      if(this.reconciliationFacade.selectionTransaction.isSelected(row)){
        selectedIds.push(row);
      }
    });
    if (this.disableGroup(group)){
      return true;
    }
    return selectedIds.length > 0 && selectedIds.length < allIds.length;
  }


  getMaxSpan():number {
    return this.displayedColumns.length;
  }

  sameStatusAsCurrentSelection(row: any): boolean {
    if (!this.selectedStatus) {
      return true;
    }
    if (this.selectedStatus === row.reconcileStatus)
      return this.sameReconciliationAsCurrentSelection(row);
    return false;
  }

  sameReconciliationAsCurrentSelection(row: any): boolean {
    if (!this.selectingReconciliation) {
      return true;
    }
    return this.reconciliationFacade.selectionTransaction.isSelected(row.id);
  }

  togglePendingView() {
    this.selectingReconciliation = !this.selectingReconciliation;
  }

  belongsToReconciliation(row: any): boolean {
    if (!this.selectingReconciliation) {
      return true;
    }
    return this.reconciliationFacade.selectionTransaction.isSelected(row.id);
  }

  disableGroup(group:CsFinTableGroup<Transaction>): boolean{
    return group.isReconciliationShared == false && group.isStatusShared == false;
  }

  getGroupNameTrimmed(groupFields: string[]): { trimmed: string, full: string }[] {
    return groupFields.map((word: string) => {
      if (word.length > 15) {
        return { trimmed: word.substring(0, 15) + "...", full: word };
      } else {
        return { trimmed: word, full: word };
      }
    });
  }

  getRowClass(row: CsFinGroupElement<Transaction, TransactionFilters>): string {
    const withDetailsRows = this.dataSource.data.filter(r => this.isEntity(r));

    const rowIndex = withDetailsRows.indexOf(row);

    if (rowIndex % 2 === 0) {
      return 'even-entity';
    } else {
      return 'odd-entity';
    }
  }

  isEntity(row: CsFinGroupElement<Transaction, TransactionFilters>): boolean {
    return row.rowType === CsFinRowType.ENTITY;
  }
}
