import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, On<PERSON>nit, ViewChild} from '@angular/core';
import {takeUntil} from "rxjs/operators";
import {Subject} from "rxjs";
import {ReconciliationFacade} from "../../facade/reconciliation.facade";
import {ListBaleComponent} from "./list-bale/list-bale.component";
import {ListTransactionComponent} from "./list-transaction/list-transaction.component";
import {CsFinAuthFacade, CsFinConfirmationDialogComponent} from "@jumia-cs-fin/common";
import {MatDialog} from "@angular/material/dialog";
import {authParams, bradAuthTarget} from "../../../auth/constants/auth.constants";
import {HttpErrorResponse} from "@angular/common/http";
import {NotificationService} from "../../../api/service/notification.service";
import {Account} from "../../../entities/account/account";
import {AccountFacade} from "../../../accounts/facade/account.facade";
import {SelectedIds} from "../../../entities/reconciliation/selectedIds";
import {ReconciliationFilters} from "../../../entities/reconciliation/reconciliation-filters";
import {SelectedAmountDifference} from "../../../entities/reconciliation/selectedAmountDifference";
import {Bale} from "../../../entities/bale/bale";
import {Reconciliation} from "../../../entities/reconciliation/reconciliation";
import {Transaction} from "../../../entities/transaction/transaction";
import {Router} from "@angular/router";

@Component({
  selector: 'brad-reconcile',
  templateUrl: './reconcile.component.html',
  styleUrls: ['./reconcile.component.scss']
})
export class ReconcileComponent implements OnInit, OnDestroy {
  @ViewChild(ListBaleComponent) listBaleComponentRef!: ListBaleComponent;
  @ViewChild(ListTransactionComponent) listTransactionComponentRef!: ListTransactionComponent;

  private _onDestroy:Subject<void> = new Subject<void>();

  selectedTransactionIds: Number[] = [];
  selectedBaleIds: Number[] = [];
  valueDifference: Number = 0;
  transactionAmount: Number = 0;
  baleAmount: Number = 0;

  isValueDifferenceDisabled: Boolean = false;
  isApproveUnmatchDisabled: Boolean = true;

  canReconcile: Boolean = false;

  selectedStatus?: string;

  baleFinishedLoading: boolean = false;
  transactionFinishedLoading: boolean = false;

  account: Account|null = null;

  amountSelectedTransactions: Number = 0;
  amountSelectedBales: Number = 0;

  activeReconciliation?: Reconciliation;

  protected filters: ReconciliationFilters = {
  };

  protected readonly zero : Number = 0;
  protected readonly bradAuthTarget = bradAuthTarget;
  protected readonly authParams = authParams;
  auth = authParams;
  constructor(
    private reconciliationFacade: ReconciliationFacade,
    private notificationService: NotificationService,
    private dialog: MatDialog,
    private accountFacade: AccountFacade,
    private csFinAuthFacade: CsFinAuthFacade,
    private router: Router
  ) { }


  ngOnInit() {
    this.subscribeTransactionCacheChange();
    this.subscribeBaleCacheChange();
    this.subscribeLoadingChange();
  }

  ngOnDestroy(): void {
    this.clearSelection();
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  subscribeLoadingChange() {
    this.reconciliationFacade.isTransactionLoadingSetSubject.subscribe((isLoading: boolean) => {
      this.transactionFinishedLoading = !isLoading;
    });
    this.reconciliationFacade.isBaleLoadingSetSubject.subscribe((isLoading: boolean) => {
      this.baleFinishedLoading = !isLoading;
    });
  }

  subscribeTransactionCacheChange() {
    this.reconciliationFacade.getSelectionTransactionCacheUpdates()
      .pipe(takeUntil(this._onDestroy))
      .subscribe((selectionTransactionCache: Record<string, boolean>) => {
        if (!this.isValueDifferenceDisabled) {
          this.selectedTransactionIds = Object.keys(selectionTransactionCache).filter(
            (key) => selectionTransactionCache[key]).map(Number);
          this.updateValueDifference();
          this.amountSelectedTransactions = this.selectedTransactionIds.length;
          this.resetSelectionType()
        }
      });
  }

  subscribeBaleCacheChange() {
    this.reconciliationFacade.getSelectionBaleCacheUpdates()
      .pipe(takeUntil(this._onDestroy))
      .subscribe((selectionBaleCache: Record<string, boolean>) => {
        if (!this.isValueDifferenceDisabled) {
          this.selectedBaleIds = Object.keys(selectionBaleCache).filter(
            (key) => selectionBaleCache[key]).map(Number);
          this.updateValueDifference();
          this.amountSelectedBales = this.selectedBaleIds.length;
          this.resetSelectionType()
        }
      });
  }

  updateSelectionType(event:any) {
    if (this.amountSelectedBales == 0 && this.amountSelectedTransactions == 0) {
      this.selectedStatus = event;
    }
  }

  resetSelectionType() {
    if (this.amountSelectedBales == 0 && this.amountSelectedTransactions == 0) {
      this.selectedStatus = undefined;
    }
  }

  updateValueDifference() {
    if (!this.account) {
      return
    }
    this.reconciliationFacade.getAmountDifference({
      account: this.account!.id,
      transactionIds: this.selectedTransactionIds,
      baleIds: this.selectedBaleIds
    } as SelectedIds)
      .pipe(takeUntil(this._onDestroy))
      .subscribe((selectedAmountDifference: SelectedAmountDifference) => {
        this.canReconcile = selectedAmountDifference.isBetweenThreshold;
        this.valueDifference = selectedAmountDifference.amountDifference;
        this.transactionAmount = selectedAmountDifference.transactionAmount;
        this.baleAmount = selectedAmountDifference.baleAmount;
      });
  }

  onReconcileClick() {
    if (!this.account) {
      return
    }
    const dialogRef = this.dialog.open(CsFinConfirmationDialogComponent, {
      autoFocus: false,
      width: '400px',
      height: '162px',
      panelClass: 'overflow-hidden-dialog',
      data: {
        titleKey: 'CONFIRMATION_DIALOG.MESSAGES.ABOUT_TO_RECONCILE',
        descriptionKey: 'CONFIRMATION_DIALOG.ACTIONS.ARE_YOU_SURE',
        params: {},
        cancelActionKey: 'CONFIRMATION_DIALOG.BUTTONS.NO',
        confirmActionKey: 'CONFIRMATION_DIALOG.BUTTONS.YES'
      }
    });
    dialogRef.afterClosed().subscribe(response => {
      if (response) {
        this.reconciliationFacade.reconcile({
          account: this.account!.id as Number,
          transactionIds: this.selectedTransactionIds,
          baleIds: this.selectedBaleIds
        })
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: () => {
              this.notificationService.successTranslated('RECONCILIATION.NOTIFICATIONS.RECONCILE_SUCCESS', {});
              this.listTransactionComponentRef.loadTransactionsOrGroups();
              this.listBaleComponentRef.loadBalesOrGroups();
              this.clearSelection();
            },
            error: (error:HttpErrorResponse) => {
              this.notificationService.errorWithResponse(error);
            }
          });
      }
    });
  }

  onApproveClick() {
    this.reconciliationFacade.approveReconciliationBySelectedIds({
      account: this.account!.id,
      transactionIds: this.selectedTransactionIds,
      baleIds: this.selectedBaleIds
    } as SelectedIds)
      .pipe(takeUntil(this._onDestroy))
      .subscribe({
        next: () => {
          this.notificationService.successTranslated('RECONCILIATION.NOTIFICATIONS.APPROVE_SUCCESS', {});
          this.listTransactionComponentRef.loadTransactionsOrGroups();
          this.listBaleComponentRef.loadBalesOrGroups();
          this.resetView();
        },
        error: (error:HttpErrorResponse) => {
          this.notificationService.errorWithResponse(error);
        }
      });

  }

  onUnmatchClick() {
    this.reconciliationFacade.unmatchReconciliationBySelectedIds({
      account: this.account!.id,
      transactionIds: this.selectedTransactionIds,
      baleIds: this.selectedBaleIds
    } as SelectedIds)
      .pipe(takeUntil(this._onDestroy))
      .subscribe({
        next: () => {
          this.notificationService.successTranslated('RECONCILIATION.NOTIFICATIONS.UNMATCH_SUCCESS', {});
          this.listTransactionComponentRef.loadTransactionsOrGroups();
          this.listBaleComponentRef.loadBalesOrGroups();
          this.resetView();
        },
        error: (error:HttpErrorResponse) => {
          this.notificationService.errorWithResponse(error);
        }
      });

  }

  disableReconciled() {
    return !(this.selectedTransactionIds.length > 0 && this.selectedBaleIds.length > 0 && this.canReconcile);
  }

  clearSelection() {
    this.selectedTransactionIds = [];
    this.selectedBaleIds = [];
    this.valueDifference = 0;
    this.transactionAmount = 0;
    this.baleAmount = 0;
    this.reconciliationFacade.selectionTransaction.clear();
    this.reconciliationFacade.selectionBale.clear();
    this.reconciliationFacade.updateSelectionTransactionCache({});
    this.reconciliationFacade.updateSelectionBaleCache({});
  }


  onAccountChosen(id: number): void {
    this.accountFacade.getById(id)
      .pipe(takeUntil(this._onDestroy))
      .subscribe((account: Account) => {
        this.account = account;
        this.filters.accountID = account.id;
        this.reconciliationFacade.filtersChanged(this.filters);
      });
  }

  clearAccount(){
    this.account = null;
    this.router.navigateByUrl(this.router.url.split('?')[0]);
  }

  setupReconciliationViewBale(event: { row: Bale, selected: boolean, isPending: boolean}) {
    if (!event.row.id) {
      return;
    }
    if (!event.selected) {
      this.isValueDifferenceDisabled = true;
      this.isApproveUnmatchDisabled = !event.isPending;
      this.reconciliationFacade.fetchReconciliationByBaleId(event.row.id)
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (reconciliation:Reconciliation) => {
            this.setupReconciliationView(reconciliation)
          },
          error: (error) => {
            this.notificationService.error(error);
          }
        });
    } else {
      this.resetView();
    }

  }

  setupReconciliationViewTransaction(event: { row: Transaction, selected: boolean, isPending: boolean}) {
    if (!event.row.id) {
      return;
    }
    if (!event.selected) {
      this.isValueDifferenceDisabled = true;
      this.isApproveUnmatchDisabled = !event.isPending;
      this.reconciliationFacade.fetchReconciliationByTransactionId(event.row.id)
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (reconciliation:Reconciliation) => {
            this.setupReconciliationView(reconciliation)
          },
          error: (error) => {
            this.notificationService.error(error);
          }
        });
    } else {
      this.resetView();
    }
  }

  resetView() {
    this.reconciliationFacade.selectionBale.clear();
    this.reconciliationFacade.selectionTransaction.clear();
    this.reconciliationFacade.updateSelectionBaleCache({});
    this.reconciliationFacade.updateSelectionTransactionCache({});
    this.selectedBaleIds = [];
    this.selectedTransactionIds = [];
    this.isValueDifferenceDisabled = false;
    this.transactionAmount = 0;
    this.baleAmount = 0;
    this.resetSelectionType();
    this.listBaleComponentRef.togglePendingView()
    this.listTransactionComponentRef.togglePendingView()
  }


  setupReconciliationView(reconciliation: Reconciliation) {

    this.reconciliationFacade.selectionBale.clear();
    this.reconciliationFacade.selectionTransaction.clear();
    this.reconciliationFacade.updateSelectionBaleCache({});
    this.reconciliationFacade.updateSelectionTransactionCache({});

    this.activeReconciliation = reconciliation;

    reconciliation.baleIds.forEach((baleId: Number) => {
      this.reconciliationFacade.updateSelectionBaleCache({
        ...this.reconciliationFacade.selectionBaleCache,
        [String(baleId)]: true
      });
      this.reconciliationFacade.selectionBale.toggle(baleId);
    });
    reconciliation.transactionIds.forEach((transactionId: Number) => {
      this.reconciliationFacade.updateSelectionTransactionCache({
        ...this.reconciliationFacade.selectionTransactionCache,
        [String(transactionId)]: true
      });
      this.reconciliationFacade.selectionTransaction.toggle(transactionId);
    });
    this.selectedTransactionIds = reconciliation.transactionIds;
    this.selectedBaleIds = reconciliation.baleIds;

    this.updateValueDifference();
    this.listBaleComponentRef.togglePendingView()
    this.listTransactionComponentRef.togglePendingView()
  }


  areYouCreator() {
    if (!this.activeReconciliation) {
      return false;
    }
    return this.activeReconciliation.creator === this.csFinAuthFacade.getUserInfo()?.email
  }

}
