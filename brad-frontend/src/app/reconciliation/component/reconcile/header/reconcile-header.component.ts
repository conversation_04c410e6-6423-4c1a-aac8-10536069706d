import {
  Component,
  Input,
  OnDestroy,
  OnInit,
  Output,
  ViewChild,
  ViewEncapsulation
} from '@angular/core';
import {CsFinApiService, CsFinSidenavService} from "@jumia-cs-fin/common";
import {BehaviorSubject, Observable, Subject, tap} from "rxjs";
import {takeUntil} from "rxjs/operators";
import {ReconciliationFacade} from "../../../facade/reconciliation.facade";
import {ReconciliationFilters} from "../../../../entities/reconciliation/reconciliation-filters";
import * as _ from "lodash";
import {ActivatedRoute, Router} from "@angular/router";
import {Account} from "../../../../entities/account/account";
import {FormControl, FormGroup} from "@angular/forms";
import {CdkConnectedOverlay} from "@angular/cdk/overlay";
import {TransactionFilters} from "../../../../entities/transaction/transaction-filters";
import {BaleFilters} from "../../../../entities/bale/bale-filters";
import {TransactionFacade} from "../../../../accounts/facade/transaction.facade";
import {BaleFacade} from "../../../../bale/facade/bale.facade";
import {HeaderBaleComponent} from "./header-bale/header-bale.component";
import {HeaderTransactionComponent} from "./header-transaction/header-transaction.component";
import {AccountFacade} from "../../../../accounts/facade/account.facade";


export enum HeaderType {
  ALL = 'All',
  BALE = 'Bales',
  TRANSACTION = 'Transactions'
}

@Component({
  selector: 'brad-reconcile-header',
  templateUrl: './reconcile-header.component.html',
  styleUrls: ['./reconcile-header.component.scss','../../../../../assets/brad-custom.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ReconcileHeaderComponent implements OnInit, OnDestroy {

  @ViewChild(HeaderBaleComponent) headerBaleComponent!: HeaderBaleComponent;
  @ViewChild(HeaderTransactionComponent) headerTransactionComponent!: HeaderTransactionComponent;
  @ViewChild(CdkConnectedOverlay, {static: true})
  private connectedOverlay!: CdkConnectedOverlay;

  @Input() account!: Account|null;
  @Output() clearAccount = new Subject<void>();
  @Output() accountChange = new Subject<Account|null>();
  queryParams: any = {};
  isRefreshing = false;
  isOpen$!: Observable<boolean>;

  currentTransactionFilters: TransactionFilters = {};
  currentBaleFilters: BaleFilters = {};

  filterTextFormControl!: FormControl;
  form?: FormGroup;

  private _onDestroy:Subject<void> = new Subject<void>();
  private _isOpen!: BehaviorSubject<boolean>;

  currentHeader: HeaderType = HeaderType.TRANSACTION;
  selectedTabIndex = 0;

  constructor(public sidenav: CsFinSidenavService,
              private reconciliationFacade: ReconciliationFacade,
              private apiService: CsFinApiService,
              private router: Router,
              private route: ActivatedRoute,
              private transactionFacade: TransactionFacade,
              private baleFacade: BaleFacade,
              private accountFacade:AccountFacade) { }

  ngOnInit(): void {
    this.initializeOverlay();
    this.subscribeFiltersChange();
    this.subscribeUrlParamsChanges();
  }

  ngOnDestroy(): void {
    this._onDestroy.next();
    this._onDestroy.complete();
  }


  private initializeFormData(params:any): void {
    this.queryParams = params;
    if(!this.form) {
      this.form = new FormGroup({});
      this.initializeFormControlsAndFilters();
    }
  }

  private initializeOverlay(): void {
    this._isOpen = new BehaviorSubject<boolean>(false);
    this.connectedOverlay.backdropClass = 'cdk-overlay-transparent-backdrop';
    this.connectedOverlay.panelClass = 'filters-overlay-panel';
    this.connectedOverlay.backdropClick
      .pipe(tap(() => this._isOpen.next(false)))
      .subscribe();
    this.isOpen$ = this._isOpen.asObservable();
  }

  private subscribeFiltersChange(): void {
    this.reconciliationFacade.filters$
      .pipe(takeUntil(this._onDestroy))
      .subscribe((filters:ReconciliationFilters) => {
          this.updateMissingUrlFilters(filters);
      });
  }

  private subscribeUrlParamsChanges(): void {
    this.route.queryParams
      .pipe(takeUntil(this._onDestroy))
      .subscribe(params => {
        if(!this.isInReconcilePage()) {
          return;
        }
        const mutableParams = { ...params };
        if (mutableParams['transactionFilters']) {
          mutableParams['transactionFilters'] = JSON.parse(mutableParams['transactionFilters']);
          this.currentTransactionFilters = mutableParams['transactionFilters'];
          this.currentTransactionFilters.accountId = undefined;
          this.submitTransactionFilters(this.currentTransactionFilters);
        }
        if (mutableParams['baleFilters']) {
          mutableParams['baleFilters'] = JSON.parse(mutableParams['baleFilters']);
          this.currentBaleFilters = mutableParams['baleFilters'];
          this.currentBaleFilters.idCompany = undefined;
          this.currentBaleFilters.accountNumber = undefined;
          this.submitBaleFilters(this.currentBaleFilters);
        }
        mutableParams['accountID'] = this.account?.id;
        this.reconciliationFacade.filtersChanged(mutableParams as ReconciliationFilters);
        this.initializeFormData(mutableParams);
        this.updateMissingUrlFilters(mutableParams as ReconciliationFilters)
      });
  }

  private updateMissingUrlFilters(filters: ReconciliationFilters): void {
    const queryParams: any = { ...filters };

    if (filters.transactionFilters) {
      let transactionFilters : TransactionFilters = filters.transactionFilters;
      transactionFilters = this.setNullToUndefined(transactionFilters);
      queryParams['transactionFilters'] = JSON.stringify(transactionFilters);
    }

    if (filters.baleFilters) {
      let baleFilters : BaleFilters = filters.baleFilters;
      baleFilters = this.setNullToUndefined(baleFilters);
      queryParams['baleFilters'] = JSON.stringify(baleFilters);
    }

    const formQueryParams = this.apiService.buildQueryParams(queryParams);
    if (!_.isEqual(this.queryParams, formQueryParams)) {
      this.router.navigate(['reconcile'], { queryParams: formQueryParams });
    }
  }

  private isInReconcilePage(): boolean {
    return this.router.url.includes('reconcile');
  }

  private initializeFormControlsAndFilters(): void {
    this.filterTextFormControl = new FormControl()
    this.setFormControlsToForm();
  }

  private setFormControlsToForm(): void {
    this.form!.addControl(this.transactionFacade.filterTextKey, this.filterTextFormControl);
    this.form!.addControl(this.baleFacade.filterTextKey, this.filterTextFormControl);
  }

  onClearAccount() {
    this.account = null;
    this.accountChange.next(null);
    this.clearAccount.next();
  }

  triggerOverlay(): void {
    this._isOpen.next(!this._isOpen.value);
  }

  closeOverlay(): void {
    this._isOpen.next(false);
  }

  submit() {
    if (this.currentHeader === HeaderType.TRANSACTION) {
      this.currentTransactionFilters.filterText = this.filterTextFormControl.value;
      this.submitTransactionFilters(this.currentTransactionFilters);
    }
    if (this.currentHeader === HeaderType.BALE) {
      this.currentBaleFilters.filterText = this.filterTextFormControl.value;
      this.submitBaleFilters(this.currentBaleFilters);
    }
    if (this.currentHeader === HeaderType.ALL) {
      this.submitAllFilters({
        transactionFilters: this.currentTransactionFilters,
        baleFilters: this.currentBaleFilters
      });
    }


  }

  submitAllFilters(filters: ReconciliationFilters) {
    this.submitTransactionFilters(filters.transactionFilters!);
    this.submitBaleFilters(filters.baleFilters!);
  }

  clearAllFilters() {
    this.headerBaleComponent.clearFilters()
    this.headerTransactionComponent.clearFilters()
  }

  submitTransactionFilters(filters: TransactionFilters) {
    this.reconciliationFacade.changeTransactionFilters(filters);
    this.transactionFacade.filtersChanged(filters);
    this.closeOverlay();
  }

  submitBaleFilters(filters: BaleFilters) {
    this.reconciliationFacade.changeBaleFilters(filters);
    this.baleFacade.filtersChanged(filters);
    this.closeOverlay();
  }

  private setNullToUndefined(obj: any): any {
    for (const key in obj) {
      if (obj.hasOwnProperty(key) && obj[key] === null) {
        obj[key] = undefined;
      } else if (typeof obj[key] === 'object' && obj[key] !== null) {
        this.setNullToUndefined(obj[key]);
      }
    }
    return obj;
  }

  onRedirectTab(tabIndex: number) {
    this.selectedTabIndex = tabIndex;
    this.currentHeader = tabIndex === 2 ? HeaderType.ALL :
                         tabIndex === 1 ? HeaderType.BALE :
                           HeaderType.TRANSACTION;
  }

  protected readonly HeaderType = HeaderType;

}
