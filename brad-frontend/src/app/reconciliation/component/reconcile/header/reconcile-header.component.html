<mat-toolbar id="header-toolbar" fxLayout="row" fxLayoutAlign="start center" fxLayoutGap="8px">

  <button mat-icon-button (click)="sidenav.toggle()" fxHide [fxShow.xs]="true" [fxShow.sm]="true">
    <mat-icon>menu</mat-icon>
  </button>
  <button mat-icon-button id="swap-accounts" *ngIf="account" (click)="onClearAccount()">
    <mat-icon>swap_vert</mat-icon>
  </button>
  <span class="page-title"> {{account?.navReference}} </span>

  <section class="search-bar" [formGroup]="form" [ngStyle]="{'display' : account == null ? 'none' : ''}">
    <!-- filter text (code) -->
    <mat-form-field id="search" class="change-header" floatLabel="always" appearance="outline" fxShow [fxShow.xs]="false" [fxShow.sm]="false"
                    cdkOverlayOrigin #trigger="cdkOverlayOrigin">
      <mat-icon matPrefix>search</mat-icon>
      <mat-label class="theLabel">{{'RECONCILIATION.DETAILS.SEARCH_BAR_GENERIC' | translate}} {{currentHeader}}</mat-label>
      <input type="text" #input matInput [formControl]="filterTextFormControl"
             (keyup.enter)="submit()">
      <mat-icon matSuffix id="filters" (click)="triggerOverlay()">filter_list</mat-icon>
    </mat-form-field>


    <button mat-raised-button color="primary" id="apply-search-btn" class="raised-primary-btn" fxShow [fxShow.xs]="false"
            [fxShow.sm]="false" (click)="submit()">
      {{ 'GENERAL.BUTTONS.LABELS.SEARCH' | translate }}
    </button>
  </section>
  <span fxFlex></span>

  <ng-template cdkConnectedOverlay class="template"
               [cdkConnectedOverlayHasBackdrop]="true"
               [cdkConnectedOverlayOrigin]="trigger"
               [cdkConnectedOverlayOpen]="(isOpen$ | async) || false">
    <div class="filters-overlay">
      <mat-tab-group class="filters-tab-group" dynamicHeight [selectedIndex]="selectedTabIndex" (selectedTabChange)="onRedirectTab($event.index)">
        <mat-tab [label]="'RECONCILIATION.FILTERS.TRANSACTION_TITLE' | translate">
          <brad-header-transaction [filters]="currentTransactionFilters" (submitFilters)="submitTransactionFilters($event)"></brad-header-transaction>
        </mat-tab>

        <mat-tab [label]="'RECONCILIATION.FILTERS.BALE_TITLE' | translate">
          <brad-header-bale [filters]="currentBaleFilters" (submitFilters)="submitBaleFilters($event)"></brad-header-bale>
        </mat-tab>

        <mat-tab [label]="'RECONCILIATION.FILTERS.ALL_TITLE' | translate">
          <brad-header-all [filters]="currentTransactionFilters" (submitFilters)="submitAllFilters($event)"
          (clearFilterEmitter)="clearAllFilters()"></brad-header-all>
        </mat-tab>
      </mat-tab-group>
    </div>
  </ng-template>
</mat-toolbar>

