import {Component, Input, OnDestroy, OnInit, Output} from '@angular/core';
import {HeaderType} from "../reconcile-header.component";
import {Subject, takeUntil} from "rxjs";
import {FormControl, FormGroup} from "@angular/forms";
import {BaleFilters} from "../../../../../entities/bale/bale-filters";
import {BaleFacade} from "../../../../../bale/facade/bale.facade";
import {ActivatedRoute, Router} from "@angular/router";
import {Currency} from "../../../../../entities/currency/currency";
import {CurrencyApiService} from "../../../../../api/service/currency-api.service";
import {TransactionFilters} from "../../../../../entities/transaction/transaction-filters";
import {ReconciliationFacade} from "../../../../facade/reconciliation.facade";
import {activeFiltersSeparator, CsFinActiveFilterChip, CsFinActiveFiltersFacade} from "@jumia-cs-fin/common";

@Component({
  selector: 'brad-header-bale',
  templateUrl: './header-bale.component.html',
  styleUrls: ['./header-bale.component.scss']
})
export class HeaderBaleComponent implements OnInit, OnDestroy {
  @Input() filters!: BaleFilters;
  @Output() switchHeader = new Subject<HeaderType>();
  @Output() submitFilters = new Subject<BaleFilters>();
  protected readonly HeaderType = HeaderType;

  queryParams = {};
  isInitializing = false;
  form!: FormGroup;

  idCompanyFormControl!: FormControl;
  accountFormControl!: FormControl;
  entryNoFormControl!: FormControl;
  documentNoFormControl!: FormControl;
  documentTypeFormControl!: FormControl;
  postingDateStartFormControl!: FormControl;
  postingDateEndFormControl!: FormControl;
  accountPostingGroupFormControl!: FormControl;
  descriptionFormControl!: FormControl;
  sourceCodeFormControl!: FormControl;
  reasonCodeFormControl!: FormControl;
  busLineFormControl!: FormControl;
  departmentFormControl!: FormControl;
  directionFormControl!: FormControl;
  directionSearchFormControl = new FormControl();
  amountFormControl!: FormControl;
  remainingAmountFormControl!: FormControl;
  transactionCurrencyFormControl!: FormControl;
  transactionCurrencySearchFormControl = new FormControl();
  amountLcyFormControl!: FormControl;
  balanceAccountTypeFormControl!: FormControl;
  isOpenFormControl!: FormControl;
  isReversedFormControl!: FormControl;
  postedByFormControl!: FormControl;
  externalDocumentNoFormControl!: FormControl;
  baleTimestampFormControl!: FormControl;
  accountTimestampFormControl!: FormControl;

  directionList:string[] = [];
  filteredDirectionList:string[] = []

  currencyList:Currency[] = [];
  filteredCurrencyList:Currency[] = [];

  activeFilterChips!: Map<string, CsFinActiveFilterChip>;
  private _onDestroy:Subject<void> = new Subject<void>();

  constructor(private baleFacade: BaleFacade,
              private activatedRoute: ActivatedRoute,
              private activeFiltersFacade: CsFinActiveFiltersFacade,
              private router: Router,
              private currencyApiService: CurrencyApiService,
              private reconciliationFacade: ReconciliationFacade
  ) { }

  ngOnDestroy(): void {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  ngOnInit(): void {
    this.isInitializing = true;
    this.initFiltersSearch();
    this.initializeFormData(this.filters);
    this.subscribeActiveFilterChipsChange();
  }

  private subscribeActiveFilterChipsChange(): void {
    this.baleFacade.activeFiltersChips$
      .pipe(takeUntil(this._onDestroy))
      .subscribe((activeFilterChips: Map<string, CsFinActiveFilterChip>) => {
        this.activeFilterChips = activeFilterChips;
      });
  }

  private initFiltersSearch(): void {
    this.initCurrencySearch();
    this.initDirectionSearch();
  }

  private initDirectionSearch(): void {
    this.directionSearchFormControl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe((value: String) => {
        const searchTerm = value.trim();
        if (searchTerm) {
          this.filteredDirectionList = this.directionList.filter((direction) => {
            return direction.trim().toLowerCase().includes(searchTerm.toLowerCase());
          })
        } else {
          this.filteredDirectionList = this.directionList;
        }
      });
  }

  private initCurrencySearch(): void {
    this.transactionCurrencySearchFormControl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe((value: String) => {
        const searchTerm = value.trim();
        if (searchTerm) {
          this.filteredCurrencyList = this.currencyList.filter((currency) => {
            return currency.code.trim().toLowerCase().includes(searchTerm.toLowerCase());
          })
        } else {
          this.filteredCurrencyList = this.currencyList;
        }
      });
  }

  private initializeFormData(params:any): void {
    this.queryParams = params;
    if(!this.form) {
      this.form = new FormGroup({});
      this.initializeFormControlsAndFilters(params);
    }
  }


  private initializeFormControlsAndFilters(params:any): void {
    const filters: BaleFilters = {};


    this.idCompanyFormControl = new FormControl(params.idCompany);
    filters.idCompany = params.idCompany;

    this.accountFormControl = new FormControl(params.account);
    filters.accountNumber = params.accountNumber;

    this.entryNoFormControl = new FormControl(params.entryNo);
    filters.entryNo = params.entryNo;

    this.documentNoFormControl = new FormControl(params.documentNo);
    filters.documentNo = params.documentNo;

    this.documentTypeFormControl = new FormControl(params.documentType);
    filters.documentType = params.documentType;

    this.postingDateStartFormControl = new FormControl(params.postingDateStart);
    filters.postingDateStart = params.postingDateStart;

    this.postingDateEndFormControl = new FormControl(params.postingDateEnd);
    filters.postingDateEnd = params.postingDateEnd;

    this.accountPostingGroupFormControl = new FormControl(params.accountPostingGroup);
    filters.accountPostingGroup = params.accountPostingGroup;

    this.descriptionFormControl = new FormControl(params.description);
    filters.description = params.description;

    this.sourceCodeFormControl = new FormControl(params.sourceCode);
    filters.sourceCode = params.sourceCode;

    this.reasonCodeFormControl = new FormControl(params.reasonCode);
    filters.reasonCode = params.reasonCode;

    this.busLineFormControl = new FormControl(params.busLine);
    filters.busLine = params.busLine;

    this.departmentFormControl = new FormControl(params.department);
    filters.department = params.department;

    let direction: string[] = params.direction === undefined ? undefined : params.direction;
    this.directionFormControl = new FormControl(direction);
    filters.direction = direction;

    let transactionCurrency: string[] = params.transactionCurrency === undefined ? [] : params.transactionCurrency;
    this.transactionCurrencyFormControl = new FormControl(transactionCurrency);
    filters.transactionCurrency = transactionCurrency;

    this.amountFormControl = new FormControl(params.amount);
    filters.amount = params.amount;

    this.remainingAmountFormControl = new FormControl(params.remainingAmount);
    filters.remainingAmount = params.remainingAmount;

    this.amountLcyFormControl = new FormControl(params.amountLcy);
    filters.amountLcy = params.amountLcy;

    this.balanceAccountTypeFormControl = new FormControl(params.balanceAccountType);
    filters.balanceAccountType = params.balanceAccountType;

    this.isOpenFormControl = new FormControl(params.isOpen);
    filters.isOpen = params.isOpen;

    this.isReversedFormControl = new FormControl(params.isReversed);
    filters.isReversed = params.isReversed;

    this.postedByFormControl = new FormControl(params.postedBy);
    filters.postedBy = params.postedBy;

    this.externalDocumentNoFormControl = new FormControl(params.externalDocumentNo);
    filters.externalDocumentNo = params.externalDocumentNo;

    this.baleTimestampFormControl = new FormControl(params.baleTimestamp);
    filters.baleTimestamp = params.baleTimestamp;

    this.accountTimestampFormControl = new FormControl(params.accountTimestamp);
    filters.accountTimestamp = params.accountTimestamp;


    filters.orderField = params.orderField;
    filters.orderDirection = params.orderDirection;
    filters.page = params.page;
    filters.size = params.size;

    Promise.all([
      this.applyCurrencyFilter(params, filters),
      this.applyDirectionFilter(params, filters)
    ]).then(() => {
      this.setFormControlsToForm();
      this.isInitializing = false;
    });
  }

  private setFormControlsToForm(): void {
    this.form.addControl(this.baleFacade.idCompanyKey, this.idCompanyFormControl);
    this.form.addControl(this.baleFacade.accountKey, this.accountFormControl);
    this.form.addControl(this.baleFacade.entryNoKey, this.entryNoFormControl);
    this.form.addControl(this.baleFacade.documentNoKey, this.documentNoFormControl);
    this.form.addControl(this.baleFacade.documentTypeKey, this.documentTypeFormControl);
    this.form.addControl(this.baleFacade.postingDateStartKey, this.postingDateStartFormControl);
    this.form.addControl(this.baleFacade.postingDateEndKey, this.postingDateEndFormControl);
    this.form.addControl(this.baleFacade.accountPostingGroupKey, this.accountPostingGroupFormControl);
    this.form.addControl(this.baleFacade.descriptionKey, this.descriptionFormControl);
    this.form.addControl(this.baleFacade.sourceCodeKey, this.sourceCodeFormControl);
    this.form.addControl(this.baleFacade.reasonCodeKey, this.reasonCodeFormControl);
    this.form.addControl(this.baleFacade.busLineKey, this.busLineFormControl);
    this.form.addControl(this.baleFacade.departmentKey, this.departmentFormControl);
    this.form.addControl(this.baleFacade.directionKey, this.directionFormControl);
    this.form.addControl(this.baleFacade.amountKey, this.amountFormControl);
    this.form.addControl(this.baleFacade.remainingAmountKey, this.remainingAmountFormControl);
    this.form.addControl(this.baleFacade.transactionCurrencyKey, this.transactionCurrencyFormControl);
    this.form.addControl(this.baleFacade.amountLcyKey, this.amountLcyFormControl);
    this.form.addControl(this.baleFacade.balanceAccountTypeKey, this.balanceAccountTypeFormControl);
    this.form.addControl(this.baleFacade.isOpenKey, this.isOpenFormControl);
    this.form.addControl(this.baleFacade.isReversedKey, this.isReversedFormControl);
    this.form.addControl(this.baleFacade.postedByKey, this.postedByFormControl);
    this.form.addControl(this.baleFacade.externalDocumentNoKey, this.externalDocumentNoFormControl);
    this.form.addControl(this.baleFacade.baleTimestampKey, this.baleTimestampFormControl);
    this.form.addControl(this.baleFacade.accountTimestampKey, this.accountTimestampFormControl);
  }

  loadDirection(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if(this.directionList.length <= 0) {
        this.baleFacade.getDirections()
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (directions: string[]) => {
              this.directionList = directions;
              this.filteredDirectionList = this.directionList;
              resolve();
            }, error: (error) => reject(error)
          });
      } else {
        resolve();
      }
    });
  }

  loadCurrencies(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if(this.currencyList.length <= 0) {
        this.currencyApiService.getAll()
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (currencies: Currency[]) => {
              this.currencyList = currencies;
              this.filteredCurrencyList = this.currencyList;
              resolve();
            }, error: (error) => reject(error)
          });
      } else {
        resolve();
      }
    });
  }


  private applyDirectionFilter(params: any, filters: TransactionFilters): Promise<void> {
    return new Promise<void>(async (resolve) => {

      if(!params.direction) {
        resolve();
        return;
      }

      await this.loadDirection();

      this.directionFormControl.setValue(filters.direction, {emitEvent: false});
      resolve();
    });
  }


  private applyCurrencyFilter(params: any, filters: BaleFilters): Promise<void> {
    return new Promise<void>(async (resolve) => {

      if(!params.transactionCurrency) {
        resolve();
        return;
      }

      await this.loadCurrencies();
      this.transactionCurrencyFormControl.setValue(filters.transactionCurrency, {emitEvent: false});
      resolve();
    });
  }

  clearFilters() {
    this.form.reset('', {emitEvent: false});
    this.submitFilters.next({});
  }

  private getFormValues(): BaleFilters {
    return this.form.value as BaleFilters;
  }

  submit() {
    this.submitFilters.next(this.getFormValues());
  }

  noFiltersSelected() {
    return !!this.form.errors;
  }

  onActiveFilterRemoveClick(removedChipKey: string): void {
    const filtersToRemove = removedChipKey.split(activeFiltersSeparator);
    if (filtersToRemove) {
      filtersToRemove.forEach((filterKey: string) => {
        // @ts-ignore
        delete this.filters[filterKey];
        if (this.form.contains(filterKey)) {
          this.form.get(filterKey)!.reset();

          if (filterKey === "postingDateStart") {
            this.form.get("postingDateEnd")!.reset();
          }
        }
      })
    }
    if (filtersToRemove.includes("postingDateStart")) {
      delete this.filters.postingDateEnd;
    }
    this.reconciliationFacade.changeBaleFilters(this.filters);
  }


}
