<mat-accordion>
  <mat-expansion-panel>
    <mat-expansion-panel-header>
      <mat-panel-title class="filters-header">
        <p class="filters-title">{{'RECONCILIATION.FILTERS.TITLE' | translate}}</p>
      </mat-panel-title>
    </mat-expansion-panel-header>
    <cs-fin-active-filters [chipsMap]="activeFilterChips"
                           (onRemove)="onActiveFilterRemoveClick($event)">
    </cs-fin-active-filters>
  </mat-expansion-panel>
</mat-accordion>
<div class="filters-container">

  <!-- entryNo -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-entry-no-filter">
    <mat-label>{{'BALE.FIELDS.ENTRY_NO' | translate}}</mat-label>
    <input matInput type="text" [formControl]="entryNoFormControl">
  </mat-form-field>

  <!-- documentNo -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-document-no-filter">
    <mat-label>{{'BALE.FIELDS.DOCUMENT_NO' | translate}}</mat-label>
    <input matInput type="text" [formControl]="documentNoFormControl">
  </mat-form-field>

  <!-- documentType -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-document-type-filter">
    <mat-label>{{'BALE.FIELDS.DOCUMENT_TYPE' | translate}}</mat-label>
    <input matInput type="text" [formControl]="documentTypeFormControl">
  </mat-form-field>

  <!-- postingDate -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-value-date-filter">
    <mat-label>{{'BALE.FIELDS.POSTING_DATE' | translate}}</mat-label>
    <mat-date-range-input [rangePicker]="postingDateFilter">
      <input matStartDate [formControl]="postingDateStartFormControl" placeholder="Start date">
      <input matEndDate [formControl]="postingDateEndFormControl" placeholder="End date">
    </mat-date-range-input>
    <mat-datepicker-toggle matIconSuffix [for]="postingDateFilter"></mat-datepicker-toggle>
    <mat-date-range-picker #postingDateFilter></mat-date-range-picker>
  </mat-form-field>


  <!-- accountPostingGroup -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-account-posting-group-filter">
    <mat-label>{{'BALE.FIELDS.ACCOUNT_POSTING_GROUP' | translate}}</mat-label>
    <input matInput type="text" [formControl]="accountPostingGroupFormControl">
  </mat-form-field>

  <!-- description -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-description-filter">
    <mat-label>{{'BALE.FIELDS.DESCRIPTION' | translate}}</mat-label>
    <input matInput type="text" [formControl]="descriptionFormControl">
  </mat-form-field>

  <!-- sourceCode -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-source-code-filter">
    <mat-label>{{'BALE.FIELDS.SOURCE_CODE' | translate}}</mat-label>
    <input matInput type="text" [formControl]="sourceCodeFormControl">
  </mat-form-field>

  <!-- reasonCode -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-reason-code-filter">
    <mat-label>{{'BALE.FIELDS.REASON_CODE' | translate}}</mat-label>
    <input matInput type="text" [formControl]="reasonCodeFormControl">
  </mat-form-field>

  <!-- busLine -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-bus-line-filter">
    <mat-label>{{'BALE.FIELDS.BUS_LINE' | translate}}</mat-label>
    <input matInput type="text" [formControl]="busLineFormControl">
  </mat-form-field>

  <!-- department -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-department-filter">
    <mat-label>{{'BALE.FIELDS.DEPARTMENT' | translate}}</mat-label>
    <input matInput type="text" [formControl]="departmentFormControl">
  </mat-form-field>


  <!-- direction -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-initial-direction-filter"
                  (click)="loadDirection()">
    <mat-label>{{'BALE.FIELDS.DIRECTION' | translate}}</mat-label>
    <mat-select [formControl]="directionFormControl" multiple>
      <ng-container *ngIf="filteredDirectionList != null; else loadingDirection">
        <mat-option>
          <ngx-mat-select-search [formControl]="directionSearchFormControl"
                                 [noEntriesFoundLabel]="'GENERAL.FILTERS.NO_MATCHING_RESULTS_FOUND' | translate"
                                 [placeholderLabel]="'GENERAL.FILTERS.SEARCH' | translate">
          </ngx-mat-select-search>
        </mat-option>
        <mat-option *ngFor="let direction of filteredDirectionList" [value]="direction">
          {{direction}}
        </mat-option>
        <button mat-button color="primary" class="clear-selection-btn"
                [disabled]="!directionFormControl.value?.length"
                (click)="directionFormControl.reset([])">

          {{'GENERAL.FILTERS.ACTIONS.CLEAR_SELECTION' | translate}}
        </button>
      </ng-container>
      <ng-template #loadingDirection>
        <mat-option disabled>
          <div class="filters-loading-container">
            <span>{{'GENERAL.FILTERS.LOADING' | translate}}</span>
            <mat-spinner diameter="20"></mat-spinner>
          </div>
        </mat-option>
      </ng-template>
    </mat-select>
  </mat-form-field>

  <!-- amount -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-amount-filter">
    <mat-label>{{'BALE.FIELDS.AMOUNT' | translate}}</mat-label>
    <input matInput type="text" [formControl]="amountFormControl">
  </mat-form-field>

  <!-- currency -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-transaction-currency-filter"
                  (click)="loadCurrencies()">
    <mat-label>{{'BALE.FIELDS.TRANSACTION_CURRENCY' | translate}}</mat-label>
    <mat-select [formControl]="transactionCurrencyFormControl" multiple>
      <ng-container *ngIf="filteredCurrencyList != null; else loadingCurrency">
        <mat-option>
          <ngx-mat-select-search [formControl]="transactionCurrencySearchFormControl"
                                 [noEntriesFoundLabel]="'GENERAL.FILTERS.NO_MATCHING_RESULTS_FOUND' | translate"
                                 [placeholderLabel]="'GENERAL.FILTERS.SEARCH' | translate">
          </ngx-mat-select-search>
        </mat-option>
        <mat-option *ngFor="let currency of filteredCurrencyList" [value]="currency.code">
          {{currency.code}}
        </mat-option>
        <button mat-button color="primary" class="clear-selection-btn"
                [disabled]="!transactionCurrencyFormControl.value?.length"
                (click)="transactionCurrencyFormControl.reset([])">
          {{'GENERAL.FILTERS.ACTIONS.CLEAR_SELECTION' | translate}}
        </button>
      </ng-container>
      <ng-template #loadingCurrency>
        <mat-option disabled>
          <div class="filters-loading-container">
            <span>{{'GENERAL.FILTERS.LOADING' | translate}}</span>
            <mat-spinner diameter="20"></mat-spinner>
          </div>
        </mat-option>
      </ng-template>
    </mat-select>
  </mat-form-field>

  <!-- amountLcy -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-amount-lcy-filter">
    <mat-label>{{'BALE.FIELDS.AMOUNT_LCY' | translate}}</mat-label>
    <input matInput type="text" [formControl]="amountLcyFormControl">
  </mat-form-field>

  <!-- balanceAccountType -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-balance-account-type-filter">
    <mat-label>{{'BALE.FIELDS.BALANCE_ACCOUNT_TYPE' | translate}}</mat-label>
    <input matInput type="text" [formControl]="balanceAccountTypeFormControl">
  </mat-form-field>

  <!-- isOpen -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-is-open-filter">
    <mat-label>{{'BALE.FIELDS.IS_OPEN' | translate}}</mat-label>
    <input matInput type="text" [formControl]="isOpenFormControl">
  </mat-form-field>

  <!-- isReversed -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-is-reversed-filter">
    <mat-label>{{'BALE.FIELDS.IS_REVERSED' | translate}}</mat-label>
    <input matInput type="text" [formControl]="isReversedFormControl">
  </mat-form-field>

  <!-- postedBy -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-posted-by-filter">
    <mat-label>{{'BALE.FIELDS.POSTED_BY' | translate}}</mat-label>
    <input matInput type="text" [formControl]="postedByFormControl">
  </mat-form-field>

  <!-- externalDocumentNo -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-external-document-no-filter">
    <mat-label>{{'BALE.FIELDS.EXTERNAL_DOCUMENT_NO' | translate}}</mat-label>
    <input matInput type="text" [formControl]="externalDocumentNoFormControl">
  </mat-form-field>

</div>


<div class="filters-actions">
  <button class="flat-white-btn filters-cancel-btn" [disabled]="noFiltersSelected()" mat-flat-button fxShow
          (click)="clearFilters()" [fxShow.xs]="false" [fxShow.sm]="false">
    {{'GENERAL.FILTERS.ACTIONS.CLEAR' | translate}}
  </button>
  <button class="raised-primary-btn filters-apply-btn" [disabled]="noFiltersSelected()" mat-raised-button
          color="primary"
          (click)="submit()">
    {{'GENERAL.FILTERS.ACTIONS.APPLY' | translate}}
  </button>
</div>
