import {Component, Input, OnDestroy, OnInit, Output} from '@angular/core';
import {Subject, takeUntil} from "rxjs";
import {FormControl, FormGroup} from "@angular/forms";
import {Currency} from "../../../../../entities/currency/currency";
import {ReconciliationFilters} from "../../../../../entities/reconciliation/reconciliation-filters";
import {TransactionFilters} from "../../../../../entities/transaction/transaction-filters";
import {TransactionFacade} from "../../../../../accounts/facade/transaction.facade";
import {CurrencyApiService} from "../../../../../api/service/currency-api.service";
import {BaleFacade} from "../../../../../bale/facade/bale.facade";
import {ReconciliationStatusApiService} from "../../../../../api/service/reconciliation-status-api.service";
import {activeFiltersSeparator, CsFinActiveFilterChip} from "@jumia-cs-fin/common";
import {ReconciliationFacade} from "../../../../facade/reconciliation.facade";

@Component({
  selector: 'brad-header-all',
  templateUrl: './header-all.component.html',
  styleUrls: ['./header-all.component.scss']
})
export class HeaderAllComponent implements OnInit, OnDestroy {
  @Input() filters!: TransactionFilters;
  @Output() submitFilters = new Subject<ReconciliationFilters>();
  @Output() clearFilterEmitter = new Subject<void>();
  queryParams = {};
  isInitializing = false;

  form!: FormGroup;
  currencyFormControl!: FormControl;
  currencySearchFormControl = new FormControl();
  directionFormControl!: FormControl;
  directionSearchFormControl = new FormControl();
  dateStartFormControl!: FormControl;
  dateEndFormControl!: FormControl;
  descriptionFormControl!: FormControl;
  referenceFormControl!: FormControl;

  reconciliationCreatorFormControl!: FormControl;
  reconciliationCreationDateStartFormControl!: FormControl;
  reconciliationCreationDateEndFormControl!: FormControl;
  reconciliationReviewerFormControl!: FormControl;
  reconciliationReviewDateStartFormControl!: FormControl;
  reconciliationReviewDateEndFormControl!: FormControl;
  reconciliationStatusFormControl!: FormControl;
  isReconciledSearchFormControl = new FormControl();


  isReconciledList: String[] = [];
  filteredIsReconciledList: String[] = [];

  directionList:string[] = [];
  filteredDirectionList:string[] = []

  currencyList:Currency[] = [];
  filteredCurrencyList:Currency[] = [];

  private allowedActiveFilterChips: string[] = [
    'currencyCodes',
    'valueDateStart',
    'valueDateEnd',
    'direction',
    'amount',
    'description',
    'reconciliationCreator',
    'reconciliationCreationDateStart',
    'reconciliationCreationDateEnd',
    'reconciliationReviewer',
    'reconciliationReviewDateStart',
    'reconciliationReviewDateEnd',
    'reconciliationStatus'
  ];

  activeFilterChips!: Map<string, CsFinActiveFilterChip>;
  private _onDestroy:Subject<void> = new Subject<void>();

  constructor(private transactionFacade: TransactionFacade,
              private baleFacade: BaleFacade,
              private currencyApiService: CurrencyApiService,
              private reconciliationApiService: ReconciliationStatusApiService,
              private reconciliationFacade: ReconciliationFacade
  ) {
  }

  ngOnInit() {
    this.isInitializing = true;
    this.initFiltersSearch();
    this.initializeFormData(this.filters);
    this.subscribeActiveFilterChipsChange();
  }

  private subscribeActiveFilterChipsChange(): void {
    this.transactionFacade.activeFiltersChips$
      .pipe(takeUntil(this._onDestroy))
      .subscribe((activeFilterChips: Map<string, CsFinActiveFilterChip>) => {
        this.activeFilterChips = activeFilterChips;
      });
  }

  getActiveFilterChips(): Map<string, CsFinActiveFilterChip> {
    let newActiveFilterChips = new Map<string, CsFinActiveFilterChip>();
    this.activeFilterChips.forEach((value: CsFinActiveFilterChip, key: string) => {
      if (this.allowedActiveFilterChips.includes(key)) {
        newActiveFilterChips.set(key, value);
      }
    });
    return newActiveFilterChips;
  }

  ngOnDestroy(): void {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  clearFilters() {
    this.form.reset('', {emitEvent: false});
    this.submitFilters.next({});
    this.clearFilterEmitter.next();
  }

  submit() {
    let reconciliationFilters: ReconciliationFilters = {
      transactionFilters: {},
      baleFilters: {}
    }
    reconciliationFilters.transactionFilters!.currencyCodes = this.currencyFormControl.value;
    reconciliationFilters.transactionFilters!.direction = this.directionFormControl.value;
    reconciliationFilters.transactionFilters!.valueDateStart = this.dateStartFormControl.value;
    reconciliationFilters.transactionFilters!.valueDateEnd = this.dateEndFormControl.value;
    reconciliationFilters.transactionFilters!.description = this.descriptionFormControl.value;
    reconciliationFilters.transactionFilters!.reference = this.referenceFormControl.value;
    reconciliationFilters.transactionFilters!.reconciliationCreator = this.reconciliationCreatorFormControl.value;
    reconciliationFilters.transactionFilters!.reconciliationCreationDateStart = this.reconciliationCreationDateStartFormControl.value;
    reconciliationFilters.transactionFilters!.reconciliationCreationDateEnd = this.reconciliationCreationDateEndFormControl.value;
    reconciliationFilters.transactionFilters!.reconciliationReviewer = this.reconciliationReviewerFormControl.value;
    reconciliationFilters.transactionFilters!.reconciliationReviewDateStart = this.reconciliationReviewDateStartFormControl.value;
    reconciliationFilters.transactionFilters!.reconciliationReviewDateEnd = this.reconciliationReviewDateEndFormControl.value;
    reconciliationFilters.transactionFilters!.reconciliationStatus = this.reconciliationStatusFormControl.value;

    reconciliationFilters.baleFilters!.transactionCurrency = this.currencyFormControl.value;
    reconciliationFilters.baleFilters!.direction = this.directionFormControl.value;
    reconciliationFilters.baleFilters!.postingDateStart = this.dateStartFormControl.value;
    reconciliationFilters.baleFilters!.postingDateEnd = this.dateEndFormControl.value;
    reconciliationFilters.baleFilters!.description = this.descriptionFormControl.value;
    reconciliationFilters.baleFilters!.externalDocumentNo = this.referenceFormControl.value;
    reconciliationFilters.baleFilters!.reconciliationCreator = this.reconciliationCreatorFormControl.value;
    reconciliationFilters.baleFilters!.reconciliationCreationDateStart = this.reconciliationCreationDateStartFormControl.value;
    reconciliationFilters.baleFilters!.reconciliationCreationDateEnd = this.reconciliationCreationDateEndFormControl.value;
    reconciliationFilters.baleFilters!.reconciliationReviewer = this.reconciliationReviewerFormControl.value;
    reconciliationFilters.baleFilters!.reconciliationReviewDateStart = this.reconciliationReviewDateStartFormControl.value;
    reconciliationFilters.baleFilters!.reconciliationReviewDateEnd = this.reconciliationReviewDateEndFormControl.value;
    reconciliationFilters.baleFilters!.reconciliationStatus = this.reconciliationStatusFormControl.value;

    this.submitFilters.next(reconciliationFilters);
  }

  private initFiltersSearch(): void {
    this.initCurrencySearch();
    this.initDirectionSearch();
    this.initIsReconciledSearch();
  }

  private initDirectionSearch(): void {
    this.directionSearchFormControl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe((value: String) => {
        const searchTerm = value.trim();
        if (searchTerm) {
          this.filteredDirectionList = this.directionList.filter((direction) => {
            return direction.trim().toLowerCase().includes(searchTerm.toLowerCase());
          })
        } else {
          this.filteredDirectionList = this.directionList;
        }
      });
  }

  private initCurrencySearch(): void {
    this.currencySearchFormControl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe((value: String) => {
        const searchTerm = value.trim();
        if (searchTerm) {
          this.filteredCurrencyList = this.currencyList.filter((currency) => {
            return currency.code.trim().toLowerCase().includes(searchTerm.toLowerCase());
          })
        } else {
          this.filteredCurrencyList = this.currencyList;
        }
      });
  }

  private initIsReconciledSearch(): void {
    this.isReconciledSearchFormControl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe((value: string) => {
        const searchTerm = value.trim();
        if (searchTerm) {
          this.filteredIsReconciledList = this.isReconciledList.filter((status) => {
            return status.trim().toLowerCase().includes(searchTerm.toLowerCase());
          })
        } else {
          this.filteredIsReconciledList = this.isReconciledList;
        }
      });
  }

  private initializeFormData(params:any): void {
    this.queryParams = params;
    if(!this.form) {
      this.form = new FormGroup({});
      this.initializeFormControlsAndFilters(params);
    }
  }


  private initializeFormControlsAndFilters(params:any): void {
    const transactionFilters: TransactionFilters = {};

    this.dateStartFormControl = new FormControl(params.valueDateStart);
    transactionFilters.valueDateStart = params.valueDateStart;

    this.dateEndFormControl = new FormControl(params.valueDateEnd);
    transactionFilters.valueDateEnd = params.valueDateEnd;

    this.descriptionFormControl = new FormControl(params.description);
    transactionFilters.description = params.description;

    this.referenceFormControl = new FormControl(params.reference);
    transactionFilters.reference = params.reference;

    let direction: string[] = params.direction === undefined ? undefined : params.direction;
    this.directionFormControl = new FormControl(direction);
    transactionFilters.direction = direction;

    let currencyCodes: string[] = params.currency === undefined ? undefined : params.currencyCodes;
    this.currencyFormControl = new FormControl(currencyCodes);
    transactionFilters.currencyCodes = currencyCodes;

    this.reconciliationCreatorFormControl = new FormControl(params.reconciliationCreator);
    transactionFilters.reconciliationCreator = params.reconciliationCreator;

    this.reconciliationCreationDateStartFormControl = new FormControl(params.reconciliationCreationDateStart);
    transactionFilters.reconciliationCreationDateStart = params.reconciliationCreationDateStart;

    this.reconciliationCreationDateEndFormControl = new FormControl(params.reconciliationCreationDateEnd);
    transactionFilters.reconciliationCreationDateEnd = params.reconciliationCreationDateEnd;

    this.reconciliationReviewerFormControl = new FormControl(params.reconciliationReviewer);
    transactionFilters.reconciliationReviewer = params.reconciliationReviewer;

    this.reconciliationReviewDateStartFormControl = new FormControl(params.reconciliationReviewDateStart);
    transactionFilters.reconciliationReviewDateStart = params.reconciliationReviewDateStart;

    this.reconciliationReviewDateEndFormControl = new FormControl(params.reconciliationReviewDateEnd);
    transactionFilters.reconciliationReviewDateEnd = params.reconciliationReviewDateEnd;

    let reconciliationStatuses: string[] = params.reconciliationStatus === undefined ? undefined : params.reconciliationStatus;
    this.reconciliationStatusFormControl = new FormControl(reconciliationStatuses);
    transactionFilters.reconciliationStatus = reconciliationStatuses;

    Promise.all([
      this.applyCurrencyFilter(params, transactionFilters),
      this.applyDirectionFilter(params, transactionFilters),
      this.applyIsReconciledFilter(params, transactionFilters)
    ]).then(() => {
      this.setFormControlsToForm();
      this.isInitializing = false;
    });
  }


  private applyDirectionFilter(params: any, filters: TransactionFilters): Promise<void> {
    return new Promise<void>(async (resolve) => {

      if(!params.direction) {
        resolve();
        return;
      }

      await this.loadDirection();

      this.directionFormControl.setValue(filters.direction, {emitEvent: false});
      resolve();
    });
  }


  private applyCurrencyFilter(params: any, filters: TransactionFilters): Promise<void> {
    return new Promise<void>(async (resolve) => {

      if(!params.currencyCodes) {
        resolve();
        return;
      }

      await this.loadCurrencies();
      this.currencyFormControl.setValue(filters.currencyCodes, {emitEvent: false});
      resolve();
    });
  }

  private applyIsReconciledFilter(params: any, filters: TransactionFilters): Promise<void> {
    return new Promise<void>(async (resolve) => {

      if(!params.isReconciled) {
        resolve();
        return;
      }

      await this.loadIsReconciled();
      this.reconciliationStatusFormControl.setValue(filters.reconciliationStatus, {emitEvent: false});
      resolve();
    });
  }

  loadDirection(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if(this.directionList.length <= 0) {
        this.transactionFacade.getDirections()
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (directions: string[]) => {
              this.directionList = directions;
              this.filteredDirectionList = this.directionList;
              resolve();
            }, error: (error) => reject(error)
          });
      } else {
        resolve();
      }
    });
  }

  loadCurrencies(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if(this.currencyList.length <= 0) {
        this.currencyApiService.getAll()
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (currencies: Currency[]) => {
              this.currencyList = currencies;
              this.filteredCurrencyList = this.currencyList;
              resolve();
            }, error: (error) => reject(error)
          });
      } else {
        resolve();
      }
    });
  }

  loadIsReconciled(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if(this.isReconciledList.length <= 0) {
        this.reconciliationApiService.getAll()
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (isReconciledStatuses: String[]) => {
              this.isReconciledList = isReconciledStatuses;
              this.filteredIsReconciledList = isReconciledStatuses;
              resolve();
            }, error: (error) => reject(error)
          });
      } else {
        resolve();
      }
    });
  }

  private setFormControlsToForm(): void {
    this.form.addControl(this.transactionFacade.valueDateStartKey, this.dateStartFormControl);
    this.form.addControl(this.baleFacade.postingDateStartKey, this.dateStartFormControl);

    this.form.addControl(this.transactionFacade.valueDateEndKey, this.dateEndFormControl);
    this.form.addControl(this.baleFacade.postingDateEndKey, this.dateEndFormControl);

    this.form.addControl(this.transactionFacade.descriptionKey, this.descriptionFormControl);
    this.form.addControl(this.baleFacade.descriptionKey, this.descriptionFormControl);

    this.form.addControl(this.transactionFacade.referenceKey, this.referenceFormControl);
    this.form.addControl(this.baleFacade.externalDocumentNoKey, this.referenceFormControl);

    this.form.addControl(this.transactionFacade.directionKey, this.directionFormControl);
    this.form.addControl(this.baleFacade.directionKey, this.directionFormControl);

    this.form.addControl(this.transactionFacade.currencyKey, this.currencyFormControl);
    this.form.addControl(this.baleFacade.transactionCurrencyKey, this.currencyFormControl);

    this.form.addControl(this.transactionFacade.reconciliationCreatorKey, this.reconciliationCreatorFormControl);
    this.form.addControl(this.baleFacade.reconciliationCreatorKey, this.reconciliationCreatorFormControl);

    this.form.addControl(this.transactionFacade.reconciliationCreationDateStartKey, this.reconciliationCreationDateStartFormControl);
    this.form.addControl(this.baleFacade.reconciliationCreationDateStartKey, this.reconciliationCreationDateStartFormControl);

    this.form.addControl(this.transactionFacade.reconciliationCreationDateEndKey, this.reconciliationCreationDateEndFormControl);
    this.form.addControl(this.baleFacade.reconciliationCreationDateEndKey, this.reconciliationCreationDateEndFormControl);

    this.form.addControl(this.transactionFacade.reconciliationReviewerKey, this.reconciliationReviewerFormControl);
    this.form.addControl(this.baleFacade.reconciliationReviewerKey, this.reconciliationReviewerFormControl);

    this.form.addControl(this.transactionFacade.reconciliationReviewDateStartKey, this.reconciliationReviewDateStartFormControl);
    this.form.addControl(this.baleFacade.reconciliationReviewDateStartKey, this.reconciliationReviewDateStartFormControl);

    this.form.addControl(this.transactionFacade.reconciliationReviewDateEndKey, this.reconciliationReviewDateEndFormControl);
    this.form.addControl(this.baleFacade.reconciliationReviewDateEndKey, this.reconciliationReviewDateEndFormControl);

    this.form.addControl(this.transactionFacade.reconciliationStatusKey, this.reconciliationStatusFormControl);
    this.form.addControl(this.baleFacade.reconciliationStatusKey, this.reconciliationStatusFormControl);
  }

  noFiltersSelected() {
    return !!this.form.errors;
  }

  onActiveFilterRemoveClick(removedChipKey: string): void {
    const filtersToRemove = removedChipKey.split(activeFiltersSeparator);
    if (filtersToRemove) {
      filtersToRemove.forEach((filterKey: string) => {
        // @ts-ignore
        delete this.filters[filterKey];
        if (this.form.contains(filterKey)) {
          this.form.get(filterKey)!.reset();

          if (filterKey === "valueDateStart") {
            this.form.get("valueDateEnd")!.reset();
          }
          if (filterKey === "reconciliationCreationDateStart") {
            this.form.get("reconciliationCreationDateEnd")!.reset();
          }
          if (filterKey === "reconciliationReviewDateStart") {
            this.form.get("reconciliationReviewDateEnd")!.reset();
          }

        }
      })
      if (filtersToRemove.includes("valueDateStart")) {
        delete this.filters.valueDateEnd
      }
      if (filtersToRemove.includes("reconciliationCreationDateStart")) {
        delete this.filters.reconciliationCreationDateEnd
      }
      if (filtersToRemove.includes("reconciliationReviewDateStart")) {
        delete this.filters.reconciliationReviewDateEnd
      }
    }
    this.reconciliationFacade.changeTransactionFilters(this.filters);
    this.reconciliationFacade.changeBaleFilters(this.filters);
  }
}
