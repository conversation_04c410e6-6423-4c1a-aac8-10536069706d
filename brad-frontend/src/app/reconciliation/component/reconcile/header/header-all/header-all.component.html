<mat-accordion>
  <mat-expansion-panel>
    <mat-expansion-panel-header>
      <mat-panel-title class="filters-header">
        <p class="filters-title">{{'RECONCILIATION.FILTERS.TITLE' | translate}}</p>
      </mat-panel-title>
    </mat-expansion-panel-header>
    <cs-fin-active-filters [chipsMap]="getActiveFilterChips()"
                           (onRemove)="onActiveFilterRemoveClick($event)">
    </cs-fin-active-filters>
  </mat-expansion-panel>
</mat-accordion>
<div class="filters-container">

  <!-- currency -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-currency-filter"
                  (click)="loadCurrencies()">
    <mat-label>{{'TRANSACTIONS.FIELDS.CURRENCY' | translate}}</mat-label>
    <mat-select [formControl]="currencyFormControl" multiple>
      <ng-container *ngIf="filteredCurrencyList != null; else loadingCurrency">
        <mat-option>
          <ngx-mat-select-search [formControl]="currencySearchFormControl"
                                 [noEntriesFoundLabel]="'GENERAL.FILTERS.NO_MATCHING_RESULTS_FOUND' | translate"
                                 [placeholderLabel]="'GENERAL.FILTERS.SEARCH' | translate">
          </ngx-mat-select-search>
        </mat-option>
        <mat-option *ngFor="let currency of filteredCurrencyList" [value]="currency.code">
          {{currency.code}}
        </mat-option>
        <button mat-button color="primary" class="clear-selection-btn"
                [disabled]="!currencyFormControl.value?.length"
                (click)="currencyFormControl.reset([])">
          {{'GENERAL.FILTERS.ACTIONS.CLEAR_SELECTION' | translate}}
        </button>
      </ng-container>
      <ng-template #loadingCurrency>
        <mat-option disabled>
          <div class="filters-loading-container">
            <span>{{'GENERAL.FILTERS.LOADING' | translate}}</span>
            <mat-spinner diameter="20"></mat-spinner>
          </div>
        </mat-option>
      </ng-template>
    </mat-select>
  </mat-form-field>

  <!-- value date | posting date -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-value-date-filter">
    <mat-label>{{'TRANSACTIONS.FIELDS.VALUE_DATE' | translate}} | {{'BALE.FIELDS.POSTING_DATE' | translate}}</mat-label>
    <mat-date-range-input [rangePicker]="picker1">
      <input matStartDate [formControl]="dateStartFormControl" placeholder="Start date">
      <input matEndDate [formControl]="dateEndFormControl" placeholder="End date">
    </mat-date-range-input>
    <mat-datepicker-toggle matIconSuffix [for]="picker1"></mat-datepicker-toggle>
    <mat-date-range-picker #picker1></mat-date-range-picker>
  </mat-form-field>


  <!-- direction -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-initial-direction-filter"
                  (click)="loadDirection()">
    <mat-label>{{'BALE.FIELDS.DIRECTION' | translate}}</mat-label>
    <mat-select [formControl]="directionFormControl" multiple>
      <ng-container *ngIf="filteredDirectionList != null; else loadingDirection">
        <mat-option>
          <ngx-mat-select-search [formControl]="directionSearchFormControl"
                                 [noEntriesFoundLabel]="'GENERAL.FILTERS.NO_MATCHING_RESULTS_FOUND' | translate"
                                 [placeholderLabel]="'GENERAL.FILTERS.SEARCH' | translate">
          </ngx-mat-select-search>
        </mat-option>
        <mat-option *ngFor="let direction of filteredDirectionList" [value]="direction">
          {{direction}}
        </mat-option>
        <button mat-button color="primary" class="clear-selection-btn"
                [disabled]="!directionFormControl.value?.length"
                (click)="directionFormControl.reset([])">

          {{'GENERAL.FILTERS.ACTIONS.CLEAR_SELECTION' | translate}}
        </button>
      </ng-container>
      <ng-template #loadingDirection>
        <mat-option disabled>
          <div class="filters-loading-container">
            <span>{{'GENERAL.FILTERS.LOADING' | translate}}</span>
            <mat-spinner diameter="20"></mat-spinner>
          </div>
        </mat-option>
      </ng-template>
    </mat-select>
  </mat-form-field>

  <!-- description -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-description-filter">
    <mat-label>{{'BALE.FIELDS.DESCRIPTION' | translate}}</mat-label>
    <input matInput type="text" [formControl]="descriptionFormControl">
  </mat-form-field>

  <!-- reference -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-reference-filter">
    <mat-label>{{'TRANSACTIONS.FIELDS.REFERENCE' | translate}} | {{'BALE.FIELDS.EXTERNAL_DOCUMENT_NO' | translate}}</mat-label>
    <input matInput type="text" [formControl]="referenceFormControl">
  </mat-form-field>

  <!-- reconciliation creator -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-reconciliation-creator-filter">
    <mat-label>{{'RECONCILIATION.FIELDS.CREATOR' | translate}}</mat-label>
    <input matInput type="text" [formControl]="reconciliationCreatorFormControl">
  </mat-form-field>

  <!-- reconciliation creation date -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-reconciliation-creation-date-filter">
    <mat-label>{{'RECONCILIATION.FIELDS.CREATION_DATE' | translate}}</mat-label>
    <mat-date-range-input [rangePicker]="picker2">
      <input matStartDate [formControl]="reconciliationCreationDateStartFormControl" placeholder="Start date">
      <input matEndDate [formControl]="reconciliationCreationDateEndFormControl" placeholder="End date">
    </mat-date-range-input>
    <mat-datepicker-toggle matIconSuffix [for]="picker2"></mat-datepicker-toggle>
    <mat-date-range-picker #picker2></mat-date-range-picker>
  </mat-form-field>

  <!-- reconciliation reviewer -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-reconciliation-reviewer-filter">
    <mat-label>{{'RECONCILIATION.FIELDS.REVIEWER' | translate}}</mat-label>
    <input matInput type="text" [formControl]="reconciliationReviewerFormControl">
  </mat-form-field>

  <!-- reconciliation review date -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-reconciliation-review-date-filter">
    <mat-label>{{'RECONCILIATION.FIELDS.REVIEW_DATE' | translate}}</mat-label>
    <mat-date-range-input [rangePicker]="picker3">
      <input matStartDate [formControl]="reconciliationReviewDateStartFormControl" placeholder="Start date">
      <input matEndDate [formControl]="reconciliationReviewDateEndFormControl" placeholder="End date">
    </mat-date-range-input>
    <mat-datepicker-toggle matIconSuffix [for]="picker3"></mat-datepicker-toggle>
    <mat-date-range-picker #picker3></mat-date-range-picker>
  </mat-form-field>


  <!-- reconciliation status -->
  <mat-form-field class="filter-field" appearance="outline" data-cy="input-reconciliation-status-filter"
                  (click)="loadIsReconciled()">
    <mat-label>{{'RECONCILIATION.FIELDS.STATUS' | translate}}</mat-label>
    <mat-select [formControl]="reconciliationStatusFormControl" multiple>
      <ng-container *ngIf="filteredIsReconciledList != null; else loadingReconciliationStatus">
        <mat-option>
          <ngx-mat-select-search [formControl]="isReconciledSearchFormControl"
                                 [noEntriesFoundLabel]="'GENERAL.FILTERS.NO_MATCHING_RESULTS_FOUND' | translate"
                                 [placeholderLabel]="'GENERAL.FILTERS.SEARCH' | translate">
          </ngx-mat-select-search>
        </mat-option>
        <mat-option *ngFor="let reconciliationStatus of filteredIsReconciledList" [value]="reconciliationStatus">
          {{reconciliationStatus}}
        </mat-option>
        <button mat-button color="primary" class="clear-selection-btn"
                [disabled]="!reconciliationStatusFormControl.value?.length"
                (click)="reconciliationStatusFormControl.reset([])">

          {{'GENERAL.FILTERS.ACTIONS.CLEAR_SELECTION' | translate}}
        </button>
      </ng-container>
      <ng-template #loadingReconciliationStatus>
        <mat-option disabled>
          <div class="filters-loading-container">
            <span>{{'GENERAL.FILTERS.LOADING' | translate}}</span>
            <mat-spinner diameter="20"></mat-spinner>
          </div>
        </mat-option>
      </ng-template>
    </mat-select>
  </mat-form-field>
</div>

<div class="filters-actions">
  <button class="flat-white-btn filters-cancel-btn" [disabled]="noFiltersSelected()" mat-flat-button fxShow
          (click)="clearFilters()" [fxShow.xs]="false" [fxShow.sm]="false">
    {{'RECONCILIATION.BUTTONS.LABELS.CLEAR_ALL' | translate}}
  </button>
  <button class="raised-primary-btn filters-apply-btn" [disabled]="noFiltersSelected()" mat-raised-button
          color="primary"
          (click)="submit()">
    {{'GENERAL.FILTERS.ACTIONS.APPLY' | translate}}
  </button>
</div>
