import {Component, Input, OnDestroy, OnInit, Output} from '@angular/core';
import {HeaderType} from "../reconcile-header.component";
import {Subject, takeUntil} from "rxjs";
import {FormControl, FormGroup} from "@angular/forms";
import {Currency} from "../../../../../entities/currency/currency";
import {ActivatedRoute, Router} from "@angular/router";
import {
  activeFiltersSeparator,
  CsFinActiveFilterChip,
  CsFinActiveFiltersFacade,
  CsFinApiService
} from "@jumia-cs-fin/common";
import {TransactionFacade} from "../../../../../accounts/facade/transaction.facade";
import {AccountFacade} from "../../../../../accounts/facade/account.facade";
import {CurrencyApiService} from "../../../../../api/service/currency-api.service";
import {TransactionFilters} from "../../../../../entities/transaction/transaction-filters";
import {ReconciliationFacade} from "../../../../facade/reconciliation.facade";

@Component({
  selector: 'brad-header-transaction',
  templateUrl: './header-transaction.component.html',
  styleUrls: ['./header-transaction.component.scss']
})
export class HeaderTransactionComponent implements OnInit, OnDestroy {
  @Input() filters!: TransactionFilters;
  @Output() switchHeader = new Subject<HeaderType>();
  @Output() submitFilters = new Subject<TransactionFilters>();
  protected readonly HeaderType = HeaderType;

  isInitializing = false;

  directionList:string[] = [];
  filteredDirectionList:string[] = []

  currencyList:Currency[] = [];
  filteredCurrencyList:Currency[] = [];

  queryParams = {};
  form!: FormGroup;
  typeFormControl!: FormControl;
  accountIdFormControl!: FormControl;
  partitionKeyFormControl!: FormControl;
  currencyFormControl!: FormControl;
  currencySearchFormControl = new FormControl();
  valueDateStartFormControl!: FormControl;
  valueDateEndFormControl!: FormControl;
  transactionDateStartFormControl!: FormControl;
  transactionDateEndFormControl!: FormControl;
  statementDateStartFormControl!: FormControl;
  statementDateEndFormControl!: FormControl;
  amountFormControl!: FormControl;
  referenceFormControl!: FormControl;
  descriptionFormControl!: FormControl;
  accountStatementIDFormControl!: FormControl;
  remittanceInformationFormControl!: FormControl;
  orderingPartyNameFormControl!: FormControl;
  createdAtStartFormControl!: FormControl;
  createdAtEndFormControl!: FormControl;
  directionFormControl!: FormControl;
  directionSearchFormControl = new FormControl();

  activeFilterChips!: Map<string, CsFinActiveFilterChip>;
  private _onDestroy: Subject<void> = new Subject<void>();

  constructor(private activatedRoute: ActivatedRoute,
              private activeFiltersFacade: CsFinActiveFiltersFacade,
              private router: Router,
              private apiService: CsFinApiService,
              private transactionFacade: TransactionFacade,
              private accountFacade: AccountFacade,
              private currencyApiService: CurrencyApiService,
              private reconciliationFacade: ReconciliationFacade
              ) {
  }

  ngOnDestroy(): void {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  ngOnInit(): void {
    this.isInitializing = true;
    this.initFiltersSearch();
    this.initializeFormData(this.filters);
    this.subscribeActiveFilterChipsChange();
  }

  private subscribeActiveFilterChipsChange(): void {
    this.transactionFacade.activeFiltersChips$
      .pipe(takeUntil(this._onDestroy))
      .subscribe((activeFilterChips: Map<string, CsFinActiveFilterChip>) => {
        this.activeFilterChips = activeFilterChips;
      });
  }

  private initFiltersSearch(): void {
    this.initCurrencySearch();
    this.initDirectionSearch();
  }

  private initCurrencySearch(): void {
    this.currencySearchFormControl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe((value: String) => {
        const searchTerm = value.trim();
        if (searchTerm) {
          this.filteredCurrencyList = this.currencyList.filter((currency) => {
            return currency.code.trim().toLowerCase().includes(searchTerm.toLowerCase());
          })
        } else {
          this.filteredCurrencyList = this.currencyList;
        }
      });
  }

  private initDirectionSearch(): void {
    this.directionSearchFormControl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe((value: String) => {
        const searchTerm = value.trim();
        if (searchTerm) {
          this.filteredDirectionList = this.directionList.filter((direction) => {
            return direction.trim().toLowerCase().includes(searchTerm.toLowerCase());
          })
        } else {
          this.filteredDirectionList = this.directionList;
        }
      });
  }

  private initializeFormData(params:any): void {
    this.queryParams = params;
    if(!this.form) {
      this.form = new FormGroup({});
      this.initializeFormControlsAndFilters(params);
    }
  }

  private setFormControlsToForm(): void {
    this.form.addControl(this.transactionFacade.typeKey, this.typeFormControl);
    this.form.addControl(this.transactionFacade.accountIDKey, this.accountIdFormControl);
    this.form.addControl(this.transactionFacade.partitionKey, this.partitionKeyFormControl);
    this.form.addControl(this.transactionFacade.currencyKey, this.currencyFormControl);
    this.form.addControl(this.transactionFacade.valueDateStartKey, this.valueDateStartFormControl);
    this.form.addControl(this.transactionFacade.valueDateEndKey, this.valueDateEndFormControl);
    this.form.addControl(this.transactionFacade.transactionDateStartKey, this.transactionDateStartFormControl);
    this.form.addControl(this.transactionFacade.transactionDateEndKey, this.transactionDateEndFormControl);
    this.form.addControl(this.transactionFacade.statementDateStartKey, this.statementDateStartFormControl);
    this.form.addControl(this.transactionFacade.statementDateEndKey, this.statementDateEndFormControl);
    this.form.addControl(this.transactionFacade.directionKey, this.directionFormControl);
    this.form.addControl(this.transactionFacade.amountKey, this.amountFormControl);
    this.form.addControl(this.transactionFacade.referenceKey, this.referenceFormControl);
    this.form.addControl(this.transactionFacade.descriptionKey, this.descriptionFormControl);
    this.form.addControl(this.transactionFacade.accountStatementIDKey, this.accountStatementIDFormControl);
    this.form.addControl(this.transactionFacade.remittanceInformationKey, this.remittanceInformationFormControl);
    this.form.addControl(this.transactionFacade.orderingPartyNameKey, this.orderingPartyNameFormControl);
    this.form.addControl(this.transactionFacade.createdAtStartKey, this.createdAtStartFormControl);
    this.form.addControl(this.transactionFacade.createdAtEndKey, this.createdAtEndFormControl);
  }

  private initializeFormControlsAndFilters(params:any): void {
    const filters: TransactionFilters = {};

    this.typeFormControl = new FormControl(params.type);
    filters.type = params.type;

    this.accountIdFormControl = new FormControl(params.accountID);
    filters.accountId = params.accountId;

    this.partitionKeyFormControl = new FormControl(params.partitionKey);
    filters.partitionKey = params.partitionKey;

    let currencyCodes: string[] = params.currencyCodes === undefined ? [] : params.currencyCodes;
    this.currencyFormControl = new FormControl(currencyCodes);
    filters.currencyCodes = currencyCodes;

    this.valueDateStartFormControl = new FormControl(params.valueDateStart);
    filters.valueDateStart = params.valueDateStart;

    this.valueDateEndFormControl = new FormControl(params.valueDateEnd);
    filters.valueDateEnd = params.valueDateEnd;

    this.transactionDateStartFormControl = new FormControl(params.transactionDateStart);
    filters.transactionDateStart = params.transactionDateStart;

    this.transactionDateEndFormControl = new FormControl(params.transactionDateEnd);
    filters.transactionDateEnd = params.transactionDateEnd;

    this.statementDateStartFormControl = new FormControl(params.statementDateStart);
    filters.statementDateStart = params.statementDateStart;

    this.statementDateEndFormControl = new FormControl(params.statementDateEnd);
    filters.statementDateEnd = params.statementDateEnd;

    let direction: string[] = params.direction === undefined ? [] : params.direction;
    this.directionFormControl = new FormControl(direction);
    filters.direction = direction;

    this.amountFormControl = new FormControl(params.amount);
    filters.amount = params.amount;

    this.referenceFormControl = new FormControl(params.reference);
    filters.reference = params.reference;

    this.descriptionFormControl = new FormControl(params.description);
    filters.description = params.description;

    let accountStatementFilter = params.accountStatementID;
    if (this.transactionFacade.mostRecentStatementIDBehaviorSubject.value !== -1) {
      accountStatementFilter = this.transactionFacade.mostRecentStatementIDBehaviorSubject.value;
    }

    this.accountStatementIDFormControl = new FormControl(accountStatementFilter);
    filters.accountStatementID = accountStatementFilter;

    this.remittanceInformationFormControl = new FormControl(params.remittanceInformation);
    filters.remittanceInformation = params.remittanceInformation;

    this.orderingPartyNameFormControl = new FormControl(params.orderingPartyName);
    filters.orderingPartyName = params.orderingPartyName;

    this.createdAtStartFormControl = new FormControl(params.createdAtStart);
    filters.createdAtStart = params.createdAtStart;

    this.createdAtEndFormControl = new FormControl(params.createdAtEnd);
    filters.createdAtEnd = params.createdAtEnd;

    filters.orderField = params.orderField;
    filters.orderDirection = params.orderDirection;
    filters.page = params.page;
    filters.size = params.size;

    Promise.all([
      this.applyCurrencyFilter(params, filters),
      this.applyDirectionFilter(params, filters),
    ]).then(() => {
      this.setFormControlsToForm();
      this.isInitializing = false;
    });
  }

  private applyCurrencyFilter(params: any, filters: TransactionFilters): Promise<void> {
    return new Promise<void>(async (resolve) => {

      if(!params.currencyCodes) {
        resolve();
        return;
      }

      await this.loadCurrencies();
      this.currencyFormControl.setValue(filters.currencyCodes, {emitEvent: false});
      resolve();
    });
  }

  private applyDirectionFilter(params: any, filters: TransactionFilters): Promise<void> {
    return new Promise<void>(async (resolve) => {

      if(!params.direction) {
        resolve();
        return;
      }

      await this.loadDirection();

      this.directionFormControl.setValue(filters.direction, {emitEvent: false});
      resolve();
    });
  }

  loadCurrencies(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if(this.currencyList.length <= 0) {
        this.currencyApiService.getAll()
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (currencies: Currency[]) => {
              this.currencyList = currencies;
              this.filteredCurrencyList = this.currencyList;
              resolve();
            }, error: (error) => reject(error)
          });
      } else {
        resolve();
      }
    });
  }

  loadDirection(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if(this.directionList.length <= 0) {
        this.transactionFacade.getDirections()
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (directions: string[]) => {
              this.directionList = directions;
              this.filteredDirectionList = this.directionList;
              resolve();
            }, error: (error) => reject(error)
          });
      } else {
        resolve();
      }
    });
  }

  clearFilters() {
    this.form.reset('', {emitEvent: false});
    this.submitFilters.next({});
  }

  private getFormValues(): TransactionFilters {
    return this.form.value as TransactionFilters;
  }

  submit() {
    this.submitFilters.next(this.getFormValues());
  }

  noFiltersSelected() {
    return !!this.form.errors;
  }

  onActiveFilterRemoveClick(removedChipKey: string): void {
    const filtersToRemove = removedChipKey.split(activeFiltersSeparator);
    if (filtersToRemove) {
      filtersToRemove.forEach((filterKey: string) => {
        // @ts-ignore
        delete this.filters[filterKey];
        if (this.form.contains(filterKey)) {
          this.form.get(filterKey)!.reset();

          if (filterKey === "valueDateStart") {
            this.form.get("valueDateEnd")!.reset();
          }
          if (filterKey === "transactionDateStart") {
            this.form.get("transactionDateEnd")!.reset();
          }
          if (filterKey === "statementDateStart") {
            this.form.get("statementDateEnd")!.reset();
          }
          if (filterKey === "createdAtStart") {
            this.form.get("createdAtEnd")!.reset();
          }
        }
      })
    }
    if (filtersToRemove.includes("valueDateStart")) {
      delete this.filters.valueDateEnd;
    }
    if (filtersToRemove.includes("transactionDateStart")) {
      delete this.filters.transactionDateEnd;
    }
    if (filtersToRemove.includes("statementDateStart")) {
      delete this.filters.statementDateEnd;
    }
    if (filtersToRemove.includes("createdAtStart")) {
      delete this.filters.createdAtEnd;
    }
    this.reconciliationFacade.changeTransactionFilters(this.filters);
  }


}
