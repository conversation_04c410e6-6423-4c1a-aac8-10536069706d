@import 'node_modules/@jumia-cs-fin/common/assets/styles/header-components';

.filter-field{
  mat-label {
    color: var(--extra-light-color) !important;
  }
  input {
    color: var(--primary-text-color) !important;
  }
}

.filters-header {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-direction: row;

  .filters-title {
    width: auto!important;
    margin-right: 20px!important;
  }
}

#button-toggle-group {
  margin: 0 0 10px 0;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;

  mat-button-toggle {
    color: black
  }

  .mat-button-toggle::after {
    content: "";
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 1px;
    background-color: #989797;
  }

  .mat-button-toggle:last-child::after {
    display: none;
  }
}

.mat-expansion-panel {
  -webkit-user-select: none;
  padding-top: 10px;
  box-shadow: none !important;
  .mat-expansion-panel-header {
    height: 40px;
    padding-left: 0;
    background-color: white !important;
  }
  .mat-expansion-indicator:after {
    color: var(--primary-color)
  }
  .mat-expansion-panel-body {
    padding: 0;
  }
}
