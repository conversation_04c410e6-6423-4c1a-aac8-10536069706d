@import "node_modules/@jumia-cs-fin/common/assets/styles/tables";

    brad-reconciliation-reconcile-list-transaction,
    brad-reconciliation-reconcile-list-bale{
      width: 100%
    }


.reconciliationMenu {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 64px);

  .reconciliationOptions{
    margin-top: 0.5vh;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 5vh !important;
    .reconcileButton {
      color:white;
      width: 15vw;
      height: 3.9vh;
      letter-spacing: 0.01em;
      div{
        display: flex;
        flex-direction: column;
      }
    }
    .approve-unmatch {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      min-width: 50vh;
      max-width: 70vh;
      button {
        margin: 0 10px !important;
        color: white;
        width: 25vw;
        height: 3.9vh;
        letter-spacing: 0.01em;
      }
      #unmatch {
        background-color: #ef4f2b;
      }
      #approve {
        background-color: #4cc897;
      }
    }
    .reconcile-value-difference {
      font-size: .8em;
      font-weight: 400
    }
  }

  .reconciliationTables {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    width: 100%;
    margin-top: 0.5vh;
    height:100vh;

  }
}
.cs-fin-table ::ng-deep .mdc-checkbox .mdc-checkbox__background{
  border: 2px solid var(--mdc-checkbox-unselected-icon-color) !important;
}

::ng-deep .table-cards {
  height: 100%;
  padding: 0 10px !important;

  .title-expansion ::ng-deep .mat-expansion-panel {
    -webkit-user-select: none;
    padding-top: 10px;
    box-shadow: none !important;
    .mat-expansion-panel-header {
      height: 40px;
      padding-left: 0;
      background-color: white !important;
    }
    .mat-expansion-indicator:after {
      color: var(--primary-color)
    }
    .mat-expansion-panel-body {
      padding: 0;
    }

    .title-panel {
      display: flex;
      justify-content: space-between;
      align-items: center;


      .amountsAndQuantities {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: lighter;

        .divider {
          margin: 0 8px;
        }

        .bold-title {
          font-weight: bold;
        }
      }

    }


  }

  .table-detail{
    height: 83% !important;
    padding: 0 10px !important;
  }
  .table-container {
    height: 100%;
  }

}

::ng-deep .table-paginator-reconciliation {
  position: absolute;
  bottom: 0;
  left:0;
  width: 100%;
  ::ng-deep .mat-mdc-paginator-navigation-first:not(.mat-mdc-tooltip-disabled) .mat-mdc-paginator-icon,
  ::ng-deep .mat-mdc-paginator-navigation-previous:not(.mat-mdc-tooltip-disabled) .mat-mdc-paginator-icon,
  ::ng-deep .mat-mdc-paginator-navigation-next:not(.mat-mdc-tooltip-disabled) .mat-mdc-paginator-icon,
  ::ng-deep .mat-mdc-paginator-navigation-last:not(.mat-mdc-tooltip-disabled) .mat-mdc-paginator-icon,
  {
    fill: var(--primary-color) !important;
  }

}

::ng-deep .filter-dropdown {
  display: flex;
  flex-direction: row;

  cs-fin-drop-group-zone {
    width: 100%
  }
}

::ng-deep .drag-icon {
  color: var(--primary-color);
  padding-top: 10px;
  font-size: 1.2em;
  width: 15px;
  margin-left: -2px;
}

