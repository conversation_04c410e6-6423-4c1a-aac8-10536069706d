<brad-reconcile-header [account]="account"
                       (clearAccount)="clearAccount()"
                       (accountChange)="onAccountChosen($event)">
</brad-reconcile-header>
<div class="reconciliationMenu" *ngIf="account!=null">

  <div class="reconciliationOptions">
      <button class="reconcileButton" mat-raised-button color="primary" *ngIf="!isValueDifferenceDisabled"
              (click)="onReconcileClick()" [disabled]="disableReconciled()">
        <div>
          <span> {{ 'RECONCILIATION.BUTTONS.LABELS.RECONCILE' | translate }} </span>
          <div class="reconcile-value-difference">
            {{ 'RECONCILIATION.BUTTONS.LABELS.REMAINING_AMOUNT' | translate }} : {{ valueDifference | number:'1.2-2'}}
          </div>
        </div>
      </button>
      <div class="approve-unmatch" *ngIf="isValueDifferenceDisabled && !isApproveUnmatchDisabled">
        <button mat-raised-button id="unmatch" (click)="onUnmatchClick()">
          <span>{{ 'RECONCILIATION.BUTTONS.LABELS.UNMATCH' | translate }}</span>
          <div class="reconcile-value-difference">
            {{ 'RECONCILIATION.BUTTONS.LABELS.REMAINING_AMOUNT' | translate }} : {{ valueDifference | number:'1.2-2'}}
          </div>
        </button>
        <button mat-raised-button id="approve" (click)="onApproveClick()" [disabled]="areYouCreator()">
          <span>{{ 'RECONCILIATION.BUTTONS.LABELS.APPROVE' | translate }}</span>
          <div class="reconcile-value-difference">
            {{ 'RECONCILIATION.BUTTONS.LABELS.REMAINING_AMOUNT' | translate }} : {{ valueDifference | number:'1.2-2'}}
          </div>
        </button>
    </div>
  </div>

  <div class="reconciliationTables">
    <brad-reconciliation-reconcile-list-transaction
      [account]="account"
      [selectedStatus]="selectedStatus"
      [transactionAmount]="transactionAmount"
      [transactionQuantity]="selectedTransactionIds.length"
      (selectedStatusChange)="updateSelectionType($event)"
      (selectedReconciliationTransaction)="setupReconciliationViewTransaction($event)"></brad-reconciliation-reconcile-list-transaction>

    <brad-reconciliation-reconcile-list-bale
      [account]="account"
      [selectedStatus]="selectedStatus"
      [baleAmount]="baleAmount"
      [baleQuantity]="selectedBaleIds.length"
      (selectedStatusChange)="updateSelectionType($event)"
      (selectedReconciliationBale)="setupReconciliationViewBale($event)"></brad-reconciliation-reconcile-list-bale>
  </div>
</div>
<brad-accounts-selector *ngIf="account==null" (account)="onAccountChosen($event)"></brad-accounts-selector>

