import {ChangeDetectorRef, Component, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild} from '@angular/core';
import {BaleFacade} from "../../../../bale/facade/bale.facade";
import {MatPaginator} from "@angular/material/paginator";
import {MatSort, SortDirection} from "@angular/material/sort";
import {MenusConsts} from "../../../../shared/constants/menus.constants";
import {MatTableDataSource} from "@angular/material/table";
import {Bale} from "../../../../entities/bale/bale";
import {paginationConsts} from "../../../../shared/constants/core.constants";
import {Observable, Subject, takeWhile} from "rxjs";
import {CdkOverlayOrigin} from "@angular/cdk/overlay";
import {BaleFilters} from "../../../../entities/bale/bale-filters";
import {MediaMatcher} from "@angular/cdk/layout";
import {ActivatedRoute, Router} from "@angular/router";
import {finalize, takeUntil} from "rxjs/operators";
import {
  activeFiltersSeparator,
  CsFinActiveFilterChip,
  CsFinAddRemoveColumns,
  CsFinAddRemoveColumnsFacade,
  CsFinColumnDetails,
  CsFinLastUsedColumns,
  CsFinPagination
} from "@jumia-cs-fin/common";
import {PageResponse} from "../../../../entities/page-response";
import {SortFilters} from "../../../../entities/SortFilters";
import * as _ from 'lodash';
import {ReconciliationFacade} from "../../../facade/reconciliation.facade";
import {authParams} from "../../../../auth/constants/auth.constants";
import {Account} from "../../../../entities/account/account";
import {
  CsFinGroupElement,
  CsFinGroupEntity,
  CsFinRowType,
  CsFinTableGroup,
  CsFinTablePaginator
} from "../../../../entities/cs-fin-table-group";
import {CsFinGroupService} from "../../../../api/service/cs-fin-group.service";
import {MatCheckboxChange} from "@angular/material/checkbox";
import {animate, state, style, transition, trigger} from "@angular/animations";

@Component({
  selector: 'brad-reconciliation-reconcile-list-bale',
  templateUrl: './list-bale.component.html',
  styleUrls: ['./list-bale.component.scss'],
  providers: [CsFinAddRemoveColumnsFacade],
  animations: [
    trigger('detailExpand', [
      state('collapsed,void', style({height: '0px', minHeight: '0'})),
      state('expanded', style({height: '*'})),
      transition('expanded <=> collapsed', animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)')),
    ]),
  ],
})
export class ListBaleComponent implements OnInit, OnDestroy {

  @ViewChild(MatPaginator, {static: true}) paginator!: MatPaginator;
  @ViewChild(MatSort, {static: true}) sort!: MatSort;

  @Input() account!: Account;
  @Input() selectedStatus?: string;
  @Input() baleAmount!: number;
  @Input() baleQuantity!: number;
  @Output() selectedStatusChange = new EventEmitter<string>();
  @Output() selectedReconciliationBale = new EventEmitter<{row: Bale, selected: boolean, isPending: boolean}>();
  readonly MENU = MenusConsts.bale;
  auth = authParams;

  selectingReconciliation: boolean = false;

  isGroupedView: boolean = false;
  isLoading = true;
  dataSource: MatTableDataSource<CsFinGroupElement<Bale, BaleFilters>> = new MatTableDataSource<CsFinGroupElement<Bale, BaleFilters>>([]);
  expandedElement!: CsFinGroupElement<Bale, BaleFilters> | null;
  groupByColumnDetails: Map<string, CsFinColumnDetails> = new Map<string, CsFinColumnDetails>();

  displayedColumns: string[] = [];
  defaultDisplayedColumns: string[] = [];
  mobileQuery: MediaQueryList;
  pagination: CsFinPagination = {
    pageSizeOptions: paginationConsts.pageSizeOptions,
    pageSize: paginationConsts.defaultPageSize,
    pageIndex: 0,
    totalItems: 0
  };
  activeFilterChips!: Map<string, CsFinActiveFilterChip>;

  groupByColumns: string[] = [];

  isOverlayOpen$!: Observable<boolean>;
  triggerOrigin!: CdkOverlayOrigin;
  private _mobileQueryListener: () => void;
  private _onDestroy:Subject<void> = new Subject<void>();

  private filters: BaleFilters = {
    page: 1,
    size: paginationConsts.defaultPageSize
  };

  amountSelected = 0;
  baleGroupableFields: string[] = [];

  expandedGroupNames: string[] = [];

  constructor(
    public ref: ChangeDetectorRef,
    public media: MediaMatcher,
    private route: ActivatedRoute,
    private router: Router,
    private baleFacade: BaleFacade,
    private addRemoveColumnsFacade: CsFinAddRemoveColumnsFacade,
    public reconciliationFacade: ReconciliationFacade,
    public csFinGroupService: CsFinGroupService
  ) {


    this.mobileQuery = media.matchMedia('(max-width: 960px)');
    this._mobileQueryListener = () => ref.detectChanges();
    this.mobileQuery?.addEventListener('change', this._mobileQueryListener);

    CsFinLastUsedColumns.getInstance().initColumns(this.MENU, this.getColumnDetails());
  }

  ngOnInit(): void {
    this.isLoading = true;
    this.baleFacade.hideUnwantedReconciliationFilters.next(true);
    this.subscribeDisplayedColumnsChange();
    this.subscribeFiltersChange();
    this.subscribeActiveFilterChipsChange();
    this.loadBaleGroupableFields();
    this.subscribeAmountSelectedChanges();
    this.setupGroupByColumnDetails();
  }

  ngOnDestroy(): void {
    this.baleFacade.filtersChanged({});
    this.mobileQuery?.removeEventListener('change', this._mobileQueryListener);
    this._onDestroy.next();
    this._onDestroy.complete();
    this.addRemoveColumnsFacade.isOverlayOpen.next(false);
    this.baleFacade.selectedBaleChangeBehaviorSubject.next(-1);
  }

  updateSelectionCache(row: any, selected: boolean) {
    if (this.selectedStatus && selected && this.selectedStatus !== row.reconcileStatus) {
      return;
    }
    this.selectedStatus = row.reconcileStatus;
    this.selectedStatusChange.emit(row.reconcileStatus);
    const key = row.id;

    this.reconciliationFacade.updateSelectionBaleCache({
      ...this.reconciliationFacade.selectionBaleCache,
      [key]: selected
    })

  }

  bulkUpdateSelectionCache(rowIds: any[], selected: boolean) {
    let updatedCache: Record<string, boolean> = {};
    rowIds.forEach((rowId: any) => {
      updatedCache[rowId] = selected;
    });
    this.reconciliationFacade.updateSelectionBaleCache({
      ...this.reconciliationFacade.selectionBaleCache,
      ...updatedCache
    });
  }

  private subscribeFiltersChange(): void {
    this.baleFacade.filters$
      .pipe(takeUntil(this._onDestroy))
      .subscribe(async (filters: BaleFilters) => {
        if (filters) {
          this.filters = filters;
          if (this.isGroupedView) {
            this.loadBaleGroups();
          } else {
            this.loadBales();
          }
        }
      });
  }



  private subscribeActiveFilterChipsChange(): void {
    this.baleFacade.activeFiltersChips$
      .pipe(takeUntil(this._onDestroy))
      .subscribe((activeFilterChips: Map<string, CsFinActiveFilterChip>) => {
        this.activeFilterChips = activeFilterChips;
      });
  }

  private subscribeDisplayedColumnsChange(): void {

    this.addRemoveColumnsFacade.fetchDisplayedColumns
      .pipe(takeUntil(this._onDestroy))
      .subscribe((columns: CsFinAddRemoveColumns) => {
        setTimeout(() => {
            if (columns) {
              this.displayedColumns = columns.displayedColumns ? columns.displayedColumns : []
              this.defaultDisplayedColumns = this.displayedColumns;
            }
          }
          , 0);
      });
  }

  setupGroupByColumnDetails() {
    let columnDetails = this.getColumnDetails();
    this.groupByColumnDetails.set("ENTRY_NO", columnDetails.filter((column: CsFinColumnDetails) => column.code === 'entryNo')[0]);
    this.groupByColumnDetails.set("DOCUMENT_NO", columnDetails.filter((column: CsFinColumnDetails) => column.code === 'documentNo')[0]);
    this.groupByColumnDetails.set("DOCUMENT_TYPE", columnDetails.filter((column: CsFinColumnDetails) => column.code === 'documentType')[0]);
    this.groupByColumnDetails.set("POSTING_DATE", columnDetails.filter((column: CsFinColumnDetails) => column.code === 'postingDate')[0]);
    this.groupByColumnDetails.set("ACCOUNT_POSTING_GROUP", columnDetails.filter((column: CsFinColumnDetails) => column.code === 'accountPostingGroup')[0]);
    this.groupByColumnDetails.set("DESCRIPTION", columnDetails.filter((column: CsFinColumnDetails) => column.code === 'description')[0]);
    this.groupByColumnDetails.set("SOURCE_CODE", columnDetails.filter((column: CsFinColumnDetails) => column.code === 'sourceCode')[0]);
    this.groupByColumnDetails.set("REASON_CODE", columnDetails.filter((column: CsFinColumnDetails) => column.code === 'reasonCode')[0]);
    this.groupByColumnDetails.set("BUS_LINE", columnDetails.filter((column: CsFinColumnDetails) => column.code === 'busLine')[0]);
    this.groupByColumnDetails.set("DEPARTMENT", columnDetails.filter((column: CsFinColumnDetails) => column.code === 'department')[0]);
    this.groupByColumnDetails.set("DIRECTION", columnDetails.filter((column: CsFinColumnDetails) => column.code === 'direction')[0]);
    this.groupByColumnDetails.set("AMOUNT", columnDetails.filter((column: CsFinColumnDetails) => column.code === 'amount')[0]);
    this.groupByColumnDetails.set("REMAINING_AMOUNT", columnDetails.filter((column: CsFinColumnDetails) => column.code === 'remainingAmount')[0]);
    this.groupByColumnDetails.set("TRANSACTION_CURRENCY", columnDetails.filter((column: CsFinColumnDetails) => column.code === 'currency')[0]);
    this.groupByColumnDetails.set("AMOUNT_LCY", columnDetails.filter((column: CsFinColumnDetails) => column.code === 'amountLcy')[0]);
    this.groupByColumnDetails.set("BALANCE_ACCOUNT_TYPE", columnDetails.filter((column: CsFinColumnDetails) => column.code === 'balanceAccountType')[0]);
    this.groupByColumnDetails.set("IS_OPEN", columnDetails.filter((column: CsFinColumnDetails) => column.code === 'isOpen')[0]);
    this.groupByColumnDetails.set("REVERSED", columnDetails.filter((column: CsFinColumnDetails) => column.code === 'isReversed')[0]);
    this.groupByColumnDetails.set("POSTED_BY", columnDetails.filter((column: CsFinColumnDetails) => column.code === 'postedBy')[0]);
    this.groupByColumnDetails.set("EXTERNAL_DOCUMENT_NO", columnDetails.filter((column: CsFinColumnDetails) => column.code === 'externalDocumentNo')[0]);
    this.groupByColumnDetails.set("BALE_TIMESTAMP", columnDetails.filter((column: CsFinColumnDetails) => column.code === 'baleTimestamp')[0]);
    this.groupByColumnDetails.set('RECONCILE_STATUS', columnDetails.filter((column: CsFinColumnDetails) => column.code === 'reconcileStatus')[0]);
    this.groupByColumnDetails.set('RECONCILIATION_ID', columnDetails.filter((column: CsFinColumnDetails) => column.code === 'reconciliationId')[0]);
    this.groupByColumnDetails.set('RECONCILIATION_CREATOR', columnDetails.filter((column: CsFinColumnDetails) => column.code === 'reconciliationCreator')[0]);
    this.groupByColumnDetails.set('RECONCILIATION_CREATION_DATE', columnDetails.filter((column: CsFinColumnDetails) => column.code === 'reconciliationCreationDate')[0]);
    this.groupByColumnDetails.set('RECONCILIATION_REVIEWER', columnDetails.filter((column: CsFinColumnDetails) => column.code === 'reconciliationReviewer')[0]);
    this.groupByColumnDetails.set('RECONCILIATION_REVIEW_DATE', columnDetails.filter((column: CsFinColumnDetails) => column.code === 'reconciliationReviewDate')[0]);
  }


  getColumnDetails(): CsFinColumnDetails[] {
      return [
        {position: 0, name: 'Select', code: 'select', isActive: true, isRemovable: false, isDefault: true},
        {position: 1, name: 'Posting Date', code: 'postingDate', isActive: true, isRemovable: true, isDefault: true},
        {position: 2, name: 'DocumentNo', code: 'documentNo', isActive: true, isRemovable: true, isDefault: true},
        {position: 3, name: 'External DocumentNo', code: 'externalDocumentNo', isActive: true, isRemovable: true, isDefault: true},
        {position: 4, name: 'Description', code: 'description', isActive: true, isRemovable: true, isDefault: true},
        {position: 5, name: 'Source Code', code: 'sourceCode', isActive: true, isRemovable: true, isDefault: true},
        {position: 6, name: 'Direction', code: 'direction', isActive: true, isRemovable: true, isDefault: true},
        {position: 7, name: 'Amount', code: 'amount', isActive: true, isRemovable: true, isDefault: true},
        {position: 8, name: 'Bal Account Type', code: 'balanceAccountType', isActive: true, isRemovable: true, isDefault: true},
        {position: 9, name: 'Remaining Amount', code: 'remainingAmount', isActive: false, isRemovable: true, isDefault: false},
        {position: 10, name: 'Account Posting Group', code: 'accountPostingGroup', isActive: false, isRemovable: true, isDefault: false},
        {position: 11, name: 'Entry No', code: 'entryNo', isActive: false, isRemovable: true, isDefault: false},
        {position: 12, name: 'Currency', code: 'currency', isActive: false, isRemovable: true, isDefault: false},
        {position: 13, name: 'Document Type', code: 'documentType', isActive: false, isRemovable: true, isDefault: false},
        {position: 14, name: 'Reason Code', code: 'reasonCode', isActive: false, isRemovable: true, isDefault: false},
        {position: 15, name: 'Bus Line', code: 'busLine', isActive: false, isRemovable: true, isDefault: false},
        {position: 16, name: 'Department', code: 'department', isActive: false, isRemovable: true, isDefault: false},
        {position: 17, name: 'Amount Lcy', code: 'amountLcy', isActive: false, isRemovable: true, isDefault: false},
        {position: 18, name: 'Is Open', code: 'isOpen', isActive: false, isRemovable: true, isDefault: false},
        {position: 19, name: 'Is Reversed', code: 'isReversed', isActive: false, isRemovable: true, isDefault: false},
        {position: 20, name: 'Posted By', code: 'postedBy', isActive: false, isRemovable: true, isDefault: false},
        {position: 21, name: 'Bale Timestamp', code: 'baleTimestamp', isActive: false, isRemovable: true, isDefault: false},
        {position: 22, name: 'Reconcile Status', code: 'reconcileStatus', isActive: false, isRemovable: true, isDefault: false},
        {position: 23, name: 'Reconciliation ID', code: 'reconciliationId', isActive: false, isRemovable: true, isDefault: false},
        {position: 24, name: 'Reconciliation Creator', code: 'reconciliationCreator', isActive: false, isRemovable: true, isDefault: false},
        {position: 25, name: 'Reconciliation Creation Date', code: 'reconciliationCreationDate', isActive: false, isRemovable: true, isDefault: false},
        {position: 26, name: 'Reconciliation Reviewer', code: 'reconciliationReviewer', isActive: false, isRemovable: true, isDefault: false},
        {position: 27, name: 'Reconciliation Review Date', code: 'reconciliationReviewDate', isActive: false, isRemovable: true, isDefault: false}
      ];
    }

  onActiveFilterRemoveClick(removedChipKey: string): void {
    const filtersToRemove = removedChipKey.split(activeFiltersSeparator);
    if (filtersToRemove) {
      filtersToRemove.forEach((filterKey: string) => {
        // @ts-ignore
        delete this.filters[filterKey];
      })
    }
    if (filtersToRemove.includes("postingDateStart")) {
      delete this.filters.postingDateEnd;
    }
    this.reconciliationFacade.changeBaleFilters(this.filters);
  }

  loadBalesOrGroups() {
    if (this.isGroupedView) {
      this.loadBaleGroups();
    } else {
      this.loadBales();
    }
  }

  loadBales() {
    this.filters.idCompany = this.account.companyID;
    this.filters.accountNumber = this.account.accountNumber;
    this.baleFacade.getAll(this.filters)
      .pipe(takeUntil(this._onDestroy))
      .pipe(
        finalize(() => {
          this.isLoading = false;
          this.reconciliationFacade.isBaleLoadingSetSubject.next(false);
        })
      )
      .subscribe((result: PageResponse<Bale>) => {
        let elements: CsFinGroupEntity<Bale>[] = [];
        result.results.forEach((item: Bale) => {
          elements.push({
            ...item,
            groupName: 'Bale',
            rowType: CsFinRowType.ENTITY
          } as CsFinGroupEntity<Bale>);
        });
        this.dataSource = new MatTableDataSource<CsFinGroupElement<Bale, BaleFilters>>(elements);
        this.pagination.totalItems = result.total;
        this.pagination.pageSize = result.size;
        this.pagination.pageIndex = result.page - 1;
        this.setSort();
      });

  }

  loadBaleGroupableFields() {
    this.baleFacade.getBaleGroupableFields()
      .pipe(takeUntil(this._onDestroy))
      .subscribe((result: string[]) => {
        this.baleGroupableFields = result;
      });
  }

  loadBaleGroups(sortFilters?: SortFilters) {
    let filters: BaleFilters = {
      ...this.filters,
      idCompany: this.account.companyID,
      accountNumber: this.account.accountNumber,
    }

    if (sortFilters) {
      filters.orderField = sortFilters.orderField;
      filters.orderDirection = sortFilters.orderDirection;
    }

    this.csFinGroupService.loadGroups(
      this.groupByColumns,
      this.dataSource,
      true,
      this.baleFacade,
      filters
    ).pipe(
      takeWhile(() => this.isGroupedView),
      finalize(() => {
        this.loadInfo();
      }))
      .subscribe((newGroups:CsFinGroupElement<Bale, BaleFilters>[]) => {
        this.dataSource.data = newGroups;
    });
  }

  loadInfo(): void {
    const allGroups: CsFinTableGroup<BaleFilters>[] = this.dataSource.data
      .filter((element: CsFinGroupElement<Bale, BaleFilters>) =>
        element.rowType === CsFinRowType.GROUP) as CsFinTableGroup<BaleFilters>[];

    this.csFinGroupService.loadAllGroupInfo(allGroups, this.baleFacade)
      .subscribe((groupedInfo: CsFinTableGroup<BaleFilters>[]) => {
        this.dataSource.data = groupedInfo;
      });

  }

  triggerOverlay(trigger: CdkOverlayOrigin): void {
    this.triggerOrigin = trigger;
    this.addRemoveColumnsFacade.isOverlayOpen.next(true);
  }

  private setSort(): void {
    if (!this.sort) {
      return;
    }

    const previousSort = {
      active: this.sort.active,
      direction: this.sort.direction
    };
    if (this.filters.orderField) {
      this.sort.active = this.decodeSortField(this.filters.orderField);
    }
    if (this.filters.orderDirection) {
      this.sort.direction = this.filters.orderDirection.toLowerCase() as SortDirection;
    }

    const sort = {
      active: this.sort.active,
      direction: this.sort.direction
    };
    if (!_.isEqual(previousSort, sort)) {
      this.sort.sortChange.emit(this.sort);
    }
  }
  encodeSortField(field: string): string {
    return this.baleFacade.encodeSortField(field);
  }

  decodeSortField(field: string): string {
    return this.baleFacade.decodeSortField(field);
  }

  onPageChange(event: any) {
    if (event) {
      this.filters.page = event.pageIndex + 1;
      this.filters.size = event.pageSize;
    }
    this.baleFacade.filtersChanged(this.filters);
  }

  onSortChange(event: any): void {
    const sortFiltersBefore = {
      orderDirection: this.filters.orderDirection,
      orderField: this.filters.orderField
    };

    this.filters.orderDirection = event.direction?.toUpperCase();
    this.filters.orderField = this.encodeSortField(event.active);

    if (this.isGroupedView) {
      this.groupedSort(this.filters as SortFilters);
      return;
    }

    if(!_.isEqual(sortFiltersBefore, this.filters as SortFilters)){
      this.baleFacade.filtersChanged(this.filters);
    }

  }

  groupedSort(sortFilters: SortFilters) {
    this.loadBaleGroups(sortFilters);
  }

  checkboxLabel(row?: any): string {
    return `${this.reconciliationFacade.selectionBale.isSelected(row) ? 'deselect' : 'select'} row ${row.id}`;
  }

  onRowClick(event: MouseEvent, row: any) {
    event.stopPropagation();
    if (!this.sameStatusAsCurrentSelection(row)) {
      return;
    }
    if (!this.belongsToReconciliation(row)) {
      return;
    }
    if (row.reconcileStatus !== 'NOT_RECONCILED') {
      this.selectedStatus = row.reconcileStatus;
      this.selectedStatusChange.emit(row.reconcileStatus);
      this.manageReconciliationSelection(row, this.reconciliationFacade.selectionBale.isSelected(row.id));
      return;
    }

    this.reconciliationFacade.selectionBale.toggle(row.id);

    const selected = this.reconciliationFacade.selectionBale.isSelected(row.id);
    this.updateSelectionCache(row, selected);
  }

  manageReconciliationSelection(row: Bale, selected: boolean) {
    this.selectedReconciliationBale.emit({row, selected, isPending: row.reconcileStatus === 'PENDING_APPROVAL'});
  }

  subscribeAmountSelectedChanges(): void {
    this.reconciliationFacade.getSelectionBaleCacheUpdates()
      .pipe(takeUntil(this._onDestroy))
      .subscribe((selectionBaleCache: Record<string, boolean>) => {
        this.amountSelected = Object.values(selectionBaleCache).filter((value: boolean) => value).length;
      });

  }

  groupKeyExists(groupKey: string): boolean {
    return !!this.baleGroupableFields.find((field: string) => field.toLowerCase() === groupKey.toLowerCase())
  }

  groupOnClick() {
    this.isGroupedView = this.groupByColumns.length != 0;
    this.expandedElement = null;
    if (this.isGroupedView) {
      this.loadBaleGroups();
    } else {
      this.loadBales();
    }
  }

  expandGroup(group: CsFinTableGroup<BaleFilters>) {
    group.expanded = !group.expanded;
    if (group.expanded) {
      this.expandedGroupNames.push(group.groupName);
      let filters: BaleFilters = {
        ...this.filters,
        idCompany: this.account.companyID,
        accountNumber: this.account.accountNumber,
      }
      this.csFinGroupService.loadGroupEntities(group, this.baleFacade, filters)
        .pipe(takeUntil(this._onDestroy))
        .subscribe((newElements: CsFinGroupElement<Bale, BaleFilters>[]) => {
          let newDataSource : CsFinGroupElement<Bale, BaleFilters>[] = [];

          this.dataSource.data.map((element: CsFinGroupElement<Bale, BaleFilters>) => {
            if (!(element.rowType === CsFinRowType.GROUP && element.groupName === group.groupName)) {
              newDataSource.push(element);
            } else {
              newDataSource.push(...newElements);
            }
          });
          this.dataSource.data = newDataSource;
        });
    } else {
      this.expandedGroupNames = this.expandedGroupNames.filter((groupName: string) => groupName !== group.groupName);
      this.dataSource.data = this.dataSource.data.filter((element: CsFinGroupElement<Bale, BaleFilters>) => {
        return !(element.rowType !== CsFinRowType.GROUP && element.groupName === group.groupName);
      });
    }
  }

  onCheckboxSelectAll(event: MatCheckboxChange, group: CsFinTableGroup<BaleFilters>) {
    let allIds: number[] = group.allIds || [];
    if (event.checked) {
      this.bulkUpdateSelectionCache(allIds, true);
      allIds.forEach((row: any) => this.reconciliationFacade.selectionBale.select(row));
    } else {
      this.bulkUpdateSelectionCache(allIds, false);
      allIds.forEach((row: any) => this.reconciliationFacade.selectionBale.deselect(row));
    }
  }


  onGroupPageChange(event: any, paginator: CsFinTablePaginator) {
    this.csFinGroupService.onGroupPageChange(event, paginator, this.dataSource, this.baleFacade)
      .subscribe((elements:CsFinGroupElement<Bale, BaleFilters>[]) => {
        this.dataSource.data = elements;
      });
  }

  isGroupSelected(group: CsFinTableGroup<Bale>): boolean {
    let allIds: number[] = group.allIds || [];
    return allIds.some((row: any) => this.reconciliationFacade.selectionBale.isSelected(row));
  }

  indeterminate(group:CsFinTableGroup<Bale>): boolean {
    let allIds: number[] = group.allIds || [];
    let selectedIds: number[] = [];
    allIds.forEach((row: any) => {
      if (this.reconciliationFacade.selectionBale.isSelected(row)) {
        selectedIds.push(row);
      }
    });
    if (this.disableGroup(group)) {
      return true;
    }
    return selectedIds.length > 0 && selectedIds.length < allIds.length;
  }

    getMaxSpan():number {
    return this.displayedColumns.length;
  }

  sameStatusAsCurrentSelection(row: any): boolean {
    if (!this.selectedStatus) {
      return true;
    }
    if (this.selectedStatus === row.reconcileStatus)
      return this.sameReconciliationAsCurrentSelection(row);
    return false;
  }

  sameReconciliationAsCurrentSelection(row: any): boolean {
    if (!this.selectingReconciliation) {
      return true;
    }
    return this.reconciliationFacade.selectionBale.isSelected(row.id);
  }

  togglePendingView() {
    this.selectingReconciliation = !this.selectingReconciliation;
  }

  belongsToReconciliation(row: any): boolean {
    if (!this.selectingReconciliation) {
      return true;
    }
    return this.reconciliationFacade.selectionBale.isSelected(row.id);
  }

  disableGroup(group:CsFinTableGroup<Bale>): boolean{
    return group.isReconciliationShared == false && group.isStatusShared == false;
  }

  getGroupNameTrimmed(groupFields: string[]): { trimmed: string, full: string }[] {
    return groupFields.map((word: string) => {
      if (word.length > 15) {
        return { trimmed: word.substring(0, 15) + "...", full: word };
      } else {
        return { trimmed: word, full: word };
      }
    });
  }

  getRowClass(row: CsFinGroupElement<Bale, BaleFilters>): string {
    const withDetailsRows = this.dataSource.data.filter(r => this.isEntity(r));

    const rowIndex = withDetailsRows.indexOf(row);

    if (rowIndex % 2 === 0) {
      return 'even-entity';
    } else {
      return 'odd-entity';
    }
  }

  isEntity(row: CsFinGroupElement<Bale, BaleFilters>): boolean {
    return row.rowType === CsFinRowType.ENTITY;
  }
}
