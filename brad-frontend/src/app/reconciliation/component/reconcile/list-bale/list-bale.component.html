<main class="container" cdkDropListGroup>
  <mat-card class="table-cards">
    <section class="title-expansion">
      <mat-accordion>
        <mat-expansion-panel>
          <mat-expansion-panel-header>
            <mat-panel-title class="title-panel">
              <span>{{"RECONCILIATION.BALES_TITLE" | translate }}</span>
              <div class="amountsAndQuantities">
                <div>
                  <span class="bold-title">Nº of Ledger Entries: </span>
                  <span>{{baleQuantity}} Entries </span>
                </div>
                <span class="divider"> / </span>
                <div>
                  <span class="bold-title">Total Amount: </span>
                  <span>{{baleAmount | number:'1.2-2'}} {{account.currency.code}}</span>
                </div>
              </div>
            </mat-panel-title>
          </mat-expansion-panel-header>
          <cs-fin-active-filters [chipsMap]="activeFilterChips"
                                 (onRemove)="onActiveFilterRemoveClick($event)">
          </cs-fin-active-filters>
        </mat-expansion-panel>
      </mat-accordion>
    </section>
    <section class="filter-dropdown filters" *ngIf="dataSource">
      <cs-fin-drop-group-zone
        [groupByColumnDetails]="groupByColumnDetails"
        [displayedColumns]="displayedColumns"
        [groupByColumns]="groupByColumns"
        [expandedGroupNames]="expandedGroupNames"
        (groupOnClick)="groupOnClick()"></cs-fin-drop-group-zone>
      <button mat-stroked-button id="change-columns-btn" color="primary" fxShow [fxHide.xs]="true" [fxHide.sm]="true"
              [disabled]="isGroupedView" cdkOverlayOrigin #trigger="cdkOverlayOrigin"
              (click)="triggerOverlay(trigger)">
        <mat-icon>table_rows</mat-icon>
      </button>
    </section>

    <section class="table-detail responsive-table">
      <mat-progress-bar mode="indeterminate" *ngIf="isLoading"></mat-progress-bar>

      <div class="table-container" *ngIf="!isLoading">

        <table mat-table class="cs-fin-table" matSort multiTemplateDataRows [dataSource]="dataSource"  [class.loading]="isLoading"
               (matSortChange)="onSortChange($event)" cdkDropList cdkDropListSortingDisabled>

          <ng-container matColumnDef="select">
            <th mat-header-cell *matHeaderCellDef></th>
            <td mat-cell *matCellDef="let bale" [ngClass]="{'select-styling': isGroupedView}">
              <mat-checkbox [checked]="reconciliationFacade.selectionBale.isSelected(bale.id)"
                            [aria-label]="checkboxLabel(bale)"
                            [disabled]="!sameStatusAsCurrentSelection(bale)"
                            (click)="onRowClick($event, bale)">
              </mat-checkbox>
            </td>
          </ng-container>

          <ng-container matColumnDef="entryNo">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag class="header-wrap" [cdkDragData]="'ENTRY_NO'"
                [cdkDragDisabled]="!groupKeyExists('ENTRY_NO')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('ENTRY_NO')">drag_indicator</mat-icon>
              {{ 'BALE.FIELDS.ENTRY_NO' | translate }}

              <mat-chip *cdkDragPreview>{{ 'BALE.FIELDS.ENTRY_NO' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let bale" matTooltip="{{bale.entryNo}}"> {{bale.entryNo}} </td>
          </ng-container>


          <ng-container matColumnDef="documentNo">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag class="header-wrap" [cdkDragData]="'DOCUMENT_NO'"
                [cdkDragDisabled]="!groupKeyExists('DOCUMENT_NO')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('DOCUMENT_NO')">drag_indicator</mat-icon>
              {{ 'BALE.FIELDS.DOCUMENT_NO' | translate }}

              <mat-chip *cdkDragPreview>{{ 'BALE.FIELDS.DOCUMENT_NO' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let bale" matTooltip="{{bale.documentNo}}"> {{bale.documentNo}} </td>
          </ng-container>

          <ng-container matColumnDef="documentType">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag class="header-wrap" [cdkDragData]="'DOCUMENT_TYPE'"
                [cdkDragDisabled]="!groupKeyExists('DOCUMENT_TYPE')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('DOCUMENT_TYPE')">drag_indicator</mat-icon>
              {{ 'BALE.FIELDS.DOCUMENT_TYPE' | translate }}

              <mat-chip *cdkDragPreview>{{ 'BALE.FIELDS.DOCUMENT_TYPE' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let bale"> {{bale.documentType}} </td>
          </ng-container>

          <ng-container matColumnDef="postingDate">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag class="header-wrap" [cdkDragData]="'POSTING_DATE'"
                [cdkDragDisabled]="!groupKeyExists('POSTING_DATE')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('POSTING_DATE')">drag_indicator</mat-icon>
              {{ 'BALE.FIELDS.POSTING_DATE' | translate }}

              <mat-chip *cdkDragPreview>{{ 'BALE.FIELDS.POSTING_DATE' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let bale"> {{bale.postingDate}} </td>
          </ng-container>

          <ng-container matColumnDef="accountPostingGroup">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag class="header-wrap" [cdkDragData]="'ACCOUNT_POSTING_GROUP'"
                [cdkDragDisabled]="!groupKeyExists('ACCOUNT_POSTING_GROUP')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('ACCOUNT_POSTING_GROUP')">drag_indicator</mat-icon>
              {{ 'BALE.FIELDS.ACCOUNT_POSTING_GROUP' | translate }}

              <mat-chip *cdkDragPreview>{{ 'BALE.FIELDS.ACCOUNT_POSTING_GROUP' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let bale"> {{bale.accountPostingGroup}} </td>
          </ng-container>

          <ng-container matColumnDef="description">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag [cdkDragData]="'DESCRIPTION'"
                [cdkDragDisabled]="!groupKeyExists('DESCRIPTION')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('DESCRIPTION')">drag_indicator</mat-icon>
              {{ 'BALE.FIELDS.DESCRIPTION' | translate }}

              <mat-chip *cdkDragPreview>{{ 'BALE.FIELDS.DESCRIPTION' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let bale" matTooltip="{{bale.description}}"> {{bale.description}} </td>
          </ng-container>

          <ng-container matColumnDef="sourceCode">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag class="header-wrap" [cdkDragData]="'SOURCE_CODE'"
                [cdkDragDisabled]="!groupKeyExists('SOURCE_CODE')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('SOURCE_CODE')">drag_indicator</mat-icon>
              {{ 'BALE.FIELDS.SOURCE_CODE' | translate }}

              <mat-chip *cdkDragPreview>{{ 'BALE.FIELDS.SOURCE_CODE' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let bale" matTooltip="{{bale.sourceCode}}"> {{bale.sourceCode}} </td>
          </ng-container>

          <ng-container matColumnDef="reasonCode">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag class="header-wrap" [cdkDragData]="'REASON_CODE'"
                [cdkDragDisabled]="!groupKeyExists('REASON_CODE')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('REASON_CODE')">drag_indicator</mat-icon>
              {{ 'BALE.FIELDS.REASON_CODE' | translate }}

              <mat-chip *cdkDragPreview>{{ 'BALE.FIELDS.REASON_CODE' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let bale" matTooltip="{{bale.reasonCode}}"> {{bale.reasonCode}} </td>
          </ng-container>

          <ng-container matColumnDef="busLine">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag class="header-wrap" [cdkDragData]="'BUS_LINE'"
                [cdkDragDisabled]="!groupKeyExists('BUS_LINE')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('BUS_LINE')">drag_indicator</mat-icon>
              {{ 'BALE.FIELDS.BUS_LINE' | translate }}

              <mat-chip *cdkDragPreview>{{ 'BALE.FIELDS.BUS_LINE' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let bale"> {{bale.busLine}} </td>
          </ng-container>

          <ng-container matColumnDef="department">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag [cdkDragData]="'DEPARTMENT'"
                [cdkDragDisabled]="!groupKeyExists('DEPARTMENT')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('DEPARTMENT')">drag_indicator</mat-icon>
              {{ 'BALE.FIELDS.DEPARTMENT' | translate }}

              <mat-chip *cdkDragPreview>{{ 'BALE.FIELDS.DEPARTMENT' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let bale"> {{bale.department}} </td>
          </ng-container>

          <ng-container matColumnDef="direction">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag [cdkDragData]="'DIRECTION'"
                [cdkDragDisabled]="!groupKeyExists('DIRECTION')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('DIRECTION')">drag_indicator</mat-icon>
              {{ 'BALE.FIELDS.DIRECTION' | translate }}

              <mat-chip *cdkDragPreview>{{ 'BALE.FIELDS.DIRECTION' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let bale">
              {{bale?.direction}}
            </td>
          </ng-container>

          <ng-container matColumnDef="amount">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag [cdkDragData]="'AMOUNT'"
                [cdkDragDisabled]="!groupKeyExists('AMOUNT')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('AMOUNT')">drag_indicator</mat-icon>
              {{ 'BALE.FIELDS.AMOUNT' | translate }}

              <mat-chip *cdkDragPreview>{{ 'BALE.FIELDS.AMOUNT' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let bale" matTooltip="{{bale.amount | number:'1.2-2'}}">
              <div class="amountWithUsd">
                {{bale?.amount | number:'1.2-2'}} {{bale?.transactionCurrency.symbol}}
                <div *ngIf="bale?.amountUsd else emptyAmount;">
                  {{bale?.amountUsd | number:'1.2-2'}} $
                </div>
              </div>
            </td>
          </ng-container>

          <ng-container matColumnDef="remainingAmount">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag class="header-wrap" [cdkDragData]="'REMAINING_AMOUNT'"
                [cdkDragDisabled]="!groupKeyExists('REMAINING_AMOUNT')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('REMAINING_AMOUNT')">drag_indicator</mat-icon>
              {{ 'BALE.FIELDS.REMAINING_AMOUNT' | translate }}

              <mat-chip *cdkDragPreview>{{ 'BALE.FIELDS.REMAINING_AMOUNT' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let bale"> {{bale.remainingAmount}} </td>
          </ng-container>

          <ng-container matColumnDef="currency">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag [cdkDragData]="'TRANSACTION_CURRENCY'"
                [cdkDragDisabled]="!groupKeyExists('TRANSACTION_CURRENCY')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('TRANSACTION_CURRENCY')">drag_indicator</mat-icon>
              {{ 'BALE.FIELDS.TRANSACTION_CURRENCY' | translate }}

              <mat-chip *cdkDragPreview>{{ 'BALE.FIELDS.TRANSACTION_CURRENCY' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let bale"> {{bale.transactionCurrency.code}} </td>
          </ng-container>

          <ng-container matColumnDef="amountLcy">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag [cdkDragData]="'AMOUNT_LCY'"
                [cdkDragDisabled]="!groupKeyExists('AMOUNT_LCY')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('AMOUNT_LCY')">drag_indicator</mat-icon>
              {{ 'BALE.FIELDS.AMOUNT_LCY' | translate }}

              <mat-chip *cdkDragPreview>{{ 'BALE.FIELDS.AMOUNT_LCY' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let bale"> {{bale.amountLcy}} </td>
          </ng-container>

          <ng-container matColumnDef="balanceAccountType">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag class="header-wrap" [cdkDragData]="'BALANCE_ACCOUNT_TYPE'"
                [cdkDragDisabled]="!groupKeyExists('BALANCE_ACCOUNT_TYPE')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('BALANCE_ACCOUNT_TYPE')">drag_indicator</mat-icon>
              {{ 'BALE.FIELDS.BALANCE_ACCOUNT_TYPE' | translate }}

              <mat-chip *cdkDragPreview>{{ 'BALE.FIELDS.BALANCE_ACCOUNT_TYPE' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let bale" matTooltip="{{bale.balanceAccountType}}"> {{bale.balanceAccountType}} </td>
          </ng-container>

          <ng-container matColumnDef="isOpen">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag [cdkDragData]="'IS_OPEN'"
                [cdkDragDisabled]="!groupKeyExists('IS_OPEN')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('IS_OPEN')">drag_indicator</mat-icon>
              {{ 'BALE.FIELDS.IS_OPEN' | translate }}

              <mat-chip *cdkDragPreview>{{ 'BALE.FIELDS.IS_OPEN' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let bale"> {{bale.isOpen}} </td>
          </ng-container>

          <ng-container matColumnDef="isReversed">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag [cdkDragData]="'REVERSED'"
                [cdkDragDisabled]="!groupKeyExists('REVERSED')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('REVERSED')">drag_indicator</mat-icon>
              {{ 'BALE.FIELDS.IS_REVERSED' | translate }}

              <mat-chip *cdkDragPreview>{{ 'BALE.FIELDS.IS_REVERSED' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let bale"> {{bale.isReversed}} </td>
          </ng-container>

          <ng-container matColumnDef="postedBy">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag class="header-wrap" [cdkDragData]="'POSTED_BY'"
                [cdkDragDisabled]="!groupKeyExists('POSTED_BY')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('POSTED_BY')">drag_indicator</mat-icon>
              {{ 'BALE.FIELDS.POSTED_BY' | translate }}

              <mat-chip *cdkDragPreview>{{ 'BALE.FIELDS.POSTED_BY' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let bale"> {{bale.postedBy}} </td>
          </ng-container>

          <ng-container matColumnDef="externalDocumentNo">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag class="header-wrap" [cdkDragData]="'EXTERNAL_DOCUMENT_NO'"
                [cdkDragDisabled]="!groupKeyExists('EXTERNAL_DOCUMENT_NO')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('EXTERNAL_DOCUMENT_NO')">drag_indicator</mat-icon>
              {{ 'BALE.FIELDS.EXTERNAL_DOCUMENT_NO' | translate }}

              <mat-chip *cdkDragPreview>{{ 'BALE.FIELDS.EXTERNAL_DOCUMENT_NO' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let bale" matTooltip="{{bale.externalDocumentNo}}"> {{bale.externalDocumentNo}} </td>
          </ng-container>

          <ng-container matColumnDef="baleTimestamp">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag [cdkDragData]="'BALE_TIMESTAMP'"
                [cdkDragDisabled]="!groupKeyExists('BALE_TIMESTAMP')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('BALE_TIMESTAMP')">drag_indicator</mat-icon>
              {{ 'BALE.FIELDS.BALE_TIMESTAMP' | translate }}

              <mat-chip *cdkDragPreview>{{ 'BALE.FIELDS.BALE_TIMESTAMP' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let bale"> {{bale.baleTimestamp}} </td>
          </ng-container>

          <ng-container matColumnDef="baTimestamp">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag [cdkDragData]="'ACCOUNT_TIMESTAMP'"
                [cdkDragDisabled]="!groupKeyExists('ACCOUNT_TIMESTAMP')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('ACCOUNT_TIMESTAMP')">drag_indicator</mat-icon>
              {{ 'BALE.FIELDS.ACCOUNT_TIMESTAMP' | translate }}

              <mat-chip *cdkDragPreview>{{ 'BALE.FIELDS.ACCOUNT_TIMESTAMP' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let bale"> {{bale.accountTimestamp}} </td>
          </ng-container>

          <ng-container matColumnDef="reconcileStatus">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag [cdkDragData]="'reconcileStatus'"
                [cdkDragDisabled]="!groupKeyExists('RECONCILE_STATUS')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('RECONCILE_STATUS')">drag_indicator</mat-icon>
              {{ 'BALE.FIELDS.RECONCILE_STATUS' | translate }}

              <mat-chip *cdkDragPreview>{{ 'BALE.FIELDS.RECONCILE_STATUS' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let bale"> {{bale.reconcileStatus}} </td>
          </ng-container>

          <ng-container matColumnDef="reconciliationId">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag class="header-wrap" [cdkDragData]="'RECONCILIATION_ID'"
                [cdkDragDisabled]="!groupKeyExists('RECONCILIATION_ID')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('RECONCILIATION_ID')">drag_indicator</mat-icon>
              {{ 'RECONCILIATION.FIELDS.RECONCILIATION_ID' | translate }}

              <mat-chip *cdkDragPreview>{{ 'RECONCILIATION.FIELDS.ID' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let bale">
              {{bale?.reconciliationId}}
            </td>
          </ng-container>

          <ng-container matColumnDef="reconciliationCreator">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag class="header-wrap" [cdkDragData]="'RECONCILIATION_CREATOR'"
                [cdkDragDisabled]="!groupKeyExists('RECONCILIATION_CREATOR')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('RECONCILIATION_CREATOR')">drag_indicator</mat-icon>
              {{ 'RECONCILIATION.FIELDS.CREATOR' | translate }}

              <mat-chip *cdkDragPreview>{{ 'RECONCILIATION.FIELDS.CREATOR' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let bale">
              {{bale?.reconciliationCreator}}
            </td>
          </ng-container>

          <ng-container matColumnDef="reconciliationCreationDate">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag class="header-wrap" [cdkDragData]="'RECONCILIATION_CREATION_DATE'"
                [cdkDragDisabled]="!groupKeyExists('RECONCILIATION_CREATION_DATE')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('RECONCILIATION_CREATION_DATE')">drag_indicator</mat-icon>
              {{ 'RECONCILIATION.FIELDS.RECONCILIATION_CREATION_DATE' | translate }}

              <mat-chip *cdkDragPreview>{{ 'RECONCILIATION.FIELDS.CREATION_DATE' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let bale">
              {{bale?.reconciliationCreationDate}}
            </td>
          </ng-container>

          <ng-container matColumnDef="reconciliationReviewer">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag class="header-wrap" [cdkDragData]="'RECONCILIATION_REVIEWER'"
                [cdkDragDisabled]="!groupKeyExists('RECONCILIATION_REVIEWER')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('RECONCILIATION_REVIEWER')">drag_indicator</mat-icon>
              {{ 'RECONCILIATION.FIELDS.REVIEWER' | translate }}

              <mat-chip *cdkDragPreview>{{ 'RECONCILIATION.FIELDS.REVIEWER' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let bale">
              {{bale?.reconciliationReviewer}}
            </td>
          </ng-container>

          <ng-container matColumnDef="reconciliationReviewDate">
            <th mat-header-cell *matHeaderCellDef mat-sort-header cdkDrag class="header-wrap" [cdkDragData]="'RECONCILIATION_REVIEW_DATE'"
                [cdkDragDisabled]="!groupKeyExists('RECONCILIATION_REVIEW_DATE')">
              <mat-icon class="drag-icon" *ngIf="groupKeyExists('RECONCILIATION_REVIEW_DATE')">drag_indicator</mat-icon>
              {{ 'RECONCILIATION.FIELDS.REVIEW_DATE' | translate }}

              <mat-chip *cdkDragPreview>{{ 'RECONCILIATION.FIELDS.REVIEW_DATE' | translate }}</mat-chip>
            </th>
            <td mat-cell *matCellDef="let bale">
              {{bale?.reconciliationReviewDate}}
            </td>
          </ng-container>

          <ng-container matColumnDef="expandedDetail">
            <td mat-cell *matCellDef="let row" [attr.colspan]="displayedColumns.length">
              <div [@detailExpand]="row == expandedElement ? 'expanded' : 'collapsed'" class="expanded-details">
                <div class="left-column">
                  <div class="attribute">
                    <span class="attribute-title">{{ 'BALE.FIELDS.ACCOUNT_POSTING_GROUP' | translate }}</span>
                    <span class="attribute-value">{{row.accountPostingGroup}}</span>
                  </div>
                  <div class="attribute">
                    <span class="attribute-title">{{ 'BALE.FIELDS.ENTRY_NO' | translate }}</span>
                    <span class="attribute-value">{{row.entryNo}}</span>
                  </div>
                  <div class="attribute">
                    <span class="attribute-title">{{ 'BALE.FIELDS.TRANSACTION_CURRENCY' | translate }}</span>
                    <span class="attribute-value">{{row.transactionCurrency?.code}}</span>
                  </div>
                  <div class="attribute">
                    <span class="attribute-title">{{ 'BALE.FIELDS.DOCUMENT_TYPE' | translate }}</span>
                    <span class="attribute-value">{{row.documentType}}</span>
                  </div>
                  <div class="attribute">
                    <span class="attribute-title">{{ 'BALE.FIELDS.REASON_CODE' | translate }}</span>
                    <span class="attribute-value">{{row.reasonCode}}</span>
                  </div>
                </div>
                <div class="right-column">
                  <div class="attribute">
                    <span class="attribute-title">{{ 'BALE.FIELDS.BUS_LINE' | translate }}</span>
                    <span class="attribute-value">{{row.busLine}}</span>
                  </div>
                  <div class="attribute">
                    <span class="attribute-title">{{ 'BALE.FIELDS.DEPARTMENT' | translate }}</span>
                    <span class="attribute-value">{{row.department}}</span>
                  </div>
                  <div class="attribute">
                    <span class="attribute-title">{{ 'BALE.FIELDS.IS_OPEN' | translate }}</span>
                    <span class="attribute-value">{{row.isOpen}}</span>
                  </div>
                  <div class="attribute">
                    <span class="attribute-title">{{ 'BALE.FIELDS.POSTED_BY' | translate }}</span>
                    <span class="attribute-value">{{row.postedBy}}</span>
                  </div>
                  <div class="attribute">
                    <span class="attribute-title">{{ 'BALE.FIELDS.RECONCILE_STATUS' | translate }}</span>
                    <span class="attribute-value">{{row.reconcileStatus}}</span>
                  </div>
                </div>
              </div>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns when:csFinGroupService.notGroupOrPaginator"
              [ngClass]="getRowClass(row)"
              class="with-details"
              [class.expanded-row]="expandedElement === row"
              [class.disabled]="!sameStatusAsCurrentSelection(row) || !belongsToReconciliation(row)"
              [class.enabled]="sameStatusAsCurrentSelection(row) && belongsToReconciliation(row)"
              (click)="expandedElement = expandedElement === row ? null : row">
          </tr>
          <tr mat-row *matRowDef="let row; columns: ['expandedDetail']" class="expanded-row"></tr>

          <ng-container matColumnDef="groupHeader">
            <td mat-cell *matCellDef="let group" (click)="expandGroup(group)" [colSpan]="getMaxSpan()">
              <div class="group-row">
                <mat-checkbox [indeterminate]="indeterminate(group)"
                              (change)="onCheckboxSelectAll($event, group)"
                              (click)="$event.stopPropagation()"
                              [checked]="isGroupSelected(group)"
                              [disabled]="disableGroup(group)"
                              [ngClass]="{'indeterminate-checkbox': indeterminate(group),
                                          'disabled-checkbox':disableGroup(group)}">
                </mat-checkbox>
                <button mat-icon-button id="expand-button">
                  <mat-icon *ngIf="group.expanded">expand_less</mat-icon>
                  <mat-icon *ngIf="!group.expanded">expand_more</mat-icon>
                </button>
                <div class="group-name-elements">
                  <ng-container *ngFor="let name of getGroupNameTrimmed(group.groupFields)">
                    <span class="name-square" [title]="name.full">{{name.trimmed}}</span>
                  </ng-container>
                </div>
                <div class="group-information">
                  <span class="group-count">
                    <span class="group-info-title">Nº of Bale: </span>
                    <span>{{group.totalRecords}}</span>
                  </span>
                  <span class="line">
                    <span class="group-info-title">Total Amount: </span>
                    <span>{{group.totalAmount | number:'1.2-2'}} {{account.currency.symbol}}</span>
                  </span>
                </div>
              </div>
            </td>
          </ng-container>

          <ng-container matColumnDef="groupPaginator">
            <td mat-cell *matCellDef="let paginator" [colSpan]="getMaxSpan()" class="cs-fin-paginator">
              <mat-paginator
                [pageSizeOptions]="pagination.pageSizeOptions"
                [pageSize]="paginator.pageSize"
                [length]="paginator.totalRecords"
                [pageIndex]="paginator.pageIndex"
                (page)="onGroupPageChange($event, paginator)"
                showFirstLastButtons>
              </mat-paginator>
            </td>
          </ng-container>

          <tr mat-row *matRowDef="let row; columns: ['groupHeader'] when:csFinGroupService.isGroup"></tr>
          <tr mat-row *matRowDef="let paginator; columns: ['groupPaginator'] when:csFinGroupService.isPaginator"></tr>

        </table>
        <mat-paginator class="table-paginator-reconciliation"
                       *ngIf="!isGroupedView"
                       [pageSizeOptions]="pagination.pageSizeOptions"
                       [pageSize]="pagination.pageSize"
                       [length]="pagination.totalItems"
                       [pageIndex]="pagination.pageIndex"
                       (page)="onPageChange($event)"
                       showFirstLastButtons>
        </mat-paginator>
        <span id="empty-table" *ngIf="!isLoading && !dataSource.data.length">
          {{'GENERAL.TABLE.NO_RESULTS_FOUND' | translate}}
        </span>
      </div>
    </section>
    <cs-fin-add-remove-columns [triggerOrigin]="triggerOrigin"
                               [isOverlayOpen$]="isOverlayOpen$"
                               [menu]="MENU">
    </cs-fin-add-remove-columns>
  </mat-card>
</main>
<ng-template #emptyAmount>
</ng-template>
