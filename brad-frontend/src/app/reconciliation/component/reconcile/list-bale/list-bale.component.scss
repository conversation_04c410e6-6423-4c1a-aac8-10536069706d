@import "node_modules/@jumia-cs-fin/common/assets/styles/list-components";
@import "node_modules/@jumia-cs-fin/common/assets/styles/tables";

.container {
  height: 89vh!important;
  padding-right: 16px!important;
  padding-left: 8px !important;
}


.title{
  font-size: 20px;
  line-height: 1.44;
}


.cs-fin-paginator {
  padding-left: 0;
  padding-right: 0 !important;
}

.select-styling {
  padding-left: 30px;
}

.group-row {
  display: flex;
  align-items: center;

  .name-square {
    width: 40px;
    height: 40px;
    border-radius: 4px;
    margin-right: 16px;
    padding: 5px;
    border: 1px solid var(--primary-color);
  }

  .group-information {
    margin-left: auto;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;

    .group-info-title {
      font-weight: bold;
    }
  }
}

.with-details.enabled:hover {
  background-color: var(--lighter-color) !important;
}

.enabled {
  cursor: pointer !important;
}

.disabled,
.even-entity.disabled,
.odd-entity.disabled {
  cursor: not-allowed !important;
  background-color: rgba(232, 232, 232, 0.99) !important;
  transition: background-color 0.2s ease-in-out !important;
}

.amountWithUsd {
  display: flex;
  flex-direction: column;
  div{
    font-size: 10px;
  }
}

.header-wrap {
  text-wrap: pretty !important;
}

.indeterminate-checkbox:not(.disabled-checkbox) ::ng-deep div.mdc-checkbox__background{
  background-color: var(--mdc-checkbox-indeterminate-icon-color) !important;
  border-color: var(--mdc-checkbox-indeterminate-icon-color) !important;
}

.mat-column-select {
  padding-left: 0;
  padding-right: 0 !important;
  width: 40px !important;
}

tr.expanded-row {
  height: 0 !important;
}

.even-entity {
  background-color: var(--table-even-row-color) !important;
}

.odd-entity {
  background-color: var(--table-odd-row-color) !important;
}

.expanded-details {
  display: flex;
  flex-direction: row;
  padding-left: 0;
  justify-content: space-evenly;

  .left-column {
    display: flex;
    flex-direction: column;
    width: 50%;
    padding-left: 0;
  }
  .right-column {
    display: flex;
    flex-direction: column;
    width: 50%;
    padding-left: 0;
  }
  .attribute {
    display: flex;
    flex-direction: row;
    width: 100%;
    padding: 4px 0;

    .attribute-title {
      width: 35%;
      color: #b6b6bf;
      font-weight: bold;
    }
  }
}
