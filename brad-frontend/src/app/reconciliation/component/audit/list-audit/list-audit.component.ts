import {ChangeDetector<PERSON><PERSON>, <PERSON>mponent, On<PERSON><PERSON>roy, OnInit, ViewChild} from '@angular/core';
import {MatPaginator} from "@angular/material/paginator";
import {MatSort, SortDirection} from "@angular/material/sort";
import {MatTableDataSource} from "@angular/material/table";
import {
  activeFiltersSeparator,
  CsFinActiveFilterChip,
  CsFinAddRemoveColumns,
  CsFinAddRemoveColumnsFacade,
  CsFinApiService,
  CsFinColumnDetails,
  CsFinLastUsedColumns,
  CsFinPagination,
  csFinPaginationConsts,
  CsFinSortingDataAccessorHelperService
} from "@jumia-cs-fin/common";
import {authParams, bradAuthTarget} from "../../../../auth/constants/auth.constants";
import {Observable, Subject} from "rxjs";
import {CdkOverlayOrigin} from "@angular/cdk/overlay";
import {ReconciliationFilters} from "../../../../entities/reconciliation/reconciliation-filters";
import {MediaMatcher} from "@angular/cdk/layout";
import {MatDialog} from "@angular/material/dialog";
import {ActivatedRoute, Router} from "@angular/router";
import {NotificationService} from "../../../../api/service/notification.service";
import {MenusConsts} from "../../../../shared/constants/menus.constants";
import {ReconciliationFacade} from "../../../facade/reconciliation.facade";
import {finalize, takeUntil} from "rxjs/operators";
import {PageResponse} from "../../../../entities/page-response";
import {HttpErrorResponse} from "@angular/common/http";
import * as _ from "lodash";
import {SortFilters} from "../../../../entities/SortFilters";
import {Reconciliation} from "../../../../entities/reconciliation/reconciliation";
import {TransactionFilters} from "../../../../entities/transaction/transaction-filters";
import {BaleFilters} from "../../../../entities/bale/bale-filters";

@Component({
  selector: 'brad-reconciliation-audit-list',
  templateUrl: './list-audit.component.html',
  styleUrls: ['./list-audit.component.scss']
})
export class ListAuditComponent implements OnInit, OnDestroy {

  @ViewChild(MatPaginator, {static: true}) paginator!: MatPaginator;
  @ViewChild(MatSort, {static: true}) sort!: MatSort;

  readonly MENU = MenusConsts.reconciliationAudit;

  isLoading = true;
  dataSource: MatTableDataSource<Reconciliation> = new MatTableDataSource<Reconciliation>([]);
  displayedColumns: string[] = [];
  pagination: CsFinPagination = {
    pageSizeOptions: csFinPaginationConsts.pageSizeOptions,
    pageSize: csFinPaginationConsts.defaultPageSize,
    pageIndex: 0,
    totalItems: 0
  };

  activeFilterChips!: Map<string, CsFinActiveFilterChip>;
  auth = authParams;

  isOverlayOpen$!: Observable<boolean>;
  triggerOrigin!: CdkOverlayOrigin;

  private _onDestroy: Subject<void> = new Subject<void>();

  private filters: ReconciliationFilters = {
    page: 1,
    size: csFinPaginationConsts.defaultPageSize
  }


  constructor(
    public ref: ChangeDetectorRef,
    public media: MediaMatcher,
    private dialog: MatDialog,
    private route: ActivatedRoute,
    private router: Router,
    private notificationService: NotificationService,
    private reconciliationFacade: ReconciliationFacade,
    private addRemoveColumnsFacade: CsFinAddRemoveColumnsFacade,
    private apiService: CsFinApiService
  ) {

    CsFinLastUsedColumns.getInstance().initColumns(this.MENU, this.getColumnDetails());
  }



  ngOnInit(): void {
    this.isLoading = true;
    this.subscribeFiltersChange();
    this.subscribeActiveFilterChipsChange();
    this.subscribeDisplayedColumnsChange();

  }

  ngOnDestroy(): void {
    this.reconciliationFacade.filtersChanged({});
    this._onDestroy.next();
    this._onDestroy.complete();
    this.addRemoveColumnsFacade.isOverlayOpen.next(false);
  }


  private subscribeFiltersChange(): void {
    this.reconciliationFacade.filters$
      .pipe(takeUntil(this._onDestroy))
      .subscribe(async (filters: ReconciliationFilters) => {
        if (Object.keys(filters).length > 0 && this.isInAuditScreen()) {
          this.filters = filters;
          this.loadReconciliations();
        }
      });
  }

  private subscribeActiveFilterChipsChange(): void {
    this.reconciliationFacade.activeFiltersChips$
      .pipe(takeUntil(this._onDestroy))
      .subscribe((activeFilterChips: Map<string, CsFinActiveFilterChip>) => {
        this.activeFilterChips = activeFilterChips;
      });

  }

  private subscribeDisplayedColumnsChange(): void {
    this.addRemoveColumnsFacade.fetchDisplayedColumns
      .pipe(takeUntil(this._onDestroy))
      .subscribe((columns: CsFinAddRemoveColumns) => {
        setTimeout(() => {
            this.displayedColumns = columns.displayedColumns ? columns.displayedColumns : [];
          }
          , 0);
      });
  }

  loadReconciliations() {
    this.reconciliationFacade.getAllReconciliations(this.filters)
      .pipe(takeUntil(this._onDestroy))
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: (result: PageResponse<Reconciliation>) => {
          this.dataSource = new MatTableDataSource<Reconciliation>(result.results);
          this.dataSource.sortingDataAccessor = CsFinSortingDataAccessorHelperService.objectSortingDataAccessor;
          this.pagination.totalItems = result.total;
          this.pagination.pageSize = result.size;
          this.pagination.pageIndex = result.page - 1;
          this.setSort();
        },
        error: (error:HttpErrorResponse) => {
          this.notificationService.errorWithResponse(error);
        }
      });
  }

  triggerOverlay(trigger: CdkOverlayOrigin): void {
    this.triggerOrigin = trigger;
    this.addRemoveColumnsFacade.isOverlayOpen.next(true);
  }

  getColumnDetails(): CsFinColumnDetails[] {
    return [
      {position: 0, name: 'ID', code: 'id', isActive: true, isRemovable: false, isDefault: true},
      {position: 1, name: 'Account', code: 'account', isActive: true, isRemovable: false, isDefault: true},
      {position: 2, name: 'Transactions Amount', code: 'amountTransaction', isActive: true, isRemovable: false, isDefault: true},
      {position: 3, name: 'Bales Amount', code: 'amountBale', isActive: true, isRemovable: false, isDefault: true},
      {position: 4, name: 'Threshold Amount', code: 'amountThreshold', isActive: true, isRemovable: false, isDefault: true},
      {position: 5, name: 'Creator', code: 'creator', isActive: true, isRemovable: false, isDefault: true},
      {position: 6, name: 'Reviewer', code: 'reviewer', isActive: true, isRemovable: false, isDefault: true},
      {position: 7, name: 'Status', code: 'status', isActive: true, isRemovable: false, isDefault: true},
      {position: 8, name: 'Actions', code: 'actions', isActive: true, isRemovable: false, isDefault: true}
    ];
  }

  onPageChange(event: any) {
    this.filters.page = event.pageIndex + 1;
    this.filters.size = event.pageSize;
    this.reconciliationFacade.filtersChanged(this.filters);
  }

  onActiveFilterRemoveClick(removedChipKey: string): void {
    const filtersToRemove = removedChipKey.split(activeFiltersSeparator);
    if (filtersToRemove) {
      filtersToRemove.forEach((filterKey: string) => {
        // @ts-ignore
        delete this.filters[filterKey];
      })
    }
    this.reconciliationFacade.filtersChanged(this.filters);
  }

  private setSort(): void {
    if (!this.sort) {
      return;
    }

    const previousSort = {
      active: this.sort.active,
      direction: this.sort.direction
    };
    if (this.filters.orderField) {
      this.sort.active = this.decodeSortField(this.filters.orderField);
    }
    if (this.filters.orderDirection) {
      this.sort.direction = this.filters.orderDirection.toLowerCase() as SortDirection;
    }

    const sort = {
      active: this.sort.active,
      direction: this.sort.direction
    };
    if (!_.isEqual(previousSort, sort)) {
      this.sort.sortChange.emit(this.sort);
    }
  }

  onSortChange(event: any): void {
    const sortFiltersBefore = {
      orderDirection: this.filters.orderDirection,
      orderField: this.filters.orderField
    };

    this.filters.orderDirection = event.direction?.toUpperCase();
    this.filters.orderField = this.encodeSortField(event.active);

    if(!_.isEqual(sortFiltersBefore, this.filters as SortFilters)){
      this.reconciliationFacade.filtersChanged(this.filters);
    }

  }

  private encodeSortField(field: string): string {
    switch (field) {
      case 'id':
        return 'ID';
      case 'account':
        return 'ACCOUNT';
      case 'status':
        return 'STATUS';
      case 'creator':
        return 'CREATOR';
      case 'creationDate':
        return 'CREATION_DATE';
      case 'reviewer':
        return 'REVIEWER';
      case 'reviewDate':
        return 'REVIEW_DATE';
      case 'amount':
        return 'AMOUNT';
      default:
        return field.toUpperCase();
    }
  }

  private decodeSortField(field: string): string {
    switch (field) {
      case 'ID':
        return 'id';
      case 'ACCOUNT':
        return 'account';
      case 'STATUS':
        return 'status';
      case 'CREATOR':
        return 'creator';
      case 'CREATION_DATE':
        return 'creationDate';
      case 'REVIEWER':
        return 'reviewer';
      case 'REVIEW_DATE':
        return 'reviewDate';
      case 'AMOUNT':
        return 'amount';
      default:
        return field.toLowerCase();
    }
  }

  protected redirectReconciliationDetails(reconciliation: Reconciliation): void {

    let transactionFilters: TransactionFilters = {
      reconciliationId: reconciliation.id
    }

    let baleFilters: BaleFilters = {
      reconciliationId: reconciliation.id
    }
    const filters: ReconciliationFilters = {
      accountID: reconciliation.account!.id,
      transactionFilters: transactionFilters,
      baleFilters: baleFilters
    }

    const queryParams: any = { ...filters };

    if (filters.transactionFilters) {
      let transactionFilters : TransactionFilters = filters.transactionFilters;
      transactionFilters = this.setNullToUndefined(transactionFilters);
      queryParams['transactionFilters'] = JSON.stringify(transactionFilters);
    }

    if (filters.baleFilters) {
      let baleFilters : BaleFilters = filters.baleFilters;
      baleFilters = this.setNullToUndefined(baleFilters);
      queryParams['baleFilters'] = JSON.stringify(baleFilters);
    }

    const formQueryParams = this.apiService.buildQueryParams(queryParams);
    this.router.navigate(['reconcile'], {queryParams: formQueryParams});
  }

  private isInAuditScreen(): boolean {
    return window.location.href.includes('audit');
  }

  private setNullToUndefined(obj: any): any {
    for (const key in obj) {
      if (obj.hasOwnProperty(key) && obj[key] === null) {
        obj[key] = undefined;
      } else if (typeof obj[key] === 'object' && obj[key] !== null) {
        this.setNullToUndefined(obj[key]);
      }
    }
    return obj;
  }

  protected readonly bradAuthTarget = bradAuthTarget;
}
