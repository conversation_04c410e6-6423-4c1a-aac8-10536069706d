<main class="container" cdkDropListGroup>
  <section class="filters" *ngIf="dataSource">
    <cs-fin-active-filters [chipsMap]="activeFilterChips"
                           (onRemove)="onActiveFilterRemoveClick($event)">
    </cs-fin-active-filters>
    <span fxFlex></span>
    <span class="actions">
      <button mat-stroked-button id="change-columns-btn" color="primary" fxShow [fxHide.xs]="true" [fxHide.sm]="true"
              cdkOverlayOrigin #trigger="cdkOverlayOrigin"
              (click)="triggerOverlay(trigger)">
        <mat-icon>table_rows</mat-icon>
      </button>
    </span>
  </section>

  <section class="table-detail responsive-table"
           *ngIf="!isLoading">

    <div class="table-container">

      <mat-progress-bar mode="indeterminate" *ngIf="isLoading"></mat-progress-bar>

      <table mat-table class="cs-fin-table" matSort [dataSource]="dataSource"  [class.loading]="isLoading"
             (matSortChange)="onSortChange($event)" cdkDropList cdkDropListSortingDisabled>

        <ng-container matColumnDef="id">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'RECONCILIATION.FIELDS.ID' | translate }}</th>
          <td mat-cell *matCellDef="let reconciliation"> {{reconciliation?.id}} </td>
        </ng-container>

        <ng-container matColumnDef="account">
          <th mat-header-cell *matHeaderCellDef>{{ 'RECONCILIATION.FIELDS.ACCOUNT' | translate }}</th>
          <td mat-cell *matCellDef="let reconciliation" [attr.data-label]="'Account'">
            <div *ngIf="reconciliation?.account; else emptyEntity">
              {{reconciliation?.account.accountNumber}}
            </div>
          </td>
        </ng-container>


        <ng-container matColumnDef="amountTransaction">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'RECONCILIATION.FIELDS.AMOUNT_TRANSACTION' | translate }}</th>
          <td mat-cell *matCellDef="let reconciliation" [attr.data-label]="'Transaction Amount'">
            {{reconciliation?.amountTransaction}}
          </td>
        </ng-container>

        <ng-container matColumnDef="amountBale">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'RECONCILIATION.FIELDS.AMOUNT_BALE' | translate }}</th>
          <td mat-cell *matCellDef="let reconciliation" [attr.data-label]="'Bale Amount'">
            {{reconciliation?.amountBale}}
          </td>
        </ng-container>

        <ng-container matColumnDef="amountThreshold">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'RECONCILIATION.FIELDS.AMOUNT_THRESHOLD' | translate }}</th>
          <td mat-cell *matCellDef="let reconciliation" [attr.data-label]="'Threshold Amount'">
            {{reconciliation?.amountThreshold}}
          </td>
        </ng-container>

        <ng-container matColumnDef="creator">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'RECONCILIATION.FIELDS.CREATOR' | translate }}</th>
          <td mat-cell *matCellDef="let reconciliation" [attr.data-label]="'Creator'">
            <div class="user-date">
              <span>{{reconciliation?.creator}}</span>
              <span>{{reconciliation?.creationDate | date: 'dd/MM/yyyy HH:mm:ss'}}</span>
            </div>
          </td>
        </ng-container>

        <ng-container matColumnDef="reviewer">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'RECONCILIATION.FIELDS.REVIEWER' | translate }}</th>
          <td mat-cell *matCellDef="let reconciliation" [attr.data-label]="'Reviewer'">
            <div *ngIf="reconciliation?.reviewer; else emptyEntity" class="user-date">
              <span>{{reconciliation?.reviewer}}</span>
              <span>{{reconciliation?.reviewDate | date: 'dd/MM/yyyy HH:mm:ss'}}</span>
            </div>
          </td>
        </ng-container>

        <ng-container matColumnDef="status">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'RECONCILIATION.FIELDS.STATUS' | translate }}</th>
          <td mat-cell *matCellDef="let reconciliation" [attr.data-label]="'Status'">
            {{reconciliation?.status}}
          </td>
        </ng-container>

        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef>{{ 'GENERAL.FIELDS.ACTIONS' | translate }}</th>
          <td mat-cell *matCellDef="let reconciliation" [attr.data-label]="'Actions'">
            <div class="table-actions">
              <mat-icon (click)="redirectReconciliationDetails(reconciliation)"
                        csFinHasPermissionOnAnyTarget
                        [authPermissions]="auth.permissions.BRAD_ACCESS_RECONCILIATION"
                        [authTarget]="bradAuthTarget"
                        [authAction]="auth.actions.HIDE">
                feed
              </mat-icon>
            </div>

          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"
            class="with-detail" style="cursor: pointer;"></tr>
      </table>

      <mat-paginator [pageSizeOptions]="pagination.pageSizeOptions"
                     [pageSize]="pagination.pageSize"
                     [length]="pagination.totalItems"
                     [pageIndex]="pagination.pageIndex"
                     (page)="onPageChange($event)"
                     showFirstLastButtons>
      </mat-paginator>

      <span id="empty-table" *ngIf="!isLoading && !dataSource.data.length">
        {{'GENERAL.TABLE.NO_RESULTS_FOUND' | translate}}
      </span>
    </div>
  </section>

  <cs-fin-add-remove-columns [triggerOrigin]="triggerOrigin"
                             [isOverlayOpen$]="isOverlayOpen$"
                             [menu]="MENU">
  </cs-fin-add-remove-columns>
</main>


<ng-template #emptyEntity>
    <span> - </span>
</ng-template>
