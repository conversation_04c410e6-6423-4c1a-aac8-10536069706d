import {ChangeDetectorRef, Component, Input, On<PERSON><PERSON>roy, OnInit, ViewChild, ViewEncapsulation} from '@angular/core';
import {FormControl, FormGroup} from "@angular/forms";
import {BehaviorSubject, Observable, Subject, tap} from "rxjs";
import {CdkConnectedOverlay} from "@angular/cdk/overlay";
import {CsFinActiveFiltersFacade, CsFinApiService, CsFinSidenavService} from "@jumia-cs-fin/common";
import {ActivatedRoute, Router} from "@angular/router";
import {takeUntil} from "rxjs/operators";
import * as _ from "lodash";
import {ReconciliationFacade} from "../../../facade/reconciliation.facade";
import {ReconciliationFilters} from "../../../../entities/reconciliation/reconciliation-filters";

@Component({
  selector: 'brad-reconciliation-audit-header',
  templateUrl: './audit-header.component.html',
  styleUrls: ['./audit-header.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class AuditHeaderComponent implements OnInit, OnDestroy {

  @Input() showFilters!: boolean;
  filterTextFormControl!: FormControl;
  queryParams = {};

  form!: FormGroup;
  idFormControl!: FormControl;
  accountNumberFormControl!: FormControl;
  amountTransactionFormControl!: FormControl;
  amountBaleFormControl!: FormControl;
  amountThresholdFormControl!: FormControl;
  creatorFormControl!: FormControl;
  creationDateStartFormControl!: FormControl;
  creationDateEndFormControl!: FormControl;
  reviewerFormControl!: FormControl;
  reviewedDateStartFormControl!: FormControl;
  reviewedDateEndFormControl!: FormControl;
  statusFormControl!: FormControl;

  isOpen$!: Observable<boolean>;
  isRefreshing = false;
  isInitializing = false;

  statusList: String[] = [];


  private readonly refreshTimeout = 2000;
  private _onDestroy: Subject<void> = new Subject<void>();
  private _isOpen!: BehaviorSubject<boolean>;

  @ViewChild(CdkConnectedOverlay, {static: true})
  private connectedOverlay!: CdkConnectedOverlay;

  constructor(public ref: ChangeDetectorRef,
              public sidenav: CsFinSidenavService,
              private activatedRoute: ActivatedRoute,
              private activeFiltersFacade: CsFinActiveFiltersFacade,
              private router: Router,
              private apiService: CsFinApiService,
              private reconciliationFacade: ReconciliationFacade) { }


  async ngOnInit(): Promise<void> {
    this.isInitializing = true;
    this.initializeOverlay();
    this.subscribeUrlParamsChanges();
    this.subscribeActiveFiltersChange();
    this.subscribeFiltersChange();
  }

  ngOnDestroy(): void {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  private subscribeUrlParamsChanges(): void {
    this.activatedRoute.queryParams
      .pipe(takeUntil(this._onDestroy))
      .subscribe(params => this.initializeFormData(params));
  }

  private subscribeActiveFiltersChange(): void {
    this.activeFiltersFacade.openFiltersOverlay
      .pipe(takeUntil(this._onDestroy))
      .subscribe(value => this._isOpen.next(value));
  }

  private subscribeFiltersChange(): void {
    this.reconciliationFacade.filters$
      .pipe(takeUntil(this._onDestroy))
      .subscribe((filters:ReconciliationFilters) => {
        if(!this.isInitializing) {
          this.updateFormData(filters);
          this.updateMissingUrlFilters(filters);
        }
      });
  }

  private initializeOverlay(): void {
    this._isOpen = new BehaviorSubject<boolean>(false);
    this.connectedOverlay.backdropClass = 'cdk-overlay-transparent-backdrop';
    this.connectedOverlay.panelClass = 'filters-overlay-panel';
    this.connectedOverlay.backdropClick
      .pipe(tap(() => this._isOpen.next(false)))
      .subscribe();
    this.isOpen$ = this._isOpen.asObservable();
  }

  clearFilters(): void {
    this.form.reset('', {emitEvent: false});
    this.reconciliationFacade.filtersChanged({});
  }

  submit(input?: HTMLInputElement) {
    this.reconciliationFacade.filtersChanged(this.getFormValues());
    const formQueryParams = this.apiService.buildQueryParams(this.getFormValues());
    if (_.isEqual(this.queryParams, formQueryParams)) {
      this.isRefreshing = true;
      this.ref.markForCheck();
      setTimeout(() => {
        this.isRefreshing = false;
        this.ref.markForCheck();
      }, this.refreshTimeout);
    } else {
      input?.blur();
      this.router.navigate(['/audit'], {queryParams: formQueryParams});
    }
    this.closeOverlay();

  }

  triggerOverlay(): void {
    this._isOpen.next(!this._isOpen.value);
  }

  closeOverlay(): void {
    this._isOpen.next(false);
  }

  private getFormValues(): ReconciliationFilters {
    return this.form.value as ReconciliationFilters;
  }

  private initializeFormData(params:any): void {
    this.queryParams = params;
    if(!this.form) {
      this.form = new FormGroup({});
      this.initializeFormControlsAndFilters(params);
    }
  }

  private initializeFormControlsAndFilters(params:any): void {
    const filters: ReconciliationFilters = {};

    this.filterTextFormControl = new FormControl(params.filterText);
    filters.filterText = params.filterText;

    this.idFormControl = new FormControl(params.id);
    filters.id = params.id;

    this.accountNumberFormControl = new FormControl(params.accountNumber);
    filters.accountNumber = params.accountNumber;

    this.amountTransactionFormControl = new FormControl(params.amountTransaction);
    filters.amountTransaction = params.amountTransaction;

    this.amountBaleFormControl = new FormControl(params.amountBale);
    filters.amountBale = params.amountBale;

    this.amountThresholdFormControl = new FormControl(params.amountThreshold);
    filters.amountThreshold = params.amountThreshold;

    this.creatorFormControl = new FormControl(params.creator);
    filters.creator = params.creator;

    this.creationDateStartFormControl = new FormControl(params.creationDateStart);
    filters.creationDateStart = params.creationDateStart;

    this.creationDateEndFormControl = new FormControl(params.creationDateEnd);
    filters.creationDateEnd = params.creationDateEnd;

    this.reviewerFormControl = new FormControl(params.reviewer);
    filters.reviewer = params.reviewer;

    this.reviewedDateStartFormControl = new FormControl(params.reviewedDateStart);
    filters.reviewedDateStart = params.reviewedDateStart;

    this.reviewedDateEndFormControl = new FormControl(params.reviewedDateEnd);
    filters.reviewedDateEnd = params.reviewedDateEnd;

    let status: string[] = params.status === undefined ? [] : params.status.split(',');
    this.statusFormControl = new FormControl(status);
    filters.status = status;

    filters.orderField = params.orderField;
    filters.orderDirection = params.orderDirection;
    filters.page = params.page;
    filters.size = params.size;

    Promise.all([
      this.applyStatusFilter(params, filters)
    ]).then(() => {
      this.setFormControlsToForm();
      this.reconciliationFacade.filtersChanged(filters);
      this.updateMissingUrlFilters(filters)
      this.isInitializing = false;
    });

  }
  private applyStatusFilter(params: any, filters: ReconciliationFilters): Promise<void> {
    return new Promise<void>(async (resolve) => {

      if(!params.status) {
        resolve();
        return;
      }

      await this.loadStatuses();

      this.statusFormControl.setValue(filters.status, {emitEvent: false})
      resolve();
    });
  }

  private updateFormData (params: ReconciliationFilters): void {
    this.filterTextFormControl.setValue(params.filterText, {emitEvent: false});
    this.statusFormControl.setValue(params.status, {emitEvent: false});
  }

  private updateMissingUrlFilters(filters: ReconciliationFilters): void {
    const formQueryParams = this.apiService.buildQueryParams(filters);
    if (!_.isEqual(this.queryParams, formQueryParams)) {
      this.router.navigate(['audit'], {queryParams: formQueryParams});
    }
  }

  private setFormControlsToForm(): void {
    this.form.addControl(this.reconciliationFacade.filterTextKey, this.filterTextFormControl);
    this.form.addControl(this.reconciliationFacade.idKey, this.idFormControl);
    this.form.addControl(this.reconciliationFacade.accountNumberKey, this.accountNumberFormControl);
    this.form.addControl(this.reconciliationFacade.amountTransactionKey, this.amountTransactionFormControl);
    this.form.addControl(this.reconciliationFacade.amountBaleKey, this.amountBaleFormControl);
    this.form.addControl(this.reconciliationFacade.amountThresholdKey, this.amountThresholdFormControl);
    this.form.addControl(this.reconciliationFacade.creatorKey, this.creatorFormControl);
    this.form.addControl(this.reconciliationFacade.creationDateStartKey, this.creationDateStartFormControl);
    this.form.addControl(this.reconciliationFacade.creationDateEndKey, this.creationDateEndFormControl);
    this.form.addControl(this.reconciliationFacade.reviewerKey, this.reviewerFormControl);
    this.form.addControl(this.reconciliationFacade.reviewedDateStartKey, this.reviewedDateStartFormControl);
    this.form.addControl(this.reconciliationFacade.reviewedDateEndKey, this.reviewedDateEndFormControl);
    this.form.addControl(this.reconciliationFacade.statusKey, this.statusFormControl);
  }

  noFiltersSelected() {
    return !!this.form.errors || this.isRefreshing;
  }

  loadStatuses() {
    if (this.statusList.length == 0) {
      new Promise<void>((resolve, reject) => {
        this.reconciliationFacade.getAllReconciliationStatus()
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (reconciliationStatus: String[]) => {
              this.statusList = reconciliationStatus;
              resolve();
            },
            error: (error) => reject(error)
          });
      });
    }
  }

}
