<mat-toolbar id="header-toolbar">
  <button mat-icon-button (click)="sidenav.toggle()" fxHide [fxShow.xs]="true" [fxShow.sm]="true">
    <mat-icon>menu</mat-icon>
  </button>
  <span class="page-title">{{ 'RECONCILIATION.LIST_TITLE' | translate }}</span>

  <section class="search-bar" [formGroup]="form">

    <!-- filter text (code) -->
    <mat-form-field id="search" appearance="outline" fxShow [fxShow.xs]="false" [fxShow.sm]="false"
                    cdkOverlayOrigin #trigger="cdkOverlayOrigin">
      <mat-icon matPrefix>search</mat-icon>
      <mat-label class="theLabel">{{'RECONCILIATION.DETAILS.SEARCH_BAR' | translate}}</mat-label>
      <input type="text" #input matInput [formControl]="filterTextFormControl"
             (keyup.enter)="submit(input)">
      <mat-icon matSuffix id="filters" (click)="triggerOverlay()">filter_list</mat-icon>
    </mat-form-field>

    <button mat-raised-button color="primary" id="apply-search-btn" class="raised-primary-btn" fxShow [fxShow.xs]="false"
            [fxShow.sm]="false" [disabled]="noFiltersSelected()" (click)="submit()">
      {{ 'GENERAL.BUTTONS.LABELS.SEARCH' | translate }}
    </button>
  </section>

  <span fxFlex fxHide [fxShow.xs]="true" [fxShow.sm]="true"></span>
  <button mat-icon-button aria-label="Filter accounts" fxHide [fxShow.xs]="true" [fxShow.sm]="true"
          *ngIf="showFilters" id="show-mobile-filters">
    <mat-icon (click)="triggerOverlay()">filter_list</mat-icon>
  </button>
  <ng-template cdkConnectedOverlay class="template"
               [cdkConnectedOverlayHasBackdrop]="true"
               [cdkConnectedOverlayOrigin]="trigger"
               [cdkConnectedOverlayOpen]="(isOpen$ | async) || false">
    <div class="filters-overlay">
      <div class="filters-header">
        <mat-icon fxHide [fxShow.xs]="true" [fxShow.sm]="true" (click)="closeOverlay()">close</mat-icon>
        <p class="filters-title">{{'GENERAL.FILTERS.TITLE' | translate}}</p>
        <button fxHide class="flat-white-btn filters-cancel-btn" [disabled]="noFiltersSelected()" [fxShow.xs]="true"
                [fxShow.sm]="true"
                mat-flat-button (click)="clearFilters()" id="clear-btn">
          {{ 'GENERAL.BUTTONS.CLEAR' | translate }}
        </button>
      </div>

      <div class="filters-container">

        <!-- id -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-id-filter">
          <mat-label>{{'RECONCILIATION.FIELDS.ID' | translate}}</mat-label>
          <input matInput [formControl]="idFormControl">
        </mat-form-field>

        <!-- account -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-account-filter">
          <mat-label>{{'RECONCILIATION.FIELDS.ACCOUNT' | translate}}</mat-label>
          <input matInput [formControl]="accountNumberFormControl">
        </mat-form-field>

        <!-- amount transaction -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-amount-filter">
          <mat-label>{{'RECONCILIATION.FIELDS.AMOUNT_TRANSACTION' | translate}}</mat-label>
          <input matInput type="number" [formControl]="amountTransactionFormControl">
        </mat-form-field>

        <!-- amount bale -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-amount-filter">
          <mat-label>{{'RECONCILIATION.FIELDS.AMOUNT_BALE' | translate}}</mat-label>
          <input matInput type="number" [formControl]="amountBaleFormControl">
        </mat-form-field>


        <!-- amount threshold -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-amount-filter">
          <mat-label>{{'RECONCILIATION.FIELDS.AMOUNT_THRESHOLD' | translate}}</mat-label>
          <input matInput type="number" [formControl]="amountThresholdFormControl">
        </mat-form-field>

        <!-- creator -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-creator-filter">
          <mat-label>{{'RECONCILIATION.FIELDS.CREATOR' | translate}}</mat-label>
          <input matInput [formControl]="creatorFormControl">
        </mat-form-field>

        <!-- createdDate -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-created-at-filter">
          <mat-label>{{'RECONCILIATION.FIELDS.CREATION_DATE' | translate}}</mat-label>
          <mat-date-range-input [rangePicker]="picker1">
            <input matStartDate [formControl]="creationDateStartFormControl" placeholder="Start date">
            <input matEndDate [formControl]="creationDateEndFormControl" placeholder="End date">
          </mat-date-range-input>
          <mat-datepicker-toggle matIconSuffix [for]="picker1"></mat-datepicker-toggle>
          <mat-date-range-picker #picker1></mat-date-range-picker>
        </mat-form-field>

        <!-- reviewer -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-creator-filter">
          <mat-label>{{'RECONCILIATION.FIELDS.REVIEWER' | translate}}</mat-label>
          <input matInput [formControl]="reviewerFormControl">
        </mat-form-field>

        <!-- createdDate -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-created-at-filter">
          <mat-label>{{'RECONCILIATION.FIELDS.REVIEW_DATE' | translate}}</mat-label>
          <mat-date-range-input [rangePicker]="picker2">
            <input matStartDate [formControl]="reviewedDateStartFormControl" placeholder="Start date">
            <input matEndDate [formControl]="reviewedDateEndFormControl" placeholder="End date">
          </mat-date-range-input>
          <mat-datepicker-toggle matIconSuffix [for]="picker2"></mat-datepicker-toggle>
          <mat-date-range-picker #picker2></mat-date-range-picker>
        </mat-form-field>

        <!-- status -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-status-filter" (click)="loadStatuses()">
          <mat-label>{{'RECONCILIATION.FIELDS.STATUS' | translate}}</mat-label>
          <mat-select [formControl]="statusFormControl" multiple>
            <ng-container *ngIf="statusList.length > 0; else loadingStatus">
              <mat-option *ngFor="let status of statusList" [value]="status">
                {{status}}
              </mat-option>
              <button mat-button color="primary" class="clear-selection-btn"
                      [disabled]="!statusFormControl.value?.length"
                      (click)="statusFormControl.reset([])">
                {{'GENERAL.FILTERS.ACTIONS.CLEAR_SELECTION' | translate}}
              </button>
            </ng-container>
            <ng-template #loadingStatus>
              <mat-option disabled>
                <div class="filters-loading-container">
                  <span>{{'GENERAL.FILTERS.LOADING' | translate}}</span>
                  <mat-spinner diameter="20"></mat-spinner>
                </div>
              </mat-option>
            </ng-template>
          </mat-select>
        </mat-form-field>


      </div>

      <div class="filters-actions">
        <button class="flat-white-btn filters-cancel-btn" [disabled]="noFiltersSelected()" mat-flat-button fxShow
                (click)="clearFilters()" [fxShow.xs]="false" [fxShow.sm]="false">
          {{'GENERAL.FILTERS.ACTIONS.CLEAR' | translate}}
        </button>
        <button class="raised-primary-btn filters-apply-btn" [disabled]="noFiltersSelected()" mat-raised-button
                color="primary"
                (click)="submit()">
          {{'GENERAL.FILTERS.ACTIONS.APPLY' | translate}}
        </button>
      </div>

    </div>

  </ng-template>

</mat-toolbar>
