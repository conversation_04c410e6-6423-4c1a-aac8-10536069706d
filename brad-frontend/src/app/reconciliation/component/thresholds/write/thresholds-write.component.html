<div class="dialog-container">
  <header class="dialog-header">
    <span class="title" mat-dialog-title>{{getTitle() | translate}}</span>
  </header>

  <div class="dialog-content" mat-dialog-content>
    <div class="form-container">

        <!-- currency -->
        <mat-form-field appearance="outline" data-cy="input-currency-filter"
                        (click)="loadCurrencies()">
          <mat-label>{{ 'THRESHOLDS.FIELDS.CURRENCY' | translate }}</mat-label>

          <mat-select [formControl]="currencyFormControl" required>
            <ng-container *ngIf="filteredCurrencyList != null; else loadingCurrency">
              <mat-option>
                <ngx-mat-select-search [formControl]="currencySearchFormControl"
                                       [noEntriesFoundLabel]="'GENERAL.FILTERS.NO_MATCHING_RESULTS_FOUND' | translate"
                                       [placeholderLabel]="'GENERAL.FILTERS.SEARCH' | translate">
                </ngx-mat-select-search>
              </mat-option>
              <mat-option *ngFor="let currency of filteredCurrencyList" [value]="currency.code">
                {{currency.code}}
              </mat-option>
              <button mat-button color="primary" class="clear-selection-btn"
                      [disabled]="!currencyFormControl.value?.length"
                      (click)="currencyFormControl.reset([])">
                {{'GENERAL.FILTERS.ACTIONS.CLEAR_SELECTION' | translate}}
              </button>
            </ng-container>
            <ng-template #loadingCurrency>
              <mat-option disabled>
                <div class="filters-loading-container">
                  <span>{{'GENERAL.FILTERS.LOADING' | translate}}</span>
                  <mat-spinner diameter="20"></mat-spinner>
                </div>
              </mat-option>
            </ng-template>
          </mat-select>
          <mat-error *ngIf="thresholdsForm.get('currency')?.errors" data-cy="input-currency-errors">
            {{ 'THRESHOLDS.WRITE.ERRORS.CURRENCY_IS_REQUIRED' | translate }}
          </mat-error>
        </mat-form-field>

        <!-- country -->
        <mat-form-field appearance="outline" data-cy="input-country-filter"
                        (click)="loadCountries()">
          <mat-label>{{ 'THRESHOLDS.FIELDS.COUNTRY' | translate }}</mat-label>

          <mat-select [formControl]="countryFormControl" required>
            <ng-container *ngIf="filteredCountryList != null; else loadingCountry">
              <mat-option>
                <ngx-mat-select-search [formControl]="countrySearchFormControl"
                                       [noEntriesFoundLabel]="'GENERAL.FILTERS.NO_MATCHING_RESULTS_FOUND' | translate"
                                       [placeholderLabel]="'GENERAL.FILTERS.SEARCH' | translate">
                </ngx-mat-select-search>
              </mat-option>
              <mat-option *ngFor="let country of filteredCountryList" [value]="country.code">
                <span class="country-name-flag">
                  {{country.name}} <cs-fin-flag [countryCode]="country.code"></cs-fin-flag>
                </span>
              </mat-option>
              <button mat-button color="primary" class="clear-selection-btn"
                      [disabled]="!countryFormControl.value?.length"
                      (click)="countryFormControl.reset([])">
                {{'GENERAL.FILTERS.ACTIONS.CLEAR_SELECTION' | translate}}
              </button>
            </ng-container>
            <ng-template #loadingCountry>
              <mat-option disabled>
                <div class="filters-loading-container">
                  <span>{{'GENERAL.FILTERS.LOADING' | translate}}</span>
                  <mat-spinner diameter="20"></mat-spinner>
                </div>
              </mat-option>
            </ng-template>
          </mat-select>
          <mat-error *ngIf="thresholdsForm.get('country')?.errors" data-cy="input-country-errors">
            {{ 'THRESHOLDS.WRITE.ERRORS.COUNTRY_IS_REQUIRED' | translate }}
          </mat-error>
        </mat-form-field>

        <!-- companyID -->
        <mat-form-field appearance="outline">
          <mat-label>{{ 'THRESHOLDS.FIELDS.AMOUNT' | translate }}</mat-label>
          <input matInput type="number" [formControl]="amountFormControl" data-cy="input-amount">
          <mat-error *ngIf="thresholdsForm.get('companyID')?.errors" data-cy="input-amount-errors">
            {{ 'THRESHOLDS.WRITE.ERRORS.AMOUNT_IS_REQUIRED' | translate }}
          </mat-error>
        </mat-form-field>

    </div>

  </div>


  <div class="dialog-actions" mat-dialog-actions>
    <span fxFlex></span>
    <button mat-flat-button mat-dialog-close id="cancel-btn" [fxShow]="true" [fxHide.xs]="true" [fxHide.sm]="true">
      {{ 'GENERAL.BUTTONS.LABELS.CANCEL' | translate }}
    </button>
    <button mat-flat-button color="primary" id="create-item-btn"
            (click)="onSaveClick()" [disabled]="thresholdsForm.invalid" data-cy="btn-save">
      {{ getButtonAction() | translate }}
    </button>
  </div>

</div>
