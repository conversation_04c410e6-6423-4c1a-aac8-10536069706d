import {Component, Inject, <PERSON><PERSON><PERSON>roy, OnInit} from '@angular/core';
import {FormControl, FormGroup, Validators} from "@angular/forms";
import {Country} from "../../../../entities/account/country";
import {Currency} from "../../../../entities/currency/currency";
import {MAT_DIALOG_DATA, MatDialogRef} from "@angular/material/dialog";
import {CsFinWritePopupType} from "@jumia-cs-fin/common";
import {NotificationService} from "../../../../api/service/notification.service";
import {CountryApiService} from "../../../../api/service/country-api.service";
import {CurrencyApiService} from "../../../../api/service/currency-api.service";
import {Subject} from "rxjs";
import {Threshold} from "../../../../entities/thresholds/threshold";
import {ThresholdsFacade} from "../facade/thresholds.facade";
import {finalize, takeUntil} from "rxjs/operators";
import {ThresholdRequest} from "../../../../entities/thresholds/threshold-request";
import {HttpErrorResponse} from "@angular/common/http";

@Component({
  selector: 'brad-write',
  templateUrl: './thresholds-write.component.html',
  styleUrls: ['./thresholds-write.component.scss']
})
export class ThresholdsWriteComponent implements OnInit, OnDestroy {

  thresholdsForm!: FormGroup;
  countryFormControl!: FormControl;
  currencyFormControl!: FormControl;
  amountFormControl!: FormControl;

  countrySearchFormControl = new FormControl();
  countryList:Country[] = [];
  filteredCountryList :Country[]= [];

  currencySearchFormControl = new FormControl();
  currencyList:Currency[] = [];
  filteredCurrencyList :Currency[]= [];

  isCountryLoading = true;
  isCurrencyLoading = true;
  isSavingData = false;
  private _onDestroy: Subject<void> = new Subject<void>();


  constructor(public dialogRef: MatDialogRef<ThresholdsWriteComponent>,
              @Inject(MAT_DIALOG_DATA) public data: { type: CsFinWritePopupType, threshold: Threshold | null},
              private notificationService: NotificationService,
              private thresholdsFacade: ThresholdsFacade,
              private countryApiService: CountryApiService,
              private currencyApiService: CurrencyApiService) { }


  ngOnInit(): void {
    this.thresholdsForm = new FormGroup({});
    this.initFiltersSearch();
    this.initFormControls();
    this.setFormControlsToForm();
    this.loadCountries();
    this.loadCurrencies();
    if (this.data.threshold?.id != null) {
      this.loadThresholdAndSetFormGroup(this.data.threshold.id);
    }
  }


  ngOnDestroy(): void {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  private initFiltersSearch(): void {
    this.initCountrySearch();
    this.initCurrencySearch();
  }

  private initCountrySearch(): void {
    this.countrySearchFormControl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .pipe(
        finalize(() => this.isCountryLoading = false)
      )
      .subscribe((value: String) => {

        const searchTerm = value.trim();
        if (searchTerm) {
          this.filteredCountryList = this.countryList.filter((country) => {
            return country.name.trim().toLowerCase().includes(searchTerm.toLowerCase());
          })
        } else {
          this.filteredCountryList = this.countryList;
        }
      });
  }


  private initCurrencySearch(): void {
    this.currencySearchFormControl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .pipe(
        finalize(() => this.isCurrencyLoading = false)
      )
      .subscribe((value: String) => {

        const searchTerm = value.trim();
        if (searchTerm) {
          this.filteredCurrencyList = this.currencyList.filter((currency: Currency) => {
            return currency.code.trim().toLowerCase().includes(searchTerm.toLowerCase());
          })
        } else {
          this.filteredCurrencyList = this.currencyList;
        }
      });
  }

  initFormControls(): void {
    this.countryFormControl = new FormControl('', Validators.required);
    this.currencyFormControl = new FormControl('', Validators.required);
    this.amountFormControl = new FormControl('', Validators.required);
  }

  setFormControlsToForm(): void {
    this.thresholdsForm.addControl('country', this.countryFormControl);
    this.thresholdsForm.addControl('currency', this.currencyFormControl);
    this.thresholdsForm.addControl('amount', this.amountFormControl);
  }

  loadThresholdAndSetFormGroup(id: number): void {
    this.thresholdsFacade.getById(id)
      .pipe(takeUntil(this._onDestroy))
      .subscribe( {
        next: (threshold: Threshold) => {
          this.countryFormControl.setValue(threshold.country.code);
          this.currencyFormControl.setValue(threshold.currency.code);
          this.amountFormControl.setValue(threshold.amount);
        },
        error: (error: any) => {
          this.notificationService.errorWithResponse(error);
        }
      });
  }

  isCreateView(): boolean {
    return this.data.type === CsFinWritePopupType.create;
  }

  isEditView(): boolean {
    return this.data.type === CsFinWritePopupType.update;
  }

  buildThresholdRequest(): ThresholdRequest {
    const thresholdRequest: ThresholdRequest = {
        countryCode: this.countryFormControl.value,
        currencyCode: this.currencyFormControl.value,
        amount: this.amountFormControl.value
      }
    if (this.isEditView() && this.data.threshold) {
      thresholdRequest.id = this.data.threshold.id;
    }
    return thresholdRequest;
  }

  loadCountries(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if(this.countryList.length <= 0) {
        this.countryApiService.getAll()
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (countries: Country[]) => {
              this.countryList = countries;
              this.filteredCountryList = this.countryList;
              resolve();
            }, error: (error) => reject(error)
          });
      } else {
        resolve();
      }
    });
  }

  loadCurrencies(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if(this.currencyList.length <= 0) {
        this.currencyApiService.getAll()
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (currency: Currency[]) => {
              this.currencyList = currency;
              this.filteredCurrencyList = this.currencyList;
              resolve();
            }, error: (error) => reject(error)
          });
      } else {
        resolve();
      }
    });
  }

  onSaveClick(): void {

    const thresholdReq = this.buildThresholdRequest();
    if (this.isCreateView()) {
      this.doCreate(thresholdReq);
    } else if (this.isEditView()) {
      this.doUpdate(thresholdReq);
    }

  }

  doCreate(threshold: ThresholdRequest): void {
    this.isSavingData = true;
    this.thresholdsFacade.create(threshold)
      .pipe(
        finalize(() => this.isSavingData = false)
      )
      .subscribe({
        next: () => {
          this.notificationService.successTranslated('THRESHOLDS.NOTIFICATIONS.CREATE_THRESHOLD_SUCCESS', {});
          this.dialogRef.close(true);
        },
        error: (error:HttpErrorResponse) => {
          this.notificationService.errorWithResponse(error);
        }
      });
  }

  doUpdate(threshold: ThresholdRequest): void {
    this.isSavingData = true;
    if (this.data.threshold?.id != null) {
      this.thresholdsFacade.update(this.data.threshold.id, threshold)
        .pipe(
          finalize(() => this.isSavingData = false)
        )
        .subscribe({
          next: () => {
            this.notificationService.successTranslated('THRESHOLDS.NOTIFICATIONS.UPDATE_THRESHOLD_SUCCESS', {id: threshold.id});
            this.dialogRef.close(true);
          },
          error: (error:HttpErrorResponse) => {
            this.notificationService.errorWithResponse(error);
          }
        });
    }
  }

  getButtonAction() {
    return this.isCreateView() ? 'GENERAL.BUTTONS.LABELS.CREATE' : 'GENERAL.BUTTONS.LABELS.SAVE';
  }

  getTitle() {
    return this.isCreateView() ? 'THRESHOLDS.CREATE.TITLE' : 'THRESHOLDS.EDIT.TITLE';
  }

}
