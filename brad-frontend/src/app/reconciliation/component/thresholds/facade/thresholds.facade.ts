import {Injectable} from '@angular/core';
import {Threshold} from "../../../../entities/thresholds/threshold";
import {ThresholdsApiService} from "../../../../api/service/thresholds-api.service";
import {BehaviorSubject, Observable, of} from "rxjs";
import {ThresholdRequest} from "../../../../entities/thresholds/threshold-request";
import {CsFinActiveFilterChip} from "@jumia-cs-fin/common";
import {ThresholdFilters} from "../../../../entities/thresholds/threshold-filters";
import {getPropertyKey} from "../../../../shared/service/ts-utils.service";
import {Params} from "@angular/router";
import {PageResponse} from "../../../../entities/page-response";

@Injectable({
  providedIn: 'root'
})
export class ThresholdsFacade {

  public fullscreenChangeBehaviorSubject = new BehaviorSubject<boolean>(true);
  public detailsCloseBehaviorSubject = new BehaviorSubject<boolean>(false);
  public selectedThresholdChangeBehaviorSubject = new BehaviorSubject<number>(-1);
  public thresholdFilterParamsBehaviorSubject = new BehaviorSubject<Params>({});


  readonly filterTextKey = getPropertyKey<ThresholdFilters>(p => p.filterText);
  readonly countryCodeKey = getPropertyKey<ThresholdFilters>(p => p.countryCode);
  readonly currencyCodeKey = getPropertyKey<ThresholdFilters>(p => p.currencyCode);
  readonly createdAtKey = getPropertyKey<ThresholdFilters>(p => p.createdAt);

  private filtersBehaviorSubject = new BehaviorSubject<ThresholdFilters>({});
  private activeFilterChips = new Map<string, CsFinActiveFilterChip>();
  public filters$: Observable<ThresholdFilters> = this.filtersBehaviorSubject.asObservable();
  public activeFiltersChips$: Observable<Map<string, CsFinActiveFilterChip>> = of(this.activeFilterChips);

  private readonly activeFiltersConfigMap = new Map<string, any>([
    [
      this.filterTextKey,
      (filterText: string) => {
        return {labelKey: 'GENERAL.FIELDS.FILTER_TEXT', displayText: filterText}
      }
    ],
    [
      this.countryCodeKey,
      (countryCode: string) => {
        return {labelKey: 'THRESHOLDS.FIELDS.COUNTRY', displayText: countryCode}
      }
    ],
    [
      this.currencyCodeKey,
      (currencyCode: string) => {
        return {labelKey: 'THRESHOLDS.FIELDS.CURRENCY', displayText: currencyCode}
      }
    ],
    [
      this.createdAtKey,
      (createdAt: string) => {
        return {labelKey: 'THRESHOLDS.FIELDS.CREATED_AT', displayText: createdAt}
      }
    ]
  ])

  constructor(public thresholdsApiService: ThresholdsApiService) {
  }

  filtersChanged(filters: ThresholdFilters) {
    this.filtersBehaviorSubject.next(filters);
    this.updateActiveFilterChips(filters);
  }

  private updateActiveFilterChips(filters: ThresholdFilters): void {
    if (!filters) {
      return;
    }
    this.activeFilterChips.clear();

    if(filters.filterText){
      this.activeFilterChips.set(this.filterTextKey, this.activeFiltersConfigMap.get(this.filterTextKey)(filters.filterText));
    }
    if(filters.countryCode){
      this.activeFilterChips.set(this.countryCodeKey, this.activeFiltersConfigMap.get(this.countryCodeKey)(filters.countryCode));
    }
    if(filters.currencyCode){
      this.activeFilterChips.set(this.currencyCodeKey, this.activeFiltersConfigMap.get(this.currencyCodeKey)(filters.currencyCode));
    }
    if(filters.createdAt){
      this.activeFilterChips.set(this.createdAtKey, this.activeFiltersConfigMap.get(this.createdAtKey)(filters.createdAt));
    }


  }

  getAll(filters?: ThresholdFilters): Observable<PageResponse<Threshold>> {
    return this.thresholdsApiService.getAll(filters);
  }

  create(thresholds: ThresholdRequest): Observable<Threshold> {
    return this.thresholdsApiService.create(thresholds);
  }

  update(id: number, thresholds: ThresholdRequest): Observable<Threshold> {
    return this.thresholdsApiService.update(id, thresholds);
  }

  delete(id: number): Observable<void> {
    return this.thresholdsApiService.delete(id);
  }

  getById(id: number): Observable<Threshold> {
    return this.thresholdsApiService.getById(id);
  }
}
