import {AfterViewInit, ChangeDetectorRef, Component, OnDestroy, OnInit, ViewChild} from '@angular/core';
import {MatPaginator} from "@angular/material/paginator";
import {MatSort, SortDirection} from "@angular/material/sort";
import {MenusConsts} from "../../../../shared/constants/menus.constants";
import {MatTableDataSource} from "@angular/material/table";
import {
  activeFiltersSeparator,
  CsFinActiveFilterChip,
  CsFinAddRemoveColumns,
  CsFinAddRemoveColumnsFacade,
  CsFinColumnDetails,
  CsFinConfirmationDialogComponent,
  CsFinLastUsedColumns,
  CsFinPagination,
  csFinPaginationConsts,
  CsFinSortingDataAccessorHelperService,
  CsFinWritePopupType
} from "@jumia-cs-fin/common";
import {authParams, bradAuthTarget} from "../../../../auth/constants/auth.constants";
import {Observable, Subject} from "rxjs";
import {CdkOverlayOrigin} from "@angular/cdk/overlay";
import {ThresholdFilters} from "../../../../entities/thresholds/threshold-filters";
import {Threshold} from "../../../../entities/thresholds/threshold";
import {MediaMatcher} from "@angular/cdk/layout";
import {MatDialog} from "@angular/material/dialog";
import {ActivatedRoute, Router} from "@angular/router";
import {NotificationService} from "../../../../api/service/notification.service";
import {finalize, takeUntil} from "rxjs/operators";
import {ThresholdsFacade} from "../facade/thresholds.facade";
import {PageResponse} from "../../../../entities/page-response";
import {HttpErrorResponse} from "@angular/common/http";
import * as _ from "lodash";
import {SortFilters} from "../../../../entities/SortFilters";
import {ThresholdsWriteComponent} from "../write/thresholds-write.component";

@Component({
  selector: 'brad-list-thresholds',
  templateUrl: './list-thresholds.component.html',
  styleUrls: ['./list-thresholds.component.scss']
})
export class ListThresholdsComponent implements OnInit, OnDestroy, AfterViewInit {


  @ViewChild(MatPaginator, {static: true}) paginator!: MatPaginator;
  @ViewChild(MatSort, {static: true}) sort!: MatSort;

  readonly MENU = MenusConsts.thresholds;

  isThresholdDetailsInFullscreen = true;
  lastOpenedThresholdDetailsID!: number | null;

  private displayedColumnsOnDetailsMode = ['id', 'country', 'currency', 'amount', 'createdAt', 'updatedAt'];
  private displayedColumnsOnFullscreenMode = ['id', 'country', 'currency'];

  protected readonly bradAuthTarget = bradAuthTarget;
  isLoading = true;
  dataSource: MatTableDataSource<Threshold> = new MatTableDataSource<Threshold>([]);
  displayedColumns: string[] = [];
  mobileQuery: MediaQueryList;
  pagination: CsFinPagination = {
    pageSizeOptions: csFinPaginationConsts.pageSizeOptions,
    pageSize: csFinPaginationConsts.defaultPageSize,
    pageIndex: 0,
    totalItems: 0
  };

  activeFilterChips!: Map<string, CsFinActiveFilterChip>;
  auth = authParams;
  thresholdDetailsOpened = false;

  isOverlayOpen$!: Observable<boolean>;
  triggerOrigin!: CdkOverlayOrigin;
  private _mobileQueryListener: () => void;
  private _onDestroy: Subject<void> = new Subject<void>();

  private filters: ThresholdFilters = {
    page: 1,
    size: csFinPaginationConsts.defaultPageSize
  };

  private lastDisplayedColumns!: string[];

  filterButtonActive = false;

  constructor(
    public ref: ChangeDetectorRef,
    public media: MediaMatcher,
    private dialog: MatDialog,
    private route: ActivatedRoute,
    private router: Router,
    private notificationService: NotificationService,
    private addRemoveColumnsFacade: CsFinAddRemoveColumnsFacade,
    private thresholdsFacade: ThresholdsFacade,
  ) {
    this.mobileQuery = media.matchMedia('(max-width: 960px)');
    this._mobileQueryListener = () => ref.detectChanges();
    this.mobileQuery?.addEventListener('change', this._mobileQueryListener);

    CsFinLastUsedColumns.getInstance().initColumns(this.MENU, this.getColumnDetails());
  }

  ngOnInit(): void {
    this.isLoading = true;
    this.subscribeFiltersChange();
    this.subscribeActiveFilterChipsChange();
    this.subscribeDisplayedColumnsChange();
    this.subscribeFullscreenChange();
    this.subscribeSelectedThresholdChange()

    this.displayedColumns = this.displayedColumnsOnFullscreenMode;
    this.loadThresholds();
  }

  ngAfterViewInit() {
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }


  ngOnDestroy(): void {
    this.thresholdsFacade.filtersChanged({});
    this.mobileQuery?.removeEventListener('change', this._mobileQueryListener);
    this._onDestroy.next();
    this._onDestroy.complete();
    this.addRemoveColumnsFacade.isOverlayOpen.next(false);
    this.thresholdsFacade.selectedThresholdChangeBehaviorSubject.next(-1);

  }

  private subscribeFiltersChange(): void {
    this.thresholdsFacade.filters$
      .pipe(takeUntil(this._onDestroy))
      .subscribe(async (filters: ThresholdFilters) => {
        if (Object.keys(filters).length > 0 && this.isInThresholdsScreen()) {
          this.closeDetailsSliderIfOpened();
          if(filters.createdAt){
            const newDate = new Date(filters.createdAt).setHours(12);
            filters.createdAt = new Date(newDate).toISOString().slice(0, 22);
          }
          this.filters = filters;
          this.loadThresholds();
        }
      });
  }

  private closeDetailsSliderIfOpened(): void {
    if (this.lastOpenedThresholdDetailsID) {
      this.closeThresholdDetails();
    }
  }

  private isInThresholdsScreen(): boolean {
    return window.location.href.includes('accounts');
  }

  private subscribeActiveFilterChipsChange(): void {
    this.thresholdsFacade.activeFiltersChips$
      .pipe(takeUntil(this._onDestroy))
      .subscribe((activeFilterChips: Map<string, CsFinActiveFilterChip>) => {
        this.activeFilterChips = activeFilterChips;
      });

  }



  private subscribeDisplayedColumnsChange(): void {
    this.addRemoveColumnsFacade.fetchDisplayedColumns
      .pipe(takeUntil(this._onDestroy))
      .subscribe((columns: CsFinAddRemoveColumns) => {
        setTimeout(() => {
            if (!this.thresholdDetailsOpened) {
              this.lastDisplayedColumns = this.displayedColumns;
              this.displayedColumns = columns.displayedColumns ? columns.displayedColumns : [];
            }
            else {
              this.lastDisplayedColumns = this.displayedColumns;
              this.displayedColumns = this.displayedColumnsOnDetailsMode;
            }

          }
          , 0);
      });
  }

  private subscribeFullscreenChange(): void {
    this.thresholdsFacade.fullscreenChangeBehaviorSubject
      .pipe(takeUntil(this._onDestroy))
      .subscribe((isFullscreen: boolean) => {
        this.handleFullscreenChange(isFullscreen);
      });
  }

  private subscribeSelectedThresholdChange(): void {
    this.thresholdsFacade.selectedThresholdChangeBehaviorSubject
      .pipe(takeUntil(this._onDestroy))
      .subscribe((thresholdID: number) => {
        if(thresholdID>0 && this.lastOpenedThresholdDetailsID != thresholdID){
          setTimeout(() => {
            this.ref.markForCheck();
          }, 0);
        }
      });
  }


  loadThresholds() {
    this.thresholdsFacade.getAll(this.filters)
      .pipe(takeUntil(this._onDestroy))
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: (result: PageResponse<Threshold>) => {
          this.dataSource = new MatTableDataSource<Threshold>(result.results);
          this.dataSource.sortingDataAccessor = CsFinSortingDataAccessorHelperService.objectSortingDataAccessor;
          this.pagination.totalItems = result.total;
          this.pagination.pageSize = result.size;
          this.pagination.pageIndex = result.page - 1;
          this.setSort();
        },
        error: (error:HttpErrorResponse) => {
          this.notificationService.errorWithResponse(error);
        }
      });
  }

  triggerOverlay(trigger: CdkOverlayOrigin): void {
    this.triggerOrigin = trigger;
    this.addRemoveColumnsFacade.isOverlayOpen.next(true);
  }

  private handleFullscreenChange(isFullscreen: boolean): void {
    this.isThresholdDetailsInFullscreen = isFullscreen;
    if(this.isThresholdDetailsInFullscreen){
      this.displayedColumns = this.displayedColumnsOnFullscreenMode
    } else {
      this.displayedColumns = this.displayedColumnsOnDetailsMode;
    }
    this.ref.markForCheck();
  }

  getColumnDetails(): CsFinColumnDetails[] {
    return [
      {position: 0, name: 'ID', code: 'id', isActive: true, isRemovable: false, isDefault: true},
      {position: 1, name: 'Country', code: 'country', isActive: true, isRemovable: true, isDefault: true},
      {position: 2, name: 'Currency', code: 'currency', isActive: true, isRemovable: true, isDefault: true},
      {position: 3, name: 'Amount', code: 'amount', isActive: true, isRemovable: true, isDefault: true},
      {position: 4, name: 'Created At', code: 'createdAt', isActive: true, isRemovable: true, isDefault: true},
      {position: 5, name: 'Created By', code: 'createdBy', isActive: false, isRemovable: true, isDefault: false},
      {position: 6, name: 'Updated At', code: 'updatedAt', isActive: false, isRemovable: true, isDefault: false},
      {position: 7, name: 'Updated By', code: 'updatedBy', isActive: false, isRemovable: true, isDefault: false},
      {position: 8, name: 'Actions', code: 'actions', isActive: true, isRemovable: false, isDefault: true},
    ];
  }

  onPageChange(event: any) {
    this.filters.page = event.pageIndex + 1;
    this.filters.size = event.pageSize;
    this.thresholdsFacade.filtersChanged(this.filters);
  }

  onActiveFilterRemoveClick(removedChipKey: string): void {
    const filtersToRemove = removedChipKey.split(activeFiltersSeparator);
    if (filtersToRemove) {
      filtersToRemove.forEach((filterKey: string) => {
        // @ts-ignore
        delete this.filters[filterKey];
      })
    }
    this.thresholdsFacade.filtersChanged(this.filters);
  }

  private setSort(): void {
    if (!this.sort) {
      return;
    }

    const previousSort = {
      active: this.sort.active,
      direction: this.sort.direction
    };
    if (this.filters.orderField) {
      this.sort.active = this.decodeSortField(this.filters.orderField);
    }
    if (this.filters.orderDirection) {
      this.sort.direction = this.filters.orderDirection.toLowerCase() as SortDirection;
    }

    const sort = {
      active: this.sort.active,
      direction: this.sort.direction
    };
    if (!_.isEqual(previousSort, sort)) {
      this.sort.sortChange.emit(this.sort);
    }
  }

  onSortChange(event: any): void {
    const sortFiltersBefore = {
      orderDirection: this.filters.orderDirection,
      orderField: this.filters.orderField
    };

    this.filters.orderDirection = event.direction?.toUpperCase();
    this.filters.orderField = this.encodeSortField(event.active);

    if(!_.isEqual(sortFiltersBefore, this.filters as SortFilters)){
      this.thresholdsFacade.filtersChanged(this.filters);
    }

  }

  private encodeSortField(field: string): string {
    switch (field) {
      case 'id':
        return 'ID';
      case 'currency':
        return 'CURRENCY';
      case 'country':
        return 'COUNTRY';
      case 'amount':
        return 'AMOUNT';
      case 'createdAt':
        return 'CREATED_AT';
      case 'createdBy':
        return 'CREATED_BY';
      case 'updatedAt':
        return 'UPDATED_AT';
      case 'updatedBy':
        return 'UPDATED_BY';
      case 'status':
        return 'STATUS';
      default:
        return field.toUpperCase();
    }
  }

  private decodeSortField(field: string): string {
    switch (field) {
      case 'ID':
        return 'id';
      case 'CURRENCY':
        return 'currency';
      case 'COUNTRY':
        return 'country';
      case 'AMOUNT':
        return 'amount';
      case 'CREATED_AT':
        return 'createdAt';
      case 'CREATED_BY':
        return 'createdBy';
      case 'UPDATED_AT':
        return 'updatedAt';
      case 'UPDATED_BY':
        return 'updatedBy';
      default:
        return field.toLowerCase();
    }
  }

  onCreateClick(): void {
    const dialogRef = this.dialog.open(ThresholdsWriteComponent, {
      data: {type: CsFinWritePopupType.create}
    });

    dialogRef.afterClosed().subscribe(async result => {
      if (result) {
        this.loadThresholds();
      }
    });
  }
  onEditClick(threshold: Threshold) {
    event?.stopPropagation();
    const dialogRef = this.dialog.open(ThresholdsWriteComponent, {
      data: {type: CsFinWritePopupType.update, threshold}
    });

    dialogRef.afterClosed().subscribe(async result => {
      if (result) {
        this.loadThresholds();
      }
    });
  }

  onDeleteClick(threshold: Threshold) {
    event?.stopPropagation();
    const dialogRef = this.dialog.open(CsFinConfirmationDialogComponent, {
      data: {
        titleKey: 'CONFIRMATION_DIALOG.MESSAGES.ABOUT_TO_DELETE_THRESHOLD',
        descriptionKey: 'CONFIRMATION_DIALOG.ACTIONS.ARE_YOU_SURE',
        params: {id: threshold.id},
        cancelActionKey: 'CONFIRMATION_DIALOG.BUTTONS.NO',
        confirmActionKey: 'CONFIRMATION_DIALOG.BUTTONS.YES'
      },
      width: '600px'
    });

    dialogRef.afterClosed().subscribe(response => {
      if (response && threshold.id) {
        this.isLoading = true;
        this.thresholdsFacade.delete(threshold.id)
          .pipe(
            finalize(() => this.isLoading = false)
          )
          .subscribe({
            next: () => {
              this.loadThresholds();
            },
            error: (error:HttpErrorResponse) => {
              this.notificationService.errorWithResponse(error);
            }
          })
      }
    });
  }

  onOpenItemDetailsClick(thresholdID:number):void {
    this.thresholdsFacade.thresholdFilterParamsBehaviorSubject.next(this.route.snapshot.queryParams);
    this.lastOpenedThresholdDetailsID = thresholdID;
  }

  closeThresholdDetails(): void {
    this.thresholdDetailsOpened = false;
    this.displayedColumns = this.lastDisplayedColumns;
    this.lastOpenedThresholdDetailsID = null;
  }

  isItemDetailsOpened(thresholdID: number): boolean {
    return thresholdID === this.lastOpenedThresholdDetailsID;
  }

}
