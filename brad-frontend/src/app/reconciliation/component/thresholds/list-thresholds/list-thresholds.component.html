<main class="container" cdkDropListGroup>
  <section class="filters" *ngIf="dataSource">
    <cs-fin-active-filters [chipsMap]="activeFilterChips"
                           (onRemove)="onActiveFilterRemoveClick($event)">
    </cs-fin-active-filters>
    <span fxFlex></span>
    <span class="actions">
      <button mat-raised-button color="primary" class="mat-mdc-raised-button" (click)="onCreateClick()"
              csFinHasPermissionOnAnyTarget
              [authPermissions]="auth.permissions.BRAD_MANAGE_THRESHOLDS"
              [authTarget]="bradAuthTarget"
              [authAction]="auth.actions.DISABLE">
        <mat-icon>add</mat-icon>
        <span class="label" fxShow [fxHide.xs]="true" [fxHide.sm]="true">
          {{ 'GENERAL.BUTTONS.LABELS.CREATE' | translate }}
        </span>
      </button>
      <button mat-stroked-button id="change-columns-btn" color="primary" fxShow [fxHide.xs]="true" [fxHide.sm]="true"
              [disabled]="thresholdDetailsOpened" cdkOverlayOrigin #trigger="cdkOverlayOrigin"
              (click)="triggerOverlay(trigger)">
        <mat-icon>table_rows</mat-icon>
      </button>
    </span>
  </section>

  <section class="table-detail responsive-table"
           *ngIf="!isLoading"
           [class.detail-opened]="thresholdDetailsOpened"
           [class.fullscreen]="isThresholdDetailsInFullscreen">

    <div class="table-container" [class.detail-opened]="thresholdDetailsOpened"
         [class.fullscreen]="isThresholdDetailsInFullscreen">

      <mat-progress-bar mode="indeterminate" *ngIf="isLoading"></mat-progress-bar>

      <table mat-table class="cs-fin-table" matSort [dataSource]="dataSource"  [class.loading]="isLoading"
             (matSortChange)="onSortChange($event)" cdkDropList cdkDropListSortingDisabled>

        <ng-container matColumnDef="id">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'THRESHOLDS.FIELDS.ID' | translate }}</th>
          <td mat-cell *matCellDef="let threshold"> {{threshold?.id}} </td>
        </ng-container>

        <ng-container matColumnDef="country">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'THRESHOLDS.FIELDS.COUNTRY' | translate }}</th>
          <td mat-cell class="country-cell" *matCellDef="let threshold">
            <div>
              {{threshold?.country.name}} <cs-fin-flag [countryCode]="threshold?.country.code"></cs-fin-flag>
              <span *ngIf="!threshold?.country"> - </span>
            </div>
          </td>
        </ng-container>

        <ng-container matColumnDef="currency">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'THRESHOLDS.FIELDS.CURRENCY' | translate }}</th>
          <td mat-cell *matCellDef="let threshold">
            <span> {{threshold?.currency.code}} </span>
            <span *ngIf="!threshold?.currency"> - </span>
          </td>
        </ng-container>

        <ng-container matColumnDef="amount">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'THRESHOLDS.FIELDS.AMOUNT' | translate }}</th>
          <td mat-cell *matCellDef="let threshold"> {{threshold?.amount | number:'1.2-2'}} </td>
        </ng-container>

        <ng-container matColumnDef="createdAt">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'THRESHOLDS.FIELDS.CREATED_AT' | translate }}</th>
          <td mat-cell *matCellDef="let threshold"> {{threshold?.createdAt | date:'short'}} </td>
        </ng-container>

        <ng-container matColumnDef="createdBy">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'THRESHOLDS.FIELDS.CREATED_BY' | translate }}</th>
          <td mat-cell *matCellDef="let threshold"> {{threshold?.createdBy}} </td>
        </ng-container>

        <ng-container matColumnDef="updatedAt">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'THRESHOLDS.FIELDS.UPDATED_AT' | translate }}</th>
          <td mat-cell *matCellDef="let threshold"> {{threshold?.updatedAt | date:'short'}} </td>
        </ng-container>

        <ng-container matColumnDef="updatedBy">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'THRESHOLDS.FIELDS.UPDATED_BY' | translate }}</th>
          <td mat-cell *matCellDef="let threshold"> {{threshold?.updatedBy}} </td>
        </ng-container>

        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef>{{ 'GENERAL.FIELDS.ACTIONS' | translate }}</th>
          <td mat-cell *matCellDef="let threshold" [attr.data-label]="'Actions'">
            <div class="table-actions">
              <mat-icon (click)="onEditClick(threshold)" csFinHasPermissionOnAnyTarget
                        [authPermissions]="auth.permissions.BRAD_MANAGE_THRESHOLDS"
                        [authTarget]="bradAuthTarget"
                        [authAction]="auth.actions.HIDE">
                edit
              </mat-icon>


              <mat-icon (click)="onDeleteClick(threshold)"  csFinHasPermissionOnAnyTarget
                        [authPermissions]="auth.permissions.BRAD_MANAGE_THRESHOLDS"
                        [authTarget]="bradAuthTarget"
                        [authAction]="auth.actions.HIDE">
                delete
              </mat-icon>
            </div>

          </td>
        </ng-container>




        <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"
            (click)="onOpenItemDetailsClick(row.id)"
            [class.item-opened]="isItemDetailsOpened(row.id)"
            class="with-detail" style="cursor: pointer;"></tr>
      </table>



      <mat-paginator [pageSizeOptions]="pagination.pageSizeOptions"
                     [pageSize]="pagination.pageSize"
                     [length]="pagination.totalItems"
                     [pageIndex]="pagination.pageIndex"
                     (page)="onPageChange($event)"
                     showFirstLastButtons>
      </mat-paginator>

      <span id="empty-table" *ngIf="!isLoading && !dataSource.data.length">
        {{'GENERAL.TABLE.NO_RESULTS_FOUND' | translate}}
      </span>
    </div>
    <div class="{{isThresholdDetailsInFullscreen ? 'details-full-screen' : 'details-container'}}"
         *ngIf="thresholdDetailsOpened">
      <router-outlet></router-outlet>
    </div>
  </section>

  <cs-fin-add-remove-columns [triggerOrigin]="triggerOrigin"
                             [isOverlayOpen$]="isOverlayOpen$"
                             [menu]="MENU">
  </cs-fin-add-remove-columns>
</main>
