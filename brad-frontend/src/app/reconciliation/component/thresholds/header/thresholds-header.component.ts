import {ChangeDetectorRef, Component, Input, OnDestroy, OnInit, ViewChild, ViewEncapsulation} from '@angular/core';
import {FormControl, FormGroup} from "@angular/forms";
import {BehaviorSubject, Observable, Subject, tap} from "rxjs";
import {Currency} from "../../../../entities/currency/currency";
import {Country} from "../../../../entities/account/country";
import {CdkConnectedOverlay} from "@angular/cdk/overlay";
import {CsFinActiveFiltersFacade, CsFinApiService, CsFinSidenavService} from "@jumia-cs-fin/common";
import {ActivatedRoute, Router} from "@angular/router";
import {CountryApiService} from "../../../../api/service/country-api.service";
import {CurrencyApiService} from "../../../../api/service/currency-api.service";
import {ThresholdsFacade} from "../facade/thresholds.facade";
import {takeUntil} from "rxjs/operators";
import * as _ from "lodash";
import {ThresholdFilters} from "../../../../entities/thresholds/threshold-filters";

@Component({
  selector: 'brad-thresholds-header',
  templateUrl: './thresholds-header.component.html',
  styleUrls: ['./thresholds-header.component.scss','../../../../../assets/brad-custom.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ThresholdsHeaderComponent implements OnInit, OnDestroy {
  @Input() showFilters!: boolean;
  filterTextFormControl!: FormControl;
  queryParams = {};

  form!: FormGroup;
  countryFormControl!: FormControl;
  currencyFormControl!: FormControl;
  createdAtFormControl!: FormControl;

  isOpen$!: Observable<boolean>;
  isRefreshing = false;
  isInitializing = false;

  currencySearchFormControl = new FormControl();
  currencyList:Currency[] = [];
  filteredCurrencyList:Currency[] = [];

  countrySearchFormControl = new FormControl();
  countryList:Country[] = [];
  filteredCountryList:Country[] = [];

  private readonly refreshTimeout = 2000;
  private _onDestroy: Subject<void> = new Subject<void>();
  private _isOpen!: BehaviorSubject<boolean>;

  @ViewChild(CdkConnectedOverlay, {static: true})
  private connectedOverlay!: CdkConnectedOverlay;

  constructor(public ref: ChangeDetectorRef,
              public sidenav: CsFinSidenavService,
              private activatedRoute: ActivatedRoute,
              private activeFiltersFacade: CsFinActiveFiltersFacade,
              private router: Router,
              private apiService: CsFinApiService,
              private countryApiService: CountryApiService,
              private currencyApiService: CurrencyApiService,
              private thresholdsFacade: ThresholdsFacade) { }

  async ngOnInit(): Promise<void> {
    this.isInitializing = true;
    this.initializeOverlay();
    this.initFiltersSearch();
    this.subscribeUrlParamsChanges();
    this.subscribeActiveFiltersChange();
    this.subscribeFiltersChange();
  }

  ngOnDestroy(): void {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  private subscribeUrlParamsChanges(): void {
    this.activatedRoute.queryParams
      .pipe(takeUntil(this._onDestroy))
      .subscribe(params => this.initializeFormData(params));
  }

  private subscribeActiveFiltersChange(): void {
    this.activeFiltersFacade.openFiltersOverlay
      .pipe(takeUntil(this._onDestroy))
      .subscribe(value => this._isOpen.next(value));
  }

  private subscribeFiltersChange(): void {
    this.thresholdsFacade.filters$
      .pipe(takeUntil(this._onDestroy))
      .subscribe((filters:ThresholdFilters) => {
        if(!this.isInitializing) {
          this.updateFormData(filters);
          this.updateMissingUrlFilters(filters);
        }
      });
  }

  private initFiltersSearch(): void {
    this.initCountrySearch();
    this.initCurrencySearch();
  }


  private initializeOverlay(): void {
    this._isOpen = new BehaviorSubject<boolean>(false);
    this.connectedOverlay.backdropClass = 'cdk-overlay-transparent-backdrop';
    this.connectedOverlay.panelClass = 'filters-overlay-panel';
    this.connectedOverlay.backdropClick
      .pipe(tap(() => this._isOpen.next(false)))
      .subscribe();
    this.isOpen$ = this._isOpen.asObservable();
  }

  private initCountrySearch(): void {
    this.countrySearchFormControl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe((value: String) => {
        const searchTerm = value.trim();
        if (searchTerm) {
          this.filteredCountryList = this.countryList.filter((country) => {
            return country.name.trim().toLowerCase().includes(searchTerm.toLowerCase());
          })
        } else {
          this.filteredCountryList = this.countryList;
        }
      });
  }

  private initCurrencySearch(): void {
    this.currencySearchFormControl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe((value: String) => {
        const searchTerm = value.trim();
        if (searchTerm) {
          this.filteredCurrencyList = this.currencyList.filter((currency) => {
            return currency.code.trim().toLowerCase().includes(searchTerm.toLowerCase());
          })
        } else {
          this.filteredCurrencyList = this.currencyList;
        }
      });
  }

  clearFilters(): void {
    this.form.reset('', {emitEvent: false});
    this.thresholdsFacade.filtersChanged({});
  }

  submit(input?: HTMLInputElement) {
    this.thresholdsFacade.filtersChanged(this.getFormValues());
    const formQueryParams = this.apiService.buildQueryParams(this.getFormValues());
    if (_.isEqual(this.queryParams, formQueryParams)) {
      this.isRefreshing = true;
      this.ref.markForCheck();
      setTimeout(() => {
        this.isRefreshing = false;
        this.ref.markForCheck();
      }, this.refreshTimeout);
    } else {
      input?.blur();
      this.router.navigate(['/thresholds'], {queryParams: formQueryParams});
    }
    this.closeOverlay();

  }

  triggerOverlay(): void {
    this._isOpen.next(!this._isOpen.value);
  }

  closeOverlay(): void {
    this._isOpen.next(false);
  }

  private getFormValues(): ThresholdFilters {
    return this.form.value as ThresholdFilters;
  }

  private initializeFormData(params:any): void {
    this.queryParams = params;
    if(!this.form) {
      this.form = new FormGroup({});
      this.initializeFormControlsAndFilters(params);
    }
  }

  private initializeFormControlsAndFilters(params:any): void {
    const filters: ThresholdFilters = {};

    this.filterTextFormControl = new FormControl(params.filterText);
    filters.filterText = params.filterText;

    this.createdAtFormControl = new FormControl(params.createdAt);
    filters.createdAt = params.createdAt;

    filters.orderField = params.orderField;
    filters.orderDirection = params.orderDirection;
    filters.page = params.page;
    filters.size = params.size;


    Promise.all([
      this.applyCountryUrlFilter(params, filters),
      this.applyCurrencyUrlFilter(params, filters)
    ]).then(() => {
      this.setFormControlsToForm();
      this.thresholdsFacade.filtersChanged(filters);
      this.updateMissingUrlFilters(filters)
      this.isInitializing = false;
    });

  }

  private applyCountryUrlFilter(params: any, filters: ThresholdFilters): Promise<void> {
    return new Promise<void>(async (resolve) => {
      this.countryFormControl = new FormControl();
      if(!params.countryCodes) {
        resolve();
        return;
      }

      await this.loadCountries();
      this.countryFormControl = new FormControl(filters.countryCode);

      resolve();
    });
  }

  private applyCurrencyUrlFilter(params: any, filters: ThresholdFilters): Promise<void> {
    return new Promise<void>(async (resolve) => {
      this.currencyFormControl = new FormControl();
      if(!params.currencyCodes) {
        resolve();
        return;
      }

      await this.loadCurrencies();
        this.currencyFormControl = new FormControl(filters.currencyCode);
      resolve();
    });
  }


  private updateFormData (params: ThresholdFilters): void {
    this.filterTextFormControl.setValue(params.filterText, {emitEvent: false});
    this.countryFormControl.setValue(params.countryCode, {emitEvent: false});
    this.currencyFormControl.setValue(params.currencyCode, {emitEvent: false});
    this.createdAtFormControl.setValue(params.createdAt, {emitEvent: false});
  }

  private updateMissingUrlFilters(filters: ThresholdFilters): void {
    const formQueryParams = this.apiService.buildQueryParams(filters);
    if (!_.isEqual(this.queryParams, formQueryParams)) {
      this.router.navigate(['thresholds'], {queryParams: formQueryParams});
    }
  }

  private setFormControlsToForm(): void {
    this.form.addControl(this.thresholdsFacade.filterTextKey, this.filterTextFormControl);
    this.form.addControl(this.thresholdsFacade.countryCodeKey, this.countryFormControl);
    this.form.addControl(this.thresholdsFacade.currencyCodeKey, this.currencyFormControl);
    this.form.addControl(this.thresholdsFacade.createdAtKey, this.createdAtFormControl);
  }

  noFiltersSelected() {
    return !!this.form.errors || this.isRefreshing;
  }

  loadCountries(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if(this.countryList.length <= 0) {
        this.countryApiService.getAll()
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (countries: Country[]) => {
              this.countryList = countries;
              this.filteredCountryList = this.countryList;
              resolve();
            }, error: (error) => reject(error)
          });
      } else {
        resolve();
      }
    });
  }

  loadCurrencies(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if(this.currencyList.length <= 0) {
        this.currencyApiService.getAll()
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (currencies: Currency[]) => {
              this.currencyList = currencies;
              this.filteredCurrencyList = this.currencyList;
              resolve();
            }, error: (error) => reject(error)
          });
      } else {
        resolve();
      }
    });
  }

  compareIdFn(o1: any, o2: any): boolean {
    return o1 && o2 && o1.id === o2.id;
  }
}
