@import 'node_modules/@jumia-cs-fin/common/assets/styles/header-components';

.filters-overlay-panel {
  background-color: transparent;
}

.filter-field{
  mat-label {
    color: var(--extra-light-color) !important;
  }
  input {
    color: var(--primary-text-color) !important;
  }

}
.country-name-flag {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  cs-fin-flag {
    margin-left: 5px;
  }
}
