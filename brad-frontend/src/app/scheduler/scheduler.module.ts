import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';

import {SchedulerRoutingModule} from './scheduler-routing.module';
import {SchedulerListComponent} from './component/list/scheduler-list.component';
import {SchedulerWriteComponent} from './component/write/scheduler-write.component';
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {MatButtonModule} from "@angular/material/button";
import {MatDialogModule} from "@angular/material/dialog";
import {MatFormFieldModule} from "@angular/material/form-field";
import {MatIconModule} from "@angular/material/icon";
import {MatInputModule} from "@angular/material/input";
import {MatOptionModule} from "@angular/material/core";
import {MatProgressBarModule} from "@angular/material/progress-bar";
import {MatSelectModule} from "@angular/material/select";
import {TranslateModule} from "@ngx-translate/core";
import {MatDatepickerModule} from "@angular/material/datepicker";
import {MatMenuModule} from "@angular/material/menu";
import {MatToolbarModule} from "@angular/material/toolbar";
import {MatTooltipModule} from "@angular/material/tooltip";
import {MatCardModule} from "@angular/material/card";
import {MatPaginatorModule} from "@angular/material/paginator";
import {MatSortModule} from "@angular/material/sort";
import {MatTableModule} from "@angular/material/table";
import {MatTabsModule} from "@angular/material/tabs";
import {SchedulerHeaderComponent} from './component/header/scheduler-header.component';
import {ExtendedModule, FlexModule} from "@angular/flex-layout";
import {DragDropModule} from "@angular/cdk/drag-drop";
import {OverlayModule} from "@angular/cdk/overlay";
import {SharedModule} from "../shared/shared.module";
import {CsFinAuthModule} from "@jumia-cs-fin/common";
import {SchedulerSelectDatabasesComponent} from './component/select-databases/scheduler-select-databases.component';
import {MatProgressSpinnerModule} from "@angular/material/progress-spinner";
import {NgxMatSelectSearchModule} from "ngx-mat-select-search";

@NgModule({
  declarations: [
    SchedulerListComponent,
    SchedulerWriteComponent,
    SchedulerHeaderComponent,
    SchedulerSelectDatabasesComponent
  ],
    imports: [
        CommonModule,
        SchedulerRoutingModule,
        FormsModule,
        MatButtonModule,
        MatDialogModule,
        MatFormFieldModule,
        MatIconModule,
        MatInputModule,
        MatOptionModule,
        MatProgressBarModule,
        MatSelectModule,
        ReactiveFormsModule,
        TranslateModule,
        MatDatepickerModule,
        MatMenuModule,
        MatToolbarModule,
        MatTooltipModule,
        MatCardModule,
        MatPaginatorModule,
        MatSortModule,
        MatTableModule,
        MatTabsModule,
        ExtendedModule,
        FlexModule,
        DragDropModule,
        OverlayModule,
        SharedModule,
        CsFinAuthModule,
        MatProgressSpinnerModule,
        NgxMatSelectSearchModule
    ]
})
export class SchedulerModule { }
