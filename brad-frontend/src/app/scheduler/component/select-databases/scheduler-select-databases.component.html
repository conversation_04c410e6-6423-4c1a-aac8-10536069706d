<div class="dialog-container">
  <header class="dialog-header">
    <span class="title" mat-dialog-title>{{'BALE_VIEW_ENTITY.TITLE' | translate}}</span>
  </header>

  <div class="dialog-content">
    <div class="form-container">
      <!-- bale view entity -->
      <mat-form-field class="filter-field" appearance="outline" data-cy="input-bale-view-entity-filter"
                      (click)="fetchBaleViewEntities()">
        <mat-label>{{'BALE_VIEW_ENTITY.FIELDS.VIEW_NAME' | translate}}</mat-label>
        <mat-select [formControl]="baleViewEntityFormControl" [compareWith]="compareIdFn" multiple>
          <ng-container *ngIf="filteredBaleViewEntityList != null; else loadingBaleViewEntity">
            <mat-option>
              <ngx-mat-select-search [formControl]="baleViewEntitySearchFormControl"
                                     [noEntriesFoundLabel]="'GENERAL.FILTERS.NO_MATCHING_RESULTS_FOUND' | translate"
                                     [placeholderLabel]="'GENERAL.FILTERS.SEARCH' | translate">
              </ngx-mat-select-search>
            </mat-option>
            <mat-option *ngFor="let baleViewEntity of filteredBaleViewEntityList" [value]="baleViewEntity">
              <div class="option-text">
                {{formatViewName(baleViewEntity.viewName)}}
                <span class="small-text">
                 ({{baleViewEntity.databaseName}})
              </span>
              </div>
            </mat-option>
            <button mat-button color="primary" class="clear-selection-btn"
                    [disabled]="!baleViewEntityFormControl.value?.length"
                    (click)="baleViewEntityFormControl.reset([])">
              {{'GENERAL.FILTERS.ACTIONS.CLEAR_SELECTION' | translate}}
            </button>
          </ng-container>
          <ng-template #loadingBaleViewEntity>
            <mat-option disabled>
              <div class="filters-loading-container">
                <span>{{'GENERAL.FILTERS.LOADING' | translate}}</span>
                <mat-spinner diameter="20"></mat-spinner>
              </div>
            </mat-option>
          </ng-template>
        </mat-select>
      </mat-form-field>
    </div>
  </div>

  <div class="dialog-actions" mat-dialog-actions>
    <span fxFlex></span>
    <button mat-flat-button mat-dialog-close id="cancel-btn" [fxShow]="true" [fxHide.xs]="true" [fxHide.sm]="true">
      {{ 'GENERAL.BUTTONS.LABELS.CANCEL' | translate }}
    </button>
    <button mat-flat-button color="primary"
            [disabled]="baleViewEntityFormControl.value?.length === 0"
            id="create-item-btn" (click)="onSaveClick()" data-cy="btn-save"
            csFinHasPermissionOnTarget
            [authPermissions]="auth.permissions.BRAD_MANAGE_SCHEDULER"
            [authTarget]="bradAuthTarget"
            [authAction]="auth.actions.DISABLE">
      {{ "BALE_VIEW_ENTITY.BUTTONS.LABELS.SYNC" | translate }}
      <span *ngIf="baleViewEntityFormControl.value?.length > 0">
         ({{baleViewEntityFormControl.value?.length}})
      </span>
    </button>
  </div>
</div>
