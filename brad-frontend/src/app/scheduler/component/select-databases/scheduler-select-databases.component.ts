import {Component, OnD<PERSON>roy, OnInit} from '@angular/core';
import {BaleViewEntityApiService} from "../../../api/service/bale-view-entity-api.service";
import {FormControl, FormGroup} from "@angular/forms";
import {BaleViewEntity} from "../../../entities/bale-view-entity/bale-view-entity";
import {takeUntil} from "rxjs/operators";
import {Subject} from "rxjs";
import {BaleJobFilters} from "../../../entities/scheduler/bale-job-filters";
import {authParams, bradAuthTarget} from "../../../auth/constants/auth.constants";
import {BaleFacade} from "../../../bale/facade/bale.facade";
import {MatDialogRef} from "@angular/material/dialog";

@Component({
  selector: 'brad-scheduler-select-databases',
  templateUrl: './scheduler-select-databases.component.html',
  styleUrls: ['./scheduler-select-databases.component.scss']
})
export class SchedulerSelectDatabasesComponent implements OnInit, OnDestroy {

  form!: FormGroup;
  baleViewEntityFormControl!: FormControl;

  baleViewEntitySearchFormControl = new FormControl();
  baleViewEntityList:BaleViewEntity[] = [];
  filteredBaleViewEntityList:BaleViewEntity[] = [];


  private _onDestroy: Subject<void> = new Subject<void>();

  constructor( public dialogRef: MatDialogRef<SchedulerSelectDatabasesComponent>,
               public baleViewEntityService: BaleViewEntityApiService,
              public baleFacade: BaleFacade) {
  }

  ngOnInit(): void {
    this.initializeFormData()
    this.initFiltersSearch();
  }

  ngOnDestroy(): void {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  private initFiltersSearch(): void {
    this.initBaleViewEntitiesSearch();
  }

  private initializeFormData(): void {
    if(!this.form) {
      this.form = new FormGroup({});

      this.initializeFormControlsAndFilters();
      this.setFormControlsToForm();
    }
  }

  private setFormControlsToForm() : void {
    this.form.addControl('baleViewEntity', this.baleViewEntityFormControl);
  }

  private initializeFormControlsAndFilters(): void {
    const filters: BaleJobFilters = {};
    this.baleViewEntityFormControl = new FormControl();

    Promise.all([
      this.applyBaleViewEntity(filters)
    ]);
  }

  private initBaleViewEntitiesSearch(): void {
    this.baleViewEntitySearchFormControl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe((value: String) => {
        const searchTerm = value.trim();
        if (searchTerm) {
          this.filteredBaleViewEntityList = this.baleViewEntityList.filter((baleViewEntity) => {
            return baleViewEntity.viewName.trim().toLowerCase().includes(searchTerm.toLowerCase());
          })
        } else {
          this.filteredBaleViewEntityList = this.baleViewEntityList;
        }
      });
  }

  private applyBaleViewEntity(filters: BaleJobFilters): Promise<void> {
    return new Promise<void>(async (resolve) => {

      await this.fetchBaleViewEntities();

      this.baleViewEntityFormControl.setValue(filters.baleEntityViewsIds, {emitEvent: false})
      resolve();
    });
  }


  protected fetchBaleViewEntities(): void {
    this.baleViewEntityService.fetchBaleViewEntities().subscribe({
      next: (baleViewEntities : BaleViewEntity[]) => {
        this.baleViewEntityList = baleViewEntities.map(baleViewEntity => baleViewEntity);
        this.filteredBaleViewEntityList = this.baleViewEntityList;
      },
      error: (error) => {
        console.error(error);
      }
    });
  }

  compareIdFn(o1: any, o2: any): boolean {
    return o1 && o2 && o1.id === o2.id;
  }

  formatViewName(viewName: string): string {
    return viewName.split("$")[0];
  }

  protected onSaveClick(): void {
    this.baleFacade.syncByBaleViewIds(this.baleViewEntityFormControl.value.map((baleViewEntity: BaleViewEntity) => baleViewEntity.id)).subscribe(
      () => {
        this.dialogRef.close(true);
      }
    )
  }

  protected readonly auth = authParams;
  protected readonly bradAuthTarget = bradAuthTarget;
}
