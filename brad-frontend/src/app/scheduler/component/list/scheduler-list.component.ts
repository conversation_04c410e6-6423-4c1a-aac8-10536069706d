import {Component, OnInit, ViewChild} from '@angular/core';
import {SchedulerApiService} from "../../../api/service/scheduler-api.service";
import {SchedulerJob} from "../../../entities/schedulerJob";
import {MatTableDataSource} from "@angular/material/table";
import {MatSort} from "@angular/material/sort";
import cronstrue from "cronstrue";
import {CsFinActiveFilterChip, CsFinWritePopupType} from "@jumia-cs-fin/common";
import {MatDialog} from "@angular/material/dialog";
import {SchedulerWriteComponent} from "../write/scheduler-write.component";
import {finalize} from "rxjs/operators";
import {authParams, bradAuthTarget} from "../../../auth/constants/auth.constants";
import {SchedulerSelectDatabasesComponent} from "../select-databases/scheduler-select-databases.component";

@Component({
  selector: 'brad-scheduler-list',
  templateUrl: './scheduler-list.component.html',
  styleUrls: ['./scheduler-list.component.scss']
})
export class SchedulerListComponent implements OnInit {

  @ViewChild(MatSort, {static: true}) sort!: MatSort;
  isLoading = true;
  displayedColumns: string[] = ['jobName','cronExpression', "cronDescription", "timezone", "lastFiredTime", "nextFireTime", "state", "actions"];
  showProgressBar = false;
  dataSource: MatTableDataSource<SchedulerJob> = new MatTableDataSource<SchedulerJob>([]);
  activeFilterChips!: Map<string, CsFinActiveFilterChip>;

  baleJob!: SchedulerJob;

  protected readonly bradAuthTarget = bradAuthTarget;
  auth = authParams;
  constructor(private schedulerApiService: SchedulerApiService,
              private dialog: MatDialog) {}

  ngOnInit(): void {
    this.isLoading = true;
    this.fetchJobs();
    this.fetchBaleJobName();
  }

  fetchJobs() {
    this.showProgressBar = true;
    this.schedulerApiService.fetchJobs()
      .pipe(finalize(() => this.isLoading = false))
      .subscribe((data: SchedulerJob[]) => {
      this.showProgressBar = false;
      for (const info of data) {
        info.cronDescription = cronstrue.toString(info.cronExpression);
      }
      this.dataSource = new MatTableDataSource<SchedulerJob>(data);
      this.dataSource.sort = this.sort;
    });
  }

  onEditClick(job: SchedulerJob) {
    const dialogRef = this.dialog.open(SchedulerWriteComponent, {
      data: {type: CsFinWritePopupType.update, jobName: job.jobName}
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.fetchJobs();
      }
    });
  }

  onRunJobClick(job: SchedulerJob) {
    this.schedulerApiService.forceJobRun(job.jobName).subscribe(() => {});
  }

  onToggleJobStateClick(job: SchedulerJob) {
    this.schedulerApiService.toggleJobState(job.jobName).subscribe(() => {
      this.fetchJobs();
    });
  }

  onRunSomeDatabasesClick(job: SchedulerJob) {
    const dialogRef = this.dialog.open(SchedulerSelectDatabasesComponent, {
      data: {jobName: job.jobName}
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.fetchJobs();
      }
    });
  }

  fetchBaleJobName() {
    this.schedulerApiService.fetchBaleJobName().subscribe((data: SchedulerJob) => {
      this.baleJob = data || {} as SchedulerJob;
    });
  }

}
