<brad-scheduler-header></brad-scheduler-header>
<main class="container" cdkDropListGroup>
    <section class="filters" *ngIf="dataSource">

        <span fxFlex></span>

    </section>
  <section class="table-detail responsive-table">

    <div class="table-container">

      <mat-progress-bar mode="indeterminate" *ngIf="isLoading"></mat-progress-bar>

      <table mat-table class="cs-fin-table" matSort [dataSource]="dataSource" data-cy="scheduler-table"
             cdkDropList cdkDropListSortingDisabled>

            <ng-container matColumnDef="jobName">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'SCHEDULER.FIELDS.JOB_NAME' | translate }}</th>
              <td mat-cell *matCellDef="let job"> {{job.jobName}} </td>
            </ng-container>

            <ng-container matColumnDef="cronExpression">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'SCHEDULER.FIELDS.CRON_STRING' | translate }}</th>
              <td mat-cell *matCellDef="let job"> {{job.cronExpression}} </td>
            </ng-container>

            <ng-container matColumnDef="cronDescription">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'SCHEDULER.FIELDS.CRON_DESCRIPTION' | translate }}</th>
              <td mat-cell *matCellDef="let job"> {{job.cronDescription}} </td>
            </ng-container>

            <ng-container matColumnDef="timezone">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'SCHEDULER.FIELDS.TIMEZONE' | translate }}</th>
              <td mat-cell *matCellDef="let job"> {{job.timezone}} </td>
            </ng-container>

            <ng-container matColumnDef="lastFiredTime">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'SCHEDULER.FIELDS.LAST_RUN' | translate }}</th>
              <td mat-cell *matCellDef="let job">
                <span *ngIf="job.lastFiredTime">
                  {{job.lastFiredTime | date: 'medium'}}
                </span>
                <span *ngIf="!job.lastFiredTime">
                  {{'SCHEDULER.LABELS.NEVER' | translate}}
                </span>
              </td>
            </ng-container>

            <ng-container matColumnDef="nextFireTime">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'SCHEDULER.FIELDS.NEXT_RUN' | translate }}</th>
              <td mat-cell *matCellDef="let job">
                <span *ngIf="job.nextFireTime">
                  {{job.nextFireTime | date: 'medium'}}
                </span>
                <span *ngIf="!job.nextFireTime">
                  {{'SCHEDULER.LABELS.NEVER' | translate}}
                </span>
              </td>
            </ng-container>

            <ng-container matColumnDef="state">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'SCHEDULER.FIELDS.STATE' | translate }}</th>
              <td mat-cell *matCellDef="let job"> {{job.state}} </td>
            </ng-container>

            <ng-container matColumnDef="actions">
              <th mat-header-cell *matHeaderCellDef>{{ 'GENERAL.FIELDS.ACTIONS' | translate }}</th>
              <td mat-cell *matCellDef="let job" [attr.data-label]="'Actions'">
                <button mat-icon-button [matMenuTriggerFor]="menuActions" [matMenuTriggerData]="{job: job}"
                        data-cy="btn-actions-menu" (click)="$event.stopPropagation()">
                  <mat-icon>more_vert</mat-icon>
                </button>
              </td>
            </ng-container>

            <mat-menu #menuActions="matMenu" class="actions-menu-container" data-cy="scheduler-action-menu">
              <ng-template matMenuContent let-aliasMenuItem="job">

                <button *ngIf="aliasMenuItem.state != 'ERROR'"  mat-menu-item (click)="onToggleJobStateClick(aliasMenuItem)" csFinHasPermissionOnTarget
                        [authPermissions]="auth.permissions.BRAD_MANAGE_SCHEDULER"
                        [authTarget]="bradAuthTarget"
                        [authAction]="auth.actions.DISABLE">
                  <mat-icon>{{ aliasMenuItem.state == 'PAUSED' ? 'play_circle_outline' : 'pause_circle_outline' }}</mat-icon>
                  {{ (aliasMenuItem.state == 'PAUSED' ? 'GENERAL.BUTTONS.TOOLTIPS.RESUME' : 'GENERAL.BUTTONS.TOOLTIPS.PAUSE') | translate }}
                </button>

                <button mat-menu-item (click)="onEditClick(aliasMenuItem)" csFinHasPermissionOnTarget
                        [authPermissions]="auth.permissions.BRAD_MANAGE_SCHEDULER"
                        [authTarget]="bradAuthTarget"
                        [authAction]="auth.actions.DISABLE">
                  <mat-icon>edit</mat-icon>
                  {{'GENERAL.BUTTONS.TOOLTIPS.EDIT' | translate}}
                </button>

                <button mat-menu-item (click)="onRunJobClick(aliasMenuItem)" csFinHasPermissionOnTarget
                        [authPermissions]="auth.permissions.BRAD_MANAGE_SCHEDULER"
                        [authTarget]="bradAuthTarget"
                        [authAction]="auth.actions.DISABLE">
                  <mat-icon>play_arrow</mat-icon>
                  <span *ngIf="aliasMenuItem.jobName == this.baleJob.jobName">
                    {{'GENERAL.BUTTONS.TOOLTIPS.RUN_ALL_DB' | translate}}
                  </span>
                  <span *ngIf="!(aliasMenuItem.jobName == this.baleJob.jobName)">
                    {{'GENERAL.BUTTONS.TOOLTIPS.RUN' | translate}}
                  </span>
                </button>

                <button mat-menu-item *ngIf="aliasMenuItem.jobName == this.baleJob.jobName"
                        (click)="onRunSomeDatabasesClick(aliasMenuItem)" csFinHasPermissionOnTarget
                        [authPermissions]="auth.permissions.BRAD_MANAGE_SCHEDULER"
                        [authTarget]="bradAuthTarget"
                        [authAction]="auth.actions.DISABLE">
                  <mat-icon>play_arrow</mat-icon>
                  <span *ngIf="aliasMenuItem.jobName == this.baleJob.jobName">
                    {{'GENERAL.BUTTONS.TOOLTIPS.RUN_SOME_DB' | translate}}
                  </span>
                </button>
              </ng-template>
            </mat-menu>

            <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;"  style="cursor: pointer;"></tr>
          </table>

    </div>
  </section>
</main>
