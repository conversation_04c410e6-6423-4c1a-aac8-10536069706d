<div class="dialog-container">
  <header class="dialog-header">
    <span class="title" mat-dialog-title>{{getTitle() | translate}}</span>
  </header>

  <div class="dialog-content">
    <div class="form-container">
      <mat-form-field appearance="outline">
        <mat-label>{{ 'SCHEDULER.FIELDS.JOB_NAME' | translate }}</mat-label>
        <input matInput type="text" [value]="schedulerForm.get('jobName')?.value" data-cy="input-jobName" disabled>
      </mat-form-field>

      <mat-form-field appearance="outline">
        <mat-label>{{ 'SCHEDULER.FIELDS.CRON_STRING' | translate }}</mat-label>
        <input matInput type="text" [formControl]="cronExpressionFormControl" (input)="onUpdateClick()" data-cy="input-accNumber" required>
        <mat-error *ngIf="schedulerForm.get('cronExpression')?.errors" data-cy="input-accNumber-errors">
          {{ 'SCHEDULER.WRITE.ERRORS.CRON_STRING_IS_REQUIRED' | translate }}
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline">
        <mat-label>{{ 'SCHEDULER.FIELDS.CRON_DESCRIPTION' | translate }}</mat-label>
        <input matInput type="text" [value]="schedulerForm.get('cronDescription')?.value" data-cy="input-cronDescription" disabled>
      </mat-form-field>

      <mat-form-field appearance="outline">
        <mat-label>{{ 'SCHEDULER.FIELDS.STATE' | translate }}</mat-label>
        <input matInput type="text" [value]="schedulerForm.get('state')?.value" data-cy="input-state" disabled>
      </mat-form-field>
    </div>


  </div>

  <div class="dialog-actions" mat-dialog-actions>
    <span fxFlex></span>
    <button mat-flat-button mat-dialog-close id="cancel-btn" [fxShow]="true" [fxHide.xs]="true" [fxHide.sm]="true">
      {{ 'GENERAL.BUTTONS.LABELS.CANCEL' | translate }}
    </button>
    <button mat-flat-button color="primary" id="create-item-btn"
            (click)="onSaveClick()" [disabled]="schedulerForm.invalid" data-cy="btn-save"
            csFinHasPermissionOnTarget
            [authPermissions]="auth.permissions.BRAD_MANAGE_SCHEDULER"
            [authTarget]="bradAuthTarget"
            [authAction]="auth.actions.DISABLE">
      {{ getButtonAction() | translate }}
    </button>
  </div>

</div>
