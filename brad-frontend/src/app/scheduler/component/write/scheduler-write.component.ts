import {Component, Inject, OnInit} from '@angular/core';
import {Form<PERSON>uilder, FormControl, FormGroup, Validators} from "@angular/forms";
import {MAT_DIALOG_DATA, MatDialogRef} from "@angular/material/dialog";
import {CsFinWritePopupType} from "@jumia-cs-fin/common";
import {SchedulerJob} from "../../../entities/schedulerJob";
import {finalize} from "rxjs/operators";
import {SchedulerApiService} from "../../../api/service/scheduler-api.service";
import cronstrue from "cronstrue";
import {authParams, bradAuthTarget} from "../../../auth/constants/auth.constants";
import {NotificationService} from "../../../api/service/notification.service";
import {HttpErrorResponse} from "@angular/common/http";

@Component({
  selector: 'brad-scheduler-write',
  templateUrl: './scheduler-write.component.html',
  styleUrls: ['./scheduler-write.component.scss']
})
export class SchedulerWriteComponent implements OnInit {

  schedulerForm!: FormGroup;
  cronExpressionFormControl!: FormControl;
  isSavingData = false;
  isLoadingData = false;

  protected readonly bradAuthTarget = bradAuthTarget;
  protected readonly authParams = authParams;
  auth = authParams;
  constructor(private schedulerApiService: SchedulerApiService,
              public dialogRef: MatDialogRef<SchedulerWriteComponent>,
              @Inject(MAT_DIALOG_DATA) public data: { type: CsFinWritePopupType, jobName: string },
              private formBuilder: FormBuilder,
              private notificationService: NotificationService,) { }


  ngOnInit(): void {
    this.schedulerForm = new FormGroup({});
    this.initFormControls();
    this.setFormControlsToForm();
    if (this.data != null) {
      this.loadSchedulerAndSetFormGroup(this.data.jobName);
    }
  }

  initFormControls(): void {
    this.cronExpressionFormControl = new FormControl('', [Validators.required]);
  }

  setFormControlsToForm(): void {
    this.schedulerForm.addControl('cronExpression', this.cronExpressionFormControl);
    this.schedulerForm.addControl('jobName', this.formBuilder.control('', ));
    this.schedulerForm.addControl('cronDescription', this.formBuilder.control('', ));
    this.schedulerForm.addControl('state', this.formBuilder.control('', ));
  }

  loadSchedulerAndSetFormGroup(name: string): void {
    this.isLoadingData = true;
    this.schedulerApiService.fetchJob(name)
      .pipe(
        finalize(() => this.isLoadingData = false)
      )
      .subscribe({
        next: (job:SchedulerJob) => {
          this.schedulerForm.setValue({
            cronExpression: job.cronExpression,
            jobName: job.jobName,
            cronDescription: cronstrue.toString(job.cronExpression),
            state: job.state
          });
        },
        error:(error:HttpErrorResponse) => {
          this.notificationService.errorWithResponse(error);
        }
      });
  }


  isEditView(): boolean {
    return this.data.type === CsFinWritePopupType.update;
  }


  buildSchedulerRequest(): SchedulerJob {
    return {
      cronExpression: this.schedulerForm.get('cronExpression')?.value,
      jobName: this.schedulerForm.get('jobName')?.value
    };
  }

  onSaveClick(): void {

    const schedulerReq = this.buildSchedulerRequest();
     if (this.isEditView()) {
      this.doUpdate(schedulerReq);
    }

  }


  doUpdate(job: SchedulerJob): void {

    this.isSavingData = true;
    this.schedulerApiService.update(job)
      .pipe(
        finalize(() => this.isSavingData = false)
      )
      .subscribe({
        next: () => {
          this.notificationService.successTranslated('SCHEDULER.NOTIFICATIONS.UPDATE_SCHEDULE_SUCCESS', {jobName: job.jobName});
          this.dialogRef.close(true);
        },
        error:(error:HttpErrorResponse) => {
          this.notificationService.errorWithResponse(error);
        }
      });
  }

  onUpdateClick(): void {
    let cronDescription: string;
    try {
      cronDescription = cronstrue.toString(this.schedulerForm.get('cronExpression')?.value);
    }
    catch (e: any) {
      cronDescription = e;
    }


    this.schedulerForm.setValue({
      cronExpression: this.schedulerForm.get('cronExpression')?.value,
      jobName: this.schedulerForm.get('jobName')?.value,
      cronDescription: cronDescription,
      state: this.schedulerForm.get('state')?.value
    })
  }

  getButtonAction() {
    return 'GENERAL.BUTTONS.LABELS.SAVE';
  }

  getTitle() {
    return 'SCHEDULER.WRITE.OPERATION_TYPES.EDIT';
  }

}
