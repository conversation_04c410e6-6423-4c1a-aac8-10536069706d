import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {NavigationComponent} from "../navigation/navigation.component";
import {SchedulerListComponent} from "./component/list/scheduler-list.component";
import {csFinAuthCanActivateGuard} from "@jumia-cs-fin/common";
import {bradPermissions} from "../auth/constants/permission.constants";
import {bradAuthTarget} from "../auth/constants/auth.constants";
import {csFinAuthorizationOnTargetService} from "../app-routing.module";

const routes: Routes = [
  {
    path: 'scheduler',
    component: NavigationComponent,
    children: [
      {
        path: '',
        canActivate: [csFinAuthCanActivateGuard],
        data: {
          auth: {
            permissions: bradPermissions.BRAD_ACCESS_SCHEDULER,
            targets: bradAuthTarget,
            authTypeService: csFinAuthorizationOnTargetService,
          }
        },
        component: SchedulerListComponent,
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class SchedulerRoutingModule { }
