import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';

import {StatementFilesRoutingModule} from './statement-files-routing.module';
import {StatementFilesHeaderComponent} from './component/header/statement-files-header.component';
import {StatementFilesListComponent} from './component/list/statement-files-list.component';

import {CdkConnectedOverlay, CdkOverlayOrigin} from "@angular/cdk/overlay";
import {ExtendedModule, FlexModule} from "@angular/flex-layout";
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {MatButtonModule} from "@angular/material/button";
import {MatDatepickerModule} from "@angular/material/datepicker";
import {MatFormFieldModule} from "@angular/material/form-field";
import {MatIconModule} from "@angular/material/icon";
import {MatInputModule} from "@angular/material/input";
import {MatOptionModule} from "@angular/material/core";
import {MatProgressSpinnerModule} from "@angular/material/progress-spinner";
import {MatSelectModule} from "@angular/material/select";
import {MatToolbarModule} from "@angular/material/toolbar";
import {NgxMatSelectSearchModule} from "ngx-mat-select-search";
import {TranslateModule} from "@ngx-translate/core";
import {CdkDropList, CdkDropListGroup} from "@angular/cdk/drag-drop";
import {
  CsFinActiveFiltersModule,
  CsFinAddRemoveColumnsModule,
  CsFinAuthModule, CsFinLinkModule,
  CsFinUtilsModule
} from "@jumia-cs-fin/common";
import {MatPaginatorModule} from "@angular/material/paginator";
import {MatProgressBarModule} from "@angular/material/progress-bar";
import {MatSortModule} from "@angular/material/sort";
import {MatTableModule} from "@angular/material/table";
import {AccountsModule} from "../accounts/accounts.module";
import {MatCardModule} from "@angular/material/card";
import {MatTabsModule} from "@angular/material/tabs";
import {JsonFormatter} from "../shared/helpers/json-formatter";
import {MatExpansionModule} from "@angular/material/expansion";
import {StatementFilesComponent} from "./statement-files.component";
import {
  StatementFilesDetailsInfoComponent
} from "./component/details/details-info/statement-files-details-info.component";
import {StatementFilesDetailsComponent} from "./component/details/statement-files-details.component";
import {MatTooltipModule} from "@angular/material/tooltip";
import {MatMenuModule} from "@angular/material/menu";



@NgModule({
  declarations: [
    StatementFilesComponent,
    StatementFilesHeaderComponent,
    StatementFilesListComponent,
    StatementFilesDetailsComponent,
    StatementFilesDetailsInfoComponent,
  ],
  exports: [
    StatementFilesHeaderComponent,
    StatementFilesListComponent
  ],
  imports: [
    CommonModule,
    StatementFilesRoutingModule,
    CdkConnectedOverlay,
    CdkOverlayOrigin,
    ExtendedModule,
    FlexModule,
    FormsModule,
    MatButtonModule,
    MatDatepickerModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatOptionModule,
    MatProgressSpinnerModule,
    MatSelectModule,
    MatToolbarModule,
    NgxMatSelectSearchModule,
    TranslateModule,
    ReactiveFormsModule,
    CdkDropList,
    CdkDropListGroup,
    CsFinActiveFiltersModule,
    CsFinAddRemoveColumnsModule,
    CsFinAuthModule,
    MatPaginatorModule,
    MatProgressBarModule,
    MatSortModule,
    MatTableModule,
    AccountsModule,
    MatCardModule,
    MatTabsModule,
    JsonFormatter,
    MatExpansionModule,
    CsFinUtilsModule,
    CsFinLinkModule,
    MatTooltipModule,
    MatMenuModule,
  ]
})
export class StatementFilesModule { }
