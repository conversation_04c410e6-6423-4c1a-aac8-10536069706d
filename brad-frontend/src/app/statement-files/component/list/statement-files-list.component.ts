import {ChangeDetector<PERSON><PERSON>, Component, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild} from '@angular/core';
import {MatPaginator} from "@angular/material/paginator";
import {MatSort, Sort} from "@angular/material/sort";
import {MenusConsts} from "../../../shared/constants/menus.constants";
import {MatTableDataSource} from "@angular/material/table";
import {
  activeFiltersSeparator,
  CsFinActiveFilterChip,
  CsFinAddRemoveColumns,
  CsFinAddRemoveColumnsFacade,
  CsFinColumnDetails,
  CsFinConfirmationDialogComponent,
  CsFinLastUsedColumns,
  CsFinPagination,
  csFinPaginationConsts,
  CsFinSortFilters,
  CsFinSortingDataAccessorHelperService,
  csFintoSnakeCase
} from "@jumia-cs-fin/common";
import {authParams, bradAuthTarget} from "../../../auth/constants/auth.constants";
import {Observable, Subject} from "rxjs";
import {CdkOverlayOrigin} from "@angular/cdk/overlay";
import {MediaMatcher} from "@angular/cdk/layout";
import {MatDialog} from "@angular/material/dialog";
import {ActivatedRoute, Router} from "@angular/router";
import {finalize, takeUntil} from "rxjs/operators";
import {PageResponse} from "../../../entities/page-response";
import {StatementFilesFacade} from "../../facade/statement-files.facade";
import {HttpErrorResponse} from "@angular/common/http";
import {NotificationService} from "../../../api/service/notification.service";
import {StatementFilesFilters} from "../../../entities/statement-files/statement-files-filters";
import {StatementFiles} from "../../../entities/statement-files/statement-files";
import {StatementFacade} from "../../../accounts/facade/statement.facade";
import {AccountFacade} from "../../../accounts/facade/account.facade";
import * as _ from "lodash";
import {SortFilters} from "../../../entities/SortFilters";
import {Document} from "../../../entities/document/document";
import {AccountStatementInformationFilters} from "../../../entities/account/account-statement-information-filters";

@Component({
  selector: 'brad-statement-files-list',
  templateUrl: './statement-files-list.component.html',
  styleUrls: ['./statement-files-list.component.scss']
})
export class StatementFilesListComponent implements OnInit, OnDestroy {


  @ViewChild(MatPaginator, {static: true}) paginator!: MatPaginator;
  @ViewChild(MatSort, {static: true}) sort!: MatSort;
  defaultSortValue: Sort = {active: 'updatedAt', direction: 'desc'};
  readonly MENU = MenusConsts.accountStatementFiles;

  lastOpenedStatementFilesDetailsID!: number | null;
  private displayedColumnsOnDetailsMode = ['id', 'name', 'processingStatus', 'accountId', 'createdAt'];

  isLoading = true;
  dataSource: MatTableDataSource<StatementFiles> = new MatTableDataSource<StatementFiles>([]);
  displayedColumns: string[] = [];
  mobileQuery: MediaQueryList;
  pagination: CsFinPagination = {
    pageSizeOptions: csFinPaginationConsts.pageSizeOptions,
    pageSize: csFinPaginationConsts.defaultPageSize,
    pageIndex: 0,
    totalItems: 0
  };

  activeFilterChips!: Map<string, CsFinActiveFilterChip>;
  auth = authParams;
  accountStatementFilesDetailsOpened = false;

  isOverlayOpen$!: Observable<boolean>;
  triggerOrigin!: CdkOverlayOrigin;
  localStorageDetailSelectedTabVariableName = 'accountStatementDetailsTabGroupSelectedIndex';
  private _mobileQueryListener: () => void;
  private _onDestroy: Subject<void> = new Subject<void>();

  private filters: StatementFilesFilters = {
    page: 1,
    size: csFinPaginationConsts.defaultPageSize
  };
  private lastDisplayedColumns!: string[];
  selectedDocumentId: number | undefined;
  selectedDownloadDocId: number | undefined;

  constructor(
    public ref: ChangeDetectorRef,
    public media: MediaMatcher,
    private dialog: MatDialog,
    private route: ActivatedRoute,
    private router: Router,
    private notificationService: NotificationService,
    private accountStatementFilesFacade: StatementFilesFacade,
    private statementFacade: StatementFacade,
    private accountFacade: AccountFacade,
    private addRemoveColumnsFacade: CsFinAddRemoveColumnsFacade
  ) {
    this.mobileQuery = media.matchMedia('(max-width: 960px)');
    this._mobileQueryListener = () => ref.detectChanges();
    this.mobileQuery?.addEventListener('change', this._mobileQueryListener);

    CsFinLastUsedColumns.getInstance().initColumns(this.MENU, this.getColumnDetails());
  }

  ngOnInit(): void {
    this.isLoading = true;
    this.subscribeFiltersChange();
    this.subscribeActiveFilterChipsChange();
    this.subscribeDisplayedColumnsChange();
    this.subscribeSelectedStatementChange()
    this.subscribeDetailsCloseChange();
  }


  ngOnDestroy(): void {
    this.accountStatementFilesFacade.filtersChanged({});
    this.mobileQuery?.removeEventListener('change', this._mobileQueryListener);
    this._onDestroy.next();
    this._onDestroy.complete();
    this.addRemoveColumnsFacade.isOverlayOpen.next(false);
    this.accountStatementFilesFacade.selectedStatementFilesChangeBehaviorSubject.next(-1);

  }

  private subscribeFiltersChange(): void {
    this.accountStatementFilesFacade.filters$
      .pipe(takeUntil(this._onDestroy))
      .subscribe(async (filters: StatementFilesFilters) => {
        if (Object.keys(filters).length > 0 && this.isInStatementScreen()) {
          this.closeDetailsSliderIfOpened();
          if (filters.createdAtTo) {
            const newDate = new Date(filters.createdAtTo).setHours(12);
            filters.createdAtTo = new Date(newDate).toISOString().slice(0, 22);
          }
          this.filters = filters;
          this.loadStatementsFiles();
        }
      });
  }

  private closeDetailsSliderIfOpened(): void {
    if (this.lastOpenedStatementFilesDetailsID) {
      this.closeStatementDetails();
    }
  }

  private isInStatementScreen(): boolean {
    return window.location.href.includes('statement-files');
  }

  private subscribeActiveFilterChipsChange(): void {

    this.accountStatementFilesFacade.activeFiltersChips$
      .pipe(takeUntil(this._onDestroy))
      .subscribe((activeFilterChips: Map<string, CsFinActiveFilterChip>) => {
        this.activeFilterChips = activeFilterChips;
      });
  }

  private subscribeDisplayedColumnsChange(): void {
    this.addRemoveColumnsFacade.fetchDisplayedColumns
      .pipe(takeUntil(this._onDestroy))
      .subscribe((columns: CsFinAddRemoveColumns) => {
        setTimeout(() => {
            if (!this.accountStatementFilesDetailsOpened) {
              this.lastDisplayedColumns = this.displayedColumns;
              this.displayedColumns = columns.displayedColumns ? columns.displayedColumns : [];
            } else {
              this.lastDisplayedColumns = this.displayedColumns;
              this.displayedColumns = this.displayedColumnsOnDetailsMode;
            }

          }
          , 0);
      });
  }

  private subscribeSelectedStatementChange(): void {
    this.accountStatementFilesFacade.selectedStatementFilesChangeBehaviorSubject
      .pipe(takeUntil(this._onDestroy))
      .subscribe((accountStatementID: number) => {
        if (accountStatementID > 0 && this.lastOpenedStatementFilesDetailsID != accountStatementID) {
          setTimeout(() => {
            this.onOpenItemDetailsClick(accountStatementID);
            this.ref.markForCheck();
          }, 0);
        }
      });
  }

  private subscribeDetailsCloseChange(): void {
    this.accountStatementFilesFacade.detailsCloseBehaviorSubject
      .pipe(takeUntil(this._onDestroy))
      .subscribe((isClosed: boolean) => {
        if (isClosed) {
          this.closeStatementDetails();
        }
      });
  }


  loadStatementsFiles() {
    this.accountStatementFilesFacade.getAll(this.filters)
      .pipe(takeUntil(this._onDestroy))
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: (result: PageResponse<StatementFiles>) => {
          this.dataSource.data = result.results;
          this.dataSource.sort = this.sort;
          this.dataSource.paginator = this.paginator;
          this.dataSource.sortingDataAccessor = CsFinSortingDataAccessorHelperService.objectSortingDataAccessor;
          this.pagination.totalItems = result.total;
          this.pagination.pageSize = result.size;
          this.pagination.pageIndex = result.page - 1;

          this.selectedDocumentId = undefined;
        },
        error: (error: HttpErrorResponse) => {
          this.notificationService.errorWithResponse(error);
        }
      });
  }

  triggerOverlay(trigger: CdkOverlayOrigin): void {
    this.triggerOrigin = trigger;
    this.addRemoveColumnsFacade.isOverlayOpen.next(true);
  }

  getColumnDetails(): CsFinColumnDetails[] {
    return [
      {position: 0, name: 'ID', code: 'id', isActive: true, isRemovable: true, isDefault: true},
      {position: 1, name: 'Name', code: 'name', isActive: true, isRemovable: true, isDefault: true},
      {position: 2, name: 'Status', code: 'processingStatus', isActive: true, isRemovable: true, isDefault: true},
      {position: 3, name: 'Account', code: 'accountId', isActive: true, isRemovable: true, isDefault: true},
      {position: 4, name: 'Created At', code: 'createdAt', isActive: true, isRemovable: true, isDefault: true},
      {position: 5, name: 'Actions', code: 'actions', isActive: true, isRemovable: true, isDefault: true}
    ];
  }

  onPageChange(event: any) {
    this.filters.page = event.pageIndex + 1;
    this.filters.size = event.pageSize;
    this.accountStatementFilesFacade.filtersChanged(this.filters);
  }

  onActiveFilterRemoveClick(removedChipKey: string): void {
    const filtersToRemove = removedChipKey.split(activeFiltersSeparator);
    if (filtersToRemove) {
      filtersToRemove.forEach((filterKey: string) => {
        // @ts-ignore
        delete this.filters[filterKey];
      })
    }
    this.accountStatementFilesFacade.filtersChanged(this.filters);
  }

  onSortChange(event: any): void {

    const sortFiltersBefore: CsFinSortFilters = {
      orderDirection: this.filters.orderDirection,
      orderField: this.filters.orderField
    };

    const orderField = csFintoSnakeCase(event.active);
    this.filters.orderDirection = event.direction?.toUpperCase();
    this.filters.orderField = orderField.toUpperCase();

    if(!_.isEqual(sortFiltersBefore, this.filters as SortFilters)){
      this.accountStatementFilesFacade.filtersChanged(this.filters);
    }
  }

  onOpenItemDetailsClick(accountStatementId: number): void {
    if (this.accountStatementFilesDetailsOpened) {
      this.resetSelectedDetailsTab();
    }
    if (!this.accountStatementFilesDetailsOpened) {
      this.accountStatementFilesDetailsOpened = true;
      this.lastDisplayedColumns = this.displayedColumns;

    } else if (this.lastOpenedStatementFilesDetailsID === accountStatementId) {
      this.router.navigate(['.'], {queryParamsHandling: 'preserve', relativeTo: this.route});
      this.closeStatementDetails();
      return;
    }
    this.lastOpenedStatementFilesDetailsID = accountStatementId;
    this.router.navigate([`${accountStatementId}`], {queryParamsHandling: 'preserve', relativeTo: this.route});

  }

  resetSelectedDetailsTab(): void {
    const detailTabGroupSelectedIndex = localStorage.getItem(this.localStorageDetailSelectedTabVariableName);
    if (detailTabGroupSelectedIndex === '1')
      localStorage.setItem(this.localStorageDetailSelectedTabVariableName, '0');
  }

  closeStatementDetails(): void {
    this.accountStatementFilesDetailsOpened = false;
    this.displayedColumns = this.lastDisplayedColumns;
    this.lastOpenedStatementFilesDetailsID = null;
  }

  isItemDetailsOpened(accountStatementID: number): boolean {
    return accountStatementID === this.lastOpenedStatementFilesDetailsID;
  }

  OnActionClick(message: string, event: any): void {
    event.stopPropagation();

    const dialogRef = this.dialog.open(CsFinConfirmationDialogComponent, {
      data: {
        titleKey: 'GENERAL.CONFIRMATION.TITLE',
        descriptionKey: message,
        cancelActionKey: 'GENERAL.ACTIONS.CANCEL',
        confirmActionKey: 'GENERAL.ACTIONS.CONFIRM'
      },
      width: '600px'
    });

    dialogRef.afterClosed().subscribe(response => {
      if (response) {
        this.onScanStatementFiles();
      }
    });
  }

  onReprocessFiles(message: string, event: any): void {
    event.stopPropagation();

    const dialogRef = this.dialog.open(CsFinConfirmationDialogComponent, {
      data: {
        titleKey: 'GENERAL.CONFIRMATION.TITLE',
        descriptionKey: message,
        cancelActionKey: 'GENERAL.ACTIONS.CANCEL',
        confirmActionKey: 'GENERAL.ACTIONS.CONFIRM'
      },
      width: '600px'
    });

    dialogRef.afterClosed().subscribe(response => {
      if (response) {
        this.reprocessFiles();
      }
    });
  }

    onScanStatementFiles(): void {
        this.accountStatementFilesFacade.scan()
            .subscribe({
                next: () => {
                    this.notificationService.successTranslated('STATEMENT_FILES.NOTIFICATIONS.SCAN_FILES_SUCCESS', {});
                },
                error: (error: HttpErrorResponse) => {
                    this.notificationService.errorWithResponse(error);
                }
            });
    }

  reprocessFiles(): void {
    this.accountStatementFilesFacade.reprocess()
    .subscribe({
      next: () => {
        this.notificationService.successTranslated('STATEMENT_FILES.NOTIFICATIONS.REPROCESS_FILES_SUCCESS', {});
      },
      error: (error: HttpErrorResponse) => {
        this.notificationService.errorWithResponse(error);
      }
    });
  }

  onDownloadStatementFileClick(id: number): void {
    this.selectedDownloadDocId = id;
    this.accountStatementFilesFacade.downloadStatementFiles(id)
      .pipe(
        finalize(() => {
          this.selectedDownloadDocId = undefined;
        })
      )
      .subscribe({
        next: (url) => {
          window.open(url);
          this.notificationService.successTranslated('STATEMENT_FILES.NOTIFICATIONS.DOWNLOAD_FILES_SUCCESS', {id});
        },
        error: (error:HttpErrorResponse) => {
          this.notificationService.errorWithResponse(error);
        }
      });
  }

  onReprocessFileClick(id: number): void {
    this.selectedDocumentId = id;
    let filters: StatementFilesFilters = {
      ids: [id]
    }
    this.accountStatementFilesFacade.reprocess(filters)
      .subscribe({
        next: (url) => {
          this.loadStatementsFiles();
        },
        error: (error:HttpErrorResponse) => {
          this.selectedDocumentId = undefined;
          this.notificationService.errorWithResponse(error);
        }
      });
  }

  protected readonly bradAuthTarget = bradAuthTarget;
}
