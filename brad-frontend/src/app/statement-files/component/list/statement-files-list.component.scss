@import "node_modules/@jumia-cs-fin/common/assets/styles/list-components";
@import "node_modules/@jumia-cs-fin/common/assets/styles/tables";


.mat-column-actions {
  width: 130px !important;
}

.rotate {
  animation: spin 2s linear infinite;
  display: inline-block;
}

.download-bounce {
  animation: drop-loop 1.5s ease-in-out infinite;
  display: inline-block;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes drop-loop {
  0% {
    transform: translateY(-20px);
    opacity: 0;
  }
  30% {
    opacity: 1;
  }
  50% {
    transform: translateY(0);
  }
  70% {
    opacity: 1;
  }
  100% {
    transform: translateY(20px);
    opacity: 0;
  }
}
