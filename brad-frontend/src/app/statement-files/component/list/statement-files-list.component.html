<main class="container" cdkDropListGroup>
  <section class="filters" *ngIf="dataSource">
    <cs-fin-active-filters [chipsMap]="activeFilterChips"
                           (onRemove)="onActiveFilterRemoveClick($event)">
    </cs-fin-active-filters>
    <span fxFlex></span>
    <span class="actions">
      <button mat-flat-button color="primary" class="mat-mdc-raised-button"
              (click)="OnActionClick(
                         'CONFIRMATION_DIALOG.MESSAGES.ABOUT_TO_SCAN_STATEMENT_FILES', $event)"
              csFinHasPermissionOnTarget
              [authPermissions]="auth.permissions.BRAD_SCAN_SFTP_FOLDER"
              [authTarget]="bradAuthTarget"
              [authAction]="auth.actions.DISABLE">
        <mat-icon>center_focus_weak</mat-icon>
                <span class="label" fxShow [fxHide.xs]="true" [fxHide.sm]="true">
          {{ 'GENERAL.BUTTONS.LABELS.SCAN' | translate }}
                </span>
            </button>
         <button mat-flat-button color="primary" class="mat-mdc-raised-button"

                 (click)="onReprocessFiles(
                         'CONFIRMATION_DIALOG.MESSAGES.ABOUT_TO_REPROCESS_STATEMENT_FILES', $event)"
                 csFinHasPermissionOnTarget
                 [authPermissions]="auth.permissions.BRAD_STATEMENT_FILES_ACCESS"
                 [authTarget]="bradAuthTarget"
                 [authAction]="auth.actions.DISABLE">
        <mat-icon>restore_page</mat-icon>
                <span class="label" fxShow [fxHide.xs]="true" [fxHide.sm]="true">
          {{ 'GENERAL.BUTTONS.LABELS.REPROCESS' | translate }}
                </span>
            </button>
      <button mat-stroked-button id="change-columns-btn" color="primary" fxShow [fxHide.xs]="true" [fxHide.sm]="true"
              [disabled]="accountStatementFilesDetailsOpened" cdkOverlayOrigin #trigger="cdkOverlayOrigin"
              (click)="triggerOverlay(trigger)">
        <mat-icon>table_rows</mat-icon>
      </button>

    </span>

  </section>
  <mat-progress-bar mode="indeterminate" *ngIf="isLoading"></mat-progress-bar>

  <section class="table-detail responsive-table"
           *ngIf="!isLoading"
           [class.detail-opened]="accountStatementFilesDetailsOpened">

    <div class="table-container" [class.detail-opened]="accountStatementFilesDetailsOpened">

      <mat-progress-bar mode="indeterminate" *ngIf="isLoading"></mat-progress-bar>

      <table mat-table class="cs-fin-table" matSort [dataSource]="dataSource" [class.loading]="isLoading"
             matSort
             [matSortActive]=defaultSortValue.active
             [matSortDirection]=defaultSortValue.direction
             (matSortChange)="onSortChange($event)" cdkDropList cdkDropListSortingDisabled>

        <ng-container matColumnDef="id">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'STATEMENT_FILES.FIELDS.ID' | translate }}</th>
          <td mat-cell *matCellDef="let accountStatementFiles"> {{ accountStatementFiles?.id }}</td>
        </ng-container>

        <ng-container matColumnDef="name">
          <th mat-header-cell *matHeaderCellDef>{{ 'STATEMENT_FILES.FIELDS.NAME' | translate }}
          </th>
          <td mat-cell *matCellDef="let accountStatementFiles"> {{ accountStatementFiles?.name }}</td>
        </ng-container>

        <ng-container matColumnDef="processingStatus">
          <th mat-header-cell *matHeaderCellDef>{{ 'STATEMENT_FILES.FIELDS.PROCESSING_STATUS' | translate }}
          </th>
          <td mat-cell *matCellDef="let accountStatementFiles"> {{ accountStatementFiles?.processingStatus }}</td>
        </ng-container>

        <ng-container matColumnDef="accountId">
          <th mat-header-cell *matHeaderCellDef>{{ 'STATEMENT_FILES.FIELDS.ACCOUNT' | translate }}
          </th>
          <td mat-cell *matCellDef="let accountStatementFiles"> {{ accountStatementFiles?.statement?.account?.accountNumber }}</td>
        </ng-container>

        <ng-container matColumnDef="createdAt">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'GENERAL.FIELDS.CREATED_AT' | translate }}</th>
          <td mat-cell *matCellDef="let accountStatementFiles"> {{ accountStatementFiles?.createdAt | date:'short' }}</td>
        </ng-container>

        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef>{{ 'GENERAL.FIELDS.ACTIONS' | translate }}</th>
          <td mat-cell *matCellDef="let accountStatementFiles" [attr.data-label]="'Actions'">
            <div class="table-actions">
              <button mat-icon-button (click)="onDownloadStatementFileClick(accountStatementFiles.id); $event.stopPropagation()"
                      [disabled]="!accountStatementFiles.url"
                      csFinHasPermissionOnTarget
                      [authPermissions]="auth.permissions.BRAD_DOWNLOAD_STATEMENT_FILE"
                      [authTarget]="bradAuthTarget"
                      [authAction]="auth.actions.HIDE">
                <mat-icon *ngIf="accountStatementFiles.id !== selectedDownloadDocId"> get_app</mat-icon>
                <mat-icon *ngIf="accountStatementFiles.id === selectedDownloadDocId"
                          [ngClass]="{'download-bounce': accountStatementFiles.id === selectedDownloadDocId}">
                  arrow_downward</mat-icon>
              </button>
              <button mat-icon-button (click)="onReprocessFileClick(accountStatementFiles.id); $event.stopPropagation()"
                      [disabled]="accountStatementFiles.processingStatus !== 'NEW'
                      || accountStatementFiles.id === selectedDocumentId"
                      csFinHasPermissionOnTarget
                      [authPermissions]="auth.permissions.BRAD_SCAN_SFTP_FOLDER"
                      [authTarget]="bradAuthTarget"
                      [authAction]="auth.actions.HIDE">
                <mat-icon [ngClass]="{'rotate': accountStatementFiles.id === selectedDocumentId}">
                  autorenew</mat-icon>
              </button>
            </div>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"
            (click)="onOpenItemDetailsClick(row.id)"
            [class.item-opened]="isItemDetailsOpened(row.id)"
            class="with-detail" style="cursor: pointer;"></tr>
      </table>

      <mat-paginator [pageSizeOptions]="pagination.pageSizeOptions"
                     [pageSize]="pagination.pageSize"
                     [length]="pagination.totalItems"
                     [pageIndex]="pagination.pageIndex"
                     (page)="onPageChange($event)"
                     showFirstLastButtons>
      </mat-paginator>

      <span id="empty-table" *ngIf="!isLoading && !dataSource.data.length">
        {{ 'GENERAL.TABLE.NO_RESULTS_FOUND' | translate }}
      </span>
    </div>
    <div class="details-container">
      <router-outlet></router-outlet>
    </div>
  </section>

  <cs-fin-add-remove-columns [triggerOrigin]="triggerOrigin"
                             [isOverlayOpen$]="isOverlayOpen$"
                             [menu]="MENU">
  </cs-fin-add-remove-columns>
</main>
