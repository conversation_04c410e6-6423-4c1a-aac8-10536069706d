import {Component, OnDestroy, OnInit} from '@angular/core';
import {Subject} from "rxjs";
import {ActivatedRoute, Params, Router} from "@angular/router";
import {takeUntil} from "rxjs/operators";
import {HttpErrorResponse} from "@angular/common/http";
import {NotificationService} from "../../../api/service/notification.service";
import {StatementFiles} from "../../../entities/statement-files/statement-files";
import {StatementFilesFacade} from "../../facade/statement-files.facade";

@Component({
  selector: 'brad-statement-files-details',
  templateUrl: './statement-files-details.component.html',
  styleUrls: ['./statement-files-details.component.scss']
})
export class StatementFilesDetailsComponent implements OnInit, OnDestroy {

  accountStatementFileID!: number;
  accountStatementFiles!: StatementFiles;

  private _onDestroy: Subject<void> = new Subject<void>();

  constructor(
    private accountStatementFilesFacade: StatementFilesFacade,
    private router: Router,
    private route: ActivatedRoute,
    private notificationService: NotificationService
  ) {
  }

  ngOnInit(): void {
    this.initRouteParams();
    this.initSelectedExecutionSubscription();
  }

  private initRouteParams(): void {
    this.route.params
      .pipe(takeUntil(this._onDestroy))
      .subscribe((params: Params) => {
        this.accountStatementFileID = Number(params['accountStatementFileID']);
        if (this.accountStatementFileID != null) {
          this.accountStatementFilesFacade.selectedStatementFilesChangeBehaviorSubject.next(this.accountStatementFileID);
        }
      });
  }

  private initSelectedExecutionSubscription(): void {
    this.accountStatementFilesFacade.selectedStatementFilesChangeBehaviorSubject
      .pipe(takeUntil(this._onDestroy))
      .subscribe((accountStatementFileID: number) => {
        this.accountStatementFileID = accountStatementFileID;
        this.loadStatementsFilesDetails();
      });
  }

  ngOnDestroy() {
    this._onDestroy.next();
    this._onDestroy.complete();
    this.accountStatementFilesFacade.detailsCloseBehaviorSubject.next(true);
  }

  onCloseClick(): void {
    this.accountStatementFilesFacade.detailsCloseBehaviorSubject.next(true);
    this.router.navigate(['statement-files'], {queryParams: this.route.snapshot.queryParams});
  }

  loadStatementsFilesDetails(): void {
    if (this.accountStatementFileID != null) {
      this.accountStatementFilesFacade.getById(this.accountStatementFileID)
        .pipe(takeUntil(this._onDestroy))
        .subscribe((accountStatementFiles: StatementFiles) => {
            this.accountStatementFiles = accountStatementFiles;
          },
          (error: HttpErrorResponse) => {
            this.notificationService.errorWithResponse(error);
          });

    }
  }

}
