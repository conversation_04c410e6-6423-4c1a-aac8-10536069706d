<div class="details-information">
  <div *ngIf="isLoading" class="loading">
    <mat-progress-spinner mode="indeterminate" diameter="75" strokeWidth="5"></mat-progress-spinner>
    <span class="label">{{ 'GENERAL.DETAILS.LOADING' | translate }}</span>
  </div>
  <div *ngIf="!isLoading && !detailedStatementFiles" class="detail-failed">
    <mat-icon>report_problem</mat-icon>
    <span class="label">{{ 'GENERAL.DETAILS.ERRORS.UNABLE_TO_FIND_DETAILS' | translate }}</span>
  </div>

  <main *ngIf="!isLoading && detailedStatementFiles">
    <section class="header">
      <button mat-mini-fab disabled>
        <mat-icon>compare</mat-icon>
      </button>
      <span class="item-name">{{ detailedStatementFiles.name }}</span>
    </section>

    <ul class="description">
      <li>
        <span class="field"
              data-cy="statement-files-details-field-id">{{ 'STATEMENT_FILES.FIELDS.ID' | translate }}
          : </span>
        <span class="value" data-cy="statement-files-details-value-id">{{ detailedStatementFiles?.id }}</span>
      </li>
      <li>
        <span class="field"
              data-cy="statement-files-details-field-name">{{ 'STATEMENT_FILES.FIELDS.NAME' | translate }}
          : </span>
        <span class="value"
              data-cy="statement-files-details-value-name">{{ detailedStatementFiles?.name }}</span>
      </li>
      <li>
        <span class="field"
              data-cy="statement-files-details-field-processing-status">{{ 'STATEMENT_FILES.FIELDS.PROCESSING_STATUS' | translate }}
          : </span>
        <span class="value"
              data-cy="statement-files-details-value-processing-status">{{ detailedStatementFiles?.processingStatus }}</span>
      </li>
      <li>
        <span class="field"
              data-cy="statement-files-details-field-account-id">
          {{ 'STATEMENT_FILES.FIELDS.ACCOUNT' | translate }}: </span>
        <span class="link"  *ngIf="detailedStatementFiles.statement" >
          <cs-fin-link (click)="onAccountIdClick($event, detailedStatementFiles?.statement?.account?.id)"
          data-cy="statement-files-details-value-account-id">
            {{detailedStatementFiles?.statement?.account?.id}}
          </cs-fin-link>
        </span>
      </li>
      <li>
        <span class="field"
              data-cy="statement-files-details-field-executionLog">{{ 'STATEMENT_FILES.FIELDS.EXECUTION_LOG' | translate }}
          : </span>
        <span class="link" *ngIf="detailedStatementFiles.executionLog">
          <cs-fin-link (click)="onExecutionClick($event, detailedStatementFiles?.executionLog)"
                       data-cy="statement-files-details-value-executionLog">
            {{detailedStatementFiles?.executionLog}}
          </cs-fin-link>
        </span>
      </li>
      <li>
        <span class="field"
              data-cy="statement-files-details-field-statements">{{ 'STATEMENT_FILES.FIELDS.STATEMENTS' | translate }}
          : </span>
        <span class="value"
              data-cy="statement-files-details-value-statements">{{ detailedStatementFiles?.statement?.id }}</span>
      </li>
    </ul>
    <ul class="additional-information">
      <li>
        <span class="field"
              data-cy="statement-files-details-field-created-at">{{ 'GENERAL.FIELDS.CREATED_AT' | translate }}
          : </span>
        <span class="value"
              data-cy="statement-files-details-value-created-at">{{ detailedStatementFiles?.createdAt | date:'short' }}</span>
      </li>
      <li>
        <span class="field"
              data-cy="statement-files-details-field-created-by">{{ 'GENERAL.FIELDS.CREATED_BY' | translate }}
          : </span>
        <span class="value"
              data-cy="statement-files-details-value-created-by">{{ detailedStatementFiles?.createdBy }}</span>
      </li>
      <li>
        <span class="field"
              data-cy="statement-files-details-field-updated-at">{{ 'GENERAL.FIELDS.UPDATED_AT' | translate }}
          : </span>
        <span class="value"
              data-cy="statement-files-details-value-updated-at">{{ detailedStatementFiles?.updatedAt | date:'short' }}</span>
      </li>
      <li>
        <span class="field"
              data-cy="statement-files-details-field-updated-by">{{ 'GENERAL.FIELDS.UPDATED_BY' | translate }}
          : </span>
        <span class="value"
              data-cy="statement-files-details-value-updated-by">{{ detailedStatementFiles?.updatedBy }}</span>
      </li>
    </ul>
  </main>
</div>
