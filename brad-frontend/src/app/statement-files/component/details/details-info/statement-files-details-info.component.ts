import {ChangeDetectionStrategy, Component, Input, OnChanges, OnDestroy, OnInit} from '@angular/core';

import {Subject} from "rxjs";

import {StatementFiles} from "../../../../entities/statement-files/statement-files";
import {environment} from "../../../../../environments/environment";

@Component({
  selector: 'brad-statement-files-details-info',
  templateUrl: './statement-files-details-info.component.html',
  styleUrls: ['./statement-files-details-info.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class StatementFilesDetailsInfoComponent implements OnInit, OnDestroy, OnChanges {

  @Input() detailedStatementFiles!: StatementFiles;

  baseFeUrl = environment.feBaseUrl;

  isLoading = false;
  private _onDestroy: Subject<void> = new Subject<void>();

  ngOnInit(): void {
    this.isLoading = true;
  }

  ngOnChanges() {
    if (this.detailedStatementFiles) {
      this.isLoading = false;
    }
  }

  ngOnDestroy(): void {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  onExecutionClick($event: MouseEvent, executionValue: number | undefined) {
    $event.preventDefault();
    if (executionValue === undefined) {
      console.error('Execution value is undefined. Cannot open execution logs.');
      return;
    }

    const url = `${this.baseFeUrl}/execution-logs/${executionValue}`
    window.open(url, '_blank', 'noopener,noreferrer');
  }

  onAccountIdClick($event: MouseEvent, accountId: number | undefined) {
    $event.preventDefault();
    if (accountId === undefined) {
      console.error('Account ID is undefined. Cannot construct URL.');
      return;
    }

    const url = `${this.baseFeUrl}/accounts/${accountId}/details`
    window.open(url, '_blank', 'noopener,noreferrer');
  }
}
