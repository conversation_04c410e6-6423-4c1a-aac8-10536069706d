<mat-card class="details-wrapper main">
  <span class="actions">
    <span fxFlex></span>
    <mat-icon (click)="onCloseClick()" data-cy="close-details-btn">close</mat-icon>
  </span>
  <mat-tab-group class="details-tab-group" fitInkBarToContent="false">

    <!-- details -->
    <mat-tab [label]="'STATEMENT_FILES.DETAILS.TABS.LABELS.STATEMENT_FILES_DETAILS' | translate">
      <brad-statement-files-details-info
        [detailedStatementFiles]="accountStatementFiles"></brad-statement-files-details-info>
    </mat-tab>

    <!-- Status Description -->
    <mat-tab [label]="'STATEMENT_FILES.DETAILS.TABS.LABELS.STATUS_DESCRIPTION' | translate">
      <ng-container>
        <p class="status-description">{{ accountStatementFiles?.statusDescription }}</p>
      </ng-container>
    </mat-tab>

  </mat-tab-group>

</mat-card>
