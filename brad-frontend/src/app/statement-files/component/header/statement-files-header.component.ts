import {ChangeDetectorRef, Component, Input, OnDestroy, OnInit, ViewChild, ViewEncapsulation} from '@angular/core';
import {FormControl, FormGroup} from "@angular/forms";
import {BehaviorSubject, Observable, Subject, tap} from "rxjs";
import {CdkConnectedOverlay} from "@angular/cdk/overlay";
import {CsFinActiveFiltersFacade, CsFinApiService, CsFinSidenavService} from "@jumia-cs-fin/common";
import {ActivatedRoute, Router} from "@angular/router";
import {StatementFilesFacade} from "../../facade/statement-files.facade";
import {takeUntil} from "rxjs/operators";
import * as _ from "lodash";
import {Account} from "../../../entities/account/account";
import {AccountFacade} from "../../../accounts/facade/account.facade";
import {StatementFilesFilters} from "../../../entities/statement-files/statement-files-filters";
import {AccountFilters} from "../../../entities/account/account-filters";
import {PageResponse} from "../../../entities/page-response";
import {Currency} from "../../../entities/currency/currency";

@Component({
  selector: 'brad-statement-files-header',
  templateUrl: './statement-files-header.component.html',
  styleUrls: ['./statement-files-header.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class StatementFilesHeaderComponent implements OnInit, OnDestroy {
  @Input() showFilters!: boolean;
  filterTextFormControl!: FormControl;
  queryParams = {};

  form!: FormGroup;

  accountNumberFormControl!: FormControl;
  createdAtToFormControl!: FormControl;
  createdAtFromFormControl!: FormControl;

  isOpen$!: Observable<boolean>;
  isRefreshing = false;
  isInitializing = false;

  accountSearchFormControl = new FormControl();
  accountList: Account[] = [];
  filteredAccountList: Account[] = [];

  private readonly refreshTimeout = 2000;
  private _onDestroy: Subject<void> = new Subject<void>();
  private _isOpen!: BehaviorSubject<boolean>;

  @ViewChild(CdkConnectedOverlay, {static: true})
  private connectedOverlay!: CdkConnectedOverlay;

  constructor(public ref: ChangeDetectorRef,
              public sidenav: CsFinSidenavService,
              private activatedRoute: ActivatedRoute,
              private activeFiltersFacade: CsFinActiveFiltersFacade,
              private router: Router,
              private apiService: CsFinApiService,
              private accountStatementFilesFacade: StatementFilesFacade,
              private accountFacade: AccountFacade) {
  }

  async ngOnInit(): Promise<void> {
    this.isInitializing = true;
    this.initializeOverlay();
    this.initAccountNumberSearch();
    this.subscribeUrlParamsChanges();
    this.subscribeActiveFiltersChange();
    this.subscribeFiltersChange();
  }

  ngOnDestroy(): void {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  private subscribeUrlParamsChanges(): void {
    this.activatedRoute.queryParams
      .pipe(takeUntil(this._onDestroy))
      .subscribe(params => this.initializeFormData(params));
  }

  private subscribeActiveFiltersChange(): void {
    this.activeFiltersFacade.openFiltersOverlay
      .pipe(takeUntil(this._onDestroy))
      .subscribe(value => this._isOpen.next(value));
  }

  private subscribeFiltersChange(): void {
    this.accountStatementFilesFacade.filters$
      .pipe(takeUntil(this._onDestroy))
      .subscribe((filters: StatementFilesFilters) => {
        if (!this.isInitializing) {
          this.updateFormData(filters);
          this.updateMissingUrlFilters(filters);
        }
      });
  }

  private initializeOverlay(): void {
    this._isOpen = new BehaviorSubject<boolean>(false);
    this.connectedOverlay.backdropClass = 'cdk-overlay-transparent-backdrop';
    this.connectedOverlay.panelClass = 'filters-overlay-panel';
    this.connectedOverlay.backdropClick
      .pipe(tap(() => this._isOpen.next(false)))
      .subscribe();
    this.isOpen$ = this._isOpen.asObservable();
  }

  private initAccountNumberSearch(): void {
    this.accountSearchFormControl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe((value: string) => {
        const searchTerm = value.trim();
        if (searchTerm) {
          this.filteredAccountList = this.accountList.filter((account) => {
            return account.accountNumber?.trim().toLowerCase().includes(searchTerm.toLowerCase());
          })
        } else {
          this.filteredAccountList = this.accountList;
        }
      });
  }

  private applyAccountNumberFilter(params: any, filters: StatementFilesFilters): Promise<void> {
    return new Promise<void>(async (resolve) => {
      if(!params.accountID) {
        resolve();
        return;
      }
      await this.loadAccountNumbers();

      this.accountNumberFormControl.setValue(filters.accountId, {emitEvent: false})
     resolve();
    });

  }

    loadAccountNumbers(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      this.accountFacade.getAllAccountNavReferences(this.getFormValues<AccountFilters>())
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (accounts: Account[]) => {
            this.accountList = accounts.filter(account => account.accountNumber !== null);
            this.filteredAccountList = this.accountList;
            resolve();
          }, error: (error) => reject(error)
        });

    });
  }

  clearFilters(): void {
    this.form.reset('', {emitEvent: false});
    this.accountStatementFilesFacade.filtersChanged({});
  }

  submit(input?: HTMLInputElement) {
    this.accountStatementFilesFacade.filtersChanged(this.getFormValues<StatementFilesFilters>());
    const formQueryParams = this.apiService.buildQueryParams(this.getFormValues<StatementFilesFilters>());
    if (_.isEqual(this.queryParams, formQueryParams)) {
      this.isRefreshing = true;
      this.ref.markForCheck();
      setTimeout(() => {
        this.isRefreshing = false;
        this.ref.markForCheck();
      }, this.refreshTimeout);
    } else {
      input?.blur();
      this.router.navigate(['/statement-files'], {queryParams: formQueryParams});
    }
    this.closeOverlay();
  }

  triggerOverlay(): void {
    this._isOpen.next(!this._isOpen.value);
  }

  closeOverlay(): void {
    this._isOpen.next(false);
  }

  private getFormValues<T>(): T {
    const formValues = this.form.value;
    formValues.accountID = formValues.accountID?.id ?? formValues.accountID;
    return formValues as T;
  }

  private initializeFormData(params: any): void {
    this.queryParams = params;
    if (!this.form) {
      this.form = new FormGroup({});
      this.initializeFormControlsAndFilters(params);
    }
  }

  private initializeFormControlsAndFilters(params: any): void {
    const filters: StatementFilesFilters = {};
    this.filterTextFormControl = new FormControl(params.name);
    filters.name = params.name;
    this.accountNumberFormControl = new FormControl(params.accountId);
    filters.accountId = params.accountId;
    this.createdAtToFormControl = new FormControl(params.createdAtTo);
    filters.createdAtTo = params.createdAtTo;

    this.createdAtFromFormControl = new FormControl(params.createdAtFrom);
    filters.createdAtFrom = params.createdAtFrom;

    filters.orderField = params.orderField;
    filters.orderDirection = params.orderDirection;
    filters.page = params.page;
    filters.size = params.size;

    Promise.all([
      this.applyAccountNumberFilter(params, filters)
    ]).then(() => {
      this.accountStatementFilesFacade.filtersChanged(filters);
      this.setFormControlsToForm();
      this.isInitializing = false;
    });
  }

  private updateFormData(params: StatementFilesFilters): void {
    this.filterTextFormControl.setValue(params.name, {emitEvent: false});
    this.accountNumberFormControl.setValue(params.accountId, {emitEvent: false});
    this.createdAtFromFormControl.setValue(params.createdAtFrom, {emitEvent: false});
    this.createdAtToFormControl.setValue(params.createdAtTo, {emitEvent: false});
  }

  private updateMissingUrlFilters(filters: StatementFilesFilters): void {
    const formQueryParams = this.apiService.buildQueryParams(filters);
    if (!_.isEqual(this.queryParams, formQueryParams)) {
      this.router.navigate(['statement-files'], {queryParams: formQueryParams});
    }
  }

  private setFormControlsToForm(): void {
    this.form.addControl(this.accountStatementFilesFacade.filterTextKey, this.filterTextFormControl);
    this.form.addControl(this.accountStatementFilesFacade.accountNumberKey, this.accountNumberFormControl);
    this.form.addControl(this.accountStatementFilesFacade.createdAtFromKey, this.createdAtFromFormControl);
    this.form.addControl(this.accountStatementFilesFacade.createdAtToKey, this.createdAtToFormControl);
  }

  noFiltersSelected() {
    return !!this.form.errors || this.isRefreshing;
  }

}
