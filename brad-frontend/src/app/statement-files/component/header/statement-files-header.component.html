<mat-toolbar id="header-toolbar">
  <button mat-icon-button (click)="sidenav.toggle()" fxHide [fxShow.xs]="true" [fxShow.sm]="true">
    <mat-icon>menu</mat-icon>
  </button>
  <span class="page-title">{{ 'STATEMENT_FILES.TITLE' | translate }}</span>

  <section class="search-bar" [formGroup]="form">
    <!-- filter text (name) -->
    <mat-form-field id="search" appearance="outline" fxShow [fxShow.xs]="false" [fxShow.sm]="false"
                    cdkOverlayOrigin #trigger="cdkOverlayOrigin">
      <mat-icon matPrefix>search</mat-icon>
      <mat-label class="theLabel">{{ 'GENERAL.DETAILS.SEARCH_BAR' | translate }}</mat-label>
      <input type="text" #input matInput [formControl]="filterTextFormControl"
             (keyup.enter)="submit(input)">
      <mat-icon matSuffix id="filters" (click)="triggerOverlay()">filter_list</mat-icon>
    </mat-form-field>

    <button mat-raised-button color="primary" id="apply-search-btn" class="raised-primary-btn" fxShow
            [fxShow.xs]="false"
            [fxShow.sm]="false" [disabled]="noFiltersSelected()" (click)="submit()">
      {{ 'GENERAL.BUTTONS.LABELS.SEARCH' | translate }}
    </button>
  </section>

  <span fxFlex fxHide [fxShow.xs]="true" [fxShow.sm]="true"></span>
  <button mat-icon-button aria-label="Filter accounts" fxHide [fxShow.xs]="true" [fxShow.sm]="true"
          *ngIf="showFilters" id="show-mobile-filters">
    <mat-icon (click)="triggerOverlay()">filter_list</mat-icon>
  </button>
  <ng-template cdkConnectedOverlay class="template"
               [cdkConnectedOverlayHasBackdrop]="true"
               [cdkConnectedOverlayOrigin]="trigger"
               [cdkConnectedOverlayOpen]="(isOpen$ | async) || false">
    <div class="filters-overlay">
      <div class="filters-header">
        <mat-icon fxHide [fxShow.xs]="true" [fxShow.sm]="true" (click)="closeOverlay()">close</mat-icon>
        <p class="filters-title">{{ 'GENERAL.FILTERS.TITLE' | translate }}</p>
        <button fxHide class="flat-white-btn filters-cancel-btn" [disabled]="noFiltersSelected()" [fxShow.xs]="true"
                [fxShow.sm]="true"
                mat-flat-button (click)="clearFilters()" id="clear-btn">
          {{ 'GENERAL.BUTTONS.CLEAR' | translate }}
        </button>
      </div>

      <div class="filters-container">

        <!-- account -->
        <mat-form-field class="filter-field" appearance="outline" id="account-field"
                        data-cy="input-account-entity-filter"
                        (click)="loadAccountNumbers()">
          <mat-label>{{ 'STATEMENT_FILES.FIELDS.ACCOUNT' | translate }}</mat-label>
          <mat-select [formControl]="accountNumberFormControl">
            <ng-container *ngIf="filteredAccountList !== null; else loadingAccountNumber">
              <mat-option>
                <ngx-mat-select-search [formControl]="accountSearchFormControl"
                                       [noEntriesFoundLabel]="'GENERAL.FILTERS.NO_MATCHING_RESULTS_FOUND' | translate"
                                       [placeholderLabel]="'GENERAL.FILTERS.SEARCH' | translate">
                </ngx-mat-select-search>
              </mat-option>
              <mat-option *ngFor="let account of filteredAccountList" [value]="account.id">
                {{ account.accountNumber}}
              </mat-option>
              <button mat-button color="primary" class="clear-selection-btn"
                      (click)="accountNumberFormControl.reset(null)">
                {{ 'GENERAL.FILTERS.ACTIONS.CLEAR_SELECTION' | translate }}
              </button>
            </ng-container>
            <ng-template #loadingAccountNumber>
              <mat-option disabled>
                <div class="filters-loading-container">
                  <span>{{ 'GENERAL.FILTERS.LOADING' | translate }}</span>
                  <mat-spinner diameter="20"></mat-spinner>
                </div>
              </mat-option>
            </ng-template>
          </mat-select>
        </mat-form-field>


        <!-- created at -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-created-at-filter">
          <mat-label>{{ 'GENERAL.FIELDS.CREATED_AT' | translate }}</mat-label>
          <mat-date-range-input [rangePicker]="picker">
            <input matStartDate [formControl]="createdAtFromFormControl" placeholder="Start date">
            <input matEndDate [formControl]="createdAtToFormControl" placeholder="End date">
          </mat-date-range-input>
          <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
          <mat-date-range-picker #picker></mat-date-range-picker>
        </mat-form-field>

      </div>

      <div class="filters-actions">
        <button class="flat-white-btn filters-cancel-btn" [disabled]="noFiltersSelected()" mat-flat-button fxShow
                (click)="clearFilters()" [fxShow.xs]="false" [fxShow.sm]="false">
          {{ 'GENERAL.FILTERS.ACTIONS.CLEAR' | translate }}
        </button>
        <button class="raised-primary-btn filters-scan-btn" [disabled]="noFiltersSelected()" mat-raised-button
                color="primary"
                (click)="submit()">
          {{ 'GENERAL.FILTERS.ACTIONS.APPLY' | translate }}
        </button>
      </div>

    </div>

  </ng-template>

</mat-toolbar>
