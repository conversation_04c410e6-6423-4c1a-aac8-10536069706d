import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import {NavigationComponent} from "../navigation/navigation.component";
import {csFinAuthCanActivateGuard} from "@jumia-cs-fin/common";
import {bradPermissions} from "../auth/constants/permission.constants";
import {bradAuthTarget} from "../auth/constants/auth.constants";
import {StatementFilesComponent} from "./statement-files.component";
import {StatementFilesDetailsComponent} from "./component/details/statement-files-details.component";
import {csFinAuthorizationOnTargetService} from "../app-routing.module";


const routes: Routes = [
  {
    path: '',
    component: NavigationComponent,
    children: [
      {
        path: '',
        canActivate: [csFinAuthCanActivateGuard],
        data: {
          auth: {
            permissions: bradPermissions.BRAD_STATEMENT_FILES_ACCESS,
            targets: bradAuthTarget,
            authTypeService: csFinAuthorizationOnTargetService,
          }
        },
        component: StatementFilesComponent,
        children: [
          {
            path: ':accountStatementFileID',
            component: StatementFilesDetailsComponent
          }
        ]
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class StatementFilesRoutingModule { }
