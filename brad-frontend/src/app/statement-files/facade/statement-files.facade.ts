import {Injectable} from "@angular/core";
import {BehaviorSubject, Observable, of} from "rxjs";
import {getPropertyKey} from "../../shared/service/ts-utils.service";
import {CsFinActiveFilterChip} from "@jumia-cs-fin/common";
import {PageResponse} from "../../entities/page-response";
import {StatementFiles} from "../../entities/statement-files/statement-files";
import {StatementFilesFilters} from "../../entities/statement-files/statement-files-filters";
import {StatementFilesApiService} from "../../api/service/statement-files.service";

@Injectable({providedIn: 'root'})
export class StatementFilesFacade {

  public fullscreenChangeBehaviorSubject = new BehaviorSubject<boolean>(true);
  public detailsCloseBehaviorSubject = new BehaviorSubject<boolean>(false);
  public selectedStatementFilesChangeBehaviorSubject = new BehaviorSubject<number>(-1);

  readonly filterTextKey = getPropertyKey<StatementFilesFilters>(p => p.name);
  readonly accountNumberKey = getPropertyKey<StatementFilesFilters>(p => p.accountId);
  readonly createdAtToKey = getPropertyKey<StatementFilesFilters>(p => p.createdAtTo);
  readonly createdAtFromKey = getPropertyKey<StatementFilesFilters>(p => p.createdAtFrom);


  private readonly activeFiltersConfigMap = new Map<string, any>([
    [
      this.filterTextKey,
      (name: string) => {
        return {labelKey: 'GENERAL.FIELDS.FILTER_TEXT', displayText: name}
      }
    ],
    [
      this.accountNumberKey,
      (accountId: number) => {
        return {
          labelKey: 'STATEMENT_FILES.FIELDS.ACCOUNT',
          displayText: accountId
        }
      }
    ],
    [
      this.createdAtFromKey,
      (createdAtFrom: string) => {
        return {labelKey: 'GENERAL.FIELDS.CREATED_AT', displayText: createdAtFrom}
      }
    ]

  ]);

  private filtersBehaviorSubject = new BehaviorSubject<StatementFilesFilters>({});
  private activeFilterChips = new Map<string, CsFinActiveFilterChip>();
  public filters$: Observable<StatementFilesFilters> = this.filtersBehaviorSubject.asObservable();
  public activeFiltersChips$: Observable<Map<string, CsFinActiveFilterChip>> = of(this.activeFilterChips);

  constructor(public accountStatementFilesApiService: StatementFilesApiService) {
  }

  filtersChanged(filters: StatementFilesFilters) {
    if (!filters.createdAtFrom) {
      filters.createdAtTo = undefined;
      filters.createdAtFrom = undefined;
    }

    this.filtersBehaviorSubject.next(filters);
    this.updateActiveFilterChips(filters);
  }

  private updateActiveFilterChips(filters: StatementFilesFilters): void {
    if (!filters) {
      return;
    }
    this.activeFilterChips.clear();

    if (filters.name) {
      this.activeFilterChips.set(this.filterTextKey, this.activeFiltersConfigMap.get(this.filterTextKey)(filters.name));
    }
    if (filters.accountId) {
      this.activeFilterChips.set(this.accountNumberKey, this.activeFiltersConfigMap.get(this.accountNumberKey)(filters.accountId));
    }
    if (filters.createdAtFrom) {
      let dateToDisplay = new Date(filters.createdAtFrom!).toDateString();
      if (filters.createdAtTo) {
        dateToDisplay += " - " + new Date(filters.createdAtTo!).toDateString();
      }
      this.activeFilterChips.set(this.createdAtFromKey, this.activeFiltersConfigMap.get(this.createdAtFromKey)(dateToDisplay));
    }
  }

  getAll(filters?: StatementFilesFilters): Observable<PageResponse<StatementFiles>> {
    return this.accountStatementFilesApiService.getAll(filters);
  }

  getById(id: number): Observable<StatementFiles> {
    return this.accountStatementFilesApiService.getById(id);
  }

  scan(): Observable<void> {
    return this.accountStatementFilesApiService.scan();
  }

  reprocess(filters?: StatementFilesFilters): Observable<void> {
    return this.accountStatementFilesApiService.reprocess(filters);
  }

  downloadStatementFiles(id: number) {
    return this.accountStatementFilesApiService.downloadStatementFiles(id);
  }

}
