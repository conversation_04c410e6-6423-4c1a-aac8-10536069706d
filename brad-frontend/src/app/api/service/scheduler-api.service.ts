import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {ApiHelperService} from './api-helper.service';
import {SchedulerJob} from "../../entities/schedulerJob";
import {CsFinApiService} from "@jumia-cs-fin/common";


@Injectable({
  providedIn: 'root'
})
export class SchedulerApiService {

  private readonly baseUrl = 'jobs';


  constructor(
    private httpClient: HttpClient,
    private apiService: CsFinApiService) { }


  fetchJobs(): Observable<SchedulerJob[]> {
    const url = ApiHelperService.buildApiUrl(this.baseUrl);
    return this.apiService.get<any>(url);
  }

  fetchJob(job: string): Observable<SchedulerJob> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/${job}`);
    return this.apiService.get<any>(url);
  }

  update(job: SchedulerJob): Observable<SchedulerJob> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/${job.jobName}`);
    return this.httpClient.put<SchedulerJob>(url, job);
  }

  forceJobRun(job: string): Observable<void> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/${job}/force-run`);
    return this.httpClient.post<any>(url, null);
  }

  toggleJobState(job: string): Observable<void> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/${job}/toggle-state`);
    return this.httpClient.post<any>(url, null);
  }

  fetchBaleJobName(): Observable<SchedulerJob> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/bale-job`);
    return this.apiService.get<any>(url);
  }


}
