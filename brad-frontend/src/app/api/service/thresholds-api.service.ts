import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {ApiHelperService} from "./api-helper.service";
import {CsFinApiService} from "@jumia-cs-fin/common";
import {Threshold} from "../../entities/thresholds/threshold";
import {ThresholdRequest} from "../../entities/thresholds/threshold-request";
import {HttpClient} from "@angular/common/http";
import {PageResponse} from "../../entities/page-response";
import {ThresholdFilters} from "../../entities/thresholds/threshold-filters";


@Injectable({providedIn: 'root'})
export class ThresholdsApiService {

  private readonly baseUrl = "thresholds";

  constructor(private httpClient: HttpClient,
              private apiService: CsFinApiService) {
  }

  getAll(filters?: ThresholdFilters): Observable<PageResponse<Threshold>> {
    const url = ApiHelperService.buildApiUrl(this.baseUrl);
    return this.apiService.get<PageResponse<Threshold>>(url, filters);
  }

  create(threshold: ThresholdRequest): Observable<Threshold> {
    const url = ApiHelperService.buildApiUrl(this.baseUrl);
    return this.httpClient.post<Threshold>(url, threshold);
  }

  update(id:number, threshold: ThresholdRequest): Observable<Threshold> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/${id}`);
    return this.httpClient.put<Threshold>(url, threshold);
  }

  delete(id: number): Observable<void> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/${id}`);
    return this.httpClient.delete<void>(url);
  }

  getById(id: number): Observable<Threshold> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/${id}`);
    return this.apiService.get<Threshold>(url);
  }

}
