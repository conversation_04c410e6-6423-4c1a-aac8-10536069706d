import {Injectable} from "@angular/core";
import {
  CsFinGroupElement,
  CsFinGroupEntity,
  CsFinGroupFacade,
  CsFinRowType,
  CsFinTableGroup,
  CsFinTableGroupFilters,
  CsFinTablePaginator
} from "../../entities/cs-fin-table-group";
import {PageResponse} from "../../entities/page-response";
import {Observable} from "rxjs";
import {MatTableDataSource} from "@angular/material/table";
import {map} from "rxjs/operators";
import {CsFinGroup} from "../../entities/cs-fin-group";
import {CsFinGroupedEntityInfo} from "../../entities/cs-fin-grouped-entity";

@Injectable({
  providedIn: 'root'
})
export class CsFinGroupService {

  loadGroups<T,F>(groupByColumns: string[],
                  dataSource:MatTableDataSource<CsFinGroupElement<T, any>>,
                  fetchAllEntitiesAmount: boolean,
                  facade: CsFinGroupFacade<T, F>,
                  defaultFilters: F): Observable<CsFinGroupElement<T, any>[]> {
    return facade.getEntityGroups(groupByColumns,defaultFilters).pipe(
        map((result: CsFinGroup) => {
          let entityGroupFilters: F[] = this.transformToFilters(result, facade);
          let groupFilters: CsFinTableGroupFilters<F>[] = [];
          let groups: CsFinTableGroup<F>[] = [];
          result.result.forEach((groupName: string[]) => {
            let newGroup: CsFinTableGroup<F> = {
              groupName: groupName.join(", "),
              groupFields: groupName,
              rowType: CsFinRowType.GROUP,
              expanded: false,
              groupFilters: null as any,
              totalRecords: 0
            };
            groups.push(newGroup);
          });
          for (let i = 0; i < entityGroupFilters.length; i++) {
            let groupFilter: CsFinTableGroupFilters<F> = {
              filters: {...defaultFilters, ...entityGroupFilters[i]},
              groupName: groups[i].groupName
            };
            groupFilters.push(groupFilter);
          }

          groupFilters.forEach((groupFilter: CsFinTableGroupFilters<F>) => {
            let group = groups.find((group: CsFinTableGroup<F>) => group.groupName === groupFilter.groupName);
            group!.groupFilters = groupFilter;
          });

          return dataSource.data = [...groups];

        })
    );
  }

  loadGroupEntities<T, F>(group: CsFinTableGroup<F>, facade: CsFinGroupFacade<T, F>, defaultFilters: F):
    Observable<CsFinGroupElement<T, any>[]> {

    return facade.getAll({...group.groupFilters.filters, defaultFilters}).pipe(
      map((result: PageResponse<T>) => {
        const elements: CsFinGroupEntity<T>[] = result.results.map((entity: T) => ({
          ...entity,
          groupName: group.groupName,
          rowType: CsFinRowType.ENTITY,
          expanded: false,
        }) as CsFinGroupEntity<T>);

        const paginator: CsFinTablePaginator = {
          rowType: CsFinRowType.PAGINATOR,
          totalRecords: group.totalRecords,
          pageSize: result.size,
          pageIndex: result.page - 1,
          groupName: group.groupName
        };

        return [group, ...elements, paginator];
      })
    );
  }


  transformToFilters<T, F>(group: CsFinGroup, facade: CsFinGroupFacade<T, any>): F[] {
    let filters: F[] = [];
    for (let i = 0; i < group.result.length; i++) {
      let filter: F = {} as F;
      for (let j = 0; j < group.groupings.length; j++ ) {
        facade.convertToFilters(group.groupings[j], group.result[i][j], filter);
      }
      filters.push(filter);
    }
    return filters;
  }

  loadAllGroupInfo<T, F>(groups: CsFinTableGroup<F>[], facade: CsFinGroupFacade<T, F>):
    Observable<CsFinTableGroup<any>[]> {

    let filters: F[] = groups.map((group: CsFinTableGroup<F>) => group.groupFilters.filters);
    return facade.getAllGroupedInfo({
      filterList: filters
    }).pipe(map((results: CsFinGroupedEntityInfo[]) => {
      let updatedGroups: CsFinTableGroup<F>[] = [];
      results.forEach((result: CsFinGroupedEntityInfo, index: number) => {
        let group = groups[index];
        group.totalRecords = result.totalQuantity;
        group.totalAmount = result.totalBalance;
        group.isStatusShared = result.isStatusShared;
        group.isReconciliationShared = result.isReconciliationShared;
        group.allIds = result.allIds;
        updatedGroups.push(group);
      });
      return updatedGroups;
    }));
  }

  isCsFinGroupEntity<T>(item: any): item is CsFinGroupEntity<T> {
    return (item as CsFinGroupEntity<T>).groupName != undefined;
  }

  isCsFinTableGroup<F>(item: any): item is CsFinTableGroup<F> {
    return ((item as CsFinTableGroup<F>).rowType == CsFinRowType.GROUP)
  }

  onGroupPageChange<T, F>(event: any, paginator: CsFinTablePaginator,
                          dataSource: MatTableDataSource<CsFinGroupElement<T, F>>,
                          facade: { getAll: (filters: F) => Observable<PageResponse<T>> }):
    Observable<CsFinGroupElement<T, any>[]> {

    paginator.pageIndex = event.pageIndex;
    paginator.pageSize = event.pageSize;
    paginator.totalRecords = event.length;

    let groupElement = dataSource.data.filter((row: any) =>
      this.isCsFinGroupEntity(row) && row.groupName === paginator.groupName && row.rowType === CsFinRowType.GROUP
    )[0] as CsFinTableGroup<F>;

    let filtersWithPagination: F = {
      ...groupElement.groupFilters.filters,
      page: event.pageIndex + 1,
      size: event.pageSize
    };

    groupElement.groupFilters.filters = filtersWithPagination;

    return facade.getAll(filtersWithPagination).pipe(
      map((result: PageResponse<T>) => {
        let elements: CsFinGroupEntity<T>[] = result.results.map((item: T) => ({
          ...item,
          groupName: groupElement.groupName,
          rowType: CsFinRowType.ENTITY
        } as CsFinGroupEntity<T>));
        return this.updateGroupDataSourceData(groupElement.groupName, elements, dataSource.data);
      })
    );
  }

  updateGroupDataSourceData<T>(groupName: string, newElements: CsFinGroupEntity<T>[], sourceData: any[]) {
    let updatedData: any[] = [];
    let groupHeaderFound = false;

    sourceData.forEach((row: any) => {
      if (this.isCsFinGroupEntity(row) && row.groupName === groupName && row.rowType === CsFinRowType.GROUP) {
        updatedData.push(row);
        groupHeaderFound = true;
      } else if (this.isCsFinGroupEntity(row) && row.groupName === groupName && row.rowType === CsFinRowType.ENTITY) {
      } else {
        updatedData.push(row);
      }
    });

    if (groupHeaderFound) {
      updatedData = [
        ...updatedData.slice(0, updatedData.findIndex(item => this.isCsFinGroupEntity(item) &&
          item.groupName === groupName && item.rowType === CsFinRowType.GROUP) + 1),
        ...newElements,
        ...updatedData.slice(updatedData.findIndex(item => this.isCsFinGroupEntity(item) &&
          item.groupName === groupName && item.rowType === CsFinRowType.GROUP) + 1)
      ];
    }

    return updatedData;
  }

  toggleDataExpand<T,F>(data: CsFinGroupElement<T, F>[]): CsFinGroupElement<T, F>[] {
    let result: CsFinGroupElement<T, F>[] = [];
    let expandedGroups: Set<string> = new Set();

    data.forEach((item: CsFinGroupElement<T, F>) => {
      if (this.isCsFinTableGroup(item)) {
        result.push(item);
        if (item.expanded) {
          expandedGroups.add(item.groupName);
        }
      } else if (this.isCsFinGroupEntity(item)) {
        if (expandedGroups.has(item.groupName!)) {
          result.push(item);
        }
      }
    });

    return result;
  }

  expandPreviousGroups<T,F>(data: CsFinGroupElement<T, F>[], groupName: string[]): CsFinGroupElement<T, F>[] {
    let result: CsFinGroupElement<T, F>[] = [];
    let expandedGroups: Set<string> = new Set();

    data.forEach((item: CsFinGroupElement<T, F>) => {
      if (this.isCsFinTableGroup(item)) {
        result.push(item);
        if (groupName.includes(item.groupName)) {
          item.expanded = true;
          expandedGroups.add(item.groupName);
        }
      } else if (this.isCsFinGroupEntity(item)) {
        if (expandedGroups.has(item.groupName!)) {
          result.push(item);
        }
      }
    });

    return result;

  }

  isGroup(index: number, item: any): boolean {
    return item && item.rowType !== undefined && item.rowType == CsFinRowType.GROUP ;
  }

  isPaginator(index: number, item: any): boolean {
    return item && item.rowType !== undefined && item.rowType == CsFinRowType.PAGINATOR ;
  }

  notGroupOrPaginator(index: number, item: any): boolean {
    return !(item && item.rowType !== undefined && item.rowType == CsFinRowType.GROUP) &&
      !(item && item.rowType !== undefined && item.rowType == CsFinRowType.PAGINATOR);
  }
}
