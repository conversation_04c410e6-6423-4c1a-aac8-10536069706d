import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {ApiHelperService} from './api-helper.service';
import {PageResponse} from "../../entities/page-response";
import {Bale} from "../../entities/bale/bale";
import {BaleFilters} from "../../entities/bale/bale-filters";
import {CsFinApiService} from "@jumia-cs-fin/common";
import {CsFinGroup} from "../../entities/cs-fin-group";
import {CsFinGroupedEntityInfo} from "../../entities/cs-fin-grouped-entity";
import {CsFinFilterList} from "../../entities/cs-fin-table-group";


@Injectable({
  providedIn: 'root'
})
export class BaleApiService {

  private readonly baseUrl = 'bales';

  constructor(private httpClient: HttpClient,
              private apiService: CsFinApiService) { }


  getAll(filters?: BaleFilters): Observable<PageResponse<Bale>> {
    const url = ApiHelperService.buildApiUrl(this.baseUrl);
    return this.apiService.get<PageResponse<Bale>>(url,filters);
  }

  getById(id: number): Observable<Bale> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/${id}`);
    return this.apiService.get<Bale>(url);
  }

  getBaleGroups(filters:string[], defaultFilters?:BaleFilters): Observable<CsFinGroup> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/grouped`);
    return this.apiService.get<CsFinGroup>(url, {fields:filters, ...defaultFilters});
  }

  getBaleGroupableFields(): Observable<string[]> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/groupable-fields`);
    return this.apiService.get<string[]>(url);
  }

  syncByBaleViewIds(baleViewIds: number[]): Observable<void> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/sync`);
    return this.apiService.get<void>(url, {baleViewIds: baleViewIds});
  }

  getAllGroupedInfo(filters:CsFinFilterList<BaleFilters>): Observable<CsFinGroupedEntityInfo[]> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/group-info`);
    return this.httpClient.post<CsFinGroupedEntityInfo[]>(url, filters);
  }

}
