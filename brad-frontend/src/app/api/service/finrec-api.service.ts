import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {ApiHelperService} from './api-helper.service';
import {Finrec} from '../../entities/finrec/finrec';
import {FinrecFilters} from "../../entities/finrec/finrec-filters";
import {ActivatedRoute, Router} from "@angular/router";
import {PageResponse} from "../../entities/page-response";
import {CsFinApiService} from "@jumia-cs-fin/common";


@Injectable({
  providedIn: 'root'
})
export class FinrecApiService {


  private readonly baseUrl = 'finrec';


  constructor(private httpClient: HttpClient,
              private route: ActivatedRoute,
              private router: Router,
              private apiService: CsFinApiService) { }


  getAll(filters?: FinrecFilters): Observable<PageResponse<Finrec>> {
    const url = ApiHelperService.buildApiUrl(this.baseUrl);
    return this.apiService.get<PageResponse<Finrec>>(url,filters);
  }

  getById(id: number): Observable<Finrec> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/${id}`);
    return this.apiService.get<Finrec>(url);
  }


}
