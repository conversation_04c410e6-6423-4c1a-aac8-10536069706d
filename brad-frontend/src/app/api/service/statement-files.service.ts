import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {ApiHelperService} from './api-helper.service';
import {PageResponse} from "../../entities/page-response";
import {CsFinApiService} from "@jumia-cs-fin/common";
import {StatementFilesFilters} from "../../entities/statement-files/statement-files-filters";
import {StatementFiles} from "../../entities/statement-files/statement-files";
import {HttpClient} from "@angular/common/http";

@Injectable({
  providedIn: 'root'
})
export class StatementFilesApiService {

  private readonly baseUrl = 'account-statement-files';

  constructor(private httpClient: HttpClient,
      private apiService: CsFinApiService){
  }

  getAll(filters?: StatementFilesFilters): Observable<PageResponse<StatementFiles>> {
    const url = ApiHelperService.buildApiUrl(this.baseUrl);
    return this.apiService.get<PageResponse<StatementFiles>>(url, filters);
  }

  getById(id: number): Observable<StatementFiles> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/${id}`);
    return this.apiService.get<StatementFiles>(url);
  }

  scan(): Observable<void> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/scan`);
    return this.apiService.patch<void>(url);
  }

  reprocess(filters?: StatementFilesFilters): Observable<void> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/reprocess`);
    return this.apiService.patch<void>(url, filters);
  }

  downloadStatementFiles(id: number) {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/${id}/download`);
    return this.httpClient.get(url, { responseType: 'text' });
  }
}
