import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {ApiHelperService} from "./api-helper.service";
import {CsFinApiService} from "@jumia-cs-fin/common";
import {Country} from "../../entities/account/country";


@Injectable({providedIn: 'root'})
export class CountryApiService {

  private readonly baseUrl = "countries";

  constructor(private apiService: CsFinApiService) {
  }

  getAll(): Observable<Country[]> {
    const url = ApiHelperService.buildApiUrl(this.baseUrl);
    return this.apiService.get<Country[]>(url);
  }

}
