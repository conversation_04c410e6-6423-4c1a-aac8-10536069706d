import {Injectable} from '@angular/core';
import {datetimeConsts} from '../../constants/datetime.constants';
import * as moment from 'moment';
import {max, min, Moment} from 'moment';

@Injectable({providedIn: 'root'})
export class DatetimeService {

  static addDaysToDate(date: Date, nDays: number): Date | null {
    if (!date) {
      return null;
    }
    return new Date(date.valueOf() + nDays * datetimeConsts.daysToMsFactor);
  }

  static subDaysToDate(date: Date, nDays: number): Date | null {
    if (!date) {
      return null;
    }
    return new Date(date.valueOf() - nDays * datetimeConsts.daysToMsFactor);
  }

  static subHoursToDate(date: Date, nHours: number): Date | null{
    if (!date) {
      return null;
    }
    return new Date(date.valueOf() - nHours * datetimeConsts.hoursToMsFactor);
  }

  static sumDaysToDate(date: Date, nDays: number): Date | null{
    if (!date) {
      return null;
    }
    return new Date(date.valueOf() + nDays * datetimeConsts.daysToMsFactor);
  }

  static convertDateToStr(date: Date): string | null {
    if (!date) {
      return null;
    }
    return moment(date).format(datetimeConsts.format);
  }

  static convertStrToDate(dateStr: string): Date | null{
    if (dateStr == null) {
      return null;
    }
    return moment(dateStr, datetimeConsts.format).toDate();
  }

  static minDate(date1: Date, date2: Date): Date | null {
    if (!date1 || !date2) {
      return null;
    }
    return min(moment(date1), moment(date2)).toDate();
  }

  static maxDate(date1: Date, date2: Date): Date | null {
    if (!date1 || !date2) {
      return null;
    }
    return max(moment(date1), moment(date2)).toDate();
  }

  static convertDateToUtc(date: string): Date {
    return moment(date).utc().toDate();
  }

  static convertDateToUtcStr(date: Date) {
    return moment(date).utc().format(datetimeConsts.format);
  }

  static lastMinuteOfTheDay(date: Date) {
    date.setHours(23);
    date.setMinutes(59);
    date.setSeconds(59);

    return date;
  }

  static firstMinuteOfTheDay(date: Date) {
    date.setHours(0);
    date.setMinutes(0);
    date.setSeconds(0);

    return date;
  }

  static getDaysAgo(days: number): Date {
    let date = new Date();
    date.setDate(date.getDate() - days);
    return date;
  }

  static getHoursAgo(hours: number): Date {
    let date = new Date();
    date.setHours(date.getHours() - hours);
    return date
  }

  static getMinutesAgo(minutes: number): Date {
    let date = new Date();
    date.setMinutes(date.getMinutes() - minutes);
    return date
  }

  static momentStartOfDay(date: Date | Moment): Date {
    return moment(date).startOf('day').toDate();
  }

  static momentEndOfDay(date: Date | Moment): Date {
    return moment(date).endOf('day').toDate();
  }

  static formatIso8601Str(date: Date | Moment | string): string | undefined {
    return (date && true) ? moment(date).format(datetimeConsts.iso8601Format) : undefined;
  }

  static formatDateUtcStr(datetime: Date | string): string {
    return moment(datetime).utc().format(datetimeConsts.dateFormat);
  }
}
