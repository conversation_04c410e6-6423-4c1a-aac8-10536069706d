import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {ApiHelperService} from './api-helper.service';
import {Statement} from '../../entities/statement/statement';
import {CsFinApiService} from "@jumia-cs-fin/common";
import {ActivatedRoute, Router} from '@angular/router';
import {PageResponse} from "../../entities/page-response";
import {StatementFilters} from "../../entities/statement/statement-filters";
import {StatementWithFileRequest} from "../../entities/statement/statement-with-file-request";
import {map} from "rxjs/operators";
import {saveAs} from "file-saver";
import {StatementWithTransactionRequest} from "../../entities/statement/statement-with-transaction-request";

@Injectable({
  providedIn: 'root'
})
export class StatementsApiService {

  private readonly baseUrl = 'account-statements';


  constructor(private httpClient: HttpClient,
              private route: ActivatedRoute,
              private router: Router,
              private apiService: CsFinApiService) { }


  createWithFile(statement: StatementWithFileRequest): Observable<Statement> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/upload-file`);
    return this.httpClient.post<Statement>(url, statement);
  }

  create(statement: StatementWithTransactionRequest): Observable<Statement> {
    const url = ApiHelperService.buildApiUrl(this.baseUrl);
    return this.httpClient.post<Statement>(url, statement);
  }

  getAll(filters?: StatementFilters): Observable<PageResponse<Statement>> {
    const url = ApiHelperService.buildApiUrl(this.baseUrl);
    return this.apiService.get<PageResponse<Statement>>(url,filters);
  }

  getAllOrdered(filters?: StatementFilters): Observable<Statement[]> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/troubleshooting-ordered`);
    return this.apiService.get<Array<Statement>>(url,filters);
  }

  getStatusTypes(): Observable<string[]> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/status-types`);
    return this.apiService.get<string[]>(url);
  }

  getErrorTypes(): Observable<string[]> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/error-types`);
    return this.apiService.get<string[]>(url);
  }

  getDirections(): Observable<string[]> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/directions`);
    return this.apiService.get<string[]>(url);
  }

  download(filters?: StatementFilters)  {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/download`);
    return this.httpClient.get<ArrayBuffer>(url, {
      headers: this.apiService.headers(),
      params: this.apiService.buildQueryParams(filters),
      responseType: 'arraybuffer' as 'json'
    }).pipe(map((response: ArrayBuffer) => {
      const blob = new Blob([response], {type: 'text/csv'});
      saveAs(blob, 'BRAD_Statements_'+new Date(Date.now()).toUTCString()+'.csv');
    }));
  }

  retryStatement(statementId: number): Observable<string> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/retry`);
    return this.httpClient.get(url, {responseType: 'text', params: {statementId: statementId.toString()}});
  }

  discardStatement(statementId: number): Observable<void> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/discard/${statementId}`);
    return this.apiService.delete<void>(url);
  }

  discardLastImportedStatement(statementId: number): Observable<void> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/discard/last-imported/${statementId}`);
    return this.apiService.delete<void>(url);
  }

  getLastStatement(accountId: number): Observable<Statement> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/last/${accountId}`);
    return this.apiService.get<Statement>(url);
  }

  getFirstError(accountId: number): Observable<Statement> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/first-error/${accountId}`);
    return this.apiService.get<Statement>(url);
  }

  getLastImportedStatement(accountId: number): Observable<Statement> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/last-imported/${accountId}`);
    return this.apiService.get<Statement>(url);
  }
}
