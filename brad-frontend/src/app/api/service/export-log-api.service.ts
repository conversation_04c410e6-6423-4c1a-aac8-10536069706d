import {Injectable} from "@angular/core";
import {HttpClient} from "@angular/common/http";
import {ActivatedRoute, Router} from "@angular/router";
import {CsFinApiService} from "@jumia-cs-fin/common";
import {Observable} from "rxjs";
import {PageResponse} from "../../entities/page-response";
import {ApiHelperService} from "./api-helper.service";
import {ExportLogFilters} from "../../entities/export-log/export-log-filters";
import {ExportLog} from "../../entities/export-log/export-log";
import {FileDownload} from "../../entities/export-log/file-download";
import {EnumCode} from "../../entities/enum-code";

@Injectable({
  providedIn: 'root'
})
export class ExportLogApiService {

  private readonly baseUrl = 'export-logs';

  constructor(private httpClient: HttpClient,
              private route: ActivatedRoute,
              private router: Router,
              private apiService: CsFinApiService) { }


  getAll(filters?: ExportLogFilters): Observable<PageResponse<ExportLog>> {
    const url = ApiHelperService.buildApiUrl(this.baseUrl);
    return this.apiService.get<PageResponse<ExportLog>>(url,filters);
  }

  download(id: number): Observable<FileDownload> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/${id}/download`);
    return this.apiService.get<FileDownload>(url, {responseType: 'text'});
  }

  getById(id: number): Observable<ExportLog> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/${id}`);
    return this.apiService.get<ExportLog>(url);
  }

  getLogTypes(): Observable<EnumCode[]> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/types`);
    return this.apiService.get<EnumCode[]>(url);
  }

  getLogStatus(): Observable<EnumCode[]> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/statuses`);
    return this.apiService.get<EnumCode[]>(url);
  }

}
