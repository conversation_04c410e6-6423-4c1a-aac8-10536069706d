import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {ApiHelperService} from "./api-helper.service";
import {CsFinApiService} from "@jumia-cs-fin/common";


@Injectable({providedIn: 'root'})
export class IsReconciledStatusApiService {

  private readonly baseUrl = "reconciliation";

  constructor(private apiService: CsFinApiService) {
  }

  getAll(): Observable<String[]> {
    const url = ApiHelperService.buildApiUrl(this.baseUrl+"/isReconciledStatuses");
    return this.apiService.get<String[]>(url);
  }
}
