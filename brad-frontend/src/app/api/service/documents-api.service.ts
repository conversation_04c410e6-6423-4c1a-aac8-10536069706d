import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {ApiHelperService} from './api-helper.service';
import {Document} from '../../entities/document/document';
import {ActivatedRoute, Router} from '@angular/router';
import {PageResponse} from "../../entities/page-response";
import {DocumentFilters} from "../../entities/document/document-filters";
import {CsFinApiService} from "@jumia-cs-fin/common";

@Injectable({
  providedIn: 'root'
})
export class DocumentsApiService {

  private readonly baseUrl = 'documents';


  constructor(private httpClient: HttpClient,
              private route: ActivatedRoute,
              private router: Router,
              private apiService: CsFinApiService) { }



  getAll(filters?: DocumentFilters): Observable<PageResponse<Document>> {
    const url = ApiHelperService.buildApiUrl(this.baseUrl);
    return this.apiService.get<PageResponse<Document>>(url,filters);
  }

  getAllFromAccount(id: number): Observable<Array<Document>> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/account/${id}`);
    return this.httpClient.get<Array<Document>>(url);
  }

  getById(id: number): Observable<Document> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/${id}`);
    return this.httpClient.get<Document>(url);
  }

  getURLByID(id: number)  {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/url/${id}`);
    return this.httpClient.get<Document>(url);
  }

  create(field: Document): Observable<Document> {
    const url = ApiHelperService.buildApiUrl(this.baseUrl);
    return this.httpClient.post<Document>(url, field);
  }

  update(id: number, field: Document): Observable<Document> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/${id}`);
    return this.httpClient.put<Document>(url, field);
  }

  delete(id: number): Observable<void> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/${id}`);
    return this.httpClient.delete<void>(url);
  }
}
