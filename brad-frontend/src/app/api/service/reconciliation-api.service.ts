import {Injectable} from "@angular/core";
import {Observable} from "rxjs";
import {ApiHelperService} from "./api-helper.service";
import {SelectedIds} from "../../entities/reconciliation/selectedIds";
import {HttpClient} from "@angular/common/http";
import {Reconciliation} from "../../entities/reconciliation/reconciliation";
import {ReconciliationFilters} from "../../entities/reconciliation/reconciliation-filters";
import {PageResponse} from "../../entities/page-response";
import {CsFinApiService} from "@jumia-cs-fin/common";
import {SelectedAmountDifference} from "../../entities/reconciliation/selectedAmountDifference";

@Injectable({
  providedIn: 'root'
})
export class ReconciliationApiService {

  private readonly baseUrl = 'reconciliation';
  constructor( private apiService: CsFinApiService,
               private httpClient: HttpClient) {
  }


  getAmountDifference(ids: SelectedIds): Observable<SelectedAmountDifference> {
    const url = ApiHelperService.buildApiUrl(this.baseUrl+'/selectedAmountDifference');
    return this.httpClient.post<SelectedAmountDifference>(url, ids);
  }

  createReconciliation(reconciliation: SelectedIds): Observable<Reconciliation> {
    const url = ApiHelperService.buildApiUrl(this.baseUrl);
    return this.httpClient.post<Reconciliation>(url, reconciliation);
  }

  getAllReconciliations(filters?: ReconciliationFilters): Observable<PageResponse<Reconciliation>>{
    const url = ApiHelperService.buildApiUrl(this.baseUrl);
    return this.apiService.get<PageResponse<Reconciliation>>(url,filters);
  }

  getReconciliation(id: Number): Observable<Reconciliation> {
    const url = ApiHelperService.buildApiUrl(this.baseUrl+'/'+id);
    return this.httpClient.get<Reconciliation>(url);
  }

  unmatchReconciliation(selectedIds: SelectedIds): Observable<void> {
    const url = ApiHelperService.buildApiUrl(this.baseUrl+'/unmatch-by-ids');
    return this.httpClient.post<void>(url, selectedIds);
  }

  approveReconciliation(selectedIds: SelectedIds): Observable<void> {
    const url = ApiHelperService.buildApiUrl(this.baseUrl+'/approve-by-ids');
    return this.httpClient.post<void>(url, selectedIds);
  }

  fetchReconciliationByBaleId(id: Number): Observable<Reconciliation> {
    const url = ApiHelperService.buildApiUrl(this.baseUrl+'/reconciliation-by-bale-id/'+id);
    return this.httpClient.get<Reconciliation>(url);
  }

  fetchReconciliationByTransactionId(id: Number): Observable<Reconciliation> {
    const url = ApiHelperService.buildApiUrl(this.baseUrl+'/reconciliation-by-transaction-id/'+id);
    return this.httpClient.get<Reconciliation>(url);
  }

}
