import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {ApiHelperService} from './api-helper.service';
import {Account} from '../../entities/account/account';
import {ActivatedRoute, Router} from '@angular/router';
import {map} from 'rxjs/operators';
import {AuditFilters} from "../../entities/audit/audit-filters";
import {AuditResponse} from "../../entities/audit/audit-response";
import {PageResponse} from "../../entities/page-response";
import {AccountFilters} from "../../entities/account/account-filters";
import {saveAs} from 'file-saver';
import {CsFinApiService} from "@jumia-cs-fin/common";
import {AccountRequest} from "../../entities/account/account-request";
import {AccountAdditionalInfo} from "../../entities/account/account-additional-info";
import {AccountStatus} from "../../entities/account/account-status";
import {TroubleshootingAccount} from "../../entities/account/troubleshooting-account";
import {NetChangeResponse} from "../../entities/account/net-change-response";
import {NetChangeFilters} from "../../entities/account/net-change-filters";
import {ExportLog} from "../../entities/export-log/export-log";

@Injectable({
  providedIn: 'root'
})
export class AccountsApiService {

  private readonly baseUrl = 'accounts';

  constructor(private httpClient: HttpClient,
              private route: ActivatedRoute,
              private router: Router,
              private apiService: CsFinApiService) { }



  getAll(filters?: AccountFilters): Observable<PageResponse<Account>> {
    const url = ApiHelperService.buildApiUrl(this.baseUrl);
    return this.apiService.get<PageResponse<Account>>(url,filters);
  }

  getTroubleShooting(filters?: AccountFilters): Observable<PageResponse<TroubleshootingAccount>> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/troubleshooting`);
    return this.apiService.get<PageResponse<TroubleshootingAccount>>(url,filters);
  }

  getById(id: number): Observable<Account> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/${id}`);
    return this.apiService.get<Account>(url);
  }
  getHistory(id: number, filters?: AuditFilters): Observable<PageResponse<AuditResponse<Account>>> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/audit/${id}`);
    return this.apiService.get<PageResponse<AuditResponse<Account>>>(url, filters);
  }

  startDownload(filters?: AccountFilters, netChangeFilters?: NetChangeFilters): Observable<ExportLog> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/download`);
    return this.apiService.get<ExportLog>(url, { ...filters, ...netChangeFilters });
  }

  download(filters?: AccountFilters, netChangeFilters?: NetChangeFilters) {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/download`);
    const params = {...filters, ...netChangeFilters}
    return this.httpClient.get<ArrayBuffer>(url, {
      headers: this.apiService.headers(),
      params: this.apiService.buildQueryParams(params),
      responseType: 'arraybuffer' as 'json'
    }).pipe(map((response: ArrayBuffer) => {
      const blob = new Blob([response], {type: 'text/csv'});
      saveAs(blob, 'BRAD_Accounts_' + new Date(Date.now()).toUTCString() + '.csv');
    }));
  }

  create(field: AccountRequest): Observable<Account> {
    const url = ApiHelperService.buildApiUrl(this.baseUrl);
    return this.httpClient.post<Account>(url, field);
  }

  update(id: number, field: AccountRequest): Observable<Account> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/${id}`);
    return this.httpClient.put<Account>(url, field);
  }
  delete(id: number): Observable<void> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/${id}`);
    return this.httpClient.delete<void>(url);
  }

  getAdditionalInfo(id: number): Observable<AccountAdditionalInfo> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/additional-info/${id}`);
    return this.apiService.get<AccountAdditionalInfo>(url);
  }

  getStatus(): Observable<AccountStatus[]> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/statuses`);
    return this.apiService.get<AccountStatus[]>(url);
  }

  getNetChange(filters: NetChangeFilters): Observable<NetChangeResponse[]> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/net-change`);
    return this.apiService.get<NetChangeResponse[]>(url, filters);
  }

  getAllAccountNavReferences(filters?: AccountFilters): Observable<Account[]> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/nav-references`);
    return this.apiService.get<Account[]>(url,filters);
  }


  getAccountByLastUpdatedStatementByUser(): Observable<Account> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/account-last-updated-statement`);
    return this.apiService.get<Account>(url);
  }
}
