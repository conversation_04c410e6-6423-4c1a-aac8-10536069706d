import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {ApiHelperService} from "./api-helper.service";
import {CsFinApiService} from "@jumia-cs-fin/common";
import {Currency} from "../../entities/currency/currency";


@Injectable({providedIn: 'root'})
export class CurrencyApiService {

  private readonly baseUrl = "currencies";

  constructor(private apiService: CsFinApiService) {
  }

  getAll(): Observable<Currency[]> {
    const url = ApiHelperService.buildApiUrl(this.baseUrl);
    return this.apiService.get<Currency[]>(url);
  }
}
