import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {ApiHelperService} from './api-helper.service';
import {ActivatedRoute, Router} from '@angular/router';
import {PageResponse} from "../../entities/page-response";
import {CsFinApiService} from "@jumia-cs-fin/common";
import {ApiLogFilters} from "../../entities/api-log/api-log-filters";
import {ApiLog} from "../../entities/api-log/api-log";

@Injectable({
  providedIn: 'root'
})
export class ApiLogApiService {

  private readonly baseUrl = 'api-logs';

  constructor(private httpClient: HttpClient,
              private route: ActivatedRoute,
              private router: Router,
              private apiService: CsFinApiService) { }


  getAll(filters?: ApiLogFilters): Observable<PageResponse<ApiLog>> {
    const url = ApiHelperService.buildApiUrl(this.baseUrl);
    return this.apiService.get<PageResponse<ApiLog>>(url,filters);
  }

  getById(id: number): Observable<ApiLog> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/${id}`);
    return this.apiService.get<ApiLog>(url);
  }

  getLogTypes(): Observable<string[]> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/api-log-types`);
    return this.apiService.get<string[]>(url);
  }

  getLogStatus(): Observable<string[]> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/api-log-status`);
    return this.apiService.get<string[]>(url);
  }

  acknowledgeFailure(id: number): Observable<ApiLog> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/${id}/acknowledge-failure`);
    return this.apiService.put<ApiLog, ApiLog | null>(url, null);
  }

}
