import {Injectable} from "@angular/core";
import {HttpClient} from "@angular/common/http";
import {ActivatedRoute, Router} from "@angular/router";
import {CsFinApiService} from "@jumia-cs-fin/common";
import {Observable} from "rxjs";
import {PageResponse} from "../../entities/page-response";
import {ApiHelperService} from "./api-helper.service";
import {ExecutionLogFilters} from "../../entities/execution-log/execution-log-filters";
import {ExecutionLog} from "../../entities/execution-log/execution-log";

@Injectable({
  providedIn: 'root'
})
export class ExecutionLogApiService {

  private readonly baseUrl = 'execution-logs';

  constructor(private httpClient: HttpClient,
              private route: ActivatedRoute,
              private router: Router,
              private apiService: CsFinApiService) { }


  getAll(filters?: ExecutionLogFilters): Observable<PageResponse<ExecutionLog>> {
    const url = ApiHelperService.buildApiUrl(this.baseUrl);
    return this.apiService.get<PageResponse<ExecutionLog>>(url,filters);
  }

  getById(id: number): Observable<ExecutionLog> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/${id}`);
    return this.apiService.get<ExecutionLog>(url);
  }

  getLogTypes(): Observable<string[]> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/types`);
    return this.apiService.get<string[]>(url);
  }

  getLogStatus(): Observable<string[]> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/statuses`);
    return this.apiService.get<string[]>(url);
  }


}
