import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {ApiHelperService} from './api-helper.service';
import {
  CsFinApiService,
  CsFinFilterList,
  CsFinGroup,
  CsFinGroupedEntityInfo,
  CsFinPageResponse
} from "@jumia-cs-fin/common";
import {TransactionFilters} from 'src/app/entities/transaction/transaction-filters';
import {Transaction} from 'src/app/entities/transaction/transaction';
import {map} from "rxjs/operators";
import {saveAs} from "file-saver";
import {HttpClient} from "@angular/common/http";

@Injectable({
  providedIn: 'root'
})
export class TransactionsApiService {

  private readonly baseUrl = 'transactions';


  constructor(private apiService: CsFinApiService,
              private httpClient: HttpClient) { }


  getAll(filters?: TransactionFilters): Observable<CsFinPageResponse<Transaction>> {
    const url = ApiHelperService.buildApiUrl(this.baseUrl);
    return this.apiService.get<CsFinPageResponse<Transaction>>(url,filters);
  }

  getDirections(): Observable<string[]> {
    const url = ApiHelperService.buildApiUrl('statements/directions');
    return this.apiService.get<string[]>(url);
  }

  download(filters?: TransactionFilters)  {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/download`);
    return this.httpClient.get<ArrayBuffer>(url, {
      headers: this.apiService.headers(),
      params: this.apiService.buildQueryParams(filters),
      responseType: 'arraybuffer' as 'json'
    }).pipe(map((response: ArrayBuffer) => {
      const blob = new Blob([response], {type: 'text/csv'});
      saveAs(blob, 'BRAD_Transactions_'+new Date(Date.now()).toUTCString()+'.csv');
    }));
  }

  getTransactionGroups(filters:string[], defaultFilters?:TransactionFilters): Observable<CsFinGroup> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/grouped`);
    return this.apiService.get<CsFinGroup>(url, {fields:filters, ...defaultFilters});
  }

  getTransactionGroupableFields(): Observable<string[]> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/groupable-fields`);
    return this.apiService.get<string[]>(url);
  }

  getAllGroupedInfo(filterList : CsFinFilterList<TransactionFilters>):Observable<CsFinGroupedEntityInfo[]> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/group-info`);
    return this.httpClient.post<CsFinGroupedEntityInfo[]>(url, filterList);
  }

  getAllTransactionsByAccount(accountId: number): Observable<CsFinPageResponse<Transaction>> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}?partitionKey=${accountId}`);
    return this.apiService.get<CsFinPageResponse<Transaction>>(url);
  }

}
