import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {ApiHelperService} from './api-helper.service';
import {CsFinApiService} from "@jumia-cs-fin/common";
import {BaleViewEntity} from "../../entities/bale-view-entity/bale-view-entity";


@Injectable({
  providedIn: 'root'
})
export class BaleViewEntityApiService {

  private readonly baseUrl = 'bale-view-entities';


  constructor(
    private httpClient: HttpClient,
    private apiService: CsFinApiService) { }


  fetchBaleViewEntities(): Observable<BaleViewEntity[]> {
    const url = ApiHelperService.buildApiUrl(this.baseUrl);
    return this.apiService.get<any>(url);
  }

}
