import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {ApiHelperService} from './api-helper.service';
import {User} from '../../entities/user/user';
import {CsFinApiService} from "@jumia-cs-fin/common";
import {ActivatedRoute, Router} from '@angular/router';
import {PageResponse} from "../../entities/page-response";
import {UserFilters} from "../../entities/user/user-filters";
import {map} from "rxjs/operators";
import {saveAs} from "file-saver";
import {AccountFilters} from "../../entities/account/account-filters";

@Injectable({
  providedIn: 'root'
})
export class UsersApiService {

  private readonly baseUrl = 'users';



  constructor(private httpClient: HttpClient,
              private route: ActivatedRoute,
              private router: Router,
              private apiService: CsFinApiService) { }


  getAll(filters?: UserFilters): Observable<PageResponse<User>> {
    const url = ApiHelperService.buildApiUrl(this.baseUrl);
    return this.apiService.get<PageResponse<User>>(url,filters);
  }

  getAllFromAccount(id: number): Observable<Array<User>> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/account/${id}`);
    return this.httpClient.get<Array<User>>(url);
  }

  getById(id: number): Observable<User> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/${id}`);
    return this.apiService.get<User>(url);
  }


  create(field: User): Observable<User> {
    const url = ApiHelperService.buildApiUrl(this.baseUrl);
    return this.httpClient.post<User>(url, field);
  }

  update(id: number, field: User): Observable<User> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/${id}`);
    return this.httpClient.put<User>(url, field);
  }
  delete(id: number): Observable<void> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/${id}`);
    return this.apiService.delete<void>(url);
  }

  downloadBasedOnAccountFilters(filters?: AccountFilters)  {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/download`);
    return this.httpClient.get<ArrayBuffer>(url, {
      headers: this.apiService.headers(),
      params: this.apiService.buildQueryParams(filters),
      responseType: 'arraybuffer' as 'json'
    }).pipe(map((response: ArrayBuffer) => {
      const blob = new Blob([response], {type: 'text/csv'});
      saveAs(blob, 'BRAD_Users_'+new Date(Date.now()).toUTCString()+'.csv');
    }));
  }
}
