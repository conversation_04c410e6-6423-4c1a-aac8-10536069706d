import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import {CashEvolutionChartDataResponse, CashEvolutionResponse} from "../../dashboard/types/dashboard.interface";
import {CsFinApiService} from "@jumia-cs-fin/common";
import {ApiHelperService} from "./api-helper.service";

@Injectable({
  providedIn: 'root'
})
export class DashboardApiService {
  private baseUrl = 'accounts-summary';

  constructor(private apiService: CsFinApiService) {}

  getCashEvolution(urlQuery: string): Observable<CashEvolutionResponse> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/cash-evolution${urlQuery}`);
    return this.apiService.get<CashEvolutionResponse>(url);
  }

  getCashEvolutionBreakDown(urlQuery: string): Observable<CashEvolutionChartDataResponse> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/cash-evolution${urlQuery}`);
    return this.apiService.get<CashEvolutionChartDataResponse>(url);
  }
}
