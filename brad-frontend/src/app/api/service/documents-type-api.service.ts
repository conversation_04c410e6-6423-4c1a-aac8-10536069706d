import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {ApiHelperService} from "./api-helper.service";
import {CsFinApiService} from "@jumia-cs-fin/common";


@Injectable({providedIn: 'root'})
export class DocumentTypesApiService {

  private readonly baseUrl = "documents";

  constructor(private apiService: CsFinApiService) {
  }

  getAll(): Observable<String[]> {
    const url = ApiHelperService.buildApiUrl(this.baseUrl+"/types");
    return this.apiService.get<String[]>(url);
  }

}
