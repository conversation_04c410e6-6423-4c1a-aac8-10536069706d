import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {ApiHelperService} from './api-helper.service';
import {JwtResponse} from '../../entities/jwt-response';

@Injectable({
  providedIn: 'root'
})
export class AuthApiService {

  private readonly baseUrl = 'auth';

  constructor(private httpClient: HttpClient) { }

  swapTempToken(tempToken: string): Observable<JwtResponse> {
    const url = ApiHelperService.buildUrl(`${this.baseUrl}/swap-token`);
    return this.httpClient.post<JwtResponse>(url, tempToken);
  }

  getUserPermissions(): Observable<any> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/user/permissions`);
    return this.httpClient.get<any>(url);
  }

  logout(): Observable<void> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/logout`);
    return this.httpClient.get<void>(url);
  }
}
