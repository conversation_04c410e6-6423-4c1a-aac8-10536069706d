import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {ApiHelperService} from './api-helper.service';
import {Contact} from '../../entities/contact/contact';
import {ActivatedRoute, Router} from '@angular/router';
import {PageResponse} from "../../entities/page-response";
import {ContactFilters} from "../../entities/contact/contact-filters";
import {CsFinApiService} from "@jumia-cs-fin/common";
import {map} from "rxjs/operators";
import {saveAs} from "file-saver";
import {AccountFilters} from "../../entities/account/account-filters";

@Injectable({
  providedIn: 'root'
})
export class ContactsApiService {

  private readonly baseUrl = 'contacts';



  constructor(private httpClient: HttpClient,
              private route: ActivatedRoute,
              private router: Router,
              private apiService: CsFinApiService) { }



  getAll(filters?: ContactFilters): Observable<PageResponse<Contact>> {
    const url = ApiHelperService.buildApiUrl(this.baseUrl);
    return this.apiService.get<PageResponse<Contact>>(url,filters);
  }

  getAllFromAccount(id: number): Observable<Array<Contact>> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/account/${id}`);
    return this.httpClient.get<Array<Contact>>(url);
  }

  getById(id: number): Observable<Contact> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/${id}`);
    return this.httpClient.get<Contact>(url);
  }

  create(field: Contact): Observable<Contact> {
    const url = ApiHelperService.buildApiUrl(this.baseUrl);
    return this.httpClient.post<Contact>(url, field);
  }

  update(id: number, field: Contact): Observable<Contact> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/${id}`);
    return this.httpClient.put<Contact>(url, field);
  }
  delete(id: number): Observable<void> {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/${id}`);
    return this.httpClient.delete<void>(url);
  }

  downloadBasedOnAccountFilters(filters?: AccountFilters)  {
    const url = ApiHelperService.buildApiUrl(`${this.baseUrl}/download`);
    return this.httpClient.get<ArrayBuffer>(url, {
      headers: this.apiService.headers(),
      params: this.apiService.buildQueryParams(filters),
      responseType: 'arraybuffer' as 'json'
    }).pipe(map((response: ArrayBuffer) => {
      const blob = new Blob([response], {type: 'text/csv'});
      saveAs(blob, 'BRAD_Contacts_'+new Date(Date.now()).toUTCString()+'.csv');
    }));
  }
}
