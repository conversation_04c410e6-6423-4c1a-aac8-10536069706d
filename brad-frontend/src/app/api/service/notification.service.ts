import {Injectable} from "@angular/core";
import {CsFinNotificationService} from "@jumia-cs-fin/common";
import {ErrorResponse} from "../../entities/error/error-response";
import {HttpErrorResponse} from "@angular/common/http";

@Injectable({providedIn: 'root'})
export class NotificationService extends CsFinNotificationService {

  errorWithResponse(httpErrorResponse: HttpErrorResponse): void {
    let errorResponse: ErrorResponse = httpErrorResponse.error;
    let validErrorResponse = this.isResponseValid(errorResponse);
    if (!validErrorResponse) {
      if (!this.isObjectOrUndefined(httpErrorResponse.error.error))
        this.error(httpErrorResponse.error.error);
      else if (!this.isObjectOrUndefined(httpErrorResponse.error))
        this.error(httpErrorResponse.error);
      else
        this.error(httpErrorResponse.message);
    }
  }

  private isResponseValid(error: ErrorResponse): boolean {
    let errorMessages: String[] = [];
    if (error.globalError) {
      this.error(error.globalError);
    } else if (error.errorFields) {
      errorMessages = error.errorFields.map(errorField => {
        let errorMessage = '';
        if(Object.hasOwn(errorField, 'field')) {
          errorMessage += `${errorField.field} : ${errorField.error}`;
        }
       return errorMessage;
      });
      this.error(errorMessages.join(' | ') || error.errorFields[0].error);
    } else {
      return false;
    }
    return true;
  }

  private isObjectOrUndefined(object: any): boolean {
    return object === undefined || object instanceof Object;
  }


}
