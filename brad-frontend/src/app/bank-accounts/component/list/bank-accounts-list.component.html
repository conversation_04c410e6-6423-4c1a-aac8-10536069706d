<main class="container">
  <section class="filters" *ngIf="dataSource">
    <cs-fin-active-filters [chipsMap]="activeFilterChips"
                           (onRemove)="onActiveFilterRemoveClick($event)">
    </cs-fin-active-filters>
    <span fxFlex></span>
    <span class="actions">

      <button mat-raised-button color="primary" class="mat-mdc-raised-button" (click)="onCreateClick()" csFinHasPermission
              [authPermissions]="auth.permissions.BRAD_MANAGE_BANK_ACCOUNTS"
              [authTarget]="bradAuthTarget"
              [authAction]="auth.actions.DISABLE">
        <mat-icon>add</mat-icon>
        <span class="label" fxShow [fxHide.xs]="true" [fxHide.sm]="true">
          {{ 'GENERAL.BUTTONS.LABELS.CREATE' | translate }}
        </span>
      </button>

      <mat-menu #menu="matMenu">
        <button mat-menu-item (click)="onDownloadBankAccountClick()" csFinHasPermission
                [authPermissions]="auth.permissions.BRAD_DOWNLOAD_BANK_ACCOUNTS_CSV"
                [authTarget]="bradAuthTarget"
                [authAction]="auth.actions.DISABLE">
          <mat-icon color="primary">info</mat-icon>
          {{ 'GENERAL.BUTTONS.LABELS.ACCOUNT_DETAILS' | translate }}
        </button>
        <button mat-menu-item disabled>
          <mat-icon color="primary">description</mat-icon>
          {{ 'GENERAL.BUTTONS.LABELS.DOCUMENTS' | translate }}
        </button>
        <button mat-menu-item (click)="onDownloadBankAccContactsClick()" csFinHasPermission
                [authPermissions]="auth.permissions.BRAD_DOWNLOAD_BANK_ACCOUNTS_CSV"
                [authTarget]="bradAuthTarget"
                [authAction]="auth.actions.DISABLE">
          <mat-icon color="primary">contacts</mat-icon>
          {{ 'GENERAL.BUTTONS.LABELS.CONTACTS' | translate }}
        </button>
        <button mat-menu-item (click)="onDownloadBankAccUsersClick()" csFinHasPermission
                [authPermissions]="auth.permissions.BRAD_DOWNLOAD_BANK_ACCOUNTS_CSV"
                [authTarget]="bradAuthTarget"
                [authAction]="auth.actions.DISABLE">
          <mat-icon color="primary">group</mat-icon>
          {{ 'GENERAL.BUTTONS.LABELS.USERS' | translate }}
        </button>
      </mat-menu>
      <button mat-stroked-button [matMenuTriggerFor]="menu" class="export-btn" color="primary"  csFinHasPermission
              [authPermissions]="auth.permissions.BRAD_DOWNLOAD_BANK_ACCOUNTS_CSV"
              [authTarget]="bradAuthTarget"
              [authAction]="auth.actions.DISABLE"
              matTooltip="{{ 'GENERAL.BUTTONS.LABELS.DOWNLOAD' | translate }}">
           <mat-icon>cloud_download</mat-icon>
      </button>

      <button mat-stroked-button id="change-columns-btn" color="primary" fxShow [fxHide.xs]="true" [fxHide.sm]="true"
              [disabled]="bankAccountDetailsOpened" cdkOverlayOrigin #trigger="cdkOverlayOrigin"
              (click)="triggerOverlay(trigger)">
        <mat-icon>table_rows</mat-icon>
      </button>
    </span>
  </section>

  <section class="table-detail responsive-table"
           *ngIf="!isLoading"
           [class.detail-opened]="bankAccountDetailsOpened"
           [class.fullscreen]="isBankAccountDetailsInFullscreen">

    <div class="table-container" [class.detail-opened]="bankAccountDetailsOpened"
         [class.fullscreen]="isBankAccountDetailsInFullscreen">

      <mat-progress-bar mode="indeterminate" *ngIf="isLoading"></mat-progress-bar>

      <table mat-table class="cs-fin-table" matSort [dataSource]="dataSource"  [class.loading]="isLoading"
             (matSortChange)="onSortChange($event)" cdkDropList cdkDropListSortingDisabled>

        <ng-container matColumnDef="id">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'BANK_ACCOUNTS.FIELDS.ID' | translate }}</th>
          <td mat-cell *matCellDef="let bankAccount"> {{bankAccount?.id}} </td>
        </ng-container>

        <ng-container matColumnDef="companyID">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'BANK_ACCOUNTS.FIELDS.COMPANY_ID' | translate }}</th>
          <td mat-cell *matCellDef="let bankAccount">
            <span> {{bankAccount?.companyID}}  </span>
            <span *ngIf="!bankAccount?.companyID"> - </span>
          </td>
        </ng-container>

        <ng-container matColumnDef="country">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'BANK_ACCOUNTS.FIELDS.COUNTRY' | translate }}</th>
          <td mat-cell class="country-cell" *matCellDef="let bankAccount">
            <div>
              {{bankAccount?.country.name}} <cs-fin-flag [countryCode]="bankAccount?.country.code"></cs-fin-flag>
              <span *ngIf="!bankAccount?.country"> - </span>
            </div>
          </td>
        </ng-container>

        <ng-container matColumnDef="currency">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'BANK_ACCOUNTS.FIELDS.CURRENCY' | translate }}</th>
          <td mat-cell *matCellDef="let bankAccount">
            <span> {{bankAccount?.currency.code}} </span>
            <span *ngIf="!bankAccount?.currency"> - </span>
          </td>
        </ng-container>

        <ng-container matColumnDef="navReference">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'BANK_ACCOUNTS.FIELDS.NAV_REFERENCE' | translate }}</th>
          <td mat-cell *matCellDef="let bankAccount">
            <span> {{bankAccount?.navReference}} </span>
            <span *ngIf="!bankAccount?.navReference"> - </span>
          </td>
        </ng-container>

        <ng-container matColumnDef="balance">
          <th mat-header-cell *matHeaderCellDef>{{ 'BANK_ACCOUNTS.FIELDS.BALANCE' | translate }}</th>
          <td mat-cell *matCellDef="let bankAccount">
            <div class="amountWithUsd">
              <div>
                <span *ngIf="!bankAccount?.balance">-</span>
                {{bankAccount?.balance | number:'1.2-2'}} {{bankAccount?.currency.symbol}}
              </div>
              <div class="usd" *ngIf="bankAccount?.balanceUSD; else emptyAmount;">
                {{bankAccount?.balanceUSD | number:'1.2-2'}} $
              </div>
            </div>
          </td>
        </ng-container>

        <ng-container matColumnDef="beneficiaryName">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'BANK_ACCOUNTS.FIELDS.BENEFICIARY_NAME' | translate }}</th>
          <td mat-cell *matCellDef="let bankAccount">
            <span> {{bankAccount?.beneficiaryName}} </span>
            <span *ngIf="!bankAccount?.beneficiaryName"> - </span>
          </td>
        </ng-container>

        <ng-container matColumnDef="beneficiaryAddress">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'BANK_ACCOUNTS.FIELDS.BENEFICIARY_ADDRESS' | translate }}</th>
          <td mat-cell *matCellDef="let bankAccount">
            <span> {{bankAccount?.beneficiaryAddress}} </span>
            <span *ngIf="!bankAccount?.beneficiaryAddress"> - </span>
          </td>
        </ng-container>

        <ng-container matColumnDef="bankAccountNumber">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'BANK_ACCOUNTS.FIELDS.BANK_ACCOUNT_NUMBER' | translate }}</th>
          <td mat-cell *matCellDef="let bankAccount">
            <span> {{bankAccount?.bankAccountNumber}} </span>
            <span *ngIf="!bankAccount?.bankAccountNumber"> - </span>
          </td>
        </ng-container>

        <ng-container matColumnDef="bankName">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'BANK_ACCOUNTS.FIELDS.BANK_NAME' | translate }}</th>
          <td mat-cell *matCellDef="let bankAccount">
            <span> {{bankAccount?.bankName}} </span>
            <span *ngIf="!bankAccount?.bankName"> - </span>
          </td>
        </ng-container>

        <ng-container matColumnDef="swiftCode">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'BANK_ACCOUNTS.FIELDS.SWIFT_CODE' | translate }}</th>
          <td mat-cell *matCellDef="let bankAccount">
            <span> {{bankAccount?.swiftCode}} </span>
            <span *ngIf="!bankAccount?.swiftCode"> - </span>
          </td>
        </ng-container>

        <ng-container matColumnDef="bankRoutingCode">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'BANK_ACCOUNTS.FIELDS.BANK_ROUTING_CODE' | translate }}</th>
          <td mat-cell *matCellDef="let bankAccount">
            <span> {{bankAccount?.bankRoutingCode}} </span>
            <span *ngIf="!bankAccount?.bankRoutingCode"> - </span>
          </td>
        </ng-container>

        <ng-container matColumnDef="sortCode">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'BANK_ACCOUNTS.FIELDS.SORT_CODE' | translate }}</th>
          <td mat-cell *matCellDef="let bankAccount">
            <span> {{bankAccount?.sortCode}} </span>
            <span *ngIf="!bankAccount?.sortCode"> - </span>
          </td>
        </ng-container>

        <ng-container matColumnDef="branchCode">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'BANK_ACCOUNTS.FIELDS.BRANCH_CODE' | translate }}</th>
          <td mat-cell *matCellDef="let bankAccount">
            <span> {{bankAccount?.branchCode}} </span>
            <span *ngIf="!bankAccount?.branchCode"> - </span>
          </td>
        </ng-container>

        <ng-container matColumnDef="rib">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'BANK_ACCOUNTS.FIELDS.RIB' | translate }}</th>
          <td mat-cell *matCellDef="let bankAccount">
            <span> {{bankAccount?.rib}} </span>
            <span *ngIf="!bankAccount?.rib"> - </span>
          </td>
        </ng-container>

        <ng-container matColumnDef="status">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'BANK_ACCOUNTS.FIELDS.STATUS' | translate }}</th>
          <td class="c-cell" mat-cell *matCellDef="let bankAccount">
            <span> {{bankAccount?.status}} </span>
            <span *ngIf="!bankAccount?.status"> - </span>
          </td>
        </ng-container>

        <ng-container matColumnDef="createdAt">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'BANK_ACCOUNTS.FIELDS.CREATED_AT' | translate }}</th>
          <td mat-cell *matCellDef="let bankAccount"> {{bankAccount?.createdAt | date:'short'}} </td>
        </ng-container>

        <ng-container matColumnDef="createdBy">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'BANK_ACCOUNTS.FIELDS.CREATED_BY' | translate }}</th>
          <td mat-cell *matCellDef="let bankAccount"> {{bankAccount?.createdBy}} </td>
        </ng-container>

        <ng-container matColumnDef="updatedAt">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'BANK_ACCOUNTS.FIELDS.UPDATED_AT' | translate }}</th>
          <td mat-cell *matCellDef="let bankAccount"> {{bankAccount?.updatedAt | date:'short'}} </td>
        </ng-container>

        <ng-container matColumnDef="updatedBy">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'BANK_ACCOUNTS.FIELDS.UPDATED_BY' | translate }}</th>
          <td mat-cell *matCellDef="let bankAccount"> {{bankAccount?.updatedBy}} </td>
        </ng-container>

        <ng-container matColumnDef="lastStatementDate">
          <th mat-header-cell *matHeaderCellDef class="last-statement-date-width">{{ 'BANK_ACCOUNTS.FIELDS.LAST_STATEMENT_DATE' | translate }}</th>
          <td mat-cell *matCellDef="let bankAccount"> {{bankAccount?.lastStatementDate}} </td>
        </ng-container>

        <ng-container matColumnDef="statementSource">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'BANK_ACCOUNTS.FIELDS.STATEMENT_SOURCE' | translate }}</th>
          <td class="c-cell" mat-cell *matCellDef="let bankAccount">
            <span> {{bankAccount?.statementSource}} </span>
            <span *ngIf="!bankAccount?.statementSource"> - </span>
          </td>
        </ng-container>

        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef>{{ 'GENERAL.FIELDS.ACTIONS' | translate }}</th>
          <td mat-cell *matCellDef="let bankAccount" [attr.data-label]="'Actions'">
            <div class="table-actions">
              <mat-icon (click)="onEditClick(bankAccount)" csFinHasPermissionOnAnyTarget
                        [authPermissions]="auth.permissions.BRAD_MANAGE_BANK_ACCOUNTS"
                        [authTarget]="bradAuthTarget"
                        [authAction]="auth.actions.HIDE">
                edit
              </mat-icon>


              <mat-icon (click)="onDeleteClick(bankAccount)"  csFinHasPermissionOnAnyTarget
                        [authPermissions]="auth.permissions.BRAD_DELETE_BANK_ACCOUNTS"
                        [authTarget]="bradAuthTarget"
                        [authAction]="auth.actions.HIDE">
                delete
              </mat-icon>
            </div>

          </td>
        </ng-container>




        <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"
            (click)="onOpenItemDetailsClick(row.id)"
            [class.item-opened]="isItemDetailsOpened(row.id)"
            class="with-detail" style="cursor: pointer;"></tr>
      </table>



      <mat-paginator [pageSizeOptions]="pagination.pageSizeOptions"
                     [pageSize]="pagination.pageSize"
                     [length]="pagination.totalItems"
                     [pageIndex]="pagination.pageIndex"
                     (page)="onPageChange($event)"
                     showFirstLastButtons>
      </mat-paginator>

      <span id="empty-table" *ngIf="!isLoading && !dataSource.data.length">
        {{'GENERAL.TABLE.NO_RESULTS_FOUND' | translate}}
      </span>
    </div>
    <div class="{{isBankAccountDetailsInFullscreen ? 'details-full-screen' : 'details-container'}}"
         *ngIf="bankAccountDetailsOpened">
      <router-outlet></router-outlet>
    </div>
  </section>

  <cs-fin-add-remove-columns [triggerOrigin]="triggerOrigin"
                             [isOverlayOpen$]="isOverlayOpen$"
                             [menu]="MENU">
  </cs-fin-add-remove-columns>
</main>

<ng-template #emptyAmount>
</ng-template>
