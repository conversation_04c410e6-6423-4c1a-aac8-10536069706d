import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';

import {ApiLogRoutingModule} from './api-log-routing.module';
import {ApiLogHeaderComponent} from './component/header/api-log-header.component';
import {ApiLogListComponent} from './component/list/api-log-list.component';
import {ApiLogDetailsComponent} from './component/details/api-log-details.component';
import {CdkConnectedOverlay, CdkOverlayOrigin} from "@angular/cdk/overlay";
import {ExtendedModule, FlexModule} from "@angular/flex-layout";
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {MatButtonModule} from "@angular/material/button";
import {MatDatepickerModule} from "@angular/material/datepicker";
import {MatFormFieldModule} from "@angular/material/form-field";
import {MatIconModule} from "@angular/material/icon";
import {MatInputModule} from "@angular/material/input";
import {MatOptionModule} from "@angular/material/core";
import {MatProgressSpinnerModule} from "@angular/material/progress-spinner";
import {MatSelectModule} from "@angular/material/select";
import {MatToolbarModule} from "@angular/material/toolbar";
import {NgxMatSelectSearchModule} from "ngx-mat-select-search";
import {TranslateModule} from "@ngx-translate/core";
import {CdkDropList, CdkDropListGroup} from "@angular/cdk/drag-drop";
import {CsFinActiveFiltersModule, CsFinAddRemoveColumnsModule, CsFinAuthModule} from "@jumia-cs-fin/common";
import {MatPaginatorModule} from "@angular/material/paginator";
import {MatProgressBarModule} from "@angular/material/progress-bar";
import {MatSortModule} from "@angular/material/sort";
import {MatTableModule} from "@angular/material/table";
import {AccountsModule} from "../accounts/accounts.module";
import {MatCardModule} from "@angular/material/card";
import {MatTabsModule} from "@angular/material/tabs";
import {ApiLogDetailsInfoComponent} from './component/details/details-info/api-log-details-info.component';
import {JsonFormatter} from "../shared/helpers/json-formatter";
import {MatExpansionModule} from "@angular/material/expansion";
import {ApiLogComponent} from "./api-log.component";
import {ApiLogRequestInfoComponent} from './component/details/request-info/api-log-request-info.component';
import {ApiLogResponseInfoComponent} from './component/details/response-info/api-log-response-info.component';
import {MatTooltipModule} from "@angular/material/tooltip";


@NgModule({
  declarations: [
    ApiLogComponent,
    ApiLogHeaderComponent,
    ApiLogListComponent,
    ApiLogDetailsComponent,
    ApiLogDetailsInfoComponent,
    ApiLogComponent,
    ApiLogRequestInfoComponent,
    ApiLogResponseInfoComponent
  ],
  exports: [
    ApiLogHeaderComponent,
    ApiLogListComponent
  ],
  imports: [
    CommonModule,
    ApiLogRoutingModule,
    CdkConnectedOverlay,
    CdkOverlayOrigin,
    ExtendedModule,
    FlexModule,
    FormsModule,
    MatButtonModule,
    MatDatepickerModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatOptionModule,
    MatProgressSpinnerModule,
    MatSelectModule,
    MatToolbarModule,
    NgxMatSelectSearchModule,
    TranslateModule,
    ReactiveFormsModule,
    CdkDropList,
    CdkDropListGroup,
    CsFinActiveFiltersModule,
    CsFinAddRemoveColumnsModule,
    CsFinAuthModule,
    MatPaginatorModule,
    MatProgressBarModule,
    MatSortModule,
    MatTableModule,
    AccountsModule,
    MatCardModule,
    MatTabsModule,
    JsonFormatter,
    MatExpansionModule,
    MatTooltipModule,
  ]
})
export class ApiLogModule { }
