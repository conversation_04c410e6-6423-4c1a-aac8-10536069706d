import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {NavigationComponent} from "../navigation/navigation.component";
import {csFinAuthCanActivateGuard} from "@jumia-cs-fin/common";
import {bradPermissions} from "../auth/constants/permission.constants";
import {bradAuthTarget} from "../auth/constants/auth.constants";
import {ApiLogComponent} from "./api-log.component";
import {ApiLogDetailsComponent} from "./component/details/api-log-details.component";
import {csFinAuthorizationOnTargetService} from "../app-routing.module";


const routes: Routes = [
  {
    path: '',
    component: NavigationComponent,
    children: [
      {
        path: '',
        canActivate: [csFinAuthCanActivateGuard],
        data: {
          auth: {
            permissions: bradPermissions.BRAD_ACCESS_API_LOG,
            targets: bradAuthTarget,
            authTypeService: csFinAuthorizationOnTargetService,
          }
        },
        component: ApiLogComponent,
        children: [
          {
            path: ':apiLogID',
            component: ApiLogDetailsComponent
          }
        ]
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ApiLogRoutingModule { }
