<main class="container" cdkDropListGroup>
  <section class="filters" *ngIf="dataSource">
    <cs-fin-active-filters [chipsMap]="activeFilterChips"
                           (onRemove)="onActiveFilterRemoveClick($event)">
    </cs-fin-active-filters>
    <span fxFlex></span>
    <span class="actions">
      <button mat-stroked-button id="change-columns-btn" color="primary" fxShow [fxHide.xs]="true" [fxHide.sm]="true"
              [disabled]="apiLogDetailsOpened" cdkOverlayOrigin #trigger="cdkOverlayOrigin"
              (click)="triggerOverlay(trigger)">
        <mat-icon>table_rows</mat-icon>
      </button>
    </span>

  </section>
  <mat-progress-bar mode="indeterminate" *ngIf="isLoading"></mat-progress-bar>

  <section class="table-detail responsive-table"
           *ngIf="!isLoading"
           [class.detail-opened]="apiLogDetailsOpened"
           [class.fullscreen]="isApiLogDetailsInFullscreen">

    <div class="table-container" [class.detail-opened]="apiLogDetailsOpened"
         [class.fullscreen]="isApiLogDetailsInFullscreen">

      <mat-progress-bar mode="indeterminate" *ngIf="isLoading"></mat-progress-bar>

      <table mat-table class="cs-fin-table" matSort [dataSource]="dataSource"  [class.loading]="isLoading"
             (matSortChange)="onSortChange($event)" cdkDropList cdkDropListSortingDisabled>

        <ng-container matColumnDef="id">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'API_LOG.FIELDS.ID' | translate }}</th>
          <td mat-cell *matCellDef="let apiLog"> {{apiLog?.id}} </td>
        </ng-container>

        <ng-container matColumnDef="logType">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'API_LOG.FIELDS.LOG_TYPE' | translate }}</th>
          <td mat-cell *matCellDef="let apiLog"> {{ 'API_LOG.TYPES.' + apiLog?.logType | translate}} </td>
        </ng-container>

        <ng-container matColumnDef="relatedEntityId">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'API_LOG.FIELDS.RELATED_ENTITY_ID' | translate }}</th>
          <td mat-cell *matCellDef="let apiLog"> {{apiLogFacade.getRelatedEntities(apiLog?.relatedEntityId)}} </td>
        </ng-container>

        <ng-container matColumnDef="logStatus">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'API_LOG.FIELDS.LOG_STATUS' | translate }}</th>
          <td mat-cell *matCellDef="let apiLog"> {{apiLog?.logStatus}} </td>
        </ng-container>

        <ng-container matColumnDef="createdAt">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'API_LOG.FIELDS.CREATED_AT' | translate }}</th>
          <td mat-cell *matCellDef="let apiLog"> {{apiLog?.createdAt | date:'short'}} </td>
        </ng-container>

        <ng-container matColumnDef="createdBy">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'API_LOG.FIELDS.CREATED_BY' | translate }}</th>
          <td mat-cell *matCellDef="let apiLog"> {{apiLog?.createdBy}} </td>
        </ng-container>

        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef>{{ 'GENERAL.FIELDS.ACTIONS' | translate }}</th>
          <td mat-cell *matCellDef="let apiLog" [attr.data-label]="'Actions'">
            <div class="table-actions">
              <button
                  mat-icon-button *ngIf="apiLog.logType=='BANK_STATEMENT_CREATION'"
                  class="action retryActionButton"
                  matTooltip="{{ 'API_LOG.ACTIONS.RETRY' | translate }}"
                  (click)="onClickRetryRequest($event, apiLog.id!)">
                <mat-icon>
                  refresh
                </mat-icon>
              </button>
              <button mat-icon-button disabled="{{!isFailedApiLog(apiLog)}}"
                      *ngIf="(apiLog.logType=='API_BANK_STATEMENT_FETCH' || apiLog.logType=='SFTP_BANK_STATEMENT_IMPORT') "
                      [ngClass]="{'acknowledgeFailureActionButton':isFailedApiLog(apiLog), 'action':true}"
                      matTooltip="{{ 'API_LOG.ACTIONS.ACKNOWLEDGE' | translate }}"
                      (click)="onClickAcknowledgeFailure($event, apiLog.id!)">

                <mat-icon>
                  verified-user
                </mat-icon>
              </button>
            </div>

          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"
            (click)="onOpenItemDetailsClick(row.id)"
            [class.item-opened]="isItemDetailsOpened(row.id)"
            class="with-detail" style="cursor: pointer;"></tr>
      </table>

      <mat-paginator [pageSizeOptions]="pagination.pageSizeOptions"
                     [pageSize]="pagination.pageSize"
                     [length]="pagination.totalItems"
                     [pageIndex]="pagination.pageIndex"
                     (page)="onPageChange($event)"
                     showFirstLastButtons>
      </mat-paginator>

      <span id="empty-table" *ngIf="!isLoading && !dataSource.data.length">
        {{'GENERAL.TABLE.NO_RESULTS_FOUND' | translate}}
      </span>
    </div>
    <div class="{{isApiLogDetailsInFullscreen ? 'details-full-screen' : 'details-container'}}"
         *ngIf="apiLogDetailsOpened">
      <router-outlet></router-outlet>
    </div>
  </section>

  <cs-fin-add-remove-columns [triggerOrigin]="triggerOrigin"
                             [isOverlayOpen$]="isOverlayOpen$"
                             [menu]="MENU">
  </cs-fin-add-remove-columns>
</main>
