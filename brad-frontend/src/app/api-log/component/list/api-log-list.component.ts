import {ChangeDetector<PERSON><PERSON>, Component, On<PERSON><PERSON>roy, OnInit, ViewChild} from '@angular/core';
import {MatPaginator} from "@angular/material/paginator";
import {MatSort, SortDirection} from "@angular/material/sort";
import {MenusConsts} from "../../../shared/constants/menus.constants";
import {MatTableDataSource} from "@angular/material/table";
import {
  activeFiltersSeparator,
  CsFinActiveFilterChip,
  CsFinAddRemoveColumns,
  CsFinAddRemoveColumnsFacade,
  CsFinColumnDetails, CsFinConfirmationDialogComponent,
  CsFinLastUsedColumns,
  CsFinPagination,
  csFinPaginationConsts,
  CsFinSortingDataAccessorHelperService
} from "@jumia-cs-fin/common";
import {authParams, bradAuthTarget} from "../../../auth/constants/auth.constants";
import {Observable, Subject} from "rxjs";
import {CdkOverlayOrigin} from "@angular/cdk/overlay";
import {MediaMatcher} from "@angular/cdk/layout";
import {MatDialog} from "@angular/material/dialog";
import {ActivatedRoute, Router} from "@angular/router";
import {finalize, takeUntil} from "rxjs/operators";
import {PageResponse} from "../../../entities/page-response";
import * as _ from "lodash";
import {SortFilters} from "../../../entities/SortFilters";
import {ApiLog} from "../../../entities/api-log/api-log";
import {ApiLogFilters} from "../../../entities/api-log/api-log-filters";
import {ApiLogFacade} from "../../facade/api-log.facade";
import {HttpErrorResponse} from "@angular/common/http";
import {NotificationService} from "../../../api/service/notification.service";
import {StatementFacade} from "../../../accounts/facade/statement.facade";
import {StatementWithTransactionRequest} from "../../../entities/statement/statement-with-transaction-request";

@Component({
  selector: 'brad-api-log-list',
  templateUrl: './api-log-list.component.html',
  styleUrls: ['./api-log-list.component.scss']
})
export class ApiLogListComponent implements OnInit, OnDestroy{


  @ViewChild(MatPaginator, {static: true}) paginator!: MatPaginator;
  @ViewChild(MatSort, {static: true}) sort!: MatSort;

  readonly MENU = MenusConsts.apiLogs;

  isApiLogDetailsInFullscreen = true;
  lastOpenedApiLogDetailsID!: number | null;
  private displayedColumnsOnDetailsMode = ['id', 'logType', 'relatedEntityId', 'logStatus', 'createdAt', 'createdBy'];
  private displayedColumnsOnFullscreenMode = ['id', 'logType', 'logStatus'];

  isLoading = true;
  dataSource: MatTableDataSource<ApiLog> = new MatTableDataSource<ApiLog>([]);
  displayedColumns: string[] = [];
  mobileQuery: MediaQueryList;
  pagination: CsFinPagination = {
    pageSizeOptions: csFinPaginationConsts.pageSizeOptions,
    pageSize: csFinPaginationConsts.defaultPageSize,
    pageIndex: 0,
    totalItems: 0
  };

  activeFilterChips!: Map<string, CsFinActiveFilterChip>;
  auth = authParams;
  apiLogDetailsOpened = false;

  isOverlayOpen$!: Observable<boolean>;
  triggerOrigin!: CdkOverlayOrigin;
  localStorageDetailSelectedTabVariableName = 'apiLogDetailsTabGroupSelectedIndex';
  private _mobileQueryListener: () => void;
  private _onDestroy: Subject<void> = new Subject<void>();

  private filters: ApiLogFilters = {
    page: 1,
    size: csFinPaginationConsts.defaultPageSize
  };
  private lastDisplayedColumns!: string[];
  private statementWithTransactionRequest!: StatementWithTransactionRequest;

  constructor(
    public ref: ChangeDetectorRef,
    public media: MediaMatcher,
    private dialog: MatDialog,
    private route: ActivatedRoute,
    private router: Router,
    private notificationService: NotificationService,
    protected apiLogFacade: ApiLogFacade,
    protected statementFacade: StatementFacade,
    private addRemoveColumnsFacade: CsFinAddRemoveColumnsFacade
  ) {
    this.mobileQuery = media.matchMedia('(max-width: 960px)');
    this._mobileQueryListener = () => ref.detectChanges();
    this.mobileQuery?.addEventListener('change', this._mobileQueryListener);

    CsFinLastUsedColumns.getInstance().initColumns(this.MENU, this.getColumnDetails());
  }

  ngOnInit(): void {
    this.isLoading = true;
    this.subscribeFiltersChange();
    this.subscribeActiveFilterChipsChange();
    this.subscribeDisplayedColumnsChange();
    this.subscribeFullscreenChange();
    this.subscribeSelectedApiLogChange()
    this.subscribeDetailsCloseChange();

    this.displayedColumns = this.displayedColumnsOnDetailsMode;
  }


  ngOnDestroy(): void {
    this.apiLogFacade.filtersChanged({});
    this.mobileQuery?.removeEventListener('change', this._mobileQueryListener);
    this._onDestroy.next();
    this._onDestroy.complete();
    this.addRemoveColumnsFacade.isOverlayOpen.next(false);
    this.apiLogFacade.selectedApiLogChangeBehaviorSubject.next(-1);

  }

  private subscribeFiltersChange(): void {
    this.apiLogFacade.filters$
      .pipe(takeUntil(this._onDestroy))
      .subscribe(async (filters: ApiLogFilters) => {
        if (Object.keys(filters).length > 0 && this.isInApiLogsScreen()) {
          this.closeDetailsSliderIfOpened();
          this.filters = filters;
          this.loadApiLogs();
        }
      });
  }

  private closeDetailsSliderIfOpened(): void {
    if (this.lastOpenedApiLogDetailsID) {
      this.closeApiLogDetails();
    }
  }

  private isInApiLogsScreen(): boolean {
    return window.location.href.includes('api-log');
  }

  private subscribeActiveFilterChipsChange(): void {

    this.apiLogFacade.activeFiltersChips$
      .pipe(takeUntil(this._onDestroy))
      .subscribe((activeFilterChips: Map<string, CsFinActiveFilterChip>) => {
        this.activeFilterChips = activeFilterChips;
      });

  }



  private subscribeDisplayedColumnsChange(): void {
    this.addRemoveColumnsFacade.fetchDisplayedColumns
      .pipe(takeUntil(this._onDestroy))
      .subscribe((columns: CsFinAddRemoveColumns) => {
        setTimeout(() => {
            if (!this.apiLogDetailsOpened) {
              this.lastDisplayedColumns = this.displayedColumns;
              this.displayedColumns = columns.displayedColumns ? columns.displayedColumns : [];
            }
            else {
              this.lastDisplayedColumns = this.displayedColumns;
              this.displayedColumns = this.displayedColumnsOnDetailsMode;
            }

          }
          , 0);
      });
  }

  private subscribeFullscreenChange(): void {
    this.apiLogFacade.fullscreenChangeBehaviorSubject
      .pipe(takeUntil(this._onDestroy))
      .subscribe((isFullscreen: boolean) => {
        this.handleFullscreenChange(isFullscreen);
      });
  }

  private subscribeSelectedApiLogChange(): void {
    this.apiLogFacade.selectedApiLogChangeBehaviorSubject
      .pipe(takeUntil(this._onDestroy))
      .subscribe((apiLogID: number) => {
        if(apiLogID>0 && this.lastOpenedApiLogDetailsID != apiLogID){
          setTimeout(() => {
            this.onOpenItemDetailsClick(apiLogID);
            this.ref.markForCheck();
          }, 0);
        }
      });
  }

  private subscribeDetailsCloseChange(): void {
    this.apiLogFacade.detailsCloseBehaviorSubject
      .pipe(takeUntil(this._onDestroy))
      .subscribe((isClosed: boolean) => {
        if(isClosed){
          this.closeApiLogDetails();
        }
      });
  }


  loadApiLogs() {
    this.filters.selectedFields = this.displayedColumnsOnDetailsMode;
    this.apiLogFacade.getAll(this.filters)
      .pipe(takeUntil(this._onDestroy))
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next:(result: PageResponse<ApiLog>) => {
          this.dataSource = new MatTableDataSource<ApiLog>(result.results);
          this.dataSource.sort = this.sort;
          this.dataSource.paginator = this.paginator;
          this.dataSource.sortingDataAccessor = CsFinSortingDataAccessorHelperService.objectSortingDataAccessor;
          this.pagination.totalItems = result.total;
          this.pagination.pageSize = result.size;
          this.pagination.pageIndex = result.page - 1;
          this.setSort();
        },
        error: (error:HttpErrorResponse) => {
          this.notificationService.errorWithResponse(error);
        }});
  }

  triggerOverlay(trigger: CdkOverlayOrigin): void {
    this.triggerOrigin = trigger;
    this.addRemoveColumnsFacade.isOverlayOpen.next(true);
  }

  private handleFullscreenChange(isFullscreen: boolean): void {
    this.isApiLogDetailsInFullscreen = isFullscreen;
    if(this.isApiLogDetailsInFullscreen){
      this.displayedColumns = this.displayedColumnsOnFullscreenMode
    } else {
      this.displayedColumns = this.displayedColumnsOnDetailsMode;
    }
    this.ref.markForCheck();
  }

  getColumnDetails(): CsFinColumnDetails[] {
    return [
      {position: 0, name: 'ID', code: 'id', isActive: true, isRemovable: false, isDefault: true},
      {position: 1, name: 'Log Type', code: 'logType', isActive: true, isRemovable: false, isDefault: true},
      {position: 2, name: 'Related Entity ID', code: 'relatedEntityId', isActive: true, isRemovable: false, isDefault: true},
      {position: 3, name: 'Log Status', code: 'logStatus', isActive: true, isRemovable: false, isDefault: true},
      {position: 4, name: 'Created At', code: 'createdAt', isActive: true, isRemovable: true, isDefault: true},
      {position: 5, name: 'Created By', code: 'createdBy', isActive: true, isRemovable: true, isDefault: true},
      {position: 6, name: 'actions', code: 'actions', isActive: true, isRemovable: false, isDefault: true}
    ];
  }

  onPageChange(event: any) {
    this.filters.page = event.pageIndex + 1;
    this.filters.size = event.pageSize;
    this.apiLogFacade.filtersChanged(this.filters);
  }

  onActiveFilterRemoveClick(removedChipKey: string): void {
    const filtersToRemove = removedChipKey.split(activeFiltersSeparator);
    if (filtersToRemove) {
      filtersToRemove.forEach((filterKey: string) => {
        // @ts-ignore
        delete this.filters[filterKey];
      })
    }
    this.apiLogFacade.filtersChanged(this.filters);
  }

  private setSort(): void {
    if (!this.sort) {
      return;
    }

    const previousSort = {
      active: this.sort.active,
      direction: this.sort.direction
    };
    if (this.filters.orderField) {
      this.sort.active = this.decodeSortField(this.filters.orderField);
    }
    if (this.filters.orderDirection) {
      this.sort.direction = this.filters.orderDirection.toLowerCase() as SortDirection;
    }

    const sort = {
      active: this.sort.active,
      direction: this.sort.direction
    };
    if (!_.isEqual(previousSort, sort)) {
      this.sort.sortChange.emit(this.sort);
    }
  }

  onSortChange(event: any): void {
    const sortFiltersBefore = {
      orderDirection: this.filters.orderDirection,
      orderField: this.filters.orderField
    };

    this.filters.orderDirection = event.direction?.toUpperCase();
    this.filters.orderField = this.encodeSortField(event.active);

    if(!_.isEqual(sortFiltersBefore, this.filters as SortFilters)){
      this.apiLogFacade.filtersChanged(this.filters);
    }

  }

  private encodeSortField(field: string): string {
    switch (field) {
      case 'id':
        return 'ID';
      case 'logType':
        return 'LOG_TYPE';
      case 'relatedEntityId':
        return 'RELATED_ENTITY_ID';
      case 'logStatus':
        return 'LOG_STATUS';
      case 'createdAt':
        return 'CREATED_AT';
      case 'createdBy':
        return 'CREATED_BY';
      default:
        return field.toUpperCase();
    }
  }

  private decodeSortField(field: string): string {
    switch (field) {
      case 'ID':
        return 'id';
      case 'LOG_TYPE':
        return 'logType';
      case 'RELATED_ENTITY_ID':
        return 'relatedEntityId';
      case 'LOG_STATUS':
        return 'logStatus';
      case 'CREATED_AT':
        return 'createdAt';
      case 'CREATED_BY':
        return 'createdBy';
      default:
        return field.toLowerCase();
    }
  }

  onOpenItemDetailsClick(apiLogID:number):void {
    this.isApiLogDetailsInFullscreen = true;

    if(this.apiLogDetailsOpened){
      this.resetSelectedDetailsTab();
    }
    if(!this.apiLogDetailsOpened){
      this.apiLogDetailsOpened = true;
      this.lastDisplayedColumns = this.displayedColumns;
      this.displayedColumns = this.displayedColumnsOnFullscreenMode;

    } else if(this.lastOpenedApiLogDetailsID === apiLogID){
      this.router.navigate(['.'], {queryParamsHandling: 'preserve', relativeTo: this.route});
      this.closeApiLogDetails();
      return;
    }
    this.lastOpenedApiLogDetailsID = apiLogID;
    this.router.navigate([`./${apiLogID}`], {queryParamsHandling: 'preserve', relativeTo: this.route});

  }

  resetSelectedDetailsTab(): void {
    const detailTabGroupSelectedIndex = localStorage.getItem(this.localStorageDetailSelectedTabVariableName);
    if(detailTabGroupSelectedIndex === '1')
      localStorage.setItem(this.localStorageDetailSelectedTabVariableName, '0');
  }

  closeApiLogDetails(): void {
    this.apiLogDetailsOpened = false;
    this.displayedColumns = this.lastDisplayedColumns;
    this.lastOpenedApiLogDetailsID = null;
  }

  isItemDetailsOpened(apiLogID: number): boolean {
    return apiLogID === this.lastOpenedApiLogDetailsID;
  }

  onClickRetryRequest(event: any, id: number) {
    event.stopPropagation();
    const dialogRef = this.dialog.open(CsFinConfirmationDialogComponent, {
      data: {
        titleKey: 'CONFIRMATION_DIALOG.MESSAGES.ABOUT_TO_RETRY_STATEMENT',
        descriptionKey: 'CONFIRMATION_DIALOG.ACTIONS.ARE_YOU_SURE',
        cancelActionKey: 'CONFIRMATION_DIALOG.BUTTONS.NO',
        confirmActionKey: 'CONFIRMATION_DIALOG.BUTTONS.YES'
      },
      width: '600px'
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadApiLogDetails(id)
      }
    });

  }

  onClickAcknowledgeFailure(event: any, id: number) {
    event.stopPropagation();
    const dialogRef = this.dialog.open(CsFinConfirmationDialogComponent, {
      data: {
        titleKey: 'CONFIRMATION_DIALOG.MESSAGES.ABOUT_TO_ACKNOWLEDGE_API_LOG_FAILURE',
        descriptionKey: 'CONFIRMATION_DIALOG.ACTIONS.ARE_YOU_SURE',
        cancelActionKey: 'CONFIRMATION_DIALOG.BUTTONS.NO',
        confirmActionKey: 'CONFIRMATION_DIALOG.BUTTONS.YES'
      },
      width: '600px'
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.onAcknowledgeFailure(id)
      }
    });

  }

  onEventRetryRequest(request: StatementWithTransactionRequest) {

    this.statementFacade.create(request).subscribe({
      next: () => {
        this.notificationService.successTranslated('STATEMENTS.NOTIFICATIONS.MESSAGES.CREATE_STATEMENT_SUCCESS',
            {statementId: this.statementWithTransactionRequest.account_statement.statement_id});
      },
      error: (error: HttpErrorResponse) => {
        this.notificationService.errorWithResponse(error);
      }
    });

  }

  onAcknowledgeFailure(apiLog: number) {

    this.apiLogFacade.acknowledgeFailure(apiLog).subscribe({
      next: (apiLog: ApiLog) => {
        this.notificationService.successTranslated('API_LOG.NOTIFICATIONS.MESSAGES.ACKNOWLEDGE_SUCCESS',
            {apiLogId: apiLog?.id});
        this.loadApiLogs()
      },
      error: (error: HttpErrorResponse) => {
        this.notificationService.errorWithResponse(error);
      }
    });

  }

  loadApiLogDetails(id: number): void {
    this.apiLogFacade.getById(id)
    .pipe(takeUntil(this._onDestroy))
    .subscribe({
      next: (apiLog: ApiLog) => {
        if (apiLog.logType === "STATEMENT_CREATION") {
          this.statementWithTransactionRequest = this.createStatementWithTransactionRequest(apiLog.request as string);
          apiLog.request = JSON.stringify(this.statementWithTransactionRequest);
          this.onEventRetryRequest(this.statementWithTransactionRequest)
        }
      },
      error: (error: HttpErrorResponse) => {
        this.notificationService.errorWithResponse(error);
      }
    });
  }

  createStatementWithTransactionRequest(jsonString: string): StatementWithTransactionRequest {
    return JSON.parse(jsonString) as StatementWithTransactionRequest;
  }

  isFailedApiLog(apiLog:ApiLog){
    return apiLog.logStatus=='FAILURE';
  }

  protected readonly bradAuthTarget = bradAuthTarget;
}
