<mat-card class="details-wrapper main">
  <span class="actions">
    <span fxFlex></span>
    <mat-icon id="fullscreen-toggle" (click)="onFullscreenClick()" fxShow [fxShow.xs]="false" [fxShow.sm]="false" data-cy="toggle-details-fullscreen-btn">
      {{isFullscreen ? 'fullscreen_exit' : 'fullscreen'}}
    </mat-icon>
    <mat-icon (click)="onCloseClick()" data-cy="close-details-btn">close</mat-icon>
  </span>
  <mat-tab-group class="details-tab-group" fitInkBarToContent="false">

    <!-- details -->
    <mat-tab [label]="'API_LOG.DETAILS.TABS.LABELS.API_LOG_DETAILS' | translate">
      <brad-api-log-details-info [detailedApiLog]="apiLog"></brad-api-log-details-info>
    </mat-tab>

    <!-- request info -->
    <mat-tab [label]="'API_LOG.DETAILS.TABS.LABELS.API_LOG_REQUEST' | translate">
      <brad-api-log-request-info [apiLog]="apiLog"
                                 (retryRequest)="onEventRetryRequest($event)"
                                 (acknowledgeEvent)="onAcknowledgeFailure($event)">
      </brad-api-log-request-info>
    </mat-tab>

    <!-- response info -->
    <mat-tab [label]="'API_LOG.DETAILS.TABS.LABELS.API_LOG_RESPONSE' | translate">
      <brad-api-log-response-info
        [response]="apiLog.response"
        [logStatus]="apiLog.logStatus"></brad-api-log-response-info>
    </mat-tab>

  </mat-tab-group>

</mat-card>
