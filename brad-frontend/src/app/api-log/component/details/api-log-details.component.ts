import {Component, On<PERSON><PERSON>roy, OnInit} from '@angular/core';
import {Subject} from "rxjs";
import {ApiLogFacade} from "../../facade/api-log.facade";
import {ActivatedRoute, Params, Router} from "@angular/router";
import {takeUntil} from "rxjs/operators";
import {ApiLog} from "../../../entities/api-log/api-log";
import {HttpErrorResponse} from "@angular/common/http";
import {NotificationService} from "../../../api/service/notification.service";
import type {StatementWithTransactionRequest} from "../../../entities/statement/statement-with-transaction-request";
import {StatementFacade} from "../../../accounts/facade/statement.facade";

@Component({
  selector: 'brad-api-log-details',
  templateUrl: './api-log-details.component.html',
  styleUrls: ['./api-log-details.component.scss']
})
export class ApiLogDetailsComponent implements OnInit, OnDestroy {

  isFullscreen = true;
  apiLogID!: number;
  apiLog!: ApiLog;
  statementWithTransactionRequest!: StatementWithTransactionRequest;

  private _onDestroy:Subject<void> = new Subject<void>();

  constructor(
    private apiLogFacade: ApiLogFacade,
    private router: Router,
    private route: ActivatedRoute,
    private notificationService: NotificationService,
    private statementFacade: StatementFacade
  ) {
  }
  ngOnInit(): void {
    this.route.params
      .pipe(takeUntil(this._onDestroy))
      .subscribe((params: Params) => {
        this.apiLogID = Number(params['apiLogID']);
        if (this.apiLogID != null) {
          this.apiLogFacade.selectedApiLogChangeBehaviorSubject.next(this.apiLogID);
        }
      });

    this.apiLogFacade.selectedApiLogChangeBehaviorSubject
      .pipe(takeUntil(this._onDestroy))
      .subscribe((apiLogID: number) => {
        this.apiLogID = apiLogID;
        this.loadApiLogDetails();
      });
  }

  ngOnDestroy() {
    this._onDestroy.next();
    this._onDestroy.complete();
    this.apiLogFacade.detailsCloseBehaviorSubject.next(true);
  }

  onFullscreenClick(): void {
    this.isFullscreen = !this.isFullscreen;
    this.apiLogFacade.fullscreenChangeBehaviorSubject.next(this.isFullscreen);
  }

  onCloseClick(): void {
    this.apiLogFacade.detailsCloseBehaviorSubject.next(true);
    this.router.navigate(['api-log'], {queryParams: this.route.snapshot.queryParams});
  }

  loadApiLogDetails(): void {
    if (this.apiLogID != null) {
      this.apiLogFacade.getById(this.apiLogID)
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (apiLog: ApiLog) => {
            if (apiLog.logType === "STATEMENT_CREATION") {
              this.statementWithTransactionRequest = this.createStatementWithTransactionRequest(apiLog.request as string);
              apiLog.request = JSON.stringify(this.statementWithTransactionRequest);
            } else {
              apiLog.request = JSON.stringify(apiLog.request);
            }

            this.apiLog = apiLog;
          },
          error: (error:HttpErrorResponse) => {
            this.notificationService.errorWithResponse(error);
          }
        });
    }

  }

  createStatementWithTransactionRequest(jsonString:string): StatementWithTransactionRequest {
    return JSON.parse(jsonString) as StatementWithTransactionRequest;
  }

  onEventRetryRequest(event:number) {
    if (event == this.apiLogID) {
      this.statementFacade.create(this.statementWithTransactionRequest).subscribe({
        next: () => {
          this.notificationService.successTranslated('STATEMENTS.NOTIFICATIONS.MESSAGES.CREATE_STATEMENT_SUCCESS',
            {statementId: this.statementWithTransactionRequest.account_statement.statement_id});
       },
        error: (error:HttpErrorResponse) => {
          this.notificationService.errorWithResponse(error);
        }
      });
    }
  }

  onAcknowledgeFailure(event: number) {
    if (event == this.apiLogID) {
      this.apiLogFacade.acknowledgeFailure(event).subscribe({
        next: (apiLog: ApiLog) => {
          this.apiLog = apiLog;
          this.notificationService.successTranslated('API_LOG.NOTIFICATIONS.MESSAGES.ACKNOWLEDGE_SUCCESS',
              {apiLogId: event});
        },
        error: (error: HttpErrorResponse) => {
          this.notificationService.errorWithResponse(error);
        }
      });
    }
  }

}
