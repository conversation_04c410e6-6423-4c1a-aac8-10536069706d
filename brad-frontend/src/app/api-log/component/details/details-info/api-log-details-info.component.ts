import {Component, Input, OnChanges, OnDestroy, OnInit} from '@angular/core';
import {ApiLog} from "../../../../entities/api-log/api-log";
import {Subject} from "rxjs";
import {ApiLogFacade} from "../../../facade/api-log.facade";

@Component({
  selector: 'brad-api-log-details-info',
  templateUrl: './api-log-details-info.component.html',
  styleUrls: ['./api-log-details-info.component.scss']
})
export class ApiLogDetailsInfoComponent implements OnInit, OnDestroy, OnChanges {

  @Input() detailedApiLog!: ApiLog;

  constructor(
    protected apiLogFacade: ApiLogFacade
  ) {
  }

  isLoading = false;
  private _onDestroy: Subject<void> = new Subject<void>();

  ngOnInit(): void {
    this.isLoading = true;
  }

  ngOnChanges() {
    if(this.detailedApiLog){
      this.isLoading = false;
    }
  }

  ngOnDestroy(): void {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

}
