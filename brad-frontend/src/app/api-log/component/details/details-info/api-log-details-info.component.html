<div class="details-information">
  <div *ngIf="isLoading" class="loading">
    <mat-progress-spinner mode="indeterminate" diameter="75" strokeWidth="5"></mat-progress-spinner>
    <span class="label">{{'GENERAL.DETAILS.LOADING' | translate}}</span>
  </div>
  <div *ngIf="!isLoading && !detailedApiLog" class="detail-failed">
    <mat-icon>report_problem</mat-icon>
    <span class="label">{{'API_LOGS.DETAILS.ERRORS.UNABLE_TO_FIND_DETAILS' | translate}}</span>
  </div>

  <main *ngIf="!isLoading && detailedApiLog">
    <section class="header">
      <button mat-mini-fab disabled>
        <mat-icon>compare</mat-icon>
      </button>
      <span class="item-name">{{detailedApiLog.id}}</span>
    </section>

    <ul class="description">
      <li>
        <span class="field" data-cy="api-log-details-field-log-type">{{'API_LOG.FIELDS.LOG_TYPE' | translate}}: </span>
        <span class="value" data-cy="api-log-details-value-log-type">{{ 'API_LOG.TYPES.' + detailedApiLog?.logType | translate}}</span>
      </li>
      <li>
        <span class="field" data-cy="api-log-details-field-log-status">{{'API_LOG.FIELDS.LOG_STATUS' | translate}}: </span>
        <span class="value" data-cy="api-log-details-value-log-status">{{detailedApiLog.logStatus}}</span>
      </li>
      <li>
        <span class="field" data-cy="api-log-details-field-related-entity">{{'API_LOG.FIELDS.RELATED_ENTITY_ID' | translate}}: </span>
        <pre class="value" data-cy="api-log-details-value-related-entity">{{apiLogFacade.getRelatedEntities(detailedApiLog?.relatedEntityId)}}</pre>
      </li>
    </ul>
    <ul class="additional-information">
      <li>
        <span class="field" data-cy="api-log-details-field-created-at">{{'GENERAL.FIELDS.CREATED_AT' | translate}}: </span>
        <span class="value" data-cy="api-log-details-value-created-at">{{detailedApiLog.createdAt | date:'short'}}</span>
      </li>
      <li>
        <span class="field" data-cy="api-log-details-field-created-by">{{'GENERAL.FIELDS.CREATED_BY' | translate}}: </span>
        <span class="value" data-cy="api-log-details-value-created-by">{{detailedApiLog.createdBy}}</span>
      </li>
      <li>
        <span class="field" data-cy="api-log-details-field-updated-at">{{'GENERAL.FIELDS.UPDATED_AT' | translate}}: </span>
        <span class="value" data-cy="api-log-details-value-updated-at">{{detailedApiLog.updatedAt | date:'short'}}</span>
      </li>
      <li>
        <span class="field" data-cy="api-log-details-field-updated-by">{{'GENERAL.FIELDS.UPDATED_BY' | translate}}: </span>
        <span class="value" data-cy="api-log-details-value-updated-by">{{detailedApiLog.updatedBy}}</span>
      </li>
    </ul>
  </main>
</div>
