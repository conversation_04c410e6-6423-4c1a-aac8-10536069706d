<mat-card class="payloads">
  <button *ngIf="apiLog.logType=='BANK_STATEMENT_CREATION'"
          class="floating-action-btn" mat-mini-fab color="primary" id="retry-request-btn"
          (click)="onClickRetryRequest($event, apiLog.id!)">
    <mat-icon>
      refresh
    </mat-icon>
  </button>
  <button
      *ngIf="apiLog.logStatus=='FAILURE' && (apiLog.logType=='API_BANK_STATEMENT_FETCH' || apiLog.logType=='SFTP_BANK_STATEMENT_IMPORT') "
      class="floating-action-btn" mat-mini-fab color="primary" id="acknowledge-failure-btn"
      (click)="onClickAcknowledgeFailure($event, apiLog.id!)">
    <mat-icon>
      verified-user
    </mat-icon>
  </button>
  <pre data-cy="api-log-details-value-request">{{ apiLog.request | jsonFormatter }}</pre>
</mat-card>
