import {Component, EventEmitter, Input, Output} from '@angular/core';
import {MatDialog} from "@angular/material/dialog";
import {CsFinConfirmationDialogComponent} from "@jumia-cs-fin/common";
import {ApiLog} from "../../../../entities/api-log/api-log";

@Component({
  selector: 'brad-api-log-request-info',
  templateUrl: './api-log-request-info.component.html',
  styleUrls: ['./api-log-request-info.component.scss']
})
export class ApiLogRequestInfoComponent {

  @Input() apiLog!: ApiLog;
  @Output() retryRequest = new EventEmitter<number>();
  @Output() acknowledgeEvent = new EventEmitter<number>();

  constructor(private dialog: MatDialog) {
  }

  onClickRetryRequest(event:any, id: number) {
    event.stopPropagation();
    const dialogRef = this.dialog.open(CsFinConfirmationDialogComponent, {
      data: {
        titleKey: 'CONFIRMATION_DIALOG.MESSAGES.ABOUT_TO_RETRY_STATEMENT',
        descriptionKey: 'CONFIRMATION_DIALOG.ACTIONS.ARE_YOU_SURE',
        cancelActionKey: 'CONFIRMATION_DIALOG.BUTTONS.NO',
        confirmActionKey: 'CONFIRMATION_DIALOG.BUTTONS.YES'
      },
      width: '600px'
    });

    dialogRef.afterClosed().subscribe( result => {
      if (result) {
        this.retryRequest.emit(id);
      }
    });

  }

  onClickAcknowledgeFailure(event:any, id: number) {
    event.stopPropagation();
    const dialogRef = this.dialog.open(CsFinConfirmationDialogComponent, {
      data: {
        titleKey: 'CONFIRMATION_DIALOG.MESSAGES.ABOUT_TO_ACKNOWLEDGE_API_LOG_FAILURE',
        descriptionKey: 'CONFIRMATION_DIALOG.ACTIONS.ARE_YOU_SURE',
        cancelActionKey: 'CONFIRMATION_DIALOG.BUTTONS.NO',
        confirmActionKey: 'CONFIRMATION_DIALOG.BUTTONS.YES'
      },
      width: '600px'
    });

    dialogRef.afterClosed().subscribe( result => {
      if (result) {
        this.acknowledgeEvent.emit(id);
      }
    });

  }

}
