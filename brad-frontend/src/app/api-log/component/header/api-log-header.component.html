<mat-toolbar id="header-toolbar">
  <button mat-icon-button (click)="sidenav.toggle()" fxHide [fxShow.xs]="true" [fxShow.sm]="true">
    <mat-icon>menu</mat-icon>
  </button>
  <span class="page-title">{{ 'API_LOG.TITLE' | translate }}</span>

  <section class="search-bar" [formGroup]="form">
    <!-- filter text (code) -->
    <mat-form-field id="search" class="change-header" appearance="outline" fxShow [fxShow.xs]="false" [fxShow.sm]="false"
                    cdkOverlayOrigin #trigger="cdkOverlayOrigin">
      <mat-icon matPrefix>search</mat-icon>
      <mat-label class="theLabel">{{'API_LOG.DETAILS.SEARCH_BAR' | translate}}</mat-label>
      <input type="text" #input matInput [formControl]="filterTextFormControl"
             (keyup.enter)="submit(input)">
      <mat-icon matSuffix id="filters" (click)="triggerOverlay()">filter_list</mat-icon>
    </mat-form-field>

    <button mat-raised-button color="primary" id="apply-search-btn" class="raised-primary-btn" fxShow [fxShow.xs]="false"
            [fxShow.sm]="false" [disabled]="noFiltersSelected()" (click)="submit()">
      {{ 'GENERAL.BUTTONS.LABELS.SEARCH' | translate }}
    </button>
  </section>

  <span fxFlex fxHide [fxShow.xs]="true" [fxShow.sm]="true"></span>
  <button mat-icon-button aria-label="Filter accounts" fxHide [fxShow.xs]="true" [fxShow.sm]="true"
          *ngIf="showFilters" id="show-mobile-filters">
    <mat-icon (click)="triggerOverlay()">filter_list</mat-icon>
  </button>
  <ng-template cdkConnectedOverlay class="template"
               [cdkConnectedOverlayHasBackdrop]="true"
               [cdkConnectedOverlayOrigin]="trigger"
               [cdkConnectedOverlayOpen]="(isOpen$ | async) || false">
    <div class="filters-overlay">
      <div class="filters-header">
        <mat-icon fxHide [fxShow.xs]="true" [fxShow.sm]="true" (click)="closeOverlay()">close</mat-icon>
        <p class="filters-title">{{'GENERAL.FILTERS.TITLE' | translate}}</p>
        <button fxHide class="flat-white-btn filters-cancel-btn" [disabled]="noFiltersSelected()" [fxShow.xs]="true"
                [fxShow.sm]="true"
                mat-flat-button (click)="clearFilters()" id="clear-btn">
          {{ 'GENERAL.BUTTONS.CLEAR' | translate }}
        </button>
      </div>

      <div class="filters-container">


        <!-- log type -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-type-entity-filter"
                        (click)="loadLogTypes()">
          <mat-label>{{'API_LOG.FIELDS.LOG_TYPE' | translate}}</mat-label>
          <mat-select [formControl]="logTypeFormControl" multiple>
            <ng-container *ngIf="filteredLogTypeList != null; else loadingLogType">
              <mat-option>
                <ngx-mat-select-search [formControl]="logTypeSearchFormControl"
                                       [noEntriesFoundLabel]="'GENERAL.FILTERS.NO_MATCHING_RESULTS_FOUND' | translate"
                                       [placeholderLabel]="'GENERAL.FILTERS.SEARCH' | translate">
                </ngx-mat-select-search>
              </mat-option>
              <mat-option *ngFor="let type of filteredLogTypeList" [value]="type">
                {{type}}
              </mat-option>
              <button mat-button color="primary" class="clear-selection-btn"
                      [disabled]="!logTypeFormControl.value?.length"
                      (click)="logTypeFormControl.reset([])">
                {{'GENERAL.FILTERS.ACTIONS.CLEAR_SELECTION' | translate}}
              </button>
            </ng-container>
            <ng-template #loadingLogType>
              <mat-option disabled>
                <div class="filters-loading-container">
                  <span>{{'GENERAL.FILTERS.LOADING' | translate}}</span>
                  <mat-spinner diameter="20"></mat-spinner>
                </div>
              </mat-option>
            </ng-template>
          </mat-select>
        </mat-form-field>

        <!-- relatedEntityId -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-related-entity-id-filter">
          <mat-label>{{'API_LOG.FIELDS.RELATED_ENTITY_ID' | translate}}</mat-label>
          <input matInput [formControl]="relatedEntityIdFormControl">
        </mat-form-field>

        <!-- log status -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-status-entity-filter"
                        (click)="loadLogStatus()">
          <mat-label>{{'API_LOG.FIELDS.LOG_STATUS' | translate}}</mat-label>
          <mat-select [formControl]="logStatusFormControl" multiple>
            <ng-container *ngIf="filteredLogStatusList != null; else loadingLogStatus">
              <mat-option>
                <ngx-mat-select-search [formControl]="logStatusSearchFormControl"
                                       [noEntriesFoundLabel]="'GENERAL.FILTERS.NO_MATCHING_RESULTS_FOUND' | translate"
                                       [placeholderLabel]="'GENERAL.FILTERS.SEARCH' | translate">
                </ngx-mat-select-search>
              </mat-option>
              <mat-option *ngFor="let status of filteredLogStatusList" [value]="status">
                {{status}}
              </mat-option>
              <button mat-button color="primary" class="clear-selection-btn"
                      [disabled]="!logStatusFormControl.value?.length"
                      (click)="logStatusFormControl.reset([])">
                {{'GENERAL.FILTERS.ACTIONS.CLEAR_SELECTION' | translate}}
              </button>
            </ng-container>
            <ng-template #loadingLogStatus>
              <mat-option disabled>
                <div class="filters-loading-container">
                  <span>{{'GENERAL.FILTERS.LOADING' | translate}}</span>
                  <mat-spinner diameter="20"></mat-spinner>
                </div>
              </mat-option>
            </ng-template>
          </mat-select>
        </mat-form-field>

        <!-- created at -->
        <mat-form-field class="filter-field" appearance="outline" data-cy="input-created-at-filter">
          <mat-label>{{'API_LOG.FIELDS.CREATED_AT' | translate}}</mat-label>
          <mat-date-range-input [rangePicker]="picker">
            <input matStartDate [formControl]="createdAtStartFormControl" placeholder="Start date">
            <input matEndDate [formControl]="createdAtEndFormControl" placeholder="End date">
          </mat-date-range-input>
          <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
          <mat-date-range-picker #picker></mat-date-range-picker>
        </mat-form-field>

      </div>

      <div class="filters-actions">
        <button class="flat-white-btn filters-cancel-btn" [disabled]="noFiltersSelected()" mat-flat-button fxShow
                (click)="clearFilters()" [fxShow.xs]="false" [fxShow.sm]="false">
          {{'GENERAL.FILTERS.ACTIONS.CLEAR' | translate}}
        </button>
        <button class="raised-primary-btn filters-apply-btn" [disabled]="noFiltersSelected()" mat-raised-button
                color="primary"
                (click)="submit()">
          {{'GENERAL.FILTERS.ACTIONS.APPLY' | translate}}
        </button>
      </div>

    </div>

  </ng-template>

</mat-toolbar>
