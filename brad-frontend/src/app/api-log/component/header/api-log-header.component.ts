import {ChangeDetectorRef, Component, Input, OnDestroy, OnInit, ViewChild, ViewEncapsulation} from '@angular/core';
import {FormControl, FormGroup} from "@angular/forms";
import {BehaviorSubject, Observable, Subject, tap} from "rxjs";
import {CdkConnectedOverlay} from "@angular/cdk/overlay";
import {CsFinActiveFiltersFacade, CsFinApiService, CsFinSidenavService} from "@jumia-cs-fin/common";
import {ActivatedRoute, Router} from "@angular/router";
import {ApiLogFacade} from "../../facade/api-log.facade";
import {takeUntil} from "rxjs/operators";
import {ApiLogFilters} from "../../../entities/api-log/api-log-filters";
import * as _ from "lodash";

@Component({
  selector: 'brad-api-log-header',
  templateUrl: './api-log-header.component.html',
  styleUrls: ['./api-log-header.component.scss', '../../../../assets/brad-custom.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ApiLogHeaderComponent implements OnInit, OnDestroy {
  @Input() showFilters!: boolean;
  filterTextFormControl!: FormControl;
  queryParams = {};

  form!: FormGroup;

  logTypeFormControl!: FormControl;
  relatedEntityIdFormControl!: FormControl;
  logStatusFormControl!: FormControl;
  createdAtStartFormControl!: FormControl;
  createdAtEndFormControl!: FormControl;

  isOpen$!: Observable<boolean>;
  isRefreshing = false;
  isInitializing = false;

  logTypeSearchFormControl = new FormControl();
  logTypeList!: string[];
  filteredLogTypeList!: string[];

  logStatusSearchFormControl = new FormControl();
  logStatusList!: string[];
  filteredLogStatusList!: string[];

  private readonly refreshTimeout = 2000;
  private _onDestroy: Subject<void> = new Subject<void>();
  private _isOpen!: BehaviorSubject<boolean>;

  @ViewChild(CdkConnectedOverlay, {static: true})
  private connectedOverlay!: CdkConnectedOverlay;

  constructor(public ref: ChangeDetectorRef,
              public sidenav: CsFinSidenavService,
              private activatedRoute: ActivatedRoute,
              private activeFiltersFacade: CsFinActiveFiltersFacade,
              private router: Router,
              private apiService: CsFinApiService,
              private apiLogFacade: ApiLogFacade) { }

  async ngOnInit(): Promise<void> {
    this.isInitializing = true;
    this.initializeOverlay();
    this.initFiltersSearch();
    this.subscribeUrlParamsChanges();
    this.subscribeActiveFiltersChange();
    this.subscribeFiltersChange();
  }

  ngOnDestroy(): void {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  private subscribeUrlParamsChanges(): void {
    this.activatedRoute.queryParams
      .pipe(takeUntil(this._onDestroy))
      .subscribe(params => this.initializeFormData(params));
  }

  private subscribeActiveFiltersChange(): void {
    this.activeFiltersFacade.openFiltersOverlay
      .pipe(takeUntil(this._onDestroy))
      .subscribe(value => this._isOpen.next(value));
  }

  private subscribeFiltersChange(): void {
    this.apiLogFacade.filters$
      .pipe(takeUntil(this._onDestroy))
      .subscribe((filters:ApiLogFilters) => {
        if(!this.isInitializing) {
          this.updateFormData(filters);
          this.updateMissingUrlFilters(filters);
        }
      });
  }

  private initFiltersSearch(): void {
    this.initLogTypesSearch();
    this.initLogStatusSearch();
  }

  private initializeOverlay(): void {
    this._isOpen = new BehaviorSubject<boolean>(false);
    this.connectedOverlay.backdropClass = 'cdk-overlay-transparent-backdrop';
    this.connectedOverlay.panelClass = 'filters-overlay-panel';
    this.connectedOverlay.backdropClick
      .pipe(tap(() => this._isOpen.next(false)))
      .subscribe();
    this.isOpen$ = this._isOpen.asObservable();
  }

  private initLogStatusSearch(): void {
    this.logStatusSearchFormControl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe((value: String) => {
        const searchTerm = value.trim();
        if (searchTerm) {
          this.filteredLogStatusList = this.logStatusList.filter((status) => {
            return status.toLowerCase().includes(searchTerm.toLowerCase());
          })
        } else {
          this.filteredLogStatusList = this.logStatusList;
        }
      });
  }

  private initLogTypesSearch(): void {
    this.logTypeSearchFormControl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe((value: String) => {
        const searchTerm = value.trim();
        if (searchTerm) {
          this.filteredLogTypeList = this.logTypeList.filter((type) => {
            return type.toLowerCase().includes(searchTerm.toLowerCase());
          })
        } else {
          this.filteredLogTypeList = this.logTypeList;
        }
      });
  }

  clearFilters(): void {
    this.form.reset('', {emitEvent: false});
    this.apiLogFacade.filtersChanged({});
  }

  submit(input?: HTMLInputElement) {
    this.apiLogFacade.filtersChanged(this.getFormValues());
    const formQueryParams = this.apiService.buildQueryParams(this.getFormValues());
    if (_.isEqual(this.queryParams, formQueryParams)) {
      this.isRefreshing = true;
      this.ref.markForCheck();
      setTimeout(() => {
        this.isRefreshing = false;
        this.ref.markForCheck();
      }, this.refreshTimeout);
    } else {
      input?.blur();
      this.router.navigate(['/api-log'], {queryParams: formQueryParams});
    }
    this.closeOverlay();

  }

  triggerOverlay(): void {
    this._isOpen.next(!this._isOpen.value);
  }

  closeOverlay(): void {
    this._isOpen.next(false);
  }

  private getFormValues(): ApiLogFilters {
    return this.form.value as ApiLogFilters;
  }

  private initializeFormData(params:any): void {
    this.queryParams = params;
    if(!this.form) {
      this.form = new FormGroup({});
      this.initializeFormControlsAndFilters(params);
    }
  }

  private initializeFormControlsAndFilters(params:any): void {
    const filters: ApiLogFilters = {};

    this.filterTextFormControl = new FormControl(params.filterText);
    filters.filterText = params.filterText;

    let logType: string[] = params.logType === undefined ? [] : params.logType.split(',');
    this.logTypeFormControl = new FormControl(logType);
    filters.logType = logType;

    this.relatedEntityIdFormControl = new FormControl(params.relatedEntityId);
    filters.relatedEntityId = params.relatedEntityId;

    let logStatus: string[] = params.logStatus === undefined ? [] : params.logStatus.split(',');
    this.logStatusFormControl = new FormControl(logStatus);
    filters.logStatus = logStatus;

    this.createdAtStartFormControl = new FormControl(params.createdAtStart);
    filters.createdAtStart = params.createdAtStart;

    this.createdAtEndFormControl = new FormControl(params.createdAtEnd);
    filters.createdAtEnd = params.createdAtEnd;

    filters.orderField = params.orderField;
    filters.orderDirection = params.orderDirection;
    filters.page = params.page;
    filters.size = params.size;

    Promise.all([
      this.applyLogTypeFilter(params, filters),
      this.applyLogStatusFilter(params, filters)
    ]).then(() => {
      this.setFormControlsToForm();
      this.apiLogFacade.filtersChanged(filters);
      this.updateMissingUrlFilters(filters);
      this.isInitializing = false;
    });

  }

  private applyLogTypeFilter(params: any, filters: ApiLogFilters): Promise<void> {
    return new Promise<void>(async (resolve) => {
      this.logTypeFormControl = new FormControl();
      if(!params.logType) {
        resolve();
        return;
      }
      await this.loadLogTypes();

      let logTypeArray = Array.isArray(params.logType?.split(',')) ?
        params.logType?.split(',') :
        [params.logType];
      filters.logType = this.logTypeList.filter((type: string) => logTypeArray.includes(type));
      this.logTypeFormControl.setValue(filters.logType)
      resolve();
    });
  }

  private applyLogStatusFilter(params: any, filters: ApiLogFilters): Promise<void> {
    return new Promise<void>(async (resolve) => {
      this.logStatusFormControl = new FormControl();
      if(!params.logStatus) {
        resolve();
        return;
      }

      await this.loadLogStatus();
      let logStatusArray = Array.isArray(params.logStatus?.split(',')) ?
        params.logStatus?.split(',') :
        [params.logStatus];
      filters.logStatus = this.logStatusList.filter((status: string) => logStatusArray.includes(status));
      this.logStatusFormControl.setValue(filters.logStatus)
      resolve();
    });
  }

  private updateFormData (params: ApiLogFilters): void {
    this.filterTextFormControl.setValue(params.filterText, {emitEvent: false});
    this.logTypeFormControl.setValue(params.logType, {emitEvent: false});
    this.relatedEntityIdFormControl.setValue(params.relatedEntityId, {emitEvent: false});
    this.logStatusFormControl.setValue(params.logStatus, {emitEvent: false});
    this.createdAtStartFormControl.setValue(params.createdAtStart, {emitEvent: false});
    this.createdAtEndFormControl.setValue(params.createdAtEnd, {emitEvent: false});
  }

  private updateMissingUrlFilters(filters: ApiLogFilters): void {
    const formQueryParams = this.apiService.buildQueryParams(filters);
    if (!_.isEqual(this.queryParams, formQueryParams)) {
      this.router.navigate(['api-log'], {queryParams: formQueryParams});
    }
  }

  private setFormControlsToForm(): void {
    this.form.addControl(this.apiLogFacade.filterTextKey, this.filterTextFormControl);
    this.form.addControl(this.apiLogFacade.logTypeKey, this.logTypeFormControl);
    this.form.addControl(this.apiLogFacade.relatedEntityIdKey, this.relatedEntityIdFormControl);
    this.form.addControl(this.apiLogFacade.statusKey, this.logStatusFormControl);
    this.form.addControl(this.apiLogFacade.createdAtStartKey, this.createdAtStartFormControl);
    this.form.addControl(this.apiLogFacade.createdAtEndKey, this.createdAtEndFormControl);
  }

  noFiltersSelected() {
    return !!this.form.errors || this.isRefreshing;
  }

  loadLogTypes(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if(!this.logTypeList) {
        this.apiLogFacade.getLogTypes()
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (types: string[]) => {
              this.logTypeList = types;
              this.filteredLogTypeList = this.logTypeList;
              resolve();
            }, error: (error) => reject(error)
          });
      } else {
        resolve();
      }
    });
  }

  loadLogStatus(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if(!this.logStatusList) {
        this.apiLogFacade.getLogStatus()
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (status: string[]) => {
              this.logStatusList = status;
              this.filteredLogStatusList = this.logStatusList;
              resolve();
            }, error: (error) => reject(error)
          });
      } else {
        resolve();
      }
    });
  }

}
