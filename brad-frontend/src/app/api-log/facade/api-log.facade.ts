import {Injectable} from "@angular/core";
import {BehaviorSubject, Observable, of, Subject} from "rxjs";
import {getPropertyKey} from "../../shared/service/ts-utils.service";
import {activeFiltersSeparator, CsFinActiveFilterChip} from "@jumia-cs-fin/common";

import {ApiLogFilters} from "../../entities/api-log/api-log-filters";
import {ApiLogApiService} from "../../api/service/api-log-api.service";
import {PageResponse} from "../../entities/page-response";
import {ApiLog} from "../../entities/api-log/api-log";
import {TransactionFilters} from "../../entities/transaction/transaction-filters";

@Injectable({providedIn: 'root'})
export class ApiLogFacade {

  forceRefresh: Subject<boolean> = new Subject<boolean>();

  public fullscreenChangeBehaviorSubject = new BehaviorSubject<boolean>(false);
  public detailsCloseBehaviorSubject = new BehaviorSubject<boolean>(false);
  public selectedApiLogChangeBehaviorSubject = new BehaviorSubject<number>(-1);

  readonly filterTextKey = getPropertyKey<ApiLogFilters>(p => p.filterText);
  readonly logTypeKey = getPropertyKey<ApiLogFilters>(p => p.logType);
  readonly relatedEntityIdKey = getPropertyKey<ApiLogFilters>(p => p.relatedEntityId);
  readonly statusKey = getPropertyKey<ApiLogFilters>(p => p.logStatus);
  readonly createdAtStartKey = getPropertyKey<TransactionFilters>(p => p.createdAtStart);
  readonly createdAtEndKey = getPropertyKey<TransactionFilters>(p => p.createdAtEnd);


  private readonly activeFiltersConfigMap = new Map<string, any>([
    [
      this.filterTextKey,
      (filterText: string) => {
        return {labelKey: 'GENERAL.FIELDS.FILTER_TEXT', displayText: filterText}
      }
    ],
    [
      this.logTypeKey,
      (types: string[]) => {
        return {
          labelKey: 'API_LOG.FIELDS.LOG_TYPE',
          displayText: types.map((type:string) => type).join(activeFiltersSeparator)
        }
      }
    ],
    [
      this.relatedEntityIdKey,
      (relatedEntityId: string) => {
        return {labelKey: 'API_LOG.FIELDS.RELATED_ENTITY_ID', displayText: relatedEntityId}
      }
    ],
    [
      this.statusKey,
      (statuses: string[]) => {
        return {
          labelKey: 'API_LOG.FIELDS.LOG_STATUS',
          displayText: statuses.map((status:string) => status).join(activeFiltersSeparator)
        }
      }
    ],
    [
      this.createdAtStartKey,
      (createdAt: string) => {
        return {labelKey: 'TRANSACTIONS.FIELDS.CREATED_AT', displayText: createdAt}
      }
    ]

  ]);

  private filtersBehaviorSubject = new BehaviorSubject<ApiLogFilters>({});
  private activeFilterChips = new Map<string, CsFinActiveFilterChip>();
  public filters$: Observable<ApiLogFilters> = this.filtersBehaviorSubject.asObservable();
  public activeFiltersChips$: Observable<Map<string, CsFinActiveFilterChip>> = of(this.activeFilterChips);

  constructor(public apiLogApiService: ApiLogApiService) {
  }

  filtersChanged(filters: ApiLogFilters) {
    if (!filters.createdAtStart) {
      filters.createdAtStart = undefined;
      filters.createdAtEnd = undefined;
    }
    this.filtersBehaviorSubject.next(filters);
    this.updateActiveFilterChips(filters);
  }


  private updateActiveFilterChips(filters: ApiLogFilters): void {
    if (!filters) {
      return;
    }
    this.activeFilterChips.clear();

    if (filters.filterText) {
      this.activeFilterChips.set(this.filterTextKey, this.activeFiltersConfigMap.get(this.filterTextKey)(filters.filterText));
    }
    if (filters.logType && filters.logType.length > 0) {
      this.activeFilterChips.set(this.logTypeKey, this.activeFiltersConfigMap.get(this.logTypeKey)(filters.logType));
    }
    if (filters.relatedEntityId) {
      this.activeFilterChips.set(this.relatedEntityIdKey, this.activeFiltersConfigMap.get(this.relatedEntityIdKey)(filters.relatedEntityId));
    }
    if (filters.logStatus && filters.logStatus.length > 0) {
      this.activeFilterChips.set(this.statusKey, this.activeFiltersConfigMap.get(this.statusKey)(filters.logStatus));
    }
    if(filters.createdAtStart){
      let dateToDisplay = new Date(filters.createdAtStart!).toDateString();
      if (filters.createdAtEnd) {
        dateToDisplay += " - " + new Date(filters.createdAtEnd!).toDateString();
      }
      this.activeFilterChips.set(this.createdAtStartKey, this.activeFiltersConfigMap.get(this.createdAtStartKey)(dateToDisplay));
    }
  }

  getRelatedEntities(relatedEntityIds : string|undefined): string {
    if (!relatedEntityIds) {
      return '';
    }
    let relatedEntityJson = JSON.parse(relatedEntityIds);
    let relatedEntities = '';
    for (let key in relatedEntityJson) {
      relatedEntities += relatedEntityJson[key] + ', ';
    }
    return relatedEntities.slice(0, -2);
  }

  getAll(filters?: ApiLogFilters): Observable<PageResponse<ApiLog>> {
    return this.apiLogApiService.getAll(filters);
  }

  getById(id: number): Observable<ApiLog> {
    return this.apiLogApiService.getById(id);
  }

  getLogStatus(): Observable<string[]> {
    return this.apiLogApiService.getLogStatus();
  }

  getLogTypes(): Observable<string[]> {
    return this.apiLogApiService.getLogTypes();
  }

  acknowledgeFailure(id: number): Observable<ApiLog> {
    return this.apiLogApiService.acknowledgeFailure(id);
  }



}
