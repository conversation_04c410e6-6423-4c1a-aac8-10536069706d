import {Component, OnInit} from '@angular/core';
import {ActivatedRoute, Router} from '@angular/router';
import {AuthApiService} from '../../../api/service/auth-api.service';
import {JwtResponse} from '../../../entities/jwt-response';
import {CsFinAuthFacade} from "@jumia-cs-fin/common";
import {HttpErrorResponse} from "@angular/common/http";
import {NotificationService} from "../../../api/service/notification.service";

@Component({
  selector: 'app-login-response',
  templateUrl: './login-response.component.html',
  styleUrls: ['./login-response.component.scss']
})
export class LoginResponseComponent implements OnInit {

  readonly baseState = '/';

  query = {
    code: '',
    url: ''
  };

  constructor(private route: ActivatedRoute,
              private router: Router,
              private authFacade: CsFinAuthFacade,
              private notificationService: NotificationService,
              private authApiService: AuthApiService) { }

  ngOnInit() {
    this.query.code = this.route.snapshot.queryParams['code'];
    this.query.url = this.route.snapshot.queryParams['url'];
    this.removeParametersFromUrl();

    if (this.authFacade.isLoggedIn()) {
      this.redirectUser();
    }

    if (!this.query.code) {
      this.authFacade.redirectToLogin();
    }

    this.authApiService.swapTempToken(this.query.code)
      .subscribe((jwtResponse: JwtResponse) => {
        this.authFacade.setJwtToken(jwtResponse.jwt);
        const userInfo = this.authFacade.getUserInfo();
        if (!userInfo) {
          this.notificationService.errorTranslated(
            'LOGIN_RESPONSE.NOTIFICATIONS.ERROR.COULD_NOT_EXTRACT_USER_ABORT', {});
          return;
        }
        this.initializeUserPermissions();
      }, (error:HttpErrorResponse) => {
        this.notificationService.errorWithResponse(error);
      });
  }

  initializeUserPermissions(): void {
    this.authApiService.getUserPermissions()
      .subscribe((response) => {
        this.authFacade.setUserPermissions(JSON.stringify(response));
        this.redirectUser();
      }, (error:HttpErrorResponse) => {
        this.notificationService.errorWithResponse(error);
      });
  }

  redirectUser(): void {
    if (this.query.url === '/login/response') {
      this.query.url = this.baseState;
    }
    const url = this.query.url ? this.query.url : this.baseState;
    this.router.navigateByUrl(url);
  }

  removeParametersFromUrl(): void {
    this.router.navigate([], {
      relativeTo: this.route,
      replaceUrl: true
    });
  }
}
