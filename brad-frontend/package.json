{"name": "brad-frontend", "version": "1.47.0", "scripts": {"build-prod": "node build --configuration=production", "ng": "ng", "start": "ng serve", "build": "ng build", "test": "ng test", "lint": "ng lint", "cy:open": "cypress open", "cy:run": "cypress run", "cy:ci": "xvfb-run -a -s \"-screen 0 1920x1080x24\" cypress run --browser chrome --config video=false,screenshoOnRunFailure=false", "wait-server": "wait-on --timeout 300000 http://localhost:4200"}, "private": true, "dependencies": {"@angular/animations": "^16.2.3", "@angular/cdk": "^16.2.2", "@angular/common": "^16.2.3", "@angular/compiler": "^16.2.3", "@angular/core": "^16.2.3", "@angular/flex-layout": "~15.0.0-beta.42", "@angular/forms": "^16.2.3", "@angular/material": "^16.2.2", "@angular/platform-browser": "^16.2.3", "@angular/platform-browser-dynamic": "^16.2.3", "@angular/router": "^16.2.3", "@auth0/angular-jwt": "5.1.2", "@jumia-cs-fin/common": "16.13.3", "@ngx-translate/core": "^15.0.0", "@ngx-translate/http-loader": "6.0.0", "cronstrue": "^2.27.0", "echarts": "^5.6.0", "file-saver": "^2.0.5", "jwt-decode": "2.2.0", "lodash": "^4.17.21", "moment": "^2.29.4", "ngx-doc-viewer": "^15.0.1", "ngx-dropzone": "^3.1.0", "ngx-echarts": "^19.0.0", "ngx-extended-pdf-viewer": "^21.4.0", "ngx-mat-select-search": "^7.0.3", "ngx-pdf-viewer": "^0.0.0", "pdfjs-dist": "^4.6.82", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "0.13.0"}, "devDependencies": {"@angular-devkit/build-angular": "^16.2.1", "@angular-eslint/schematics": "^16.2.0", "@angular/cli": "~16.2.1", "@angular/compiler-cli": "^16.2.3", "@angular/language-service": "~15.1.5", "@types/file-saver": "^2.0.6", "@types/jasmine": "4.6.0", "@types/jasminewd2": "2.0.3", "@types/lodash": "^4.14.200", "@types/node": "^16.11.7", "@typescript-eslint/eslint-plugin": "5.18.0", "@typescript-eslint/parser": "5.18.0", "cypress": "9.1.0", "deep-equal-in-any-order": "^1.1.4", "eslint": "^8.41.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-jsdoc": "^46.8.2", "eslint-plugin-prefer-arrow": "^1.2.3", "jasmine-core": "4.6.0", "jasmine-spec-reporter": "5.0.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "protractor": "7.0.0", "ts-node": "7.0.0", "tslint": "~6.1.3", "typescript": "~5.1.3"}}