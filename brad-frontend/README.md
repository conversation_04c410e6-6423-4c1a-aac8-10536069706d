# brad-frontend

This project was generated with [Angular CLI](https://github.com/angular/angular-cli) version 8.0.6.

## Development server

Run `ng serve` for a dev server. Navigate to `http://localhost:4200/`. The app will automatically reload if you change any of the source files.

## Code scaffolding

Run `ng generate component component-name` to generate a new component. You can also use `ng generate directive|pipe|service|class|guard|interface|enum|module`.

## Build

Run `ng build` to build the project. The build artifacts will be stored in the `dist/` directory. Use the `--prod` flag for a production build.

## Running unit tests

Run `ng test` to execute the unit tests via [Karma](https://karma-runner.github.io).

## Running end-to-end tests

This project uses [cypress](https://www.cypress.io/) for e2e tests.

On the project's root folder you can find the `cypress.json` file containing the global configuration options, as well as the testing code inside the `cypress` folder. The later has the following structure:
* _fixtures_: contains default data and files to be reused on the tests
* _integration_: main specs folder
* _plugins_: plugins that hook in different lifecycle events in the testing process
* _support_: write and override commands

The api requests should be stubbed, and fake entities should be provided on the fixtures folder.

#### How to run

In order to run the e2e tests, the FE and cypress must be started in two different terminal windows (no need to run the BE):

1. start the application
    ``` 
    npm start
    ``` 

1. start cypress
    ```
    npm run cy:open
    ```

The cypress UI must be running at this stage. There you can specify the browser you want to test, and run the tests from there.

It is also possible to run the tests on the command line by executing the following command (no need to previously run the script `cy:open`):
``` 
npm run cy:run
```

## Further help

To get more help on the Angular CLI use `ng help` or go check out the [Angular CLI README](https://github.com/angular/angular-cli/blob/master/README.md).
