{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"brad-frontend": {"projectType": "application", "schematics": {}, "root": "", "sourceRoot": "src", "prefix": "brad", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/brad-frontend", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "aot": true, "assets": ["src/version", "src/assets", {"glob": "**/*", "input": "node_modules/@jumia-cs-fin/common/assets", "output": "/assets/jumia-cs-fin/common/"}], "styles": ["@angular/material/prebuilt-themes/indigo-pink.css", "src/app/shared/styles/material-overrides.scss", "src/styles.scss"], "scripts": []}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb"}]}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "fileReplacements": []}}, "defaultConfiguration": "development"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "brad-frontend:build"}, "configurations": {"production": {"browserTarget": "brad-frontend:build:production"}, "development": {"browserTarget": "brad-frontend:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "brad-frontend:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "assets": ["src/version", "src/assets", {"glob": "**/*", "input": "node_modules/@jumia-cs-fin/common/assets", "output": "/assets/jumia-cs-fin/common/"}], "styles": ["@angular/material/prebuilt-themes/indigo-pink.css", "src/styles.scss"], "scripts": []}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}, "defaultProject": "brad-frontend", "schematics": {"@schematics/angular:component": {"style": "scss", "skipTests": true}, "@schematics/angular:class": {"skipTests": true}, "@schematics/angular:directive": {"skipTests": true}, "@schematics/angular:pipe": {"skipTests": true}, "@schematics/angular:service": {"skipTests": true}, "@schematics/angular:interceptor": {"skipTests": true}, "@schematics/angular:guard": {"skipTests": true}}, "cli": {"analytics": "5eddbdd9-0e5e-455c-b0a1-2299ba81f5d1", "defaultCollection": "@angular-eslint/schematics"}}