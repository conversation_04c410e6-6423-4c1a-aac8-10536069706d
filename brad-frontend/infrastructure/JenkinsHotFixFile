#!/usr/bin/env groovy

@Library('jenkins-bops@1.0.0')
import bops.util.AppImage

gradleTools = new bops.util.Gradle()
slackTools = new bops.util.Slack()
jenkinsTools = new bops.util.Jenkins()

// ====================== GLOBAL VARS ========================

branchToBuild              = env.BRANCH_NAME
githubStage                = ''
githubBuildStage           = 'BE Build'
githubArtifactsUploadStage = 'BE Upload artifacts'
gitJiraSyncCheckStage      = 'Git-Jira sync'

appImage = new AppImage(
    script: this,
    appName:    'brad-frontend',
    repo:       'CS-BRAD',
    repoBranch:  branchToBuild,
    repoKey:    'github-AIGEXP',
    account:    'AIGEXP',
    vertical:   'jservices',
    dockerfile: 'dockers/brad-frontend/build/Dockerfile',
)

slackConfig = [
    baseUrl: 'https://jumia-it.slack.com/services/hooks/jenkins-ci/',
    messageAuthor: 'Alfred',
    teamDomain: 'jumia-it',
    tokenCredentialId: 'finance-slack-token',
    channel: '#js_phoenix_cd_prod'
]

// ====================== PIPELINE ========================

pipeline {
    agent {
        kubernetes {
            defaultContainer 'builder'
            inheritFrom 'builder-finance-java11'
        }
    }

    options {
        buildDiscarder(logRotator(numToKeepStr: '5'))
        disableConcurrentBuilds()
    }

    stages {
        stage('Checkout Repository') {
            steps {
                script {
                    appImage.checkoutToSubdir(appImage.getRepo())
                    version = sh(script: "head ${appImage.getRepo()}/apps/brad/src/assets/version",  returnStdout: true).trim()
                    appImage.version = "${version}-HOTFIX"
                    currentBuild.description = "${version} from ${appImage.getRepoBranch()}"
                    slackSend(
                        baseUrl: slackConfig.baseUrl,
                        teamDomain: slackConfig.teamDomain,
                        tokenCredentialId: slackConfig.tokenCredentialId,
                        channel: slackConfig.channel,
                        color: '#FFFF00',
                        message: "Hotfix ${appImage.getVersion()} build on ${appImage.appName} started for branch ${branchToBuild}! ${env.BUILD_URL}"
                    )
                }
            }
        }

        stage('Artifact Build') {
            steps {
                script {
                    githubStage = githubBuildStage
                    jenkinsTools.setGitBuildStatus(githubStage, 'PENDING', 'Build', appImage.getAccount(), appImage.getRepo())
                    echo ">>>>>>> Building ${appImage.getAppName()} on version ${version} from branch ${appImage.getRepoBranch()}"
                    dir(appImage.getRepo()) {
                        sh 'mkdir -p dockers/brad-frontend/build/dist/apps/brad'
                        sh 'npm install'
                        sh 'npm run build-brad'
                        sh 'cp -r dist/apps/brad/* dockers/brad-frontend/build/dist/brad/brad'
                        appImage.buildDockerImage()
                        appImage.tagDockerImage("${version}")
                    }
                    jenkinsTools.setGitBuildStatus(githubStage, 'SUCCESS', 'Build', appImage.getAccount(), appImage.getRepo())
                }
            }
        }

        stage('Lint check') {
            steps {
                script {
                    dir(appImage.getRepo()) {
                        // sh 'npm run lint'
                    }
                }
            }
        }

        stage('Push Artifacts') {
            steps {
                script {
                    githubStage = githubArtifactsUploadStage
                    jenkinsTools.setGitBuildStatus(githubStage, 'PENDING', 'Artifacts upload', appImage.getAccount(), appImage.getRepo())
                    echo ">>>>>>> Pushing ${appImage.getAppTitle()} image"
                    appImage.pushDockerImage()
                    jenkinsTools.setGitBuildStatus(githubStage, 'SUCCESS', 'Artifacts upload', appImage.getAccount(), appImage.getRepo())
                }
            }
        }
    }

    post {
        success {
            script {
                slackSend(
                    baseUrl: slackConfig.baseUrl,
                    teamDomain: slackConfig.teamDomain,
                    tokenCredentialId: slackConfig.tokenCredentialId,
                    channel: slackConfig.channel,
                    color: '#00FF00',
                    message: "Hotfix build ${appImage.getVersion()} on ${appImage.appName} for branch ${branchToBuild} completed successfully! ${env.BUILD_URL}"
                )
                jenkinsTools.setGitBuildStatus(githubStage, 'SUCCESS', 'Great success! All seems good.', appImage.getAccount(), appImage.getRepo())
            }
        }

        failure {
            script {
                slackSend(
                    baseUrl: slackConfig.baseUrl,
                    teamDomain: slackConfig.teamDomain,
                    tokenCredentialId: slackConfig.tokenCredentialId,
                    channel: slackConfig.channel,
                    color: '#FF0000',
                    message: "@here Hotfix build ${appImage.getVersion()} on ${appImage.appName} for branch ${branchToBuild} failed! ${env.BUILD_URL}"
                )
                jenkinsTools.setGitBuildStatus(githubStage, 'FAILURE', 'Ups, something went wrong!', appImage.getAccount(), appImage.getRepo())
            }
        }
    }
}
