#!/usr/bin/env groovy

@Library('jenkins-bops@1.0.0')
import bops.util.AppImage

gradleTools = new bops.util.Gradle()
slackTools = new bops.util.Slack()
jenkinsTools = new bops.util.Jenkins()

// ====================== GLOBAL VARS ========================

branchToBuild              = env.BRANCH_NAME
gitBranchName              = env.CHANGE_BRANCH
githubStage                = ""
githubBuildStage           = "BE Build"
githubTestsStage           = "BE Tests"
githubQualityCheckStage    = "BE Code Inspection"
githubArtifactsUploadStage = "BE Upload artifacts"
gitJiraSyncCheckStage      = "Git-Jira sync"
firstRelease               = "0.1.0"

baseBranch                 = env.CHANGE_TARGET
if (!baseBranch)
    baseBranch = "master"

appImage = new AppImage( script: this,
  appName:    'brad-frontend',
  repo:       'CS-BRAD',
  repoBranch:  branchToBuild,
  repoKey:    'github-AIGEXP',
  account:    'AIGEXP',
  vertical:   'jcentral',
  dockerfile: 'dockers/brad-frontend/Dockerfile',
)

// ====================== PIPELINE ========================

pipeline {
  agent {
    kubernetes {
        defaultContainer 'builder'
        inheritFrom 'builder-finance-java11'
    }
  }

  options {
    buildDiscarder(logRotator(numToKeepStr: '5'))
    disableConcurrentBuilds()
  }

  stages {
    stage('Checkout Repository') {
      steps {
        script {
          currentStage = "Checkout Repository"
          setGitBuildStatusCheck('pending', currentStage, "Starting...")
          withFolderProperties {
            if (params.branch) {
              if (params.branch.startsWith('origin/')) {
                appImage.repoBranch = "${(params.branch).split('/')[1..-1].join('/')}"
            } else {
                appImage.repoBranch = "${params.branch}"
              }
            }
          }

          appImage.checkoutToSubdir(appImage.getRepo())
          version = getVersion()
          buildQualifier = env.BRANCH_NAME == 'master' ? 'RELEASE' : 'SNAPSHOT'
          _version = gitBranchName ? "${version}-${gitBranchName.split('/')[0]}" : version
          appImage.version = "${_version}-${buildQualifier}"
          currentBuild.description = "${version} from ${appImage.getRepoBranch()}"
          sh(script: "git merge-base HEAD origin/${baseBranch} | tr -d \"[:space:]\"", returnStdout: true)
          setGitBuildStatusCheck('success', currentStage, 'Success!')
        }
      }
    }

    stage('Artifact Build') {
      steps {
        script {
            currentStage = "Artifact Build"
            setGitBuildStatusCheck('pending', currentStage, "Starting...")
            jenkinsTools.getECRLogin("jservices")
            githubStage = githubBuildStage
            echo ">>>>>>> Building ${appImage.getAppName()} on version ${version} from branch ${appImage.getRepoBranch()}"
            dir(appImage.getRepo()) {
                sh "mkdir -p dockers/brad-frontend/dist/build"
                dir("brad-frontend") {
                    sh "npm install"
                    sh "npx ng build --configuration=production"
                    sh "cp -r dist/brad-frontend/* ../dockers/brad-frontend/dist/build/"
                }
                dir("dockers/brad-frontend") {
                    appImage.buildDockerImage()
                    appImage.tagDockerImage("${version}")
                }
            }
            setGitBuildStatusCheck('success', currentStage, 'Success!')
        }
      }
    }

    stage('Lint check') {
      steps {
        script {
          dir(appImage.getRepo()) {
            dir("brad-frontend") {
              currentStage = "Lint check"
              setGitBuildStatusCheck('pending', currentStage, "Starting...")
              // sh "npm run lint"
              setGitBuildStatusCheck('success', currentStage, 'Success!')
            }
          }
        }
      }
    }

    stage('Run tests') {
      steps {
        script {
          dir(appImage.getRepo()) {
            currentStage = "Run tests"
            setGitBuildStatusCheck('pending', currentStage, "Starting...")
            // we're executing the cypress tests commands here instead of using the NPM script
            // because we were having permissions issues due to the fact npm was running the
            // commands using user with uid 1000
            // sh "NO_COLOR=1 xvfb-run -a -s \"-screen 0 1920x1080x24\" node_modules/nx/bin/nx.js e2e githubArtifactsUploadStage-e2e --verbose"
            setGitBuildStatusCheck('success', currentStage, 'Success!')
          }
        }
      }
    }

    stage('Push Artifacts') {
      steps {
        script {
          currentStage = "Push Artifacts"
          setGitBuildStatusCheck('pending', currentStage, "Starting...")
          echo ">>>>>>> Pushing ${appImage.getAppTitle()} image"
          appImage.pushDockerImage()
          setGitBuildStatusCheck('success', currentStage, 'Success!')
        }
      }
    }
  }

  post {
    failure {
      script {
        setGitBuildStatusCheck('failure', currentStage, "Failed!")
        // test report
        junit allowEmptyResults: true, testResults: '**/build/test-results/test/*.xml'

        currentBuild.result = 'FAILURE';
      }
    }

    always {
      script {
        println("Build result: ${currentBuild.result}")
      }
    }
  }
}

String getVersion() {
    return sh (script: "head ${appImage.getRepo()}/brad-frontend/src/version",  returnStdout: true).trim()
}

def isFirstRelease() {
  return getVersion() == firstRelease;
}

def getCompliantCommitMessages() {
  def comparison = '';
  if (!isFirstRelease()) {
    def latestRelease = sh(script: "git describe --tags --abbrev=0", returnStdout: true).trim()
    comparison = "${latestRelease}...HEAD"
  }
  def allCommitMessages = sh(
          script: """git log --format=%s ${comparison} | { grep -E '^AFRFINIT-[0-9]+ .+' || true; }""",
          returnStdout: true)
          .trim()
  return allCommitMessages.split('\n');
}

def setGitBuildStatusCheck(String state, String context, String description) {
    withCredentials([usernamePassword(credentialsId: 'github-AIGEXP', usernameVariable: 'GITHUB_USERNAME', passwordVariable: 'GITHUB_PASSWORD')]) {
        sh "curl --silent --output nul -u ${GITHUB_USERNAME}:${GITHUB_PASSWORD} \
            -H 'Accept: application/vnd.github.v3+json' \
            -X POST \
            -d '{\"state\": \"${state}\", \"target_url\": \"${env.BUILD_URL}\", \"context\": \"FE/${context}\", \"description\": \"${description}\"}' \
            https://api.github.com/repos/AIGEXP/CS-BRAD/statuses/${env.GIT_COMMIT}"
    }
}
