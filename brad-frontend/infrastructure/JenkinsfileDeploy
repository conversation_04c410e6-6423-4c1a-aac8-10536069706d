#!/usr/bin/env groovy

@Library('jenkins-bops@1.0.0')
import bops.util.AppImage

gradleTools = new bops.util.Gradle()
slackTools = new bops.util.Slack()
jenkinsDeployTools = new bops.util.Jenkins()

appImage = new AppImage( script: this,
  appName:    'brad-frontend',
  vertical:   'jcentral',
)

slackConfig = [
  baseUrl: 'https://jumia-it.slack.com/services/hooks/jenkins-ci/',
  messageAuthor: 'Alfred',
  teamDomain: 'jumia-it',
  tokenCredentialId: 'finance-slack-token',
  channel: ''
]

pipeline {
  agent {
    kubernetes {
      defaultContainer 'builder'
      inheritFrom 'builder-finance-java11'
    }
  }


  stages {
    stage('Checkout Repository') {
      steps {
        script {
          withFolderProperties {
            environment = "${env.environment}"
            namespaces = "${params.namespaces}"
            version = "${params.version}"
            deployType = "${params.deployType}"
            appImage.version = "${version.split(':')[1]}"
          }
          slackConfig.channel = "${(environment == 'production') ? '#js_fox_deploys_prod' : '#js_fox_deploys_dev'}"
          slackSend(
            baseUrl: slackConfig.baseUrl,
            teamDomain: slackConfig.teamDomain,
            tokenCredentialId: slackConfig.tokenCredentialId,
            channel: slackConfig.channel,
            color: '#FFFF00',
            message: "Deploy ${appImage.appName} on ${environment} started! ${env.BUILD_URL}"
          )
        }
      }
    }

    stage('Apply properties') {
      steps {
        script {
          appImage.packageChart()
          appImage.pushChart()
        }
      }
    }

    stage('Deploy') {
        steps{
            script {
              for (namespace in namespaces.split(",")) {
                stage("Deploy ${namespace}") {
                  jenkinsDeployTools.deploy(
                          appName: appImage.getAppName(),
                          repo: appImage.getRepo(),
                          environment: environment,
                          vertical: appImage.getVertical(),
                          namespace: namespace,
                          version: version
                  )
                }
              }
            }
        }
    }
  }

  post {
    success {
      slackSend(
        baseUrl: slackConfig.baseUrl,
        teamDomain: slackConfig.teamDomain,
        tokenCredentialId: slackConfig.tokenCredentialId,
        channel: slackConfig.channel,
        color: '#00FF00',
        message: "Deploy ${appImage.appName} on ${environment} completed successfully! ${env.BUILD_URL}"
      )
    }

    failure {
      slackSend(
        baseUrl: slackConfig.baseUrl,
        teamDomain: slackConfig.teamDomain,
        tokenCredentialId: slackConfig.tokenCredentialId,
        channel: slackConfig.channel,
        color: '#FF0000',
        message: "@here Deploy ${appImage.appName} Job failed on ${environment}! ${env.BUILD_URL}"
      )
    }
  }
}
