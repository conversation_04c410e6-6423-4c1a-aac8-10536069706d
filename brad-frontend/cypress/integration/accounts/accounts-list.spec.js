describe('Account List', function () {

  let fakeAccounts;
  let fakeAccountsHistory;

  before(() => {
    cy.fixture('fake/fake-accounts.json').then((accounts) => {
      fakeAccounts = accounts;
    });


  })

  after(() => {
    cy.clearUserInfo();
  })



  it('should display account details', () => {
    cy.skipAuthStep('can-access-permissions');

    cy.intercept('GET', '/api/account', fakeAccounts).as('getAccounts');
    cy.visit('accounts');
    cy.wait('@getAccounts');


    for (let i = 0; i < fakeAccounts.length; i++) {
      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i).click();
      cy.get('.mat-tab-label').contains('Details').click();
      cy.get('[data-cy=tab-group]').should('exist');
      cy.get('[data-cy=tab-group]').find('[data-cy=card-label-id]').should('exist');
      cy.get('[data-cy=tab-group]').find('[data-cy=card-value-id]').should('contain', fakeAccounts[i].id);
      cy.get('[data-cy=tab-group]').find('[data-cy=card-label-accNumber]').should('exist');
      cy.get('[data-cy=tab-group]').find('[data-cy=card-value-accNumber]').should('contain', fakeAccounts[i].accNumber);
      cy.get('[data-cy=tab-group]').find('[data-cy=card-label-legalEntity]').should('exist');
      cy.get('[data-cy=tab-group]').find('[data-cy=card-value-legalEntity]').should('contain', fakeAccounts[i].legalEntity);
      cy.get('[data-cy=tab-group]').find('[data-cy=card-label-reference]').should('exist');
      cy.get('[data-cy=tab-group]').find('[data-cy=card-value-reference]').should('contain', fakeAccounts[i].reference);
      cy.get('[data-cy=tab-group]').find('[data-cy=card-label-currencyCode]').should('exist');
      cy.get('[data-cy=tab-group]').find('[data-cy=card-value-currencyCode]').should('contain', fakeAccounts[i].currencyCode);
      cy.get('[data-cy=tab-group]').find('[data-cy=card-label-status]').should('exist');
      cy.get('[data-cy=tab-group]').find('[data-cy=card-value-status]').should('contain', fakeAccounts[i].status);
      let createdAt = new Date(fakeAccounts[i].createdAt).toISOString().slice(0, 19).replace('T', ' ');
      let updatedAt = new Date(fakeAccounts[i].updatedAt).toISOString().slice(0, 19).replace('T', ' ');
      cy.get('[data-cy=tab-group]').find('[data-cy=card-label-createdAt]').should('exist');
      cy.get('[data-cy=tab-group]').find('[data-cy=card-value-createdAt]').should('contain', createdAt);
      cy.get('[data-cy=tab-group]').find('[data-cy=card-label-createdBy]').should('exist');
      cy.get('[data-cy=tab-group]').find('[data-cy=card-value-createdBy]').should('contain', fakeAccounts[i].createdBy);
      cy.get('[data-cy=tab-group]').find('[data-cy=card-label-updatedAt]').should('exist');
      cy.get('[data-cy=tab-group]').find('[data-cy=card-value-updatedAt]').should('contain', updatedAt);
      cy.get('[data-cy=tab-group]').find('[data-cy=card-label-updatedBy]').should('exist');
      cy.get('[data-cy=tab-group]').find('[data-cy=card-value-updatedBy]').should('contain', fakeAccounts[i].updatedBy);
    }


  })


  it('should display empty account page', () => {
    cy.skipAuthStep('can-access-permissions');

    cy.intercept('GET', '/api/account', []).as('getAccounts');
    cy.visit('accounts');
    cy.wait('@getAccounts');

    cy.get('[data-cy=no-results-found]').should('exist')
      .and('be.visible')
      .and('contain', 'No results found.');
  })

  it('should have filter menu and input box', () => {
    cy.skipAuthStep('can-access-permissions');

    cy.intercept('GET', '/api/account', fakeAccounts).as('getAccounts');
    cy.visit('accounts');
    cy.wait('@getAccounts');

    cy.get('[data-cy=input-filter-button]').should('exist');
    cy.get('[data-cy=input-filter-form]').should('not.exist');
    cy.get('[data-cy=input-filter-searchBar]').should('exist');

  })

  it('should filter by LegalEntity', () => {
    cy.skipAuthStep('can-access-permissions');

    cy.intercept('GET', '/api/account', fakeAccounts).as('getAccounts');
    cy.visit('accounts');
    cy.wait('@getAccounts');

    cy.get('[data-cy=input-filter-button]').should('exist');
    cy.get('[data-cy=input-filter-form]').should('not.exist');
    cy.get('[data-cy=dropdown-filter-legalEntity]').should('not.exist');
    cy.get('[data-cy=input-filter-button]').click();

    cy.get('[data-cy=dropdown-filter-legalEntity]').should('exist');
    cy.get('[data-cy=dropdown-filter-legalEntity-input]').should('not.exist');
    cy.get('[data-cy=dropdown-filter-legalEntity]').click();

    cy.get('[data-cy=dropdown-filter-legalEntity]').should('exist');


    cy.get('[data-cy=dropdown-filter-legalEntity-option]').should('have.length', 3);

    cy.get('[data-cy=dropdown-filter-legalEntity-clear-selection]').should('exist');
    cy.get('[data-cy=dropdown-filter-legalEntity-clear-selection]').should('be.disabled');

    cy.get('[data-cy=dropdown-filter-legalEntity-option]').eq(0).should('contain', 'JUMIA1');
    cy.get('[data-cy=dropdown-filter-legalEntity-option]').eq(1).should('contain', 'JUMIA2');
    cy.get('[data-cy=dropdown-filter-legalEntity-option]').eq(2).should('contain', 'JUMIA3');
    cy.get('[data-cy=dropdown-filter-legalEntity-option]').eq(0).click();
    cy.get('[data-cy=dropdown-filter-legalEntity-option]').eq(1).click();
    cy.get('[data-cy=dropdown-filter-legalEntity-option]').eq(2).click();
    cy.get('[data-cy=dropdown-filter-legalEntity-clear-selection]').should('be.enabled');
    cy.get('[data-cy=dropdown-filter-legalEntity-clear-selection]').click();

    cy.get('[data-cy=dropdown-filter-legalEntity-input]').should('exist');
    cy.get('[data-cy=dropdown-filter-legalEntity-input]').type('4');
    cy.get('[data-cy=dropdown-filter-legalEntity-option]').should('have.length', 0);
    cy.get('[data-cy=dropdown-filter-legalEntity-no-options]').should('have.length', 1);


    cy.get('[data-cy=dropdown-filter-legalEntity-clear-input]').click();

    cy.get('[data-cy=dropdown-filter-legalEntity-input]').type('1');
    cy.get('[data-cy=dropdown-filter-legalEntity-option]').should('have.length', 1);
    cy.get('[data-cy=dropdown-filter-legalEntity-option]').should('contain', 'JUMIA1');

    cy.get('[data-cy=dropdown-filter-legalEntity-option]').click();

    cy.get('[data-cy="btn-reload"]').click({force: true});

    cy.get('[data-cy=accounts-table]').find('tbody > tr').should('have.length', 3);

    cy.get('[data-cy=btn-clear-filters]').click({force: true});

    cy.get('[data-cy=accounts-table]').find('tbody > tr').should('have.length', 7);

  })

  it('should filter by KyribaReference', () => {
    cy.skipAuthStep('can-access-permissions');

    cy.intercept('GET', '/api/account', fakeAccounts).as('getAccounts');
    cy.visit('accounts');
    cy.wait('@getAccounts');

    cy.get('[data-cy=input-filter-button]').should('exist');
    cy.get('[data-cy=input-filter-form]').should('not.exist');
    cy.get('[data-cy=input-filter-reference]').should('not.exist');
    cy.get('[data-cy=input-filter-button]').click();
    cy.get('[data-cy=input-filter-reference]').should('exist');
    cy.get('[data-cy=input-filter-reference]').type('123457');
    cy.get('[data-cy="btn-reload"]').click({force: true});
    cy.get('[data-cy=accounts-table]').find('tbody > tr').should('have.length', 1);
    cy.get('[data-cy=btn-clear-filters]').click({force: true});
    cy.get('[data-cy=accounts-table]').find('tbody > tr').should('have.length', 7);
  })

  it('should filter by status', () => {
    cy.skipAuthStep('can-access-permissions');

    cy.intercept('GET', '/api/account', fakeAccounts).as('getAccounts');
    cy.visit('accounts');
    cy.wait('@getAccounts');

    cy.get('[data-cy=input-filter-button]').should('exist');
    cy.get('[data-cy=input-filter-form]').should('not.exist');
    cy.get('[data-cy=dropdown-filter-status]').should('not.exist');
    cy.get('[data-cy=input-filter-button]').click();

    cy.get('[data-cy=dropdown-filter-status]').should('exist');
    cy.get('[data-cy=dropdown-filter-status]').click();

    cy.get('[data-cy=dropdown-filter-status-option]').should('have.length', 2);

    cy.get('[data-cy=dropdown-filter-status-option]').eq(0).should('contain', 'OPEN');
    cy.get('[data-cy=dropdown-filter-status-option]').eq(1).should('contain', 'CLOSED');

    cy.get('[data-cy=dropdown-filter-status-option]').eq(0).click();


    cy.get('[data-cy=btn-reload]').click({force: true});

    cy.get('[data-cy=accounts-table]').find('tbody > tr').should('have.length', 5);

    cy.get('[data-cy=dropdown-filter-status-option]').eq(0).click();
    cy.get('[data-cy=dropdown-filter-status-option]').eq(1).click();

    cy.get('[data-cy=btn-reload]').click({force: true});

    cy.get('[data-cy=accounts-table]').find('tbody > tr').should('have.length', 2);

    cy.get('[data-cy=btn-clear-filters]').click({force: true});

    cy.get('[data-cy=accounts-table]').find('tbody > tr').should('have.length', 7);

  })

  it('should filter by Nav Account Number', () => {
    cy.skipAuthStep('can-access-permissions');

    cy.intercept('GET', '/api/account', fakeAccounts).as('getAccounts');
    cy.visit('accounts');
    cy.wait('@getAccounts');

    cy.get('[data-cy=input-filter-searchBar]').type('NG-3PL-AKUTE TWO FLE2');
    cy.get('[data-cy=btn-search-bar-search]').click({force: true});
    cy.get('[data-cy=accounts-table]').find('tbody > tr').should('have.length', 1);

    cy.get('[data-cy=input-filter-button]').should('exist');
    cy.get('[data-cy=input-filter-form]').should('not.exist');
    cy.get('[data-cy=input-filter-accNumber]').should('not.exist');
    cy.get('[data-cy=input-filter-button]').click();
    cy.get('[data-cy=btn-clear-filters]').click({force: true});
    cy.get('[data-cy=input-filter-accNumber]').should('exist');
    cy.get('[data-cy=input-filter-accNumber]').type('NG-3PL-AKUTE TWO FLE1');
    cy.get('[data-cy=btn-reload]').click({force: true});
    cy.get('[data-cy=accounts-table]').find('tbody > tr').should('have.length', 1);
    cy.get('[data-cy=btn-clear-filters]').click({force: true});
    cy.get('[data-cy=accounts-table]').find('tbody > tr').should('have.length', 7);


  })

  it('should filter by created at CustomDateAdapter.ts', () => {
    cy.skipAuthStep('can-access-permissions');

    cy.intercept('GET', '/api/account', fakeAccounts).as('getAccounts');
    cy.visit('accounts');
    cy.wait('@getAccounts');

    cy.get('[data-cy=input-filter-button]').should('exist');
    cy.get('[data-cy=dropdown-filter-created-at-input]').should('not.exist');

    cy.get('[data-cy=input-filter-button]').click();

    cy.get('[data-cy=dropdown-filter-created-at-input]').should('exist');
    cy.get('[data-cy=dropdown-filter-created-at-input]').type('2023-04-19');
    cy.get('[data-cy=btn-reload]').click({force: true});
    cy.get('[data-cy=accounts-table]').find('tbody > tr').should('have.length', 1);
    cy.get('[data-cy=btn-clear-filters]').click({force: true});
    cy.get('[data-cy=dropdown-filter-created-at-input]').type('2023-04-20');
    cy.get('[data-cy=btn-reload]').click({force: true});
    cy.get('[data-cy=accounts-table]').find('tbody > tr').should('have.length', 6);
    cy.get('[data-cy=btn-clear-filters]').click({force: true});
    cy.get('[data-cy=accounts-table]').find('tbody > tr').should('have.length', 7);

  })

  it('should filter by CurrencyCode', () => {
      cy.skipAuthStep('can-access-permissions');

      cy.intercept('GET', '/api/account', fakeAccounts).as('getAccounts');
      cy.visit('accounts');
      cy.wait('@getAccounts');

      cy.get('[data-cy=input-filter-button]').should('exist');
      cy.get('[data-cy=input-filter-form]').should('not.exist');
      cy.get('[data-cy=dropdown-filter-currencyCode]').should('not.exist');
      cy.get('[data-cy=input-filter-button]').click();

      cy.get('[data-cy=dropdown-filter-currencyCode]').should('exist');
      cy.get('[data-cy=dropdown-filter-currencyCode-input]').should('not.exist');
      cy.get('[data-cy=dropdown-filter-currencyCode]').click();

      cy.get('[data-cy=dropdown-filter-currencyCode]').should('exist');


      cy.get('[data-cy=dropdown-filter-currencyCode-option]').should('have.length', 3);

      cy.get('[data-cy=dropdown-filter-currencyCode-clear-selection]').should('exist');
      cy.get('[data-cy=dropdown-filter-currencyCode-clear-selection]').should('be.disabled');

      cy.get('[data-cy=dropdown-filter-currencyCode-option]').eq(0).should('contain', 'USD');
      cy.get('[data-cy=dropdown-filter-currencyCode-option]').eq(1).should('contain', 'EUR');
      cy.get('[data-cy=dropdown-filter-currencyCode-option]').eq(2).should('contain', 'GBP');
      cy.get('[data-cy=dropdown-filter-currencyCode-option]').eq(0).click();
      cy.get('[data-cy=dropdown-filter-currencyCode-option]').eq(1).click();
      cy.get('[data-cy=dropdown-filter-currencyCode-option]').eq(2).click();
      cy.get('[data-cy=dropdown-filter-currencyCode-clear-selection]').should('be.enabled');
      cy.get('[data-cy=dropdown-filter-currencyCode-clear-selection]').click();

      cy.get('[data-cy=dropdown-filter-currencyCode-input]').should('exist');
      cy.get('[data-cy=dropdown-filter-currencyCode-input]').type('PLN');
      cy.get('[data-cy=dropdown-filter-currencyCode-option]').should('have.length', 0);
      cy.get('[data-cy=dropdown-filter-currencyCode-no-options]').should('have.length', 1);


      cy.get('[data-cy=dropdown-filter-currencyCode-clear-input]').click();

      cy.get('[data-cy=dropdown-filter-currencyCode-input]').type('EUR');
      cy.get('[data-cy=dropdown-filter-currencyCode-option]').should('have.length', 1);
      cy.get('[data-cy=dropdown-filter-currencyCode-option]').should('contain', 'EUR');

      cy.get('[data-cy=dropdown-filter-currencyCode-option]').click();

      cy.get('[data-cy="btn-reload"]').click({force: true});

      cy.get('[data-cy=accounts-table]').find('tbody > tr').should('have.length', 3);

      cy.get('[data-cy=btn-clear-filters]').click({force: true});

      cy.get('[data-cy=accounts-table]').find('tbody > tr').should('have.length', 7);


  })

  it('should display selected columns', () => {
    cy.skipAuthStep('can-access-permissions');

    cy.intercept('GET', '/api/account', fakeAccounts).as('getAccounts');
    cy.visit('accounts');
    cy.wait('@getAccounts');

    cy.get('[data-cy=accounts-table]').find('thead > tr > th').should('have.length', 7);
    cy.get('[data-cy=accounts-table]').find('thead > tr > th').eq(0).should('contain', 'ID');
    cy.get('[data-cy=accounts-table]').find('thead > tr > th').eq(1).should('contain', 'Legal Entity');
    cy.get('[data-cy=accounts-table]').find('thead > tr > th').eq(2).should('contain', 'Nav Account Number');
    cy.get('[data-cy=accounts-table]').find('thead > tr > th').eq(3).should('contain', 'Kyriba Reference');
    cy.get('[data-cy=accounts-table]').find('thead > tr > th').eq(4).should('contain', 'Status');
    cy.get('[data-cy=accounts-table]').find('thead > tr > th').eq(5).should('contain', 'Currency Code');
    cy.get('[data-cy=accounts-table]').find('thead > tr > th').eq(6).should('contain', 'Actions');

    cy.get('[data-cy=div-filter-column-menu]').should('not.exist');
    cy.get('[data-cy=filter-menu-restore-button]').should('not.exist');
    cy.get('[data-cy=filter-menu-apply-button]').should('not.exist');
    cy.get('[data-cy=btn-filter-column-menu]').should('exist');
    cy.get('[data-cy=btn-filter-column-menu]').click();
    cy.get('[data-cy=div-filter-column-menu]').should('exist');
    cy.get('[data-cy=filter-menu-restore-button]').should('exist');
    cy.get('[data-cy=filter-menu-apply-button]').should('exist');
    cy.get('[data-cy=div-filter-column-menu]').should('exist');

    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').should('have.length', 11);
    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').eq(0).should('contain', 'ID');
    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').eq(0).find('input').should('be.checked');
    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').eq(0).find('input').should('be.disabled');

    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').eq(1).should('contain', 'Legal Entity');
    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').eq(1).find('input').should('be.checked');
    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').eq(1).find('input').should('be.enabled');


    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').eq(2).should('contain', 'Nav Account Number');
    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').eq(2).find('input').should('be.checked');
    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').eq(2).find('input').should('be.enabled');

    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').eq(3).should('contain', 'Kyriba Reference');
    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').eq(3).find('input').should('be.checked');
    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').eq(3).find('input').should('be.enabled');

    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').eq(4).should('contain', 'Status');
    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').eq(4).find('input').should('be.checked');
    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').eq(4).find('input').should('be.enabled');

    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').eq(5).should('contain', 'Currency Code');
    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').eq(5).find('input').should('be.checked');
    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').eq(5).find('input').should('be.enabled');

    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').eq(6).should('contain', 'Actions');
    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').eq(6).find('input').should('be.checked');
    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').eq(6).find('input').should('be.disabled');

    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').eq(7).should('contain', 'Created At');
    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').eq(7).find('input').should('not.be.checked');
    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').eq(7).find('input').should('be.enabled');

    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').eq(8).should('contain', 'Created By');
    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').eq(8).find('input').should('not.be.checked');
    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').eq(8).find('input').should('be.enabled');

    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').eq(9).should('contain', 'Updated At');
    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').eq(9).find('input').should('not.be.checked');
    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').eq(9).find('input').should('be.enabled');

    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').eq(10).should('contain', 'Updated By');
    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').eq(10).find('input').should('not.be.checked');
    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').eq(10).find('input').should('be.enabled');

    //hidding all available columns
    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').eq(1).find('input').click({force: true});
    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').eq(2).find('input').click({force: true});
    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').eq(3).find('input').click({force: true});
    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').eq(4).find('input').click({force: true});
    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').eq(5).find('input').click({force: true});

    cy.get('[data-cy=filter-menu-apply-button]').click();

    cy.get('[data-cy=accounts-table]').find('thead > tr > th').should('have.length', 2);

    cy.get('[data-cy=div-filter-column-menu]').should('not.exist');
    cy.get('[data-cy=filter-menu-restore-button]').should('not.exist');
    cy.get('[data-cy=filter-menu-apply-button]').should('not.exist');
    cy.get('[data-cy=btn-filter-column-menu]').click();

    //showing all available columns
    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').eq(1).find('input').click({force: true});
    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').eq(2).find('input').click({force: true});
    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').eq(3).find('input').click({force: true});
    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').eq(4).find('input').click({force: true});
    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').eq(5).find('input').click({force: true});
    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').eq(7).find('input').click({force: true});
    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').eq(8).find('input').click({force: true});
    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').eq(9).find('input').click({force: true});
    cy.get('[data-cy=div-filter-column-menu]').find('mat-checkbox').eq(10).find('input').click({force: true});

    cy.get('[data-cy=filter-menu-apply-button]').click();

    cy.get('[data-cy=accounts-table]').find('thead > tr > th').should('have.length', 11);
    cy.get('[data-cy=div-filter-column-menu]').should('not.exist');
    cy.get('[data-cy=filter-menu-restore-button]').should('not.exist');
    cy.get('[data-cy=filter-menu-apply-button]').should('not.exist');
    cy.get('[data-cy=btn-filter-column-menu]').click();

    //restoring default columns
    cy.get('[data-cy=filter-menu-restore-button]').click();

    cy.get('[data-cy=accounts-table]').find('thead > tr > th').should('have.length', 7);



  })

  it('should display loaded accounts [only access permissions]', () => {
    cy.skipAuthStep('can-access-permissions');

    cy.intercept('GET', '/api/account', fakeAccounts).as('getAccounts');
    cy.visit('accounts');
    cy.wait('@getAccounts');

    cy.get('[data-cy=btn-create]').should('exist');
    cy.get('[data-cy=btn-create]').should('be.disabled');

    cy.get('[data-cy=btn-downloadCsv]').should('exist');
    cy.get('[data-cy=btn-downloadCsv]').should('be.disabled');

    cy.get('[data-cy=accounts-table]').find('tbody > tr').should('have.length', 7);
    for (const [i, fakeAccount] of fakeAccounts.entries()) {

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-id').should('contain', fakeAccount.id);

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-legalEntity').should('contain', fakeAccount.legalEntity);

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-accNumber').should('contain', fakeAccount.accNumber);

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-reference').should('contain', fakeAccount.reference);

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-currencyCode').should('contain', fakeAccount.currencyCode);

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-status').should('contain', fakeAccount.status);

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-createdAt').should('not.exist');

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-createdBy').should('not.exist');

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-updatedAt').should('not.exist');

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-updatedBy').should('not.exist');

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-actions').find("button").click({force:true});

      cy.get('[data-cy=accounts-action-menu]').should('exist');
      cy.get('[data-cy=btn-menuDiv-account]').should('exist');
      cy.get('[data-cy=btn-menuDiv-account]').find('[data-cy=btn-edit-account]').should('not.exist');
      cy.get('[data-cy=btn-menuDiv-account]').find('[data-cy=btn-delete-account]').should('not.exist');
      cy.get('[data-cy=btn-menuDiv-account]').find('[data-cy=btn-close-account]').should('not.exist');


    }
  })

  it('should display loaded accounts [with manage_bank_account permissions]', () => {
    cy.skipAuthStep('manage-accounts-permissions');

    cy.intercept('GET', '/api/account', fakeAccounts).as('getAccounts');
    cy.visit('accounts');
    cy.wait('@getAccounts');


    cy.get('[data-cy=btn-create]').should('exist');
    cy.get('[data-cy=btn-create]').should('be.enabled');

    cy.get('[data-cy=btn-downloadCsv]').should('exist');
    cy.get('[data-cy=btn-downloadCsv]').should('be.disabled');

    cy.get('[data-cy=accounts-table]').find('tbody > tr').should('have.length', 7);
    for (const [i, fakeAccount] of fakeAccounts.entries()) {
      // test  account fields
      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-id').should('contain', fakeAccount.id);

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-legalEntity').should('contain', fakeAccount.legalEntity);

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-accNumber').should('contain', fakeAccount.accNumber);

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-reference').should('contain', fakeAccount.reference);

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-currencyCode').should('contain', fakeAccount.currencyCode);

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-status').should('contain', fakeAccount.status);

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-createdAt').should('not.exist');

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-createdBy').should('not.exist');

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-updatedAt').should('not.exist');

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-updatedBy').should('not.exist');

      //press mat-column-actions
      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-actions').find("button").click({force:true});

      // mat-menu with class menuActions should be visible
      cy.get('[data-cy=accounts-action-menu]').should('exist');
      cy.get('[data-cy=btn-menuDiv-account]').should('exist');
      cy.get('[data-cy=btn-menuDiv-account]').find('[data-cy=btn-edit-account]').should('exist');
      cy.get('[data-cy=btn-menuDiv-account]').find('[data-cy=btn-delete-account]').should('not.exist');
      cy.get('[data-cy=btn-menuDiv-account]').find('[data-cy=btn-close-account]').should('not.exist');


    }
  })

  it('should display loaded accounts [with download_csv permissions]', () => {
    cy.skipAuthStep('download-permissions');

    cy.intercept('GET', '/api/account', fakeAccounts).as('getAccounts');
    cy.visit('accounts');
    cy.wait('@getAccounts');

    cy.get('[data-cy=btn-create]').should('exist');
    cy.get('[data-cy=btn-create]').should('be.disabled');

    cy.get('[data-cy=btn-downloadCsv]').should('exist');
    cy.get('[data-cy=btn-downloadCsv]').should('be.enabled');

    cy.get('[data-cy=accounts-table]').find('tbody > tr').should('have.length', 7);
    for (const [i, fakeAccount] of fakeAccounts.entries()) {
      // test  account fields
      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-id').should('contain', fakeAccount.id);

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-legalEntity').should('contain', fakeAccount.legalEntity);

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-accNumber').should('contain', fakeAccount.accNumber);

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-reference').should('contain', fakeAccount.reference);

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-currencyCode').should('contain', fakeAccount.currencyCode);

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-status').should('contain', fakeAccount.status);

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-createdAt').should('not.exist');

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-createdBy').should('not.exist');

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-updatedAt').should('not.exist');

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-updatedBy').should('not.exist');

      //press mat-column-actions
      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-actions').find("button").click({force:true});

      // mat-menu with class menuActions should be visible
      cy.get('[data-cy=accounts-action-menu]').should('exist');
      cy.get('[data-cy=btn-menuDiv-account]').should('exist');
      cy.get('[data-cy=btn-menuDiv-account]').find('[data-cy=btn-edit-account]').should('not.exist');
      cy.get('[data-cy=btn-menuDiv-account]').find('[data-cy=btn-delete-account]').should('not.exist');
      cy.get('[data-cy=btn-menuDiv-account]').find('[data-cy=btn-close-account]').should('not.exist');


    }
  })

  it('should display loaded accounts [with close_bank_account permissions]', () => {
    cy.skipAuthStep('close-accounts-permissions');

    cy.intercept('GET', '/api/account', fakeAccounts).as('getAccounts');
    cy.visit('accounts');
    cy.wait('@getAccounts');

    cy.get('[data-cy=btn-create]').should('exist');
    cy.get('[data-cy=btn-create]').should('be.disabled');

    cy.get('[data-cy=btn-downloadCsv]').should('exist');
    cy.get('[data-cy=btn-downloadCsv]').should('be.disabled');

    cy.get('[data-cy=accounts-table]').find('tbody > tr').should('have.length', 7);
    for (const [i, fakeAccount] of fakeAccounts.entries()) {

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-id').should('contain', fakeAccount.id);

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-legalEntity').should('contain', fakeAccount.legalEntity);

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-accNumber').should('contain', fakeAccount.accNumber);

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-reference').should('contain', fakeAccount.reference);

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-currencyCode').should('contain', fakeAccount.currencyCode);

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-status').should('contain', fakeAccount.status);

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-createdAt').should('not.exist');

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-createdBy').should('not.exist');

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-updatedAt').should('not.exist');

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-updatedBy').should('not.exist');


      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-actions').find("button").click({force:true});

      cy.get('[data-cy=accounts-action-menu]').should('exist');
      cy.get('[data-cy=btn-menuDiv-account]').should('exist');
      cy.get('[data-cy=btn-menuDiv-account]').find('[data-cy=btn-edit-account]').should('not.exist');
      cy.get('[data-cy=btn-menuDiv-account]').find('[data-cy=btn-delete-account]').should('not.exist');
      cy.get('[data-cy=btn-menuDiv-account]').find('[data-cy=btn-close-account]').should('exist');


    }
  })

  it('should display loaded accounts [with admin-delete-accounts permissions]', () => {
    cy.skipAuthStep('admin-delete-accounts-permissions');

    cy.intercept('GET', '/api/account', fakeAccounts).as('getAccounts');
    cy.visit('accounts');
    cy.wait('@getAccounts');

    cy.get('[data-cy=btn-create]').should('exist');
    cy.get('[data-cy=btn-create]').should('be.disabled');

    cy.get('[data-cy=btn-downloadCsv]').should('exist');
    cy.get('[data-cy=btn-downloadCsv]').should('be.disabled');

    cy.get('[data-cy=accounts-table]').find('tbody > tr').should('have.length', 7);
    for (const [i, fakeAccount] of fakeAccounts.entries()) {

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-id').should('contain', fakeAccount.id);

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-legalEntity').should('contain', fakeAccount.legalEntity);

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-accNumber').should('contain', fakeAccount.accNumber);

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-reference').should('contain', fakeAccount.reference);

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-currencyCode').should('contain', fakeAccount.currencyCode);

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-status').should('contain', fakeAccount.status);

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-createdAt').should('not.exist');

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-createdBy').should('not.exist');

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-updatedAt').should('not.exist');

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-updatedBy').should('not.exist');

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-actions').find("button").click({force:true});


      cy.get('[data-cy=accounts-action-menu]').should('exist');
      cy.get('[data-cy=btn-menuDiv-account]').should('exist');
      cy.get('[data-cy=btn-menuDiv-account]').find('[data-cy=btn-edit-account]').should('not.exist');
      cy.get('[data-cy=btn-menuDiv-account]').find('[data-cy=btn-delete-account]').should('exist');
      cy.get('[data-cy=btn-menuDiv-account]').find('[data-cy=btn-close-account]').should('not.exist');


    }
  })

  it('should display loaded accounts [with admin permissions]', () => {
    cy.skipAuthStep('admin-permissions');

    cy.intercept('GET', '/api/account', fakeAccounts).as('getAccounts');
    cy.visit('accounts');
    cy.wait('@getAccounts');

    cy.get('[data-cy=btn-create]').should('exist');
    cy.get('[data-cy=btn-create]').should('be.enabled');

    cy.get('[data-cy=btn-downloadCsv]').should('exist');
    cy.get('[data-cy=btn-downloadCsv]').should('be.enabled');

    cy.get('[data-cy=accounts-table]').find('tbody > tr').should('have.length', 7);
    for (const [i, fakeAccount] of fakeAccounts.entries()) {

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-id').should('contain', fakeAccount.id);

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-legalEntity').should('contain', fakeAccount.legalEntity);

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-accNumber').should('contain', fakeAccount.accNumber);

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-reference').should('contain', fakeAccount.reference);

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-currencyCode').should('contain', fakeAccount.currencyCode);

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-status').should('contain', fakeAccount.status);

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-createdAt').should('not.exist');

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-createdBy').should('not.exist');

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-updatedAt').should('not.exist');

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-updatedBy').should('not.exist');

      cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(i)
        .find('.mat-column-actions').find("button").click({force:true});

      cy.get('[data-cy=accounts-action-menu]').should('exist');
      cy.get('[data-cy=btn-menuDiv-account]').should('exist');
      cy.get('[data-cy=btn-menuDiv-account]').find('[data-cy=btn-edit-account]').should('exist');
      cy.get('[data-cy=btn-menuDiv-account]').find('[data-cy=btn-delete-account]').should('exist');
      if(fakeAccount.status === 'OPEN')
        cy.get('[data-cy=btn-menuDiv-account]').find('[data-cy=btn-close-account]').should('exist');
      else
        cy.get('[data-cy=btn-menuDiv-account]').find('[data-cy=btn-close-account]').should('be.hidden');


    }
  })


});
