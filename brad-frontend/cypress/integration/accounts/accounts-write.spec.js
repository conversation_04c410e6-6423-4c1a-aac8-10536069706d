describe('Account Write', function () {

  let fakeAccounts;
  let fakeNewAccounts;

  before(() => {
    cy.fixture('fake/fake-accounts.json').then((accounts) => {
      fakeAccounts = accounts;
    });
    cy.fixture('fake/fake-new-accounts.json').then((accounts) => {
      fakeNewAccounts = accounts;
    });
  })

  after(() => {
    cy.clearUserInfo();
  })

  it('should create a new account', function () {
    cy.skipAuthStep('manage-accounts-permissions');

    cy.intercept('GET', '/api/account', []).as('getAccounts');
    cy.visit('accounts');
    cy.wait('@getAccounts');

    cy.get('[data-cy=btn-create]').click();
    cy.get('[data-cy=dialog-title]').should('contain', 'Create Account');

    cy.get('[data-cy=input-accNumber]').click();
    cy.get('[data-cy=select-legal-entity]').click();
    cy.get('[data-cy=input-legal-entity-filter]').click().type('JUMIA');
    cy.get('mat-option').contains('JUMIA').click();

    cy.get('[data-cy=input-reference]').click();
    cy.get('[data-cy=select-currency-code]').click();
    cy.get('[data-cy=input-currency-code-filter]').click().type('EUR');
    cy.get('mat-option').contains('EUR').click();

    cy.get('[data-cy=input-accNumber-errors]')
      .should('be.visible')
    cy.get('[data-cy=input-reference-errors]')
      .should('be.visible')
     cy.get('[data-cy=btn-save]').should('be.disabled');


    cy.get('[data-cy=input-accNumber]').click().type('NG-3PL-AKUTE TWO FLE2');
    cy.get('[data-cy=select-legal-entity]').click();
    cy.get('mat-option').contains('JUMIA').click();
    cy.get('[data-cy=input-reference]').click().type('123452');
    cy.get('[data-cy=select-currency-code]').click()
    cy.get('mat-option').contains('EUR').click();
    cy.get('[data-cy=btn-save]').should('not.be.disabled');


    cy.intercept('POST', '/api/account', fakeAccounts[0]).as('postAccount');
    cy.get('[data-cy=btn-save]').click();
    cy.wait('@postAccount').then((request) => {
      expect(request.request.body).to.deep.equalInAnyOrder({
        accNumber: 'NG-3PL-AKUTE TWO FLE2',
        legalEntity: 'JUMIA',
        reference: '123452',
        currencyCode: 'EUR',
        status: ''
      });
    });

  });

  it('should edit an account', function () {
    cy.skipAuthStep('manage-accounts-permissions');

    cy.intercept('GET', '/api/account', fakeAccounts).as('getAccounts');
    cy.visit('accounts');
    cy.wait('@getAccounts');

    cy.get('[data-cy=accounts-table]').find('tbody > tr').eq(0)
      .find('.mat-column-actions').find("button").click({force:true});

    cy.get('[data-cy=btn-menuDiv-account]').find('[data-cy=btn-edit-account]').should('exist');
    cy.get('[data-cy=btn-menuDiv-account]').find('[data-cy=btn-edit-account]').click();


    cy.get('[data-cy=input-accNumber]').click();
    cy.get('[data-cy=select-legal-entity]').click();
    cy.get('[data-cy=input-legal-entity-filter]').click().type('JUMIA');
    cy.get('mat-option').contains('JUMIA').click();

    cy.get('[data-cy=input-reference]').click();
    cy.get('[data-cy=select-currency-code]').click();
    cy.get('[data-cy=input-currency-code-filter]').click().type('EUR');
    cy.get('mat-option').contains('EUR').click();
    // Check that all fields are required
    cy.get('[data-cy=input-accNumber-errors]')
      .should('be.visible')
    cy.get('[data-cy=input-reference-errors]')
      .should('be.visible')
     cy.get('[data-cy=btn-save]').should('be.disabled');


    cy.get('[data-cy=input-accNumber]').click().type('NG-3PL-AKUTE TWO FLE2');
    cy.get('[data-cy=select-legal-entity]').click();
    cy.get('mat-option').contains('JUMIA').click();
    cy.get('[data-cy=input-reference]').click().type('123452');
    cy.get('[data-cy=select-currency-code]').click()
    cy.get('mat-option').contains('EUR').click();
    cy.get('[data-cy=btn-save]').should('not.be.disabled');

    // Submit the form and check that the request is valid
    cy.intercept('PUT', '/api/account/'+fakeAccounts[0].id).as('putAccount');
    cy.get('[data-cy=btn-save]').click();


  });

})
