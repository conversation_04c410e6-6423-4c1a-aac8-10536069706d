import {localStorageKeys} from "../../src/app/shared/constants/core.constants";

Cypress.Commands.add('clearUserInfo', () => {
  window.localStorage.removeItem(localStorageKeys.satellizerToken);
  window.localStorage.removeItem(localStorageKeys.userPermissions);
})

Cypress.Commands.add('skipAuthStep', (userPermissionsFixture) => {
  cy.fixture('user-login-info').then((userLoginInfo) => {
    window.localStorage.setItem(localStorageKeys.satellizerToken, userLoginInfo.jwt);
  });

  if (userPermissionsFixture) {
    cy.fixture('permissions/' + userPermissionsFixture).then((userPermissions) => {
      window.localStorage.setItem(localStorageKeys.userPermissions, JSON.stringify(userPermissions));
    })
  } else {
    window.localStorage.setItem(localStorageKeys.userPermissions, '{}');
  }
})
