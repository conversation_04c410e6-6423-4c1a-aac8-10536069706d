version: "3.7"

services:
  brad-database:
    container_name: "brad-database"
    image: brad-database:latest
    build:
      context: brad-database
    ports:
      - "15432:5432"
  brad-backend:
    container_name: "brad-be.services"
    image: brad-be.services:latest
    build:
      context: ./brad-backend
      args:
        ECR: "089879264256.dkr.ecr.eu-west-1.amazonaws.com/jumiaservices"
    environment:
      JMX_REMOTE_PORT: 9199
      SPRING_DATASOURCE_URL: **************************************************?user=postgres&password=postgres
      DATA_DB_URL: **************************************************
      BRAD_API_ALLOWED_DOMAINS: http://brad-fe.services,http://localhost:4202
    ports:
      - "8080:8080"
  brad-fe.services:
    container_name: "brad-fe.services"
    image: brad-fe.services:latest
    build:
      context: ./brad-frontend
    environment:
      NGINX_SERVER_NAME: brad-fe.services
      NGINX_SERVER_PORT: 80
      BRAD_BACKEND: http:\/\/localhost:8080
      BRAD_FRONTEND: http:\/\/localhost:4202\/
      ACL_FRONTEND: https:\/\/acl-staging.jumia.services\/
    ports:
      - 4202:80
  bale-database:
    container_name: bale-database
    image: bale-database:latest
    build:
      context: bale-database
    ports:
      - "1433:1433"
  fxrates-database:
    container_name: fxrates-database
    image: fxrates-database:latest
    build:
      context: fxrates-database
    ports:
      - "1434:1433"
  finrec-statements-database:
    container_name: finrec-statements-database
    image: finrec-statements-database:latest
    build:
      context: finrec-statements-database
    ports:
      - "1435:1433"

networks:
  default:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: **********/16
