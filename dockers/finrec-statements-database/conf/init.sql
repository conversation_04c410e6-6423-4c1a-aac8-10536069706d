CREATE DATABASE finrec_statements;
GO
USE finrec_statements;
GO
CREATE SCHEMA BRAD;
GO
CREATE TABLE [BRAD].[BANK_STATEMENTS]
(
    [ID] [varchar](100) NOT NULL,
    [openingBalance] [money] NULL,
    [currency] [varchar](50) NULL,
    [Type] [varchar](50) NULL,
    [InitialDate] [varchar](11) NULL,
    [FinalDate] [varchar](11) NULL,
    [hasTransaction] [varchar](50) NULL,
    [runningBalance] [money] NULL,
    [reference] [varchar](100) NULL,
    [valueDate] [varchar](50) NULL,
    [Description] [varchar](900) NULL,
    [tranAmount] [money] NULL,
    [Direction] [varchar](50) NULL,
    [tranDate] [varchar](50) NULL,
    [SK_AUD_INSERT] [int] NULL,
    [TIMESTAMP_RUN_AT] [datetime] NULL,
    [account_number] [varchar](50) NULL
)
GO

ALTER TABLE [BRAD].[BANK_STATEMENTS] ADD DEFAULT (getdate()) FOR [TIMESTAMP_RUN_AT]
GO

INSERT INTO [BRAD].[BANK_STATEMENTS] (
    [ID],
    [openingBalance],
    [currency],
    [Type],
    [InitialDate],
    [FinalDate],
    [hasTransaction],
    [runningBalance],
    [reference],
    [valueDate],
    [Description],
    [tranAmount],
    [Direction],
    [tranDate],
    [SK_AUD_INSERT],
    [TIMESTAMP_RUN_AT],
    [account_number]
) VALUES
    ('S84746295', 40486.32, 'NGN', 'TRF', '01-JAN-2019', '03-JAN-2024', 'Y', 33886.32, '*********',                       '1/1/2019 6:03:00 AM', 'Online : SWIFT', 6600, 'D',                                    '1/1/2019 6:03:00 AM', 9585656, CONVERT(datetime, '2024-01-04 14:50:46.683', 121), '**********'),
    ('S85432230', 40486.32, 'NGN', 'TRF', '01-JAN-2019', '03-JAN-2024', 'Y', 1125886.32, NULL,                            '2/1/2019 6:03:00 AM', 'UPFRONT ALLO. 2019FY', 1092000, 'C',                           '2/1/2019 6:03:00 AM', 9585656, CONVERT(datetime, '2024-01-04 14:50:46.683', 121), '**********'),
    ('S85466149', 40486.32, 'NGN', 'TRF', '01-JAN-2019', '03-JAN-2024', 'Y', 1699186.32, NULL,                            '2/1/2019 6:03:00 AM', 'QTRLY ALLCE Q1 2019FY', 573300, 'C',                           '2/1/2019 6:03:00 AM', 9585656, CONVERT(datetime, '2024-01-04 14:50:46.683', 121), '**********'),
    ('S85500400', 40486.32, 'NGN', 'TRF', '01-JAN-2019', '03-JAN-2024', 'Y', 1894186.32, NULL,                            '2/1/2019 6:03:00 AM', 'WELFARE ALLO. 2019FY', 195000, 'C',                            '2/1/2019 6:03:00 AM', 9585656, CONVERT(datetime, '2024-01-04 14:50:46.683', 121), '**********'),
    ('S86511463', 40486.32, 'NGN', 'TRF', '01-JAN-2019', '03-JAN-2024', 'Y', 1714186.32, '152/**********/000184337925',   '3/1/2019 4:13:00 PM', 'Online   mercury battery', 180000, 'D',                        '3/1/2019 4:13:00 PM', 9585656, CONVERT(datetime, '2024-01-04 14:50:46.683', 121), '**********'),
    ('S86511492', 40486.32, 'NGN', 'TRF', '01-JAN-2019', '03-JAN-2024', 'Y', 1714133.82, '152/**********/000184337938',   '3/1/2019 4:13:00 PM', 'TRANSACTION CHARGE-Online   mercury battery', 52.5, 'D',       '3/1/2019 4:13:00 PM', 9585656, CONVERT(datetime, '2024-01-04 14:50:46.683', 121), '**********'),
    ('S86512618', 40486.32, 'NGN', 'TRF', '01-JAN-2019', '03-JAN-2024', 'Y', 1894133.82, '152/**********/000184337925',   '3/1/2019 4:13:00 PM', 'Rsvl:Online   mercury battery', 180000, 'C',                   '3/1/2019 4:13:00 PM', 9585656, CONVERT(datetime, '2024-01-04 14:50:46.683', 121), '**********'),
    ('S86512624', 40486.32, 'NGN', 'TRF', '01-JAN-2019', '03-JAN-2024', 'Y', 1894186.32, '152/**********/000184337938',   '3/1/2019 4:13:00 PM', 'TRANSACTION CHARGE-Rsvl:Online   mercury battery', 52.5, 'C',  '3/1/2019 4:13:00 PM', 9585656, CONVERT(datetime, '2024-01-04 14:50:46.683', 121), '**********'),
    ('S86513559', 40486.32, 'NGN', 'TRF', '01-JAN-2019', '03-JAN-2024', 'Y', 1714186.32, '152/**********/000184338837',   '3/1/2019 4:13:00 PM', 'Online   mercury battery', 180000, 'D',                        '3/1/2019 4:13:00 PM', 9585656, CONVERT(datetime, '2024-01-04 14:50:46.683', 121), '**********'),
    ('S86513574', 40486.32, 'NGN', 'TRF', '01-JAN-2019', '03-JAN-2024', 'Y', 1714133.82, '152/**********/000184338843',   '3/1/2019 4:13:00 PM', 'TRANSACTION CHARGE-Online   mercury battery', 52.5, 'D',       '3/1/2019 4:13:00 PM', 9585656, CONVERT(datetime, '2024-01-04 14:50:46.683', 121), '**********'),
    ('S86595497', 40486.32, 'NGN', 'TRF', '01-JAN-2019', '03-JAN-2024', 'Y', 1703683.82, 'V32924/10369579328/072143/MSA', '3/1/2019 5:13:00 PM', 'POS PYMNT @ SW GLOBAL FRSC         ABUJA MUNICIP', 10450, 'D', '3/1/2019 5:13:00 PM', 9585656, CONVERT(datetime, '2024-01-04 14:50:46.683', 121), '**********'),
    ('S86513578', 40486.32, 'NGN', 'TRF', '01-JAN-2019', '03-JAN-2024', 'Y', 40486.32, '001/**********/000449271198',   '3/1/2019 4:13:00 PM', 'TRANSACTION CHARGE-Online   mercury battery', 10000, 'D',       '3/1/2019 4:13:00 PM', 9585656, CONVERT(datetime, '2024-01-04 14:50:46.683', 121), '1289907028'),
    ('S86595498', 40486.32, 'NGN', 'TRF', '01-JAN-2019', '03-JAN-2024', 'Y', 30486.32, '001/**********/000449271199', '3/1/2019 5:13:00 PM', 'POS PYMNT @ SW GLOBAL FRSC         ABUJA MUNICIP', 2, 'D', '3/1/2019 5:13:00 PM', 9585656, CONVERT(datetime, '2024-01-04 14:50:46.683', 121), '1289907028');