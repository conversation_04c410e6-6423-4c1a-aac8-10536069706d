worker_processes  1;

events {
    worker_connections  1024;
}

http {
    server {
        listen NGINX_SERVER_PORT;
        server_name  NGINX_SERVER_NAME;
        index index.html;
        include /etc/nginx/mime.types;

        set $redirect off;
        if ($http_x_forwarded_proto = 'http') {
          set $redirect on;
        }
        if ($request_uri ~* rev.txt) {
          set $redirect off;
        }
        if ($host ~* dev.k8s.js) {
          set $redirect off;
        }
        if ($http_x_real_ip ~* 10.18) {
          set $redirect off;
        }
        if ($redirect = on) {
          rewrite ^ https://$host$request_uri permanent;
        }

        location / {

          root   /usr/share/nginx/html;

          gzip             on;
          gzip_proxied     any;
          gzip_types       text/css text/plain text/xml application/xml application/javascript application/x-javascript text/javascript application/json text/x-json;
          gzip_vary        on;
          gzip_disable     "MSIE [1-6]\.";

          server_tokens off;
          expires off;
          add_header Cache-Control "no-cache";
          add_header X-Frame-Options SAMEORIGIN;
          add_header X-Content-Type-Options nosniff;
          add_header X-XSS-Protection "1; mode=block";
          add_header Last-Modified "";
          if_modified_since off;
          etag off;

          # https://stackoverflow.com/a/********
          # Angular navigation rule, if uri not found use angular routing from index.html
          # try_files rules don't work with proxy_pass
          error_page 404 =200 /index.html;
        }
    }
}
