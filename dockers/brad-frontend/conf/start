#!/bin/bash
set -e

sed -i "s|NGINX_SERVER_NAME|${NGINX_SERVER_NAME}|g" /etc/nginx/nginx.conf
sed -i "s|NGINX_SERVER_PORT|${NGINX_SERVER_PORT:-80}|g" /etc/nginx/nginx.conf
sed -i "s|BRAD_FRONTEND|${BRAD_FRONTEND}|g" /usr/share/nginx/html/main*.js
sed -i "s|BRAD_BACKEND|${BRAD_BACKEND}|g" /usr/share/nginx/html/main*.js
sed -i "s|ACL_FRONTEND|${ACL_FRONTEND}|g" /usr/share/nginx/html/main*.js
sed -i "s|GA_TRACKID|${GOOGLE_ANALYTICS_TRACKING_ID}|g" /usr/share/nginx/html/main*.js
sed -i "s|GTA_TRACKID|${GOOGLE_TAG_TRACKING_ID}|g" /usr/share/nginx/html/main*.js
sed -i "s|GTA_TRACKID|${GOOGLE_TAG_TRACKING_ID}|g" /usr/share/nginx/html/index.html

nginx -g "daemon off;"
