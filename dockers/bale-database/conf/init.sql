-- Creating Databases

DECLARE @dbname VARCHAR(50)
DECLARE db_cursor CURSOR FOR
    SELECT name
    FROM (VALUES ('D365BC14_CI'),
                 ('D365BC14_TN'),
                 ('D365BC14_DZ'),
                 ('D365BC14_GH'),
                 ('D365BC14_KE'),
                 ('D365BC14_MA'),
                 ('D365BC14_SN'),
                 ('D365BC14_US'),
                 ('D365BC14_ZA'),
                 ('D365BC14_ES'),
                 ('D365BC14_NG'),
                 ('D365BC14_RW'),
                 ('D365BC14_CM'),
                 ('D365BC14_FMS'),
                 ('D365BC14_FMS_USD'),
                 ('D365BC14_TZ'),
                 ('D365BC14_FR'),
                 ('D365BC14_EG'),
                 ('D365BC14_UG'),
                 ('D365BC14_CN'),
                 ('D365BC14_PT')
          ) AS databases(name)

OPEN db_cursor
FETCH NEXT FROM db_cursor INTO @dbname

WHILE @@FETCH_STATUS = 0
    BEGIN
        DECLARE @SQL1 NVARCHAR(100)
        SET @SQL1 = 'CREATE DATABASE ' + @dbname
        EXEC sp_executesql @SQL1

        FETCH NEXT FROM db_cursor INTO @dbname
    END

CLOSE db_cursor
DEALLOCATE db_cursor


--Creating Schemas

DECLARE db_cursor2 CURSOR FOR
    SELECT name
    FROM (VALUES ('D365BC14_CI'),
                 ('D365BC14_TN'),
                 ('D365BC14_DZ'),
                 ('D365BC14_GH'),
                 ('D365BC14_KE'),
                 ('D365BC14_MA'),
                 ('D365BC14_SN'),
                 ('D365BC14_US'),
                 ('D365BC14_ZA'),
                 ('D365BC14_ES'),
                 ('D365BC14_NG'),
                 ('D365BC14_RW'),
                 ('D365BC14_CM'),
                 ('D365BC14_FMS'),
                 ('D365BC14_FMS_USD'),
                 ('D365BC14_TZ'),
                 ('D365BC14_FR'),
                 ('D365BC14_EG'),
                 ('D365BC14_UG'),
                 ('D365BC14_CN'),
                 ('D365BC14_PT')
         ) AS databases(name)

OPEN db_cursor2
FETCH NEXT FROM db_cursor2 INTO @dbname

WHILE @@FETCH_STATUS = 0
    BEGIN

        DECLARE @SQL21 NVARCHAR(MAX) = 'IF NOT EXISTS (
                                       SELECT * FROM [' + @dbname + '].sys.schemas WHERE name = ''BRAD''
                                   )
                                   BEGIN
                                       USE [' + @dbname + '];
                                       EXEC(''CREATE SCHEMA BRAD'');
                                   END;';


           EXEC sp_executesql @SQL21;

           FETCH NEXT FROM db_cursor2 INTO @dbname;
    END

CLOSE db_cursor2
DEALLOCATE db_cursor2


--Creating Tables

DECLARE @tablePerDatabase TABLE (
                                 DatabaseName NVARCHAR(50),
                                 SchemaName NVARCHAR(5),
                                 BankAccountTableName NVARCHAR(200),
                                 BankAccountLedgerTableName NVARCHAR(200)
                             );

INSERT INTO @tablePerDatabase (DatabaseName, SchemaName, BankAccountTableName, BankAccountLedgerTableName)
VALUES
    ('D365BC14_CI', 'dbo', 'ATOL IVORY COAST$Bank Account','ATOL IVORY COAST$Bank Account Ledger Entry'),
    ('D365BC14_TN', 'dbo', 'Atol TN$Bank Account','Atol TN$Bank Account Ledger Entry'),
    ('D365BC14_DZ', 'dbo', 'JUMIA ALGERIA$Bank Account','JUMIA ALGERIA$Bank Account Ledger Entry'),
    ('D365BC14_GH', 'dbo', 'JUMIA GHANA$Bank Account','JUMIA GHANA$Bank Account Ledger Entry'),
    ('D365BC14_CI', 'dbo', 'JUMIA IVORY COAST$Bank Account','JUMIA IVORY COAST$Bank Account Ledger Entry'),
    ('D365BC14_KE', 'dbo', 'JUMIA KENYA$Bank Account','JUMIA KENYA$Bank Account Ledger Entry'),
    ('D365BC14_MA', 'dbo', 'JUMIA MOROCCO$Bank Account','JUMIA MOROCCO$Bank Account Ledger Entry'),
    ('D365BC14_SN', 'dbo', 'JUMIA SENEGAL$Bank Account','JUMIA SENEGAL$Bank Account Ledger Entry'),
    ('D365BC14_US', 'dbo', 'JUMIA USA$Bank Account','JUMIA USA$Bank Account Ledger Entry'),
    ('D365BC14_MA', 'dbo', 'eCommerce Service MA$Bank Account','eCommerce Service MA$Bank Account Ledger Entry'),
    ('D365BC14_SN', 'dbo', 'Hellofood SN$Bank Account','Hellofood SN$Bank Account Ledger Entry'),
    ('D365BC14_DZ', 'dbo', 'Jade DZ$Bank Account','Jade DZ$Bank Account Ledger Entry'),
    ('D365BC14_GH', 'dbo', 'Jade GH$Bank Account','Jade GH$Bank Account Ledger Entry'),
    ('D365BC14_CI', 'dbo', 'Jade IC$Bank Account','Jade IC$Bank Account Ledger Entry'),
    ('D365BC14_KE', 'dbo', 'Jade KE$Bank Account','Jade KE$Bank Account Ledger Entry'),
    ('D365BC14_TN', 'dbo', 'Jade TN$Bank Account','Jade TN$Bank Account Ledger Entry'),
    ('D365BC14_ZA', 'dbo', 'Jade ZA$Bank Account','Jade ZA$Bank Account Ledger Entry'),
    ('D365BC14_ES', 'dbo', 'JUMIA SPAIN$Bank Account','JUMIA SPAIN$Bank Account Ledger Entry'),
    ('D365BC14_TN', 'dbo', 'JM TN$Bank Account','JM TN$Bank Account Ledger Entry'),
    ('D365BC14_KE', 'dbo', 'JUMIA PAYMENT SERV KE LIMITED$Bank Account','JUMIA PAYMENT SERV KE LIMITED$Bank Account Ledger Entry'),
    ('D365BC14_TN', 'dbo', 'JUMIA PAY TN$Bank Account','JUMIA PAY TN$Bank Account Ledger Entry'),
    ('D365BC14_CI', 'dbo', 'JUMIA TECHNOLOGIES SARL$Bank Account','JUMIA TECHNOLOGIES SARL$Bank Account Ledger Entry'),
    ('D365BC14_TN', 'dbo', 'STE JUMIA TECHNOLOGIES$Bank Account','STE JUMIA TECHNOLOGIES$Bank Account Ledger Entry'),
    ('D365BC14_ZA', 'dbo', 'Lendico ZA$Bank Account','Lendico ZA$Bank Account Ledger Entry'),
    ('D365BC14_NG', 'dbo', 'Atol NG$Bank Account','Atol NG$Bank Account Ledger Entry'),
    ('D365BC14_RW', 'dbo', 'Atol RW$Bank Account','Atol RW$Bank Account Ledger Entry'),
    ('D365BC14_CM', 'dbo', 'Ecart CM$Bank Account','Ecart CM$Bank Account Ledger Entry'),
    ('D365BC14_FMS', 'dbo', 'JUMIA FMS$Bank Account','JUMIA FMS$Bank Account Ledger Entry'),
    ('D365BC14_FMS', 'dbo', 'Jumia Services EC_FZ$Bank Account','Jumia Services EC_FZ$Bank Account Ledger Entry'),
    ('D365BC14_FMS_USD', 'dbo', 'Jumia Services EC_FZ$Bank Account','Jumia Services EC_FZ$Bank Account Ledger Entry'),
    ('D365BC14_NG', 'dbo', 'JUMIA NIGERIA$Bank Account','JUMIA NIGERIA$Bank Account Ledger Entry'),
    ('D365BC14_TZ', 'dbo', 'JUMIA TANZANIA$Bank Account','JUMIA TANZANIA$Bank Account Ledger Entry'),
    ('D365BC14_FR', 'dbo', 'Africa Ecommerce Services$Bank Account','Africa Ecommerce Services$Bank Account Ledger Entry'),
    ('D365BC14_EG', 'dbo', 'Easy Taxi EG$Bank Account','Easy Taxi EG$Bank Account Ledger Entry'),
    ('D365BC14_NG', 'dbo', 'Etaxi NG$Bank Account','Etaxi NG$Bank Account Ledger Entry'),
    ('D365BC14_NG', 'dbo', 'Gabi NG$Bank Account','Gabi NG$Bank Account Ledger Entry'),
    ('D365BC14_EG', 'dbo', 'Hellofood EG$Bank Account','Hellofood EG$Bank Account Ledger Entry'),
    ('D365BC14_NG', 'dbo', 'Hellopay NG$Bank Account','Hellopay NG$Bank Account Ledger Entry'),
    ('D365BC14_FR', 'dbo', 'Africa Internet Services$Bank Account','Africa Internet Services$Bank Account Ledger Entry'),
    ('D365BC14_CM', 'dbo', 'Jade CM$Bank Account','Jade CM$Bank Account Ledger Entry'),
    ('D365BC14_NG', 'dbo', 'Jade NG$Bank Account','Jade NG$Bank Account Ledger Entry'),
    ('D365BC14_UG', 'dbo', 'Jade UG$Bank Account','Jade UG$Bank Account Ledger Entry'),
    ('D365BC14_NG', 'dbo', 'JUMIA FINANCIAL SERV LIMITED$Bank Account','JUMIA FINANCIAL SERV LIMITED$Bank Account Ledger Entry'),
    ('D365BC14_CN', 'dbo', 'Jumia China$Bank Account','Jumia China$Bank Account Ledger Entry'),
    ('D365BC14_EG', 'dbo', 'JUMIA EGYPT$Bank Account','JUMIA EGYPT$Bank Account Ledger Entry'),
    ('D365BC14_EG', 'dbo', 'Jumia Electr Payment Serv_ SAE$Bank Account','Jumia Electr Payment Serv_ SAE$Bank Account Ledger Entry'),
    ('D365BC14_UG', 'dbo', 'Jumia Payment Serv UG Limited$Bank Account','Jumia Payment Serv UG Limited$Bank Account Ledger Entry'),
    ('D365BC14_EG', 'dbo', 'Jumia For Trade$Bank Account','Jumia For Trade$Bank Account Ledger Entry'),
    ('D365BC14_TZ', 'dbo', 'Juwel TZ$Bank Account','Juwel TZ$Bank Account Ledger Entry'),
    ('D365BC14_NG', 'dbo', 'Lipco NG$Bank Account','Lipco NG$Bank Account Ledger Entry'),
    ('D365BC14_NG', 'dbo', 'R-SC NG$Bank Account','R-SC NG$Bank Account Ledger Entry'),
    ('D365BC14_PT', 'dbo', 'Jumia PTC$Bank Account','Jumia PTC$Bank Account Ledger Entry');

DECLARE @databaseName NVARCHAR(50);
DECLARE @schemaName NVARCHAR(5);
DECLARE @bankAccountTableName NVARCHAR(200);
DECLARE @bankAccountLedgerTableName NVARCHAR(200);

DECLARE table_cursor CURSOR FOR
    SELECT DatabaseName, SchemaName, BankAccountTableName, BankAccountLedgerTableName
    FROM @tablePerDatabase;

OPEN table_cursor;
FETCH NEXT FROM table_cursor INTO @databaseName, @schemaName, @bankAccountTableName, @bankAccountLedgerTableName;

WHILE @@FETCH_STATUS = 0
    BEGIN
        DECLARE @SQL31 NVARCHAR(MAX);
        SET @SQL31 = 'USE [' + @databaseName + '];' +
                    ' IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = ''' + @bankAccountTableName + ''' AND SCHEMA_NAME(schema_id) = ''' + @schemaName + ''')' +
                    ' BEGIN' +
                    ' CREATE TABLE [' + @schemaName + '].[' + @bankAccountTableName + '] (' +
                    ' [No_] VARCHAR(50) PRIMARY KEY,' +
                    ' [Name] VARCHAR(255),' +
                    ' [Account Type] VARCHAR(50),' +
                    ' [Currency Code] CHAR(3),' +
                    ' [Kyriba Ref_] VARCHAR(50),' +
                    ' [timestamp] NVARCHAR(50)' +
                    ' ) ON [PRIMARY];' +
                    ' END;';

        EXEC sp_executesql @SQL31;

        DECLARE @SQL32 NVARCHAR(MAX);
        SET @SQL32 = 'USE [' + @databaseName + '];' +
                    ' IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = ''' + @bankAccountLedgerTableName + ''' AND SCHEMA_NAME(schema_id) = ''' + @schemaName + ''')' +
                    ' BEGIN' +
                    ' CREATE TABLE [' + @schemaName + '].[' + @bankAccountLedgerTableName + '] (' +
                    ' [Entry No_] INT PRIMARY KEY,' +
                    ' [Document No_] VARCHAR(50),' +
                    ' [Document Type] INT,' +
                    ' [Posting Date] DATE,' +
                    ' [Bank Account No_] VARCHAR(50),' +
                    ' [Bank Acc_ Posting Group] VARCHAR(50),' +
                    ' [Description] VARCHAR(255),' +
                    ' [Source Code] VARCHAR(50),' +
                    ' [Reason Code] VARCHAR(50),' +
                    ' [Global Dimension 1 Code] VARCHAR(50),' +
                    ' [Global Dimension 2 Code] VARCHAR(50),' +
                    ' [Amount] DECIMAL(18, 2),' +
                    ' [Remaining Amount] DECIMAL(18, 2),' +
                    ' [Currency Code] CHAR(3),' +
                    ' [Amount (LCY)] DECIMAL(18, 2),' +
                    ' [Bal_ Account No_] VARCHAR(50),' +
                    ' [Bal_ Account Type] INT,' +
                    ' [Open] TINYINT,' +
                    ' [Reversed] TINYINT,' +
                    ' [User ID] VARCHAR(50),' +
                    ' [External Document No_] VARCHAR(50),' +
                    ' [timestamp] NVARCHAR(50)' +
                    ' ) ON [PRIMARY];' +
                    ' END;';

        EXEC sp_executesql @SQL32;

        FETCH NEXT FROM table_cursor INTO @databaseName, @schemaName, @bankAccountTableName, @bankAccountLedgerTableName;
    END;

CLOSE table_cursor;
DEALLOCATE table_cursor;