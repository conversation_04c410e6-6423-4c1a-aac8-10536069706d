DECLARE @databaseName NVARCHAR(50);
DECLAR<PERSON> @schemaName NVARCHAR(5);
DECLARE @bankAccountTableName NVARCHAR(200);
DECLARE @bankAccountLedgerTableName NVARCHAR(200);
DECLARE @currencyCode NVARCHAR(3);
DECLARE @bankAccountNo NVARCHAR(10);
DECLARE @i INT;

DECLARE @tablePerDatabase TABLE (
                                 DatabaseName NVARCHAR(50),
                                 SchemaName NVARCHAR(5),
                                 BankAccountTableName NVARCHAR(200),
                                 BankAccountLedgerTableName NVARCHAR(200)
                             );

INSERT INTO @tablePerDatabase (DatabaseName, SchemaName, BankAccountTableName, BankAccountLedgerTableName)
VALUES
    ('D365BC14_CI', 'dbo', 'ATOL IVORY COAST$Bank Account','ATOL IVORY COAST$Bank Account Ledger Entry'),
    ('D365BC14_TN', 'dbo', 'Atol TN$Bank Account','Atol TN$Bank Account Ledger Entry'),
    ('D365BC14_DZ', 'dbo', 'JUMIA ALGERIA$Bank Account','JUMIA ALGERIA$Bank Account Ledger Entry'),
    ('D365BC14_GH', 'dbo', 'JUMIA GHANA$Bank Account','JUMIA GHANA$Bank Account Ledger Entry'),
    ('D365BC14_CI', 'dbo', 'JUMIA IVORY COAST$Bank Account','JUMIA IVORY COAST$Bank Account Ledger Entry'),
    ('D365BC14_KE', 'dbo', 'JUMIA KENYA$Bank Account','JUMIA KENYA$Bank Account Ledger Entry'),
    ('D365BC14_MA', 'dbo', 'JUMIA MOROCCO$Bank Account','JUMIA MOROCCO$Bank Account Ledger Entry'),
    ('D365BC14_SN', 'dbo', 'JUMIA SENEGAL$Bank Account','JUMIA SENEGAL$Bank Account Ledger Entry'),
    ('D365BC14_US', 'dbo', 'JUMIA USA$Bank Account','JUMIA USA$Bank Account Ledger Entry'),
    ('D365BC14_MA', 'dbo', 'eCommerce Service MA$Bank Account','eCommerce Service MA$Bank Account Ledger Entry'),
    ('D365BC14_SN', 'dbo', 'Hellofood SN$Bank Account','Hellofood SN$Bank Account Ledger Entry'),
    ('D365BC14_DZ', 'dbo', 'Jade DZ$Bank Account','Jade DZ$Bank Account Ledger Entry'),
    ('D365BC14_GH', 'dbo', 'Jade GH$Bank Account','Jade GH$Bank Account Ledger Entry'),
    ('D365BC14_CI', 'dbo', 'Jade IC$Bank Account','Jade IC$Bank Account Ledger Entry'),
    ('D365BC14_KE', 'dbo', 'Jade KE$Bank Account','Jade KE$Bank Account Ledger Entry'),
    ('D365BC14_TN', 'dbo', 'Jade TN$Bank Account','Jade TN$Bank Account Ledger Entry'),
    ('D365BC14_ZA', 'dbo', 'Jade ZA$Bank Account','Jade ZA$Bank Account Ledger Entry'),
    ('D365BC14_ES', 'dbo', 'JUMIA SPAIN$Bank Account','JUMIA SPAIN$Bank Account Ledger Entry'),
    ('D365BC14_TN', 'dbo', 'JM TN$Bank Account','JM TN$Bank Account Ledger Entry'),
    ('D365BC14_KE', 'dbo', 'JUMIA PAYMENT SERV KE LIMITED$Bank Account','JUMIA PAYMENT SERV KE LIMITED$Bank Account Ledger Entry'),
    ('D365BC14_TN', 'dbo', 'JUMIA PAY TN$Bank Account','JUMIA PAY TN$Bank Account Ledger Entry'),
    ('D365BC14_CI', 'dbo', 'JUMIA TECHNOLOGIES SARL$Bank Account','JUMIA TECHNOLOGIES SARL$Bank Account Ledger Entry'),
    ('D365BC14_TN', 'dbo', 'STE JUMIA TECHNOLOGIES$Bank Account','STE JUMIA TECHNOLOGIES$Bank Account Ledger Entry'),
    ('D365BC14_ZA', 'dbo', 'Lendico ZA$Bank Account','Lendico ZA$Bank Account Ledger Entry'),
    ('D365BC14_NG', 'dbo', 'Atol NG$Bank Account','Atol NG$Bank Account Ledger Entry'),
    ('D365BC14_RW', 'dbo', 'Atol RW$Bank Account','Atol RW$Bank Account Ledger Entry'),
    ('D365BC14_CM', 'dbo', 'Ecart CM$Bank Account','Ecart CM$Bank Account Ledger Entry'),
    ('D365BC14_FMS', 'dbo', 'JUMIA FMS$Bank Account','JUMIA FMS$Bank Account Ledger Entry'),
    ('D365BC14_FMS', 'dbo', 'Jumia Services EC_FZ$Bank Account','Jumia Services EC_FZ$Bank Account Ledger Entry'),
    ('D365BC14_FMS_USD', 'dbo', 'Jumia Services EC_FZ$Bank Account','Jumia Services EC_FZ$Bank Account Ledger Entry'),
    ('D365BC14_NG', 'dbo', 'JUMIA NIGERIA$Bank Account','JUMIA NIGERIA$Bank Account Ledger Entry'),
    ('D365BC14_TZ', 'dbo', 'JUMIA TANZANIA$Bank Account','JUMIA TANZANIA$Bank Account Ledger Entry'),
    ('D365BC14_FR', 'dbo', 'Africa Ecommerce Services$Bank Account','Africa Ecommerce Services$Bank Account Ledger Entry'),
    ('D365BC14_EG', 'dbo', 'Easy Taxi EG$Bank Account','Easy Taxi EG$Bank Account Ledger Entry'),
    ('D365BC14_NG', 'dbo', 'Etaxi NG$Bank Account','Etaxi NG$Bank Account Ledger Entry'),
    ('D365BC14_NG', 'dbo', 'Gabi NG$Bank Account','Gabi NG$Bank Account Ledger Entry'),
    ('D365BC14_EG', 'dbo', 'Hellofood EG$Bank Account','Hellofood EG$Bank Account Ledger Entry'),
    ('D365BC14_NG', 'dbo', 'Hellopay NG$Bank Account','Hellopay NG$Bank Account Ledger Entry'),
    ('D365BC14_FR', 'dbo', 'Africa Internet Services$Bank Account','Africa Internet Services$Bank Account Ledger Entry'),
    ('D365BC14_CM', 'dbo', 'Jade CM$Bank Account','Jade CM$Bank Account Ledger Entry'),
    ('D365BC14_NG', 'dbo', 'Jade NG$Bank Account','Jade NG$Bank Account Ledger Entry'),
    ('D365BC14_UG', 'dbo', 'Jade UG$Bank Account','Jade UG$Bank Account Ledger Entry'),
    ('D365BC14_NG', 'dbo', 'JUMIA FINANCIAL SERV LIMITED$Bank Account','JUMIA FINANCIAL SERV LIMITED$Bank Account Ledger Entry'),
    ('D365BC14_CN', 'dbo', 'Jumia China$Bank Account','Jumia China$Bank Account Ledger Entry'),
    ('D365BC14_EG', 'dbo', 'JUMIA EGYPT$Bank Account','JUMIA EGYPT$Bank Account Ledger Entry'),
    ('D365BC14_EG', 'dbo', 'Jumia Electr Payment Serv_ SAE$Bank Account','Jumia Electr Payment Serv_ SAE$Bank Account Ledger Entry'),
    ('D365BC14_UG', 'dbo', 'Jumia Payment Serv UG Limited$Bank Account','Jumia Payment Serv UG Limited$Bank Account Ledger Entry'),
    ('D365BC14_EG', 'dbo', 'Jumia For Trade$Bank Account','Jumia For Trade$Bank Account Ledger Entry'),
    ('D365BC14_TZ', 'dbo', 'Juwel TZ$Bank Account','Juwel TZ$Bank Account Ledger Entry'),
    ('D365BC14_NG', 'dbo', 'Lipco NG$Bank Account','Lipco NG$Bank Account Ledger Entry'),
    ('D365BC14_NG', 'dbo', 'R-SC NG$Bank Account','R-SC NG$Bank Account Ledger Entry'),
    ('D365BC14_PT', 'dbo', 'Jumia PTC$Bank Account','Jumia PTC$Bank Account Ledger Entry');




DECLARE @currencyMapping TABLE (DatabasePattern NVARCHAR(50), CurrencyCode NVARCHAR(3));
INSERT INTO @currencyMapping (DatabasePattern, CurrencyCode) VALUES
('D365BC14_CI', 'XOF'),
('D365BC14_TN', 'TND'),
('D365BC14_DZ', 'DZD'),
('D365BC14_GH', 'GHS'),
('D365BC14_KE', 'KES'),
('D365BC14_MA', 'MAD'),
('D365BC14_SN', 'XOF'),
('D365BC14_US', 'USD'),
('D365BC14_ZA', 'ZAR'),
('D365BC14_ES', 'EUR'),
('D365BC14_NG', 'NGN'),
('D365BC14_RW', 'RWF'),
('D365BC14_CM', 'XAF'),
('D365BC14_FMS', 'XOF'),
('D365BC14_FMS_USD', 'USD'),
('D365BC14_TZ', 'TZS'),
('D365BC14_FR', 'EUR'),
('D365BC14_EG', 'EGP'),
('D365BC14_UG', 'UGX'),
('D365BC14_CN', 'CNY'),
('D365BC14_PT', 'EUR');

DECLARE db_cursor CURSOR FOR
SELECT DatabaseName, SchemaName, BankAccountTableName, BankAccountLedgerTableName
FROM @tablePerDatabase;

OPEN db_cursor;
FETCH NEXT FROM db_cursor INTO @databaseName, @schemaName, @bankAccountTableName, @bankAccountLedgerTableName;

DECLARE @ENTRY_NO INT;
SET @ENTRY_NO = 10000;

-- Initialize hexadecimal timestamp
DECLARE @timestampHex VARCHAR(18);
SET @timestampHex = '0x000000000AE1AAF2';

WHILE @@FETCH_STATUS = 0
    BEGIN
        SELECT @currencyCode = CurrencyCode FROM @currencyMapping WHERE @databaseName LIKE DatabasePattern;

        DECLARE @SQL NVARCHAR(MAX);
        SET @SQL = '';

        SET @i = 1;

        WHILE @i <= 2
            BEGIN
                SET @bankAccountNo = 'BA' + RIGHT('0000' + CAST(@i AS NVARCHAR(4)), 4);
                SET @SQL = @SQL + 'INSERT INTO [' + @databaseName + '].[' + @schemaName + '].[' + @bankAccountTableName + '] ([No_], [Name], [Account Type], [Currency Code], [timestamp]) ' +
                           'VALUES (''' + @bankAccountNo + ''', ''Account ' + CAST(@i AS NVARCHAR) + ''', ''Checking'', ''' + @currencyCode + ''', '''+ @timestampHex +'''); ';
                SET @i = @i + 1;
                SET @timestampHex = '0x' + FORMAT(CONVERT(INT, CONVERT(VARBINARY, @timestampHex, 1)) + 1, 'X16');
            END;

        SET @i = 1;
        WHILE @i <= 10
            BEGIN
                SET @bankAccountNo = 'BA' + RIGHT('0000' + CAST(((@i - 1) / 5 + 1) AS NVARCHAR(4)), 4);  -- Two sets of 5 for each bank account
                SET @SQL = @SQL + 'INSERT INTO [' + @databaseName + '].[' + @schemaName + '].[' + @bankAccountLedgerTableName + '] ([Entry No_], [Document No_], [Document Type], [Posting Date], [Bank Account No_], [Bal_ Account No_], [Bal_ Account Type], [Amount], [Currency Code], [Reversed], [timestamp]) ' +
                           'VALUES (' + CAST(@ENTRY_NO AS NVARCHAR) + ', ''DOC' + CAST(@i AS NVARCHAR) + ''', 1, GETDATE(), ''' + @bankAccountNo + ''', ''Bal Acc Num.'', 1, 1000.00, ''' + @currencyCode + ''', 0, ''' + @timestampHex + '''); ';
                SET @i = @i + 1;
                SET @ENTRY_NO = @ENTRY_NO + 1;
                SET @timestampHex = '0x' + FORMAT(CONVERT(INT, CONVERT(VARBINARY, @timestampHex, 1)) + 1, 'X16');
            END;

        EXEC sp_executesql @SQL;

        FETCH NEXT FROM db_cursor INTO @databaseName, @schemaName, @bankAccountTableName, @bankAccountLedgerTableName;
    END;

CLOSE db_cursor;
DEALLOCATE db_cursor;