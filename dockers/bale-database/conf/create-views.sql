DECLARE @tablePerDatabase TABLE
(
    DatabaseName NVARCHAR(50),
    BankAccountLedgerTableName NVARCHAR(200),
    CountryCode NVARCHAR(10)
);

DECLARE @dbName NVARCHAR(50),
        @bankAccountLedgerTable NVARCHAR(200),
        @countryCode NVARCHAR(10),
        @SQL NVARCHAR(MAX);


DECLARE view_cursor CURSOR FOR
    SELECT DatabaseName, BankAccountLedgerTableName, CountryCode
    FROM @tablePerDatabase;


INSERT INTO @tablePerDatabase (DatabaseName, BankAccountLedgerTableName, CountryCode)
VALUES
    ('D365BC14_CI', 'ATOL IVORY COAST', 'AT_IC'),
    ('D365BC14_TN', 'Atol TN', 'AT_TN'),
    ('D365BC14_DZ', 'JUMIA ALGERIA', 'EC_DZ'),
    ('D365BC14_GH', 'J<PERSON><PERSON> GHANA', 'EC_GH'),
    ('D365BC14_CI', 'JUMIA IVORY COAST', 'EC_IC'),
    ('D365BC14_KE', '<PERSON><PERSON><PERSON> KENYA', 'EC_KE'),
    ('D365BC14_MA', 'JUMIA MOROCCO', 'EC_MA'),
    ('D365BC14_SN', 'JUMIA SENEGAL', 'EC_SN'),
    ('D365BC14_US', 'JUMIA USA', 'EC_US'),
    ('D365BC14_MA', 'eCommerce Service MA', 'ES_MA'),
    ('D365BC14_SN', 'Hellofood SN', 'HF_SN'),
    ('D365BC14_DZ', 'Jade DZ', 'JD_DZ'),
    ('D365BC14_GH', 'Jade GH', 'JD_GH'),
    ('D365BC14_CI', 'Jade IC', 'JD_IC'),
    ('D365BC14_KE', 'Jade KE', 'JD_KE'),
    ('D365BC14_TN', 'Jade TN', 'JD_TN'),
    ('D365BC14_ZA', 'Jade ZA', 'JD_ZA'),
    ('D365BC14_ES', 'JUMIA SPAIN', 'JM_ES'),
    ('D365BC14_TN', 'JM TN', 'JM_TN'),
    ('D365BC14_KE', 'JUMIA PAYMENT SERV KE LIMITED', 'JP_KE'),
    ('D365BC14_TN', 'JUMIA PAY TN', 'JP_TN'),
    ('D365BC14_CI', 'JUMIA TECHNOLOGIES SARL', 'JT_IC'),
    ('D365BC14_TN', 'STE JUMIA TECHNOLOGIES', 'JT_TN'),
    ('D365BC14_ZA', 'Lendico ZA', 'LN_ZA'),
    ('D365BC14_NG', 'Atol NG', 'AT_NG'),
    ('D365BC14_RW', 'Atol RW', 'AT_RW'),
    ('D365BC14_CM', 'Ecart CM', 'EC_CM'),
    ('D365BC14_FMS', 'JUMIA FMS', 'EC_FM'),
    ('D365BC14_FMS', 'Jumia Services EC_FZ', 'EC_FZ'),
    ('D365BC14_FMS_USD', 'Jumia Services EC_FZ', 'EC_FZ_USD'),
    ('D365BC14_NG', 'JUMIA NIGERIA', 'EC_NG'),
    ('D365BC14_TZ', 'JUMIA TANZANIA', 'EC_TZ'),
    ('D365BC14_FR', 'Africa Ecommerce Services', 'ES_FR'),
    ('D365BC14_EG', 'Easy Taxi EG', 'ET_EG'),
    ('D365BC14_NG', 'Etaxi NG', 'ET_NG'),
    ('D365BC14_NG', 'Gabi NG', 'GB_NG'),
    ('D365BC14_EG', 'Hellofood EG', 'HF_EG'),
    ('D365BC14_NG', 'Hellopay NG', 'HP_NG'),
    ('D365BC14_FR', 'Africa Internet Services', 'IS_FR'),
    ('D365BC14_CM', 'Jade CM', 'JD_CM'),
    ('D365BC14_NG', 'Jade NG', 'JD_NG'),
    ('D365BC14_UG', 'Jade UG', 'JD_UG'),
    ('D365BC14_NG', 'JUMIA FINANCIAL SERV LIMITED', 'JF_NG'),
    ('D365BC14_CN', 'Jumia China', 'JM_CN'),
    ('D365BC14_EG', 'JUMIA EGYPT', 'JM_EG'),
    ('D365BC14_EG', 'Jumia Electr Payment Serv_ SAE', 'JP_EG'),
    ('D365BC14_UG', 'Jumia Payment Serv UG Limited', 'JP_UG'),
    ('D365BC14_EG', 'Jumia For Trade', 'JT_EG'),
    ('D365BC14_TZ', 'Juwel TZ', 'JW_TZ'),
    ('D365BC14_NG', 'Lipco NG', 'LP_NG'),
    ('D365BC14_NG', 'R-SC NG', 'RS_NG'),
    ('D365BC14_PT', 'Jumia PTC', 'SK_PT');

OPEN view_cursor;
FETCH NEXT FROM view_cursor INTO @dbName, @bankAccountLedgerTable, @countryCode;

WHILE @@FETCH_STATUS = 0
BEGIN

     SET @SQL = 'USE ' + QUOTENAME(@dbName) + ';
                    IF OBJECT_ID(''BRAD.' + QUOTENAME(@bankAccountLedgerTable + '$Bank Account Ledger') + ''', ''V'') IS NULL
                    BEGIN
                        EXEC(''
                            CREATE VIEW BRAD.' + QUOTENAME(@bankAccountLedgerTable + '$Bank Account Ledger') + ' AS
                            SELECT
                                ''''' + @countryCode + ''''' AS id_company,
                                ba.[No_],
                                ba.[Name],
                                ba.[Account Type],
                                ba.[Currency Code],
                                ba.[Kyriba Ref_],
                                baf.[Entry No_],
                                baf.[Document No_],
                                CASE baf.[Document Type]
                                    WHEN 1 THEN ''''Payment''''
                                    WHEN 2 THEN ''''Invoice''''
                                    WHEN 3 THEN ''''Credit Memo''''
                                    WHEN 4 THEN ''''Finance Charge Memo''''
                                    WHEN 5 THEN ''''Reminder''''
                                    WHEN 6 THEN ''''Refund''''
                                    ELSE NULL
                                END AS [Document Type],
                                baf.[Posting Date],
                                baf.[Bank Account No_],
                                baf.[Bank Acc_ Posting Group] AS [Bank Account Posting Group],
                                baf.[Description],
                                baf.[Source Code],
                                baf.[Reason Code],
                                baf.[Global Dimension 1 Code] AS [Busline],
                                baf.[Global Dimension 2 Code] AS [Department],
                                baf.[Amount],
                                baf.[Remaining Amount],
                                baf.[Currency Code] AS [Currency],
                                baf.[Amount (LCY)],
                                baf.[Bal_ Account No_],
                                CASE baf.[Bal_ Account Type]
                                    WHEN 0 THEN ''''G/L Account''''
                                    WHEN 1 THEN ''''Customer''''
                                    WHEN 2 THEN ''''Vendor''''
                                    WHEN 3 THEN ''''Bank Account''''
                                    WHEN 4 THEN ''''Fixed Asset''''
                                    ELSE NULL
                                END AS [Bal_ Account Type],
                                baf.[Open],
                                baf.[Reversed],
                                baf.[User ID] AS [Posted by],
                                baf.[External Document No_],
                                baf.[timestamp] AS [BALE_Timestamp],
                                ba.[timestamp] AS [BA_Timestamp]
                            FROM
                                dbo.' + QUOTENAME(@bankAccountLedgerTable + '$Bank Account') + ' ba
                            LEFT JOIN
                                dbo.' + QUOTENAME(@bankAccountLedgerTable + '$Bank Account Ledger Entry') + ' baf ON ba.No_ = baf.[Bank Account No_]'');
                    END';


        EXEC sp_executesql @SQL;


        FETCH NEXT FROM view_cursor INTO @dbName, @bankAccountLedgerTable, @countryCode;

END;

CLOSE view_cursor;
DEALLOCATE view_cursor;

