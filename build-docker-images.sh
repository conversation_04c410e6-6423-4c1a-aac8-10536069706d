#!/bin/bash
BE_IMAGE_KEY="BE"
FE_IMAGE_KEY="FE"
DEFAULT_BUILD_IMAGE="$BE_IMAGE_KEY,$FE_IMAGE_KEY"

IMAGES="${1:-$DEFAULT_BUILD_IMAGE}"
NEXUS_USERNAME="${2}"
NEXUS_PASSWORD="${3}"
NEXUS_PATH="${4}"
NEXUS_URL="${5}"

BASE_PATH="$(pwd)"
BRAD_BACKEND_PATH="$BASE_PATH/brad-backend"
BRAD_FRONTEND_PATH="$BASE_PATH/brad-frontend"
DOCKERS_CONFIG_PATH="$BASE_PATH/dockers"

build_fe_image() {
    cd $BASE_PATH

    rm -rf $DOCKERS_CONFIG_PATH/brad-frontend/dist/build/
    mkdir -p $DOCKERS_CONFIG_PATH/brad-frontend/dist/build

    echo "Building brad frontend"
    cd $BRAD_FRONTEND_PATH

    npm ci concurrently
    npm run build-aws

    cd $BASE_PATH

    cp -r $BRAD_FRONTEND_PATH/dist/brad-frontend/* $BASE_PATH/dockers/brad-frontend/dist/build/

    docker-compose -f $DOCKERS_CONFIG_PATH/docker-compose.yml build brad-fe.services

    rm -rf $BASE_PATH/dockers/brad-frontend/dist/build/
}

build_be_image() {
    cd $BASE_PATH

    rm -f $DOCKERS_CONFIG_PATH/brad-backend/app/*.jar

    echo "Building brad backend"
    cd $BRAD_BACKEND_PATH

    if [ -z "$NEXUS_USERNAME" ] && [ -z "$NEXUS_PASSWORD" ] && [ -z "$NEXUS_PATH" ] && [ -z "$NEXUS_URL" ]
    then
        ./gradlew clean assemble
    else
        ./gradlew clean assemble -PnexusUsername="$NEXUS_USERNAME" -PnexusPassword="$NEXUS_PASSWORD" -PnexusPath="$NEXUS_PATH" -PnexusUrl="$NEXUS_URL"
    fi

    cd $BASE_PATH

    mkdir -p $DOCKERS_CONFIG_PATH/brad-backend/app
    cp $BRAD_BACKEND_PATH/build/libs/*.jar $DOCKERS_CONFIG_PATH/brad-backend/app

    docker-compose -f $DOCKERS_CONFIG_PATH/docker-compose.yml build brad-be

    rm -rf $DOCKERS_CONFIG_PATH/brad-backend/app
}

build_images() {
    images_array=($(echo $IMAGES | tr "," "\n"))

    for index in "${!images_array[@]}"
    do
        img="${images_array[$index]}"

        if [ "$img" = "$BE_IMAGE_KEY" ]; then
            build_be_image
        elif [ "$img" = "$FE_IMAGE_KEY" ]; then
            build_fe_image
        else
            echo "invalid"
        fi
    done

}

build_images
