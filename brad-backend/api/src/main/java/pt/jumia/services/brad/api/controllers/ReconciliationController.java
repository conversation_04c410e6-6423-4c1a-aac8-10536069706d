package pt.jumia.services.brad.api.controllers;

import com.neovisionaries.i18n.CountryCode;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import pt.jumia.services.brad.api.payloads.request.PageFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.reconciliation.ReconciliationApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.reconciliation.ReconciliationFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.reconciliation.ReconciliationSortFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.response.DifferenceBetweenThresholdApiResponsePayload;
import pt.jumia.services.brad.api.payloads.response.PageApiResponsePayload;
import pt.jumia.services.brad.api.payloads.response.ReconciliationApiResponsePayload;
import pt.jumia.services.brad.domain.RequestContext;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.reconciliation.ReconciliationFilters;
import pt.jumia.services.brad.domain.entities.filter.reconciliation.ReconciliationSortFilters;
import pt.jumia.services.brad.domain.exceptions.*;
import pt.jumia.services.brad.domain.usecases.accounts.ReadAccountsUseCase;
import pt.jumia.services.brad.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.brad.domain.usecases.reconciliation.CreateReconciliationUseCase;
import pt.jumia.services.brad.domain.usecases.reconciliation.ReadReconciliationUseCase;
import pt.jumia.services.brad.domain.usecases.reconciliation.UpdateReconciliationUseCase;

import java.text.ParseException;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping(value = "/api/reconciliation")
public class ReconciliationController {

    private final ValidateUserAccessUseCase validateUserAccessUseCase;
    private final ReadReconciliationUseCase readReconciliationUseCase;
    private final CreateReconciliationUseCase createReconciliationUseCase;
    private final UpdateReconciliationUseCase updateReconciliationUseCase;
    private final ReadAccountsUseCase readAccountsUseCase;

    @PostMapping(value = "/selectedAmountDifference")
    public DifferenceBetweenThresholdApiResponsePayload getSelectedAmountDifference(@RequestBody @Valid ReconciliationApiRequestPayload payload)
            throws UserForbiddenException, NotFoundException {

        validateUserAccessUseCase.checkCanAccessReconciliation(RequestContext.getUser());


        log.info("Fetching selected amount difference for user with identifier {}",
                RequestContext.getUsername());

        return new DifferenceBetweenThresholdApiResponsePayload(
                readReconciliationUseCase.executeAmount(
                        payload.getAccount(),
                        payload.getBaleIds(),
                        payload.getTransactionIds()
                )
        );
    }

    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public ReconciliationApiResponsePayload reconcile(@RequestBody @Valid ReconciliationApiRequestPayload payload)
            throws UserForbiddenException, ReconciliationException, EntityErrorsException, NotFoundException {

        validateUserAccessUseCase.checkCanAccessReconciliation(RequestContext.getUser());
        Long account = payload.getAccount();
        String country = readAccountsUseCase.execute(Math.toIntExact(account)).getCountry().getCode();
        validateUserAccessUseCase.checkCanManageReconciliation(RequestContext.getUser(), CountryCode.valueOf(country));

        log.info("Reconciling for user with identifier {}", RequestContext.getUsername());

        return new ReconciliationApiResponsePayload(createReconciliationUseCase.execute(
                account,
                payload.toEntity()
        ));
    }

    @GetMapping(value = "/{id}")
    public ReconciliationApiResponsePayload fetchById(@PathVariable(value = "id") Integer id)
            throws NotFoundException, UserForbiddenException {

        validateUserAccessUseCase.checkCanAccessReconciliation(RequestContext.getUser());

        log.info("Fetching reconciliation {} for user with identifier {}",
                id, RequestContext.getUsername());

        return new ReconciliationApiResponsePayload(readReconciliationUseCase.execute(id));

    }

    @GetMapping
    public PageApiResponsePayload<ReconciliationApiResponsePayload> getReconciliations(HttpServletRequest httpServletRequest,
               @Valid ReconciliationFiltersApiRequestPayload reconciliationFiltersApiRequestPaylod,
               @Valid ReconciliationSortFiltersApiRequestPayload reconciliationSortFiltersApiRequestPayload,
               @Valid PageFiltersApiRequestPayload pageFiltersApiRequestPayload)
            throws UserForbiddenException, EntityErrorsException, ParseException {

        validateUserAccessUseCase.checkCanAccessReconciliation(RequestContext.getUser());

        log.info("Fetching reconciliations for user with identifier {}",
                RequestContext.getUsername());

        ReconciliationFilters reconciliationFilters = reconciliationFiltersApiRequestPaylod.toEntity();
        ReconciliationSortFilters reconciliationSortFilters = reconciliationSortFiltersApiRequestPayload.toEntity();
        PageFilters pageFilters = pageFiltersApiRequestPayload.toEntity();

        List<ReconciliationApiResponsePayload> reconciliations = readReconciliationUseCase.execute(reconciliationFilters,
                        reconciliationSortFilters, pageFilters)
                .stream().map(ReconciliationApiResponsePayload::new).collect(Collectors.toList());

        long total = readReconciliationUseCase.executeCount(reconciliationFilters);

        return PageApiResponsePayload.buildPageResponsePayload(
                httpServletRequest,
                pageFilters,
                reconciliations,
                total
        );
    }

    @PostMapping(value = "/unmatch-by-ids")
    public void unmatch(@RequestBody @Valid ReconciliationApiRequestPayload payload)
            throws UserForbiddenException, NotFoundException, ConflictOfInterestException, ReconciliationException {

        Long account = payload.getAccount();
        String country = readAccountsUseCase.execute(Math.toIntExact(account)).getCountry().getCode();
        validateUserAccessUseCase.checkCanManageReconciliation(RequestContext.getUser(), CountryCode.valueOf(country));

        log.info("Unmatching reconciliation by ids {} for user with identifier {}",
                payload,
                RequestContext.getUsername());

        updateReconciliationUseCase.executeUnmatchByIds(payload.toEntity());

    }

    @PostMapping(value = "/approve-by-ids")
    public void approve(@RequestBody @Valid ReconciliationApiRequestPayload payload)
            throws UserForbiddenException, NotFoundException, ConflictOfInterestException, ReconciliationException {

        Long account = payload.getAccount();
        String country = readAccountsUseCase.execute(Math.toIntExact(account)).getCountry().getCode();
        validateUserAccessUseCase.checkCanManageReconciliation(RequestContext.getUser(), CountryCode.valueOf(country));

        log.info("Approving reconciliation by ids {} for user with identifier {}",
                payload,
                RequestContext.getUsername());

        updateReconciliationUseCase.executeApprove(payload.toEntity());

    }

    @GetMapping(value = "/reconciliation-by-transaction-id/{transactionId}")
    public ReconciliationApiResponsePayload fetchReconciliationByTransactionId(@PathVariable(value = "transactionId") Long transactionId)
            throws NotFoundException, UserForbiddenException {

        validateUserAccessUseCase.checkCanAccessReconciliation(RequestContext.getUser());

        log.info("Fetching pending reconciliation by transaction id {} for user with identifier {}",
                transactionId, RequestContext.getUsername());

        return new ReconciliationApiResponsePayload(readReconciliationUseCase.executeReconciliationByTransactionId(transactionId));
    }

    @GetMapping(value = "/reconciliation-by-bale-id/{baleId}")
    public ReconciliationApiResponsePayload fetchReconciliationByBaleId(@PathVariable(value = "baleId") Long baleId)
            throws NotFoundException, UserForbiddenException {

        validateUserAccessUseCase.checkCanAccessReconciliation(RequestContext.getUser());

        log.info("Fetching pending reconciliation by bale id {} for user with identifier {}",
                baleId, RequestContext.getUsername());

        return new ReconciliationApiResponsePayload(readReconciliationUseCase.executeReconciliationByBaleId(baleId));
    }


    @GetMapping(value = "/isReconciledStatuses")
    public List<String> getIsReconciledStatuses() throws UserForbiddenException {
        validateUserAccessUseCase.checkCanAccess(RequestContext.getUser());
        log.info("Fetching all IsReconciled Statuses for user with identifier {}",
                RequestContext.getUsername());
        return readReconciliationUseCase.executeIsReconciledStatus();
    }

    @GetMapping(value = "/reconciliationStatuses")
    public List<String> getReconciliationStatuses() throws UserForbiddenException {
        validateUserAccessUseCase.checkCanAccess(RequestContext.getUser());
        log.info("Fetching all Reconciliation Statuses for user with identifier {}",
                RequestContext.getUsername());
        return readReconciliationUseCase.executeReconciliationStatus();
    }

}
