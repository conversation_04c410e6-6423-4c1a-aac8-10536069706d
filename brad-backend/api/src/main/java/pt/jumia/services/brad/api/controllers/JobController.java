package pt.jumia.services.brad.api.controllers;


import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.SchedulerException;
import org.springframework.web.bind.annotation.*;
import pt.jumia.services.brad.api.payloads.request.JobApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.scheduler.BaleJobApiRequestPayload;
import pt.jumia.services.brad.api.payloads.response.JobApiResponsePayload;
import pt.jumia.services.brad.domain.RequestContext;
import pt.jumia.services.brad.domain.entities.Jobs;
import pt.jumia.services.brad.domain.exceptions.UserForbiddenException;
import pt.jumia.services.brad.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.brad.domain.usecases.jobs.ReadJobsUseCase;
import pt.jumia.services.brad.domain.usecases.jobs.RunJobsUsecase;
import pt.jumia.services.brad.domain.usecases.jobs.UpdateJobUseCase;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/api/" + JobController.JOB + "s")
public class JobController {

    public static final String JOB = "job";
    private final ReadJobsUseCase readJobsUseCase;
    private final UpdateJobUseCase updateJobUseCase;
    private final RunJobsUsecase runJobsUsecase;
    private final ValidateUserAccessUseCase validateUserAccessUseCase;


    @GetMapping
    public List<JobApiResponsePayload> getAllJob() throws SchedulerException, UserForbiddenException {

        validateUserAccessUseCase.checkCanAccessScheduler(RequestContext.getUser());

        log.info("Fetching all " + JOB + "s for user with identifier {}",
                RequestContext.getUsername());
        List<Jobs> jobs = readJobsUseCase.execute();
        List<JobApiResponsePayload> allJobs = new ArrayList<>();
        for (Jobs job : jobs) {
            JobApiResponsePayload jobApiResponsePayload = new JobApiResponsePayload(job);
            allJobs.add(jobApiResponsePayload);
        }

        return allJobs;

    }

    @GetMapping(value = "/{" + JOB + "}")
    public JobApiResponsePayload getJob(@PathVariable(value = JOB) String job) throws SchedulerException, UserForbiddenException {

        validateUserAccessUseCase.checkCanAccessScheduler(RequestContext.getUser());
        log.info("Fetching " + JOB + " {} for user with identifier {}",
            job, RequestContext.getUsername());
        Jobs jobDetail = readJobsUseCase.execute(job);

        return new JobApiResponsePayload(jobDetail);


    }

    @PutMapping(value = "/{" + JOB + "}")
    public void updateJob(@PathVariable(value = JOB) String job, @RequestBody @Valid JobApiRequestPayload payload)
            throws SchedulerException, UserForbiddenException {

        validateUserAccessUseCase.checkCanAccessScheduler(RequestContext.getUser());
        validateUserAccessUseCase.checkCanManageScheduler(RequestContext.getUser());

        log.info("User with identifier {} updating " + JOB + " named {}",
                RequestContext.getUsername(), job);

        updateJobUseCase.execute(job, payload.toEntity());

    }


    @PostMapping(value = "/{" + JOB + "}/force-run")
    public void runJob(@PathVariable(value = JOB) String job) throws SchedulerException, UserForbiddenException {

        validateUserAccessUseCase.checkCanAccessScheduler(RequestContext.getUser());
        validateUserAccessUseCase.checkCanManageScheduler(RequestContext.getUser());

        log.info("User with identifier {} running " + JOB + " named {}",
                RequestContext.getUsername(), job);

        runJobsUsecase.execute(job);

    }

    @PostMapping(value = "/{" + JOB + "}/toggle-state")
    public void toggleJobState(@PathVariable(value = JOB) String job) throws SchedulerException, UserForbiddenException {

        validateUserAccessUseCase.checkCanAccessScheduler(RequestContext.getUser());
        validateUserAccessUseCase.checkCanManageScheduler(RequestContext.getUser());

        log.info("User with identifier {} changing state for job " + JOB + " named {}",
            RequestContext.getUsername(), job);

        updateJobUseCase.toggleState(job);

    }

    @GetMapping(value = "/bale-" + JOB)
    public JobApiResponsePayload getBaleJob() throws UserForbiddenException, SchedulerException {

        validateUserAccessUseCase.checkCanAccessScheduler(RequestContext.getUser());

        log.info("Fetching bale " + JOB + " name for user with identifier {}",
                RequestContext.getUsername());

        Jobs jobDetail = readJobsUseCase.getBaleJob();

        return new JobApiResponsePayload(jobDetail);
    }


    @PostMapping(value = "/bale-" + JOB + "/force-run")
    public void runBaleJob(HttpServletRequest httpServletRequest,
                           @Valid BaleJobApiRequestPayload baleJobApiRequestPayload) throws SchedulerException, UserForbiddenException {

        validateUserAccessUseCase.checkCanAccessScheduler(RequestContext.getUser());
        validateUserAccessUseCase.checkCanManageScheduler(RequestContext.getUser());

        log.info("User with identifier {} running bale " + JOB,
                RequestContext.getUsername());

        runJobsUsecase.execute(baleJobApiRequestPayload.getJobName());

    }
}
