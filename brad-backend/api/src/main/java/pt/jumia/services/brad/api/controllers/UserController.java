package pt.jumia.services.brad.api.controllers;

import com.neovisionaries.i18n.CountryCode;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import pt.jumia.services.brad.api.csvs.exports.UserExportsCSV;
import pt.jumia.services.brad.api.payloads.request.PageFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.account.AccountFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.user.UserApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.user.UserFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.user.UserSortFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.response.PageApiResponsePayload;
import pt.jumia.services.brad.api.payloads.response.UserApiResponsePayload;
import pt.jumia.services.brad.domain.RequestContext;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.account.Country;
import pt.jumia.services.brad.domain.entities.account.User;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.account.AccountFilters;
import pt.jumia.services.brad.domain.entities.filter.user.UserFilters;
import pt.jumia.services.brad.domain.entities.filter.user.UserSortFilters;
import pt.jumia.services.brad.domain.exceptions.AlreadyExistsException;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.exceptions.UserForbiddenException;
import pt.jumia.services.brad.domain.usecases.accounts.ReadAccountsUseCase;
import pt.jumia.services.brad.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.brad.domain.usecases.countries.ReadCountriesUseCase;
import pt.jumia.services.brad.domain.usecases.user.CreateUserUseCase;
import pt.jumia.services.brad.domain.usecases.user.DeleteUserUseCase;
import pt.jumia.services.brad.domain.usecases.user.ReadUserUseCase;
import pt.jumia.services.brad.domain.usecases.user.UpdateUserUseCase;
import pt.jumia.services.brad.domain.utils.JsonUtils;

import java.text.ParseException;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * Controller responsible for handling CRUD operations for users.
 */
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping(value = "/api/users")
public class UserController {

    private final CreateUserUseCase createUserUseCase;
    private final ReadUserUseCase readUserUseCase;
    private final UpdateUserUseCase updateUserUseCase;
    private final DeleteUserUseCase deleteUserUseCase;
    private final ValidateUserAccessUseCase validateUserAccessUseCase;
    private final ReadCountriesUseCase readCountriesUseCase;
    private final ReadAccountsUseCase readAccountsUseCase;


    @GetMapping
    public PageApiResponsePayload<UserApiResponsePayload> fetch(HttpServletRequest httpServletRequest,
                                                                @Valid UserFiltersApiRequestPayload userFiltersApiRequestPayload,
                                                                @Valid UserSortFiltersApiRequestPayload userSortFiltersApiRequestPayload,
                                                                @Valid PageFiltersApiRequestPayload pageFiltersApiRequestPayload)
            throws UserForbiddenException, EntityErrorsException, NotFoundException {

        String country = readAccountsUseCase.execute(Math.toIntExact(userFiltersApiRequestPayload.getAccountID()))
                .getCountry().getCode();
        validateUserAccessUseCase.checkCanAccessAccounts(RequestContext.getUser(), CountryCode.valueOf(country));

        log.info("Fetching all users for admin with identifier {}",
                RequestContext.getUsername());
        UserFilters userFilters = userFiltersApiRequestPayload.toEntity();
        UserSortFilters userSortFilters = userSortFiltersApiRequestPayload.toEntity();
        PageFilters pageFilters = pageFiltersApiRequestPayload.toEntity();


        List<UserApiResponsePayload> users = readUserUseCase.execute(userFilters,
                        userSortFilters, pageFilters)
                .stream().map(UserApiResponsePayload::new)
                .collect(Collectors.toList());


        long total = readUserUseCase.executeCount(userFilters);


        return PageApiResponsePayload.buildPageResponsePayload(
                httpServletRequest,
                pageFilters,
                users,
                total
        );
    }

    @GetMapping(value = "/account/{id}")
    public List<UserApiResponsePayload> fetchUserByAccount(@PathVariable(value = "id") Long accountId)
            throws UserForbiddenException, EntityErrorsException, NotFoundException {

        String country = readAccountsUseCase.execute(Math.toIntExact(accountId))
                .getCountry().getCode();
        validateUserAccessUseCase.checkCanAccessAccounts(RequestContext.getUser(), CountryCode.valueOf(country));

        log.info("Fetching users for account {} for user with identifier {}",
                accountId, RequestContext.getUsername());

        UserFilters userFilters = UserFilters.builder()
                .accountID(accountId)
                .build();
        UserSortFilters userSortFilters = UserSortFilters.builder()
                .field(User.SortingFields.USER_NAME)
                .build();
        PageFilters pageFilters = null;

        return readUserUseCase.execute(userFilters,
                        userSortFilters, pageFilters)
                .stream().map(UserApiResponsePayload::new)
                .collect(Collectors.toList());
    }


    @GetMapping(value = "/{id}")
    public UserApiResponsePayload fetchById(@PathVariable(value = "id") Integer id)
            throws NotFoundException, UserForbiddenException {

        try {
            User user = readUserUseCase.execute(id);
            validateUserAccessUseCase.checkCanAccessAccounts(RequestContext.getUser(),
                    CountryCode.valueOf(user.getAccount().getCountry().getCode()));

            log.info("Fetching user {} for admin with identifier {}",
                    id, RequestContext.getUsername());

            return new UserApiResponsePayload(user);
        } catch (NotFoundException e) {
            throw NotFoundException.createNotFound(User.class, id);
        }
    }


    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public UserApiResponsePayload create(@RequestBody @Valid UserApiRequestPayload payload)
            throws UserForbiddenException, AlreadyExistsException, NotFoundException, EntityErrorsException {

        String country = readAccountsUseCase.execute(Math.toIntExact(payload.getAccountID()))
                .getCountry().getCode();
        validateUserAccessUseCase.checkCanManageAccounts(RequestContext.getUser(), CountryCode.valueOf(country));
        log.info("Creating new user: {} for admin with identifier {}",
                JsonUtils.toJson(payload), RequestContext.getUsername());

        return new UserApiResponsePayload(createUserUseCase.execute(payload.toEntity(), payload.getAccountID()));


    }

    @PutMapping(value = "/{id}")
    public void update(@PathVariable(value = "id") Long id, @RequestBody @Valid UserApiRequestPayload payload)
            throws NotFoundException, UserForbiddenException, EntityErrorsException {

        String country = readAccountsUseCase.execute(Math.toIntExact(payload.getAccountID()))
                .getCountry().getCode();

        validateUserAccessUseCase.checkCanManageAccounts(RequestContext.getUser(), CountryCode.valueOf(country));
        log.info("Updating user with id {} : {} for admin with identifier {}",
                id, JsonUtils.toJson(payload), RequestContext.getUsername());

        updateUserUseCase.execute(payload.toEntity());

    }

    @DeleteMapping(value = "/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void delete(@PathVariable(value = "id") Integer id)
            throws NotFoundException, UserForbiddenException {

        String country = readUserUseCase.execute(id).getAccount().getCountry().getCode();
        validateUserAccessUseCase.checkCanManageAccounts(RequestContext.getUser(), CountryCode.valueOf(country));


        log.info("Deleting user with id {} for admin with identifier {}",
                id, RequestContext.getUsername());

        deleteUserUseCase.execute(id);

    }

    @GetMapping(value = "/download")
    @ResponseStatus(HttpStatus.OK)
    public Future<ResponseEntity<byte[]>> download(@Valid AccountFiltersApiRequestPayload accountFiltersApiRequestPayload)
            throws UserForbiddenException, NotFoundException, ParseException, EntityErrorsException {

        log.info("Downloading Bank Account Users for user with identifier {}", RequestContext.getUsername());

        AccountFilters accountFilters = accountFiltersApiRequestPayload.toEntity();
        List<Long> bankAccountIds = readAccountsUseCase.execute(accountFilters, null, null).stream()
                .map(Account::getId).toList();

        UserFilters userFilters = UserFilters.builder().accountIds(bankAccountIds).build();

        userFilters = getUserFiltersWithUpdatedCountryCodesBasedOnUserAccessToDownload(userFilters);

        CompletableFuture<ResponseEntity<byte[]>> future = new CompletableFuture<>();
        UserFilters finalUserFilters = userFilters;
        CompletableFuture.runAsync(() -> {
            try {
                List<User> users = readUserUseCase.execute(finalUserFilters,
                        null, null);

                future.complete(UserExportsCSV.buildUsersCSV(users));
            } catch (Exception e) {
                log.error("Error downloading Users", e);
                future.completeExceptionally(e);
            }
        });
        return future;
    }

    private UserFilters getUserFiltersWithUpdatedCountryCodesBasedOnUserAccessToDownload
            (UserFilters userFilters)
            throws UserForbiddenException {
        List<CountryCode> countriesWithCanViewPermission =
                validateUserAccessUseCase.getCountriesCanDownloadUsersOrThrow(RequestContext.getUser());
        return getUserFiltersAccordingToCountryPermissions(userFilters, countriesWithCanViewPermission);
    }

    private UserFilters getUserFiltersAccordingToCountryPermissions
            (UserFilters userFilters, List<CountryCode> countriesWithCanViewPermission) {

        List<Long> filteredCountryCodes = countriesWithCanViewPermission.stream().map(countryCode -> {
                try {
                    Country country = readCountriesUseCase.execute(String.valueOf(countryCode));
                    log.info("Country found for : {}", countryCode);
                    return country.getId();
                } catch (NotFoundException e) {

                    return null;
                }
            }).filter(Objects::nonNull).collect(Collectors.toList());

        userFilters.setCountryCodes(filteredCountryCodes);
        return userFilters;
    }

}
