package pt.jumia.services.brad.api.controllers;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import pt.jumia.services.brad.api.payloads.request.PageFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.threshold.ThresholdApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.threshold.ThresholdFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.threshold.ThresholdSortFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.response.PageApiResponsePayload;
import pt.jumia.services.brad.api.payloads.response.ThresholdApiResponsePayload;
import pt.jumia.services.brad.domain.RequestContext;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.threshold.ThresholdFilters;
import pt.jumia.services.brad.domain.entities.filter.threshold.ThresholdSortFilters;
import pt.jumia.services.brad.domain.exceptions.AlreadyExistsException;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.exceptions.UserForbiddenException;
import pt.jumia.services.brad.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.brad.domain.usecases.thresholds.CreateThresholdUseCase;
import pt.jumia.services.brad.domain.usecases.thresholds.DeleteThresholdUseCase;
import pt.jumia.services.brad.domain.usecases.thresholds.ReadThresholdUseCase;
import pt.jumia.services.brad.domain.usecases.thresholds.UpdateThresholdUseCase;
import pt.jumia.services.brad.domain.utils.JsonUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Controller responsible for handling CRUD operations for threshold.
 */
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping(value = "/api/thresholds")
public class ThresholdController {

    private final CreateThresholdUseCase createThresholdUseCase;
    private final ReadThresholdUseCase readThresholdUseCase;
    private final UpdateThresholdUseCase updateThresholdUseCase;
    private final DeleteThresholdUseCase deleteThresholdUseCase;

    private final ValidateUserAccessUseCase validateUserAccessUseCase;

    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public ThresholdApiResponsePayload create(@RequestBody @Valid ThresholdApiRequestPayload thresholdApiRequestPayload)
            throws UserForbiddenException, AlreadyExistsException, EntityErrorsException, NotFoundException {

        validateUserAccessUseCase.checkCanManageThresholds(RequestContext.getUser());

        log.info("Creating threshold {} for user with identifier {}",
                JsonUtils.toJson(thresholdApiRequestPayload), RequestContext.getUsername());

        return new ThresholdApiResponsePayload(createThresholdUseCase.execute(thresholdApiRequestPayload.toEntity()));

    }

    @GetMapping(value = "/{id}")
    public ThresholdApiResponsePayload fetchById(@PathVariable(value = "id") Long id)
            throws NotFoundException, UserForbiddenException {

        validateUserAccessUseCase.checkCanManageThresholds(RequestContext.getUser());

        log.info("Fetching threshold {} for user with identifier {}",
                id, RequestContext.getUsername());

        return new ThresholdApiResponsePayload(readThresholdUseCase.execute(id));

    }

    @GetMapping
    public PageApiResponsePayload<ThresholdApiResponsePayload> fetchThresholds(
            HttpServletRequest httpServletRequest,
            @Valid ThresholdFiltersApiRequestPayload thresholdFiltersApiRequestPayload,
            @Valid ThresholdSortFiltersApiRequestPayload thresholdSortFiltersApiRequestPayload,
            @Valid PageFiltersApiRequestPayload pageFiltersApiRequestPayload
    ) throws UserForbiddenException, EntityErrorsException {

        validateUserAccessUseCase.checkCanManageThresholds(RequestContext.getUser());

        log.info("Fetching all thresholds for user with identifier {}",
                RequestContext.getUsername());

        ThresholdFilters thresholdFilters = thresholdFiltersApiRequestPayload.toEntity();
        ThresholdSortFilters thresholdSortFilters = thresholdSortFiltersApiRequestPayload.toEntity();
        PageFilters pageFilters = pageFiltersApiRequestPayload.toEntity();

        List<ThresholdApiResponsePayload> thresholdList = readThresholdUseCase.executeWithDefault(
                thresholdFilters, thresholdSortFilters, pageFilters)
                .stream().map(ThresholdApiResponsePayload::new).collect(Collectors.toList());


        long total = readThresholdUseCase.executeCount(thresholdFilters);


        return PageApiResponsePayload.buildPageResponsePayload(
                httpServletRequest,
                pageFilters,
                thresholdList,
                total
        );

    }

    @PutMapping(value = "/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void updateThreshold(@PathVariable(value = "id") Long id, @RequestBody @Valid ThresholdApiRequestPayload payload)
            throws UserForbiddenException, NotFoundException, EntityErrorsException {

        validateUserAccessUseCase.checkCanManageThresholds(RequestContext.getUser());

        log.info("Updating threshold with id {} : {} for user with identifier {}",
                id, JsonUtils.toJson(payload), RequestContext.getUsername());

        payload.setId(id);
        updateThresholdUseCase.execute(payload.toEntity());
    }

    @DeleteMapping(value = "/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void delete(@PathVariable(value = "id") Integer id)
            throws NotFoundException, UserForbiddenException {

        validateUserAccessUseCase.checkCanManageThresholds(RequestContext.getUser());

        log.info("Deleting threshold with id {} for user with identifier {}",
                id, RequestContext.getUsername());

        deleteThresholdUseCase.execute(id);
    }
}
