package pt.jumia.services.brad.api.controllers;

import com.neovisionaries.i18n.CountryCode;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import pt.jumia.services.brad.api.payloads.request.PageFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.document.DocumentApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.document.DocumentFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.document.DocumentSortFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.response.DocumentApiResponsePayload;
import pt.jumia.services.brad.api.payloads.response.PageApiResponsePayload;
import pt.jumia.services.brad.domain.RequestContext;
import pt.jumia.services.brad.domain.entities.account.Document;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.document.DocumentFilters;
import pt.jumia.services.brad.domain.entities.filter.document.DocumentSortFilters;
import pt.jumia.services.brad.domain.exceptions.AlreadyExistsException;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.exceptions.UserForbiddenException;
import pt.jumia.services.brad.domain.usecases.accounts.ReadAccountsUseCase;
import pt.jumia.services.brad.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.brad.domain.usecases.documents.CreateDocumentsUseCase;
import pt.jumia.services.brad.domain.usecases.documents.DeleteDocumentsUseCase;
import pt.jumia.services.brad.domain.usecases.documents.ReadDocumentsUseCase;
import pt.jumia.services.brad.domain.usecases.documents.UpdateDocumentsUseCase;
import pt.jumia.services.brad.domain.utils.JsonUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Controller responsible for handling CRUD operations for documents.
 */
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping(value = "/api/documents")
public class DocumentController {

    private final CreateDocumentsUseCase createDocumentsUseCase;
    private final ReadDocumentsUseCase readDocumentsUseCase;
    private final UpdateDocumentsUseCase updateDocumentsUseCase;
    private final DeleteDocumentsUseCase deleteDocumentsUseCase;
    private final ValidateUserAccessUseCase validateUserAccessUseCase;
    private final ReadAccountsUseCase readAccountsUseCase;

    @GetMapping
    public PageApiResponsePayload<DocumentApiResponsePayload> fetch(HttpServletRequest httpServletRequest,
         @Valid DocumentFiltersApiRequestPayload documentFiltersApiRequestPayload,
         @Valid DocumentSortFiltersApiRequestPayload documentSortFiltersApiRequestPayload,
         @Valid PageFiltersApiRequestPayload pageFiltersApiRequestPayload) throws UserForbiddenException, EntityErrorsException, NotFoundException {

        String country = readAccountsUseCase.execute(Math.toIntExact(documentFiltersApiRequestPayload.getAccountID()))
                .getCountry().getCode();


        validateUserAccessUseCase.checkCanAccessAccounts(RequestContext.getUser(), CountryCode.valueOf(country));

        log.info("Fetching all documents for user with identifier {}",
                RequestContext.getUsername());
        DocumentFilters documentFilters = documentFiltersApiRequestPayload.toEntity();
        DocumentSortFilters documentSortFilters = documentSortFiltersApiRequestPayload.toEntity();
        PageFilters pageFilters = pageFiltersApiRequestPayload.toEntity();


        List<DocumentApiResponsePayload> documents = readDocumentsUseCase.execute(documentFilters,
                        documentSortFilters, pageFilters)
                .stream().map(DocumentApiResponsePayload::new).collect(Collectors.toList());


        long total = readDocumentsUseCase.executeCount(documentFilters);


        return PageApiResponsePayload.buildPageResponsePayload(
                httpServletRequest,
                pageFilters,
                documents,
                total
            );
    }

    @GetMapping(value = "/account/{id}")
    public List<DocumentApiResponsePayload> fetchByAccount(@PathVariable(value = "id") Long accountId)
            throws UserForbiddenException, EntityErrorsException, NotFoundException {

        String country = readAccountsUseCase.execute(Math.toIntExact(accountId))
                .getCountry().getCode();
        validateUserAccessUseCase.checkCanAccessAccounts(RequestContext.getUser(), CountryCode.valueOf(country));

        log.info("Fetching documents for account {} for user with identifier {}",
                accountId, RequestContext.getUsername());

        DocumentFilters documentFilters = DocumentFilters.builder()
                .accountId(accountId)
                .build();
        DocumentSortFilters documentSortFilters = DocumentSortFilters.builder()
                .field(Document.SortingFields.NAME)
                .build();
        PageFilters pageFilters = null;

        return readDocumentsUseCase.execute(documentFilters,
                        documentSortFilters, pageFilters)
                .stream().map(DocumentApiResponsePayload::new)
                .collect(Collectors.toList());
    }

    @GetMapping(value = "/{id}")
    public DocumentApiResponsePayload fetchById(@PathVariable(value = "id") Integer id)
            throws NotFoundException, UserForbiddenException {

        Document document = readDocumentsUseCase.execute(id);
        String country = document.getAccount().getCountry().getCode();
        validateUserAccessUseCase.checkCanAccessAccounts(RequestContext.getUser(), CountryCode.valueOf(country));

        log.info("Fetching documents {} for user with identifier {}",
                id, RequestContext.getUsername());

        return new DocumentApiResponsePayload(document);

    }

    @GetMapping(value = "/url/{id}")
    public DocumentApiResponsePayload fetchWithURLByID(@PathVariable(value = "id") Integer id)
            throws NotFoundException, UserForbiddenException {

        Document document = readDocumentsUseCase.executeWithURL(id);
        String country = document.getAccount().getCountry().getCode();
        validateUserAccessUseCase.checkCanAccessAccounts(RequestContext.getUser(), CountryCode.valueOf(country));

        log.info("Fetching documents with URL {} for user with identifier {}",
                id, RequestContext.getUsername());

        return new DocumentApiResponsePayload(document);

    }


    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public DocumentApiResponsePayload create(@RequestBody @Valid DocumentApiRequestPayload payload)
            throws UserForbiddenException, AlreadyExistsException, NotFoundException, EntityErrorsException {

        String country = readAccountsUseCase.execute(Math.toIntExact(payload.getAccountID()))
                .getCountry().getCode();

        validateUserAccessUseCase.checkCanManageAccounts(RequestContext.getUser(), CountryCode.valueOf(country));

        log.info("Creating new document: {} for user with identifier {}",
                JsonUtils.toJson(payload), RequestContext.getUsername());

        return new DocumentApiResponsePayload(
                createDocumentsUseCase.execute(payload.toEntity(), payload.getAccountID())
        );

    }

    @PutMapping(value = "/{id}")
    public void update(@PathVariable(value = "id") Long id, @RequestBody @Valid DocumentApiRequestPayload payload)
            throws NotFoundException, UserForbiddenException, EntityErrorsException {

        String country = readAccountsUseCase.execute(Math.toIntExact(payload.getAccountID()))
                .getCountry().getCode();

        validateUserAccessUseCase.checkCanManageAccounts(RequestContext.getUser(), CountryCode.valueOf(country));

        log.info("Updating document with id {} : {} for user with identifier {}",
                id, JsonUtils.toJson(payload), RequestContext.getUsername());

        updateDocumentsUseCase.execute(payload.toEntity());

    }

    @DeleteMapping(value = "/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void delete(@PathVariable(value = "id") Integer id)
            throws NotFoundException, UserForbiddenException {

        String country = readDocumentsUseCase.execute(id).getAccount().getCountry().getCode();
        validateUserAccessUseCase.checkCanManageAccounts(RequestContext.getUser(), CountryCode.valueOf(country));


        log.info("Deleting document with id {} for user with identifier {}",
                id, RequestContext.getUsername());

        deleteDocumentsUseCase.execute(id);
    }

    @GetMapping(value = "/types")
    public List<String> fetchDocumentTypes() throws UserForbiddenException {

        validateUserAccessUseCase.checkCanAccess(RequestContext.getUser());

        log.info("Fetching all document types for user with identifier {}",
                RequestContext.getUsername());

        return readDocumentsUseCase.executeDocumentTypes();
    }


}
