package pt.jumia.services.brad.api.config;

import com.fasterxml.jackson.databind.exc.InvalidFormatException;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.quartz.SchedulerException;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;
import pt.aig.aigx.commons.exceptions.InvalidArgumentException;
import pt.jumia.services.brad.api.payloads.response.error.ErrorFieldPayload;
import pt.jumia.services.brad.api.payloads.response.error.ErrorResponsePayload;
import pt.jumia.services.brad.domain.exceptions.AlreadyExistsException;
import pt.jumia.services.brad.domain.exceptions.ConflictOfInterestException;
import pt.jumia.services.brad.domain.exceptions.DatabaseErrorsException;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.EntityMismatchException;
import pt.jumia.services.brad.domain.exceptions.InvalidEntityException;
import pt.jumia.services.brad.domain.exceptions.InvalidFileException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.exceptions.NotPositiveException;
import pt.jumia.services.brad.domain.exceptions.ReconciliationException;
import pt.jumia.services.brad.domain.exceptions.UserForbiddenException;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Registers the error handlers. All the exceptions that are thrown by the controllers can be dealt with in here
 */
@ControllerAdvice
@Slf4j
public class RestErrorHandler extends ResponseEntityExceptionHandler {

    @Override
    protected ResponseEntity<Object> handleMethodArgumentNotValid(MethodArgumentNotValidException ex,
                                                                  HttpHeaders headers,
                                                                  HttpStatusCode status,
                                                                  WebRequest request) {
        List<ErrorFieldPayload> errorFields = ex.getBindingResult().getFieldErrors().stream()
                .map(error -> new ErrorFieldPayload(error.getField(),
                        error.getRejectedValue() == null ? null : error.getRejectedValue().toString(),
                        error.getDefaultMessage()))
                .collect(Collectors.toList());

        if (errorFields.isEmpty()) {
            ObjectError globalError = ex.getBindingResult().getGlobalError();
            ErrorResponsePayload errorPayload = ErrorResponsePayload.createGlobal(ex.getBindingResult().getObjectName(),
                    globalError != null ? globalError.getDefaultMessage() : null);
            return new ResponseEntity<>(errorPayload, HttpStatus.BAD_REQUEST);
        }

        ErrorResponsePayload errorPayload = ErrorResponsePayload.createWithFields(ex.getBindingResult().getObjectName(),
                errorFields);
        return new ResponseEntity<>(errorPayload, HttpStatus.BAD_REQUEST);
    }

    @Override
    protected ResponseEntity<Object> handleHttpMessageNotReadable(HttpMessageNotReadableException ex,
                                                                  HttpHeaders headers,
                                                                  HttpStatusCode status,
                                                                  WebRequest request) {
        String errorMessage = ex.getMessage();
        if (ex.getCause() instanceof InvalidFormatException invalidFormatException) {
            if (invalidFormatException.getTargetType() != null && invalidFormatException.getTargetType().equals(java.math.BigDecimal.class)) {
                errorMessage = String.format("Invalid value '%s' for a numeric field. Please use a valid number format.",
                        invalidFormatException.getValue());
            }
        }
        ErrorResponsePayload errorResponse = ErrorResponsePayload.createGlobal(errorMessage);
        return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
    }

    /**
     * Handles constraint exceptions on arguments annotated with {@link org.springframework.validation.annotation.Validated}
     *
     * Will make sure we have a detailed and comprehensive response with errors by field if applicable
     *
     * @param exception
     * @return
     */
    @ExceptionHandler(value = ConstraintViolationException.class)
    public ResponseEntity<Object> handleConstraintValidation(ConstraintViolationException exception) {
        return new ResponseEntity<>(exception.getConstraintViolations()
            .stream()
            .map(ErrorFieldPayload::new)
            .collect(Collectors.toList()), HttpStatus.BAD_REQUEST);
    }

    /**
     * Handles DB exceptions that generally represent Unique constraints not being respected, so we return a client error
     *
     * @param e
     * @return
     * @throws Exception
     */
    @ExceptionHandler(value = DataIntegrityViolationException.class)
    private ResponseEntity<Object> handleDbUniques(Exception e) throws Exception {

        DataIntegrityViolationException dataIntegrityException = (DataIntegrityViolationException) e;

        // exclude SQL statement from the error message
        String originalMessage = dataIntegrityException.getCause().getCause().getMessage();
        String errorMessage = originalMessage.split("SQL statement")[0];

        ErrorResponsePayload errorResponse = ErrorResponsePayload.createGlobal(errorMessage);
        return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
    }

    /**
     * Handles all the other exceptions that may occur, returning a server error
     *
     * @param e
     * @return
     * @throws Exception
     */
    @ExceptionHandler
    private ResponseEntity<Object> handleGeneric(Exception e) throws Exception {

        log.error(ExceptionUtils.getStackTrace(e));
        return new ResponseEntity<>("An exception occurred. Please try again.", HttpStatus.INTERNAL_SERVER_ERROR);
    }

    /**
     * Handles exception when user is not authorized to execute a task, returning a Unauthorized error
     *
     * @param e
     * @param response
     * @throws IOException
     */
    @ExceptionHandler(value = UserForbiddenException.class)
    void handleException(UserForbiddenException e, HttpServletResponse response)
        throws IOException {
        response.sendError(HttpStatus.FORBIDDEN.value(), e.getMessage());
    }

    @ExceptionHandler(value = ConflictOfInterestException.class)
    public ResponseEntity<Object> handleConflictOfInterest(Exception e) {
        ErrorResponsePayload errorResponse = ErrorResponsePayload.createGlobal(e.getMessage());
        return new ResponseEntity<>(errorResponse, HttpStatus.FORBIDDEN);
    }

    @ExceptionHandler(value = ReconciliationException.class)
    public ResponseEntity<Object> handleEntityAlreadyReconciled(Exception e) {
        ErrorResponsePayload errorResponse = ErrorResponsePayload.createGlobal(e.getMessage());
        return new ResponseEntity<>(errorResponse, HttpStatus.CONFLICT);
    }


    @ExceptionHandler(value = AlreadyExistsException.class)
    public ResponseEntity<Object> handleAccountsAlreadyExists(Exception e) {
        ErrorResponsePayload errorResponse = ErrorResponsePayload.createGlobal(e.getMessage());
        return new ResponseEntity<>(errorResponse, HttpStatus.CONFLICT);
    }

    @ExceptionHandler(value = InvalidFileException.class)
    public ResponseEntity<Object> handleInvalidFile(Exception e) {
        ErrorResponsePayload errorResponse = ErrorResponsePayload.createGlobal(e.getMessage());
        return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(value = NotFoundException.class)
    public ResponseEntity<Object> handleNotFound(Exception e) {
        ErrorResponsePayload errorResponse = ErrorResponsePayload.createGlobal(e.getMessage());
        return new ResponseEntity<>(errorResponse, HttpStatus.NOT_FOUND);
    }

    @ExceptionHandler(value = EntityMismatchException.class)
    public ResponseEntity<Object> handleEntityMismatch(Exception e) {
        ErrorResponsePayload errorResponse = ErrorResponsePayload.createGlobal(e.getMessage());
        return new ResponseEntity<>(errorResponse, HttpStatus.CONFLICT);
    }

    @ExceptionHandler(value = NotPositiveException.class)
    public ResponseEntity<Object> handleNotPositive(Exception e) {
        ErrorResponsePayload errorResponse = ErrorResponsePayload.createGlobal(e.getMessage());
        return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(value = EntityErrorsException.class)
    public ResponseEntity<Object> handleEntityError(Exception e) {
        ErrorResponsePayload errorResponse = ErrorResponsePayload.createGlobal(e.getMessage());
        return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(value = InvalidEntityException.class)
    public ResponseEntity<Object> handleInvalidEntity(Exception e) {
        ErrorResponsePayload errorResponse = ErrorResponsePayload.createGlobal(e.getMessage());
        return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(value = IllegalArgumentException.class)
    public ResponseEntity<Object> handleIncorrectArguments(IllegalArgumentException e) {

        List<ErrorFieldPayload> errorFields = new ArrayList<>();

        if (e.getMessage().contains("Account cannot be null")) {
            errorFields.add(new ErrorFieldPayload("account", "Account cannot be null", e.getMessage()));
        }
        if (e.getMessage().contains("Country cannot be null")) {
            errorFields.add(new ErrorFieldPayload("country", "Country cannot be null", e.getMessage()));
        }
        if (e.getMessage().contains("Id must be greater than 0")) {
            errorFields.add(new ErrorFieldPayload("Country ID", "Id must be greater than 0", e.getMessage()));
        }
        if (e.getMessage().contains("Reconciliation is not pending")) {
            errorFields.add(new ErrorFieldPayload("status", "Reconciliation is not pending", e.getMessage()));
        }
        if (e.getMessage().contains("Reconciliation cannot be null")) {
            errorFields.add(new ErrorFieldPayload("reconciliation", "Reconciliation cannot be null", e.getMessage()));
        }
        if (e.getMessage().contains("Cannot reconcile different amounts")) {
            errorFields.add(new ErrorFieldPayload("amount", "Cannot reconcile different amounts", e.getMessage()));
        }

        ErrorResponsePayload errorResponse;

        if (CollectionUtils.isEmpty(errorFields)) {
            errorResponse = ErrorResponsePayload.createGlobal(e.getMessage());

        } else {
            errorResponse = ErrorResponsePayload.createWithFields(null, errorFields);
        }
        return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
    }


    @ExceptionHandler(value = SchedulerException.class)
    private ResponseEntity<Object> handleIncorrectArguments(SchedulerException e) {

        List<ErrorFieldPayload> errorFields = new ArrayList<>();


        errorFields.add(new ErrorFieldPayload("Cron String", "", e.getMessage()));


        ErrorResponsePayload errorResponse = ErrorResponsePayload.createWithFields(null, errorFields);
        return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(value = DatabaseErrorsException.class)
    private ResponseEntity<Object> handleDatabaseErrors(DatabaseErrorsException e){
        ErrorResponsePayload errorResponse = ErrorResponsePayload.createGlobal(e.getMessage());
        return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
    }


    @ExceptionHandler(value = InvalidArgumentException.class)
    private ResponseEntity<Object> handleInvalidArgumentException(InvalidArgumentException e) {

        ErrorResponsePayload errorResponse = ErrorResponsePayload.createGlobal(e.getMessage());
        return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
    }


}
