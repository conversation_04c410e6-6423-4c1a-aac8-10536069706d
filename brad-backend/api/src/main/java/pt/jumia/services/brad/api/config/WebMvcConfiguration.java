package pt.jumia.services.brad.api.config;

import lombok.RequiredArgsConstructor;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport;
import pt.jumia.services.brad.api.filters.AuthFilter;
import pt.jumia.services.brad.api.filters.CorsFilter;
import pt.jumia.services.brad.domain.AccessController;
import pt.jumia.services.brad.domain.properties.AclProperties;
import pt.jumia.services.brad.domain.properties.ApiProperties;
import pt.jumia.services.brad.domain.properties.SpringdocProperties;

/**
 * Configuration file loaded by Spring to configure filter
 */
@Configuration
@RequiredArgsConstructor
@EnableWebMvc
public class WebMvcConfiguration extends WebMvcConfigurationSupport {

    private final AccessController accessController;
    private final ApiProperties apiProperties;
    private final SpringdocProperties springdocProperties;
    private final AclProperties aclProperties;

    @Override
    public void addResourceHandlers(final ResourceHandlerRegistry registry) {
        // Add mapping if swagger is enabled.
        if (springdocProperties.getSwaggerUi().isEnabled()) {
            // Make Swagger meta-data available via <baseURL>/v3/api-docs/
            registry.addResourceHandler("/webjars/**").addResourceLocations("classpath:/META-INF/resources/webjars/");
            // Make Swagger UI available via <baseURL>/swagger-ui.html
            registry.addResourceHandler("/swagger-ui.html").addResourceLocations("classpath:/META-INF/resources/");
        }
    }

    /**
     * Configuring CORS filters according to the allowed domains defined in the application.properties file
     */
    @Bean
    public FilterRegistrationBean registerCORSFilter() {
        FilterRegistrationBean<CorsFilter> filterRegistrationBean = new FilterRegistrationBean<>();

        // Register Spring internal CORS filter
        filterRegistrationBean.setFilter(corsFilter());

        // Apply the filter to all created servlets in the tomcat server
        filterRegistrationBean.addUrlPatterns("/api/*", "/public/*", "/auth/*");
        return filterRegistrationBean;
    }

    @Bean
    public FilterRegistrationBean registerAuthFilter() {
        FilterRegistrationBean<AuthFilter> filterRegistrationBean = new FilterRegistrationBean<>();

        // Register our custom AuthFilter
        filterRegistrationBean.setFilter(authFilter());
        filterRegistrationBean.setMatchAfter(true);

        // Apply the filter to all created servlets in the tomcat server
        filterRegistrationBean.addUrlPatterns("/api/*");
        return filterRegistrationBean;
    }

    @Bean
    public AuthFilter authFilter() {
        return new AuthFilter(accessController, aclProperties);
    }

    @Bean
    public CorsFilter corsFilter() {
        return new CorsFilter(apiProperties);
    }
    
}
