package pt.jumia.services.brad.api.controllers;

import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import pt.jumia.services.brad.api.payloads.request.PageFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.apilog.ApiLogFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.apilog.ApiLogSortFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.response.ApiLogApiResponsePayload;
import pt.jumia.services.brad.api.payloads.response.PageApiResponsePayload;
import pt.jumia.services.brad.domain.RequestContext;
import pt.jumia.services.brad.domain.entities.ApiLog;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.apilog.ApiLogFilters;
import pt.jumia.services.brad.domain.entities.filter.apilog.ApiLogSortFilters;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.exceptions.UserForbiddenException;
import pt.jumia.services.brad.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.brad.domain.usecases.apilog.ReadApiLogUseCase;
import pt.jumia.services.brad.domain.usecases.accounts.ReadAccountsUseCase;

import javax.validation.Valid;
import java.text.ParseException;
import java.util.List;
import java.util.stream.Collectors;
import pt.jumia.services.brad.domain.usecases.apilog.UpdateApiLogUseCase;

@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping(value = "/api/api-logs")
public class ApiLogController {

    private final ValidateUserAccessUseCase validateUserAccessUseCase;

    private final ReadApiLogUseCase readApiLogUseCase;

    private final UpdateApiLogUseCase updateApiLogUseCase;

    private final ReadAccountsUseCase readAccountsUseCase;

    @GetMapping
    public PageApiResponsePayload<ApiLogApiResponsePayload> fetch(HttpServletRequest httpServletRequest,
                     @Valid ApiLogFiltersApiRequestPayload apiLogFiltersApiRequestPayload,
                     @Valid ApiLogSortFiltersApiRequestPayload apiLogSortFiltersApiRequestPayload,
                     @Valid PageFiltersApiRequestPayload pageFiltersApiRequestPayload)
            throws UserForbiddenException, EntityErrorsException, ParseException {

        validateUserAccessUseCase.checkCanAccessApiLogs(RequestContext.getUser());


        log.info("Fetching all api logs for user with identifier {}",
                RequestContext.getUsername());


        ApiLogFilters apiLogFilters = apiLogFiltersApiRequestPayload.toEntity();
        ApiLogSortFilters apiLogSortFilters = apiLogSortFiltersApiRequestPayload.toEntity();
        PageFilters pageFilters = pageFiltersApiRequestPayload.toEntity();


        List<ApiLogApiResponsePayload> apiLogs = readApiLogUseCase.execute(apiLogFilters,
                        apiLogSortFilters, pageFilters)
                .stream().map(apiLog -> buildResponse(apiLog)).collect(Collectors.toList());


        long total = readApiLogUseCase.executeCount(apiLogFilters);


        return PageApiResponsePayload.buildPageResponsePayload(
                httpServletRequest,
                pageFilters,
                apiLogs,
                total
        );
    }

    @GetMapping(value = "/{id}")
    public ApiLogApiResponsePayload fetchById(@PathVariable(value = "id") Integer id)
            throws NotFoundException, UserForbiddenException {

        validateUserAccessUseCase.checkCanAccessApiLogs(RequestContext.getUser());

        log.info("Fetching api logs {} for user with identifier {}",
                id, RequestContext.getUsername());

        return new ApiLogApiResponsePayload(readApiLogUseCase.execute(id));

    }

    @GetMapping(value = "/api-log-types")
    public List<String> fetchLogTypes() throws UserForbiddenException {

        validateUserAccessUseCase.checkCanAccessApiLogs(RequestContext.getUser());

        log.info("Fetching all api log types for user with identifier {}",
                RequestContext.getUsername());

        return readApiLogUseCase.executeLogTypes();
    }

    @GetMapping(value = "/api-log-status")
    public List<String> fetchLogStatus() throws UserForbiddenException {

        validateUserAccessUseCase.checkCanAccessApiLogs(RequestContext.getUser());

        log.info("Fetching all api log status for user with identifier {}",
                RequestContext.getUsername());

        return readApiLogUseCase.executeLogStatus();
    }

    @PutMapping(value = "/{id}/acknowledge-failure")
    public ApiLogApiResponsePayload acknowledgeFailure(@PathVariable long id) throws UserForbiddenException {

        validateUserAccessUseCase.checkCanAccessApiLogs(RequestContext.getUser());

        log.info("Acknowledging api log with id {} for user with identifier {}", id, RequestContext.getUsername());
        ApiLog apiLog = updateApiLogUseCase.acknowledgeFailure(id);
        return buildResponse(apiLog);
    }

    private @NotNull ApiLogApiResponsePayload buildResponse(final ApiLog apiLog) {

        return new ApiLogApiResponsePayload(apiLog.toBuilder()
            .request(null)
            .response(
                apiLog.getLogStatus().equals(ApiLog.ApiLogStatus.FAILURE) ?
                    apiLog.getResponse() : "-"
            )
            .build());
    }

}
