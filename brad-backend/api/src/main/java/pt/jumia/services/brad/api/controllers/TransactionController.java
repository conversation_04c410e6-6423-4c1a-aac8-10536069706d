package pt.jumia.services.brad.api.controllers;

import com.neovisionaries.i18n.CountryCode;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import pt.jumia.services.brad.api.csvs.exports.TransactionExportCSV;
import pt.jumia.services.brad.api.payloads.request.PageFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.transaction.TransactionFilterListApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.transaction.TransactionFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.transaction.TransactionGroupFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.transaction.TransactionSortFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.response.PageApiResponsePayload;
import pt.jumia.services.brad.api.payloads.response.TransactionApiResponsePayload;
import pt.jumia.services.brad.api.payloads.response.group.GroupInfoResponsePayload;
import pt.jumia.services.brad.api.payloads.response.group.TransactionGroupingsApiResponsePayload;
import pt.jumia.services.brad.domain.RequestContext;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.Transaction;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.transaction.TransactionFilters;
import pt.jumia.services.brad.domain.entities.filter.transaction.TransactionGroupFilters;
import pt.jumia.services.brad.domain.entities.filter.transaction.TransactionSortFilters;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.exceptions.UserForbiddenException;
import pt.jumia.services.brad.domain.usecases.accounts.ReadAccountsUseCase;
import pt.jumia.services.brad.domain.usecases.accountstatement.ReadAccountStatementUseCase;
import pt.jumia.services.brad.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.brad.domain.usecases.transactions.ReadTransactionUseCase;

import javax.validation.Valid;
import java.text.ParseException;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping(value = "/api/transactions")
public class TransactionController {

    private final ValidateUserAccessUseCase validateUserAccessUseCase;

    private final ReadTransactionUseCase readTransactionUseCase;

    private final ReadAccountsUseCase readAccountsUseCase;

    private final ReadAccountStatementUseCase readAccountStatementUseCase;

    @GetMapping
    public PageApiResponsePayload<TransactionApiResponsePayload> fetch(HttpServletRequest httpServletRequest,
                   @Valid TransactionFiltersApiRequestPayload transactionFiltersApiRequestPayload,
                   @Valid TransactionSortFiltersApiRequestPayload transactionSortFiltersApiRequestPayload,
                   @Valid PageFiltersApiRequestPayload pageFiltersApiRequestPayload)
            throws UserForbiddenException, EntityErrorsException, ParseException, NotFoundException {
        String country = null;

        if (transactionFiltersApiRequestPayload.getPartitionKey() != null) {
            country = readAccountsUseCase.execute(Math.toIntExact(Long.parseLong(transactionFiltersApiRequestPayload.getPartitionKey())))
                    .getCountry().getCode();

        } else if (transactionFiltersApiRequestPayload.getAccountStatementID() != null) {
            AccountStatement bankStatement = readAccountStatementUseCase
                    .execute(Long.valueOf(transactionFiltersApiRequestPayload.getAccountStatementID()));
            country = readAccountsUseCase.execute(Math.toIntExact(bankStatement.getAccount().getId()))
                    .getCountry().getCode();
        }

        validateUserAccessUseCase.checkCanAccessStatements(RequestContext.getUser(), CountryCode.valueOf(country));


        log.info("Fetching all transactions for user with identifier {}",
                RequestContext.getUsername());

        TransactionFilters transactionFilters = transactionFiltersApiRequestPayload.toEntity();
        TransactionSortFilters transactionSortFilters = transactionSortFiltersApiRequestPayload.toEntity();
        PageFilters pageFilters = pageFiltersApiRequestPayload.toEntity();


        List<TransactionApiResponsePayload> transactions = readTransactionUseCase.execute(transactionFilters,
                        transactionSortFilters, pageFilters)
                .stream().map(TransactionApiResponsePayload::new)
                .collect(Collectors.toList());


        long total = readTransactionUseCase.executeCount(transactionFilters);


        return PageApiResponsePayload.buildPageResponsePayload(
                httpServletRequest,
                pageFilters,
                transactions,
                total
        );
    }

    @PostMapping(value = "/group-info")
    public List<GroupInfoResponsePayload> fetchGroupInfo(
            @RequestBody @Valid TransactionFilterListApiRequestPayload transactionFilterListApiRequestPayload)
            throws UserForbiddenException, ParseException {

        validateUserAccessUseCase.checkCanAccessReconciliation(RequestContext.getUser());

        log.info("Fetching transaction group info for user with identifier {}",
                RequestContext.getUsername());

        List<TransactionFilters> transactionFilterList = transactionFilterListApiRequestPayload.toEntities();

        return readTransactionUseCase.executeGroupInfoList(transactionFilterList)
                .stream().map(GroupInfoResponsePayload::new).collect(Collectors.toList());
    }

    @GetMapping(value = "/download")
    @ResponseStatus(HttpStatus.OK)
    public Future<ResponseEntity<byte[]>> download(@Valid TransactionFiltersApiRequestPayload transactionFiltersApiRequestPayload,
                                                   @Valid TransactionSortFiltersApiRequestPayload transactionSortFiltersApiRequestPayload,
                                                   @Valid PageFiltersApiRequestPayload pageFiltersApiRequestPayload)
            throws UserForbiddenException, ParseException, NotFoundException {

        String country = readAccountsUseCase.execute
                (Integer.parseInt(transactionFiltersApiRequestPayload.getPartitionKey())).getCountry().getCode();
        validateUserAccessUseCase.checkCanExportStatements(RequestContext.getUser(), CountryCode.valueOf(country));

        log.info("Downloading transactions for user with identifier {}", RequestContext.getUsername());


        TransactionFilters transactionFilters = transactionFiltersApiRequestPayload.toEntity();
        TransactionSortFilters transactionSortFilters = transactionSortFiltersApiRequestPayload.toEntity();
        PageFilters pageFilters = pageFiltersApiRequestPayload.toBuilder()
                .size(readTransactionUseCase.executeCount(transactionFilters).intValue())
                .build()
                .toEntity();

        CompletableFuture<ResponseEntity<byte[]>> future = new CompletableFuture<>();
        CompletableFuture.runAsync(() -> {
            try {
                List<Transaction> transactions = readTransactionUseCase.execute(transactionFilters,
                        transactionSortFilters, pageFilters);
                if (transactions.isEmpty()) {
                    future.completeExceptionally(new NotFoundException("No transactions found"));
                    return;
                }
                future.complete(TransactionExportCSV.buildTransactionCSV(transactions));
            } catch (Exception e) {
                log.error("Error downloading transactions", e);
                future.completeExceptionally(e);
            }
        });
        return future;
    }

    @GetMapping(value = "/grouped")
    public TransactionGroupingsApiResponsePayload fetchGrouped(
            @Valid TransactionFiltersApiRequestPayload transactionFiltersApiRequestPayload,
            @Valid TransactionGroupFiltersApiRequestPayload transactionGroupFiltersApiRequestPayload)
            throws UserForbiddenException, ParseException, NotFoundException {

        validateUserAccessUseCase.checkCanAccessReconciliation(RequestContext.getUser());

        log.info("Fetching all transaction grouped for user with identifier {}",
                RequestContext.getUsername());

        TransactionFilters transactionFilters = transactionFiltersApiRequestPayload.toEntity();
        TransactionGroupFilters transactionGroupFilters = transactionGroupFiltersApiRequestPayload.toEntity();

        return new TransactionGroupingsApiResponsePayload(readTransactionUseCase.executeGroup(transactionFilters, transactionGroupFilters));

    }

    @GetMapping(value = "/groupable-fields")
    public List<String> fetchGroupableFields() throws UserForbiddenException {

        validateUserAccessUseCase.checkCanAccessReconciliation(RequestContext.getUser());

        log.info("Fetching all groupable transaction fields for user with identifier {}",
                RequestContext.getUsername());
        return readTransactionUseCase.executeGroupableFields();
    }
}
