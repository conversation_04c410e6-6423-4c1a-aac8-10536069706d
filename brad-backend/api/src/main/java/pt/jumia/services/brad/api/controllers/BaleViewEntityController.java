package pt.jumia.services.brad.api.controllers;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import pt.jumia.services.brad.api.payloads.request.viewentity.ViewEntityApiRequestPayload;
import pt.jumia.services.brad.api.payloads.response.ViewEntityApiResponsePayload;
import pt.jumia.services.brad.domain.RequestContext;
import pt.jumia.services.brad.domain.entities.ViewEntity;
import pt.jumia.services.brad.domain.exceptions.*;
import pt.jumia.services.brad.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.brad.domain.usecases.viewentity.CreateViewEntityUseCase;
import pt.jumia.services.brad.domain.usecases.viewentity.DeleteViewEntityUseCase;
import pt.jumia.services.brad.domain.usecases.viewentity.ReadViewEntityUseCase;
import pt.jumia.services.brad.domain.usecases.viewentity.UpdateViewEntityUseCase;
import pt.jumia.services.brad.domain.utils.JsonUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Controller responsible for handling CRUD operations for bale view entities.
 */
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping(value = "/api/bale-view-entities")
public class BaleViewEntityController {

    private final CreateViewEntityUseCase createViewEntityUseCase;
    private final ReadViewEntityUseCase readViewEntityUseCase;
    private final UpdateViewEntityUseCase updateViewEntityUseCase;
    private final DeleteViewEntityUseCase deleteViewEntityUseCase;

    private final ValidateUserAccessUseCase validateUserAccessUseCase;

    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public ViewEntityApiResponsePayload create(@RequestBody @Valid ViewEntityApiRequestPayload baleViewEntityApiRequestPayload)
            throws UserForbiddenException, AlreadyExistsException, EntityErrorsException, NotFoundException, DatabaseErrorsException {

        validateUserAccessUseCase.checkCanManageViewEntities(RequestContext.getUser());

        log.info("Creating bale view entity {} for user with identifier {}",
                JsonUtils.toJson(baleViewEntityApiRequestPayload), RequestContext.getUsername());

        return new ViewEntityApiResponsePayload(
                createViewEntityUseCase.execute(baleViewEntityApiRequestPayload.toEntity(), ViewEntity.EntityType.BALE)
        );
    }

    @GetMapping(value = "/{id}")
    public ViewEntityApiResponsePayload fetchById(@PathVariable(value = "id") Long id)
            throws NotFoundException, UserForbiddenException {

        validateUserAccessUseCase.checkCanManageViewEntities(RequestContext.getUser());

        log.info("Fetching bale view entity {} for user with identifier {}",
                id, RequestContext.getUsername());

        return new ViewEntityApiResponsePayload(readViewEntityUseCase.execute(id, ViewEntity.EntityType.BALE));

    }

    @GetMapping
    public List<ViewEntityApiResponsePayload> fetchAll() throws UserForbiddenException, EntityErrorsException {

        validateUserAccessUseCase.checkCanManageViewEntities(RequestContext.getUser());

        log.info("Fetching all bale view entities for user with identifier {}",
                RequestContext.getUsername());
        return readViewEntityUseCase.execute(ViewEntity.EntityType.BALE).stream()
                .map(ViewEntityApiResponsePayload::new)
                .collect(Collectors.toList());
    }

    @PutMapping(value = "/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void updateBaleViewEntity(@PathVariable(value = "id") Long id, @RequestBody @Valid ViewEntityApiRequestPayload payload)
            throws UserForbiddenException, NotFoundException, EntityErrorsException {

        validateUserAccessUseCase.checkCanManageViewEntities(RequestContext.getUser());

        log.info("Updating bale view entity with id {} : {} for user with identifier {}",
                id, JsonUtils.toJson(payload), RequestContext.getUsername());

        payload.setId(id);
        updateViewEntityUseCase.execute(payload.toEntity(), ViewEntity.EntityType.BALE);
    }

    @DeleteMapping(value = "/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void delete(@PathVariable(value = "id") Long id)
            throws NotFoundException, UserForbiddenException {

        validateUserAccessUseCase.checkCanManageViewEntities(RequestContext.getUser());

        log.info("Deleting bale view entity with id {} for user with identifier {}",
                id, RequestContext.getUsername());

        deleteViewEntityUseCase.execute(id, ViewEntity.EntityType.BALE);
    }

}
