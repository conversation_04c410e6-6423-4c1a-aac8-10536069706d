package pt.jumia.services.brad.api.controllers;

import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import pt.jumia.services.brad.api.payloads.response.JwtResponsePayload;
import pt.jumia.services.brad.domain.RequestContext;
import pt.jumia.services.brad.domain.usecases.ReadUserAccessUseCase;

import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "")
public class AuthController {

    private final ReadUserAccessUseCase readUserAccessUseCase;

    @PostMapping(value = "/auth/swap-token")
    public JwtResponsePayload swapTemporaryToken(@RequestBody @NotBlank String code) {
        log.info("Swapping temporary token for real token");
        return new JwtResponsePayload(readUserAccessUseCase.findRealToken(code));
    }

    @GetMapping(value = "/api/auth/user/permissions")
    public Map<String, Map<String, List<String>>> fetchAllPermissions() {
        log.info("Fetching all permissions for user: {}", RequestContext.getUser().getUsername());
        return this.readUserAccessUseCase.execute(RequestContext.getUser());
    }

    @GetMapping(value = "/api/auth/logout")
    public void logout() {
        this.readUserAccessUseCase.logout(RequestContext.getUser());
    }
}
