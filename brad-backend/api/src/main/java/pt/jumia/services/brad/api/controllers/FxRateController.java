package pt.jumia.services.brad.api.controllers;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import pt.jumia.services.brad.api.payloads.request.PageFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.fxrate.FxRateFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.fxrate.FxRateSortFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.response.FxRateApiResponsePayload;
import pt.jumia.services.brad.api.payloads.response.PageApiResponsePayload;
import pt.jumia.services.brad.api.payloads.response.views.FxRateRawResponsePayload;
import pt.jumia.services.brad.domain.RequestContext;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.fxrate.FxRateFilters;
import pt.jumia.services.brad.domain.entities.filter.fxrate.FxRateSortFilters;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.exceptions.UserForbiddenException;
import pt.jumia.services.brad.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.brad.domain.usecases.fxrates.ReadFxRateUseCase;
import pt.jumia.services.brad.domain.usecases.fxrates.brad.ReadBradFxRateUseCase;
import pt.jumia.services.brad.domain.utils.DateParser;

import java.text.ParseException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping(value = "/api/fx-rates")
public class FxRateController {

    private final ValidateUserAccessUseCase validateUserAccessUseCase;

    private final ReadBradFxRateUseCase readBradFxRateUseCase;

    private final ReadFxRateUseCase readFxRateUseCase;

    @GetMapping
    public PageApiResponsePayload<FxRateApiResponsePayload> fetch(HttpServletRequest httpServletRequest,
        @Valid FxRateFiltersApiRequestPayload fxRateFiltersApiRequestPayload,
        @Valid FxRateSortFiltersApiRequestPayload fxRateSortFiltersApiRequestPayload,
        @Valid PageFiltersApiRequestPayload pageFiltersApiRequestPayload) throws UserForbiddenException, EntityErrorsException {

        validateUserAccessUseCase.checkCanAccessFxRates(RequestContext.getUser());

        log.info("Fetching all fx rates for user with identifier {}", RequestContext.getUsername());

        FxRateFilters fxRateFilters = fxRateFiltersApiRequestPayload.toEntity();
        FxRateSortFilters fxRateSortFilters = fxRateSortFiltersApiRequestPayload.toEntity();
        PageFilters pageFilters = pageFiltersApiRequestPayload.toEntity();


        List<FxRateApiResponsePayload> fxRatesList = readBradFxRateUseCase.execute(fxRateFilters,
                        fxRateSortFilters, pageFilters)
                .stream().map(FxRateApiResponsePayload::new).collect(Collectors.toList());


        long total = readBradFxRateUseCase.executeCount(fxRateFilters);

        return PageApiResponsePayload.buildPageResponsePayload(
                httpServletRequest,
                pageFilters,
                fxRatesList,
                total
        );

    }

    @GetMapping(value = "/{rateDate}/{currency}")
    public List<FxRateApiResponsePayload> fetchAllFxRateInRateDateOfCurrency(
            @PathVariable(value = "rateDate") LocalDate rateDate,
            @PathVariable(value = "currency") String currency)
            throws UserForbiddenException, NotFoundException {

        validateUserAccessUseCase.checkCanAccessFxRates(RequestContext.getUser());

        log.info("Fetching all fx rates for user with identifier {} in rate date {} and currency {}",
                RequestContext.getUsername(), rateDate, currency);

        return readBradFxRateUseCase.executeAllFxRateInRateDateOfCurrency(rateDate, currency)
                .stream().map(FxRateApiResponsePayload::new).collect(Collectors.toList());

    }

    @GetMapping(value = "/{id}")
    public FxRateApiResponsePayload fetchById(@PathVariable(value = "id") Integer id)
            throws NotFoundException, UserForbiddenException {

        validateUserAccessUseCase.checkCanAccessFxRates(RequestContext.getUser());

        log.info("Fetching fx rate {} for user with identifier {}",
                id, RequestContext.getUsername());


        return new FxRateApiResponsePayload(readBradFxRateUseCase.execute(id));

    }

    @GetMapping(value = "/sync/{date}")
    public void syncByDate(@PathVariable(value = "date") String date)
            throws UserForbiddenException, ParseException {

        validateUserAccessUseCase.checkCanAccessScheduler(RequestContext.getUser());

        log.info("Syncing fx rates by date {} for user with identifier {}",
                date, RequestContext.getUsername());

        readFxRateUseCase.executeByDate(DateParser.parseToLocalDateTime(date));

    }

    @GetMapping(value = "/last/{fxRateViewEntityId}/{offset}")
    public LocalDateTime fetchLastBaleWithOffset(@PathVariable(value = "fxRateViewEntityId") Long fxRateViewEntityId,
                                                 @PathVariable(value = "offset") Integer offset)
            throws UserForbiddenException, EntityErrorsException, NotFoundException {

        validateUserAccessUseCase.checkCanManageViewEntities(RequestContext.getUser());

        log.info("Fetching last fxRate with an offset of {} in fxRate view with id {} for user with identifier {}",
                offset, fxRateViewEntityId, RequestContext.getUsername());

        return readFxRateUseCase.executeLastFxRateOfOffset(fxRateViewEntityId, offset);
    }

    @GetMapping(value = "/all-last/{fxRateViewEntityId}/{offset}")
    public List<FxRateRawResponsePayload> fetchAllLastFxRateWithOffset(@PathVariable(value = "fxRateViewEntityId") Long fxRateViewEntityId,
                                                                     @PathVariable(value = "offset") Integer offset)
            throws UserForbiddenException, EntityErrorsException, NotFoundException {

        validateUserAccessUseCase.checkCanManageViewEntities(RequestContext.getUser());

        log.info("Fetching all last fxRate with an offset of {} in fxrate view with id {} for user with identifier {}",
                offset, fxRateViewEntityId, RequestContext.getUsername());

        return readFxRateUseCase.executeAllLastFxRateOfOffset(fxRateViewEntityId, offset)
                .stream().map(FxRateRawResponsePayload::new).collect(Collectors.toList());
    }
}
