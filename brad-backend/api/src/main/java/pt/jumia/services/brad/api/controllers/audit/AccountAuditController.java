package pt.jumia.services.brad.api.controllers.audit;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import pt.jumia.services.brad.api.payloads.request.AuditedEntitySortFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.PageFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.response.AuditApiResponsePayload;
import pt.jumia.services.brad.api.payloads.response.PageApiResponsePayload;
import pt.jumia.services.brad.domain.RequestContext;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.audit.AuditedEntitySortFilters;
import pt.jumia.services.brad.domain.exceptions.UserForbiddenException;
import pt.jumia.services.brad.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.brad.domain.usecases.audit.account.ReadAccountAuditUseCase;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping(value = "/api/accounts/audit")
public class AccountAuditController {

    private final ValidateUserAccessUseCase validateUserAccessUseCase;
    private final ReadAccountAuditUseCase readAccountAuditUseCase;

    @GetMapping("/{id}")
    public PageApiResponsePayload<AuditApiResponsePayload> fetchAuditById(HttpServletRequest httpServletRequest,
                          @PathVariable(value = "id") long id,
                          @Valid PageFiltersApiRequestPayload pageFiltersApiRequestPayload,
                          @Valid AuditedEntitySortFiltersApiRequestPayload auditedEntitySortFiltersApiRequestPayload)
            throws UserForbiddenException {

        validateUserAccessUseCase.checkCanAccess(RequestContext.getUser());

        log.info("Fetching audit info for account with id {} for user with identifier {}", id,
                RequestContext.getUsername());

        PageFilters pageFilters = pageFiltersApiRequestPayload.toEntity();

        AuditedEntitySortFilters sortFilters = auditedEntitySortFiltersApiRequestPayload.toEntity();
        List<AuditApiResponsePayload> result = readAccountAuditUseCase.executeById(id, sortFilters, pageFilters)
                .stream()
                .map(AuditApiResponsePayload::new)
                .collect(Collectors.toList());
        long total = readAccountAuditUseCase.executeCountById(id);
        log.info("Found {} audit entries for account with id {}", total, id);
        return PageApiResponsePayload.buildPageResponsePayload(
                httpServletRequest,
                pageFilters,
                result,
                total
        );
    }
}

