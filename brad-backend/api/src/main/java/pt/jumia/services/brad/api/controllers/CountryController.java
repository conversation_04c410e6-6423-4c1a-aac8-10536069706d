package pt.jumia.services.brad.api.controllers;

import com.neovisionaries.i18n.CountryCode;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import pt.jumia.services.brad.api.payloads.request.country.CountryApiRequestPayload;
import pt.jumia.services.brad.api.payloads.response.CountryApiResponsePayload;
import pt.jumia.services.brad.domain.RequestContext;
import pt.jumia.services.brad.domain.entities.account.Country;
import pt.jumia.services.brad.domain.exceptions.AlreadyExistsException;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.exceptions.UserForbiddenException;
import pt.jumia.services.brad.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.brad.domain.usecases.countries.CreateCountriesUseCase;
import pt.jumia.services.brad.domain.usecases.countries.DeleteCountriesUseCase;
import pt.jumia.services.brad.domain.usecases.countries.ReadCountriesUseCase;
import pt.jumia.services.brad.domain.usecases.countries.UpdateCountriesUseCase;
import pt.jumia.services.brad.domain.utils.JsonUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Controller responsible for handling CRUD operations for countries.
 */
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping(value = "/api/countries")
public class CountryController {

    private final CreateCountriesUseCase createCountriesUseCase;
    private final ReadCountriesUseCase readCountriesUseCase;
    private final UpdateCountriesUseCase updateCountriesUseCase;
    private final DeleteCountriesUseCase deleteCountriesUseCase;

    private final ValidateUserAccessUseCase validateUserAccessUseCase;

    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public CountryApiResponsePayload create(@RequestBody @Valid  CountryApiRequestPayload countryApiRequestPayload)
            throws UserForbiddenException, AlreadyExistsException, EntityErrorsException, NotFoundException {

        validateUserAccessUseCase.checkCanAccessCountries(RequestContext.getUser(), CountryCode.valueOf(countryApiRequestPayload.getCode()));
        validateUserAccessUseCase.checkCanManageCountries(RequestContext.getUser());

        log.info("Creating country {} for user with identifier {}",
                JsonUtils.toJson(countryApiRequestPayload), RequestContext.getUsername());

        return new CountryApiResponsePayload(createCountriesUseCase.execute(countryApiRequestPayload.toEntity()));

    }

    @GetMapping(value = "/{id}")
    public CountryApiResponsePayload fetchById(@PathVariable(value = "id") Long id)
            throws NotFoundException, UserForbiddenException {

        Country country = readCountriesUseCase.execute(id);
        validateUserAccessUseCase.checkCanAccessCountries(RequestContext.getUser(), CountryCode.valueOf(country.getCode()));

        log.info("Fetching country {} for user with identifier {}",
                id, RequestContext.getUsername());

        return new CountryApiResponsePayload(country);

    }

    @GetMapping
    public List<CountryApiResponsePayload> fetchCountries() throws UserForbiddenException, EntityErrorsException {

        validateUserAccessUseCase.checkCanAccess(RequestContext.getUser());

        log.info("Fetching all countries for user with identifier {}",
                RequestContext.getUsername());
        return readCountriesUseCase.executeCountries()
                .stream().map(CountryApiResponsePayload::new).collect(Collectors.toList());
    }

    @PutMapping(value = "/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void updateCountry(@PathVariable(value = "id") Long id, @RequestBody @Valid CountryApiRequestPayload payload)
            throws UserForbiddenException, NotFoundException, EntityErrorsException {

        validateUserAccessUseCase.checkCanAccessCountries(RequestContext.getUser(), CountryCode.valueOf(payload.getCode()));
        validateUserAccessUseCase.checkCanManageCountries(RequestContext.getUser());

        log.info("Updating country with id {} : {} for user with identifier {}",
                id, JsonUtils.toJson(payload), RequestContext.getUsername());

        updateCountriesUseCase.execute(payload.toEntity(), id);
    }

    @DeleteMapping(value = "/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void delete(@PathVariable(value = "id") Integer id)
            throws NotFoundException, UserForbiddenException {

        Country country = readCountriesUseCase.execute(Long.valueOf(id));
        validateUserAccessUseCase.checkCanAccessCountries(RequestContext.getUser(), CountryCode.valueOf(country.getCode()));
        validateUserAccessUseCase.checkCanManageCountries(RequestContext.getUser());

        log.info("Deleting country with id {} for user with identifier {}",
                id, RequestContext.getUsername());

        deleteCountriesUseCase.execute(id);
    }

}
