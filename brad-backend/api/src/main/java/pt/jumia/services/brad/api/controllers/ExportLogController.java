package pt.jumia.services.brad.api.controllers;

import com.neovisionaries.i18n.CountryCode;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import pt.jumia.services.brad.api.payloads.request.PageFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.exportlog.ExportLogFiltersRequestPayload;
import pt.jumia.services.brad.api.payloads.request.exportlog.ExportLogSortRequestPayload;
import pt.jumia.services.brad.api.payloads.response.*;
import pt.jumia.services.brad.domain.RequestContext;
import pt.jumia.services.brad.domain.entities.ExportLog;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.account.Country;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.exportlog.ExportLogFilters;
import pt.jumia.services.brad.domain.entities.filter.exportlog.ExportLogSortFilters;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.exceptions.UserForbiddenException;
import pt.jumia.services.brad.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.brad.domain.usecases.countries.ReadCountriesUseCase;
import pt.jumia.services.brad.domain.usecases.exportlog.DownloadExportFromFileStorageUseCase;
import pt.jumia.services.brad.domain.usecases.exportlog.ReadExportLogUseCase;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@RestController
@RequestMapping(value = "/api/export-logs")
@Slf4j
@RequiredArgsConstructor
public class ExportLogController {

    private final ReadExportLogUseCase readExportLogUseCase;
    private final ReadCountriesUseCase readCountriesUseCase;

    private final ValidateUserAccessUseCase validateUserAccessUseCase;
    
    private final DownloadExportFromFileStorageUseCase downloadExportFromFileStorageUseCase;

    @GetMapping
    public PageApiResponsePayload<ExportLogResponsePayload> fetch(
            HttpServletRequest httpServletRequest,
            @Valid ExportLogFiltersRequestPayload exportLogFiltersRequestPayload,
            @Valid ExportLogSortRequestPayload exportLogSortRequestPayload,
            @Valid PageFiltersApiRequestPayload pageFiltersApiRequestPayload)
            throws UserForbiddenException, EntityErrorsException {

        ExportLogFilters filters = exportLogFiltersRequestPayload.toEntity();
        filters = getFiltersWithCountryIds(filters);
        ExportLogSortFilters sortFilters = exportLogSortRequestPayload.toEntity();
        PageFilters pageFilters = pageFiltersApiRequestPayload.toEntity();

        List<ExportLog> exportLogs = readExportLogUseCase.execute(filters, sortFilters, pageFilters);
        List<ExportLogResponsePayload> result = exportLogs.stream()
                .map(ExportLogResponsePayload::new)
                .collect(Collectors.toList());

        long total = readExportLogUseCase.executeCount(filters);

        return PageApiResponsePayload.buildPageResponsePayload(
                httpServletRequest,
                pageFilters,
                result,
                total
        );
    }

    @GetMapping(value = "/{id}")
    public ExportLogResponsePayload fetchById(@PathVariable(value = "id") Integer id)
            throws NotFoundException, UserForbiddenException {

        ExportLog exportLog = readExportLogUseCase.execute(id);
        //String country = getCountry(account);

        //validateUserAccessUseCase.checkCanAccessAccounts(RequestContext.getUser(), CountryCode.valueOf(country));

        log.info("Fetching account {} for user with identifier {}",
                id, RequestContext.getUsername());

        return new ExportLogResponsePayload(exportLog);

    }
    
    @GetMapping(value = "/{id}/download")
    public FileDownloadResponsePayload download(@PathVariable(value = "id") Long id) throws UserForbiddenException, NotFoundException {

        //validateUserAccessUseCase.checkCanAccessExportLog(RequestContext.getUser());

        String url = downloadExportFromFileStorageUseCase.executeGetTempDownloadUrl(id);
        return FileDownloadResponsePayload.builder()
                .url(url)
                .build();
    }

    @GetMapping(value = "/statuses")
    public List<EnumCodeResponsePayload> fetchExportLogStatuses() throws UserForbiddenException {

        validateUserAccessUseCase.checkCanAccess(RequestContext.getUser());

        log.info("Fetching additional export log statuses for user with identifier {}", RequestContext.getUsername());

        return Arrays.stream(ExportLog.Status.values())
                .map(status -> new EnumCodeResponsePayload(status.name()))
                .toList();
    }

    @GetMapping(value = "/types")
    public List<EnumCodeResponsePayload> fetchExportTypesStatuses() throws UserForbiddenException {

        validateUserAccessUseCase.checkCanAccess(RequestContext.getUser());

        log.info("Fetching additional export log types for user with identifier {}", RequestContext.getUsername());

        return Arrays.stream(ExportLog.Type.values())
                .map(status -> new EnumCodeResponsePayload(status.name()))
                .toList();
    }

    private ExportLogFilters getFiltersWithCountryIds(ExportLogFilters filters)
            throws UserForbiddenException {
        List<CountryCode> countriesWithCanViewPermission =
                validateUserAccessUseCase.getCountriesCanViewAccountsOrThrow(RequestContext.getUser());
        return getExportFiltersAccordingToCountryPermissions(filters, countriesWithCanViewPermission);
    }

    private ExportLogFilters getExportFiltersAccordingToCountryPermissions
            (ExportLogFilters exportLogFilters, List<CountryCode> countriesWithCanViewPermission) {
        List<String> countryCodesFromFilters = exportLogFilters.getCountryCodes();
        List<String> filteredCountryCodes;
        if (countryCodesFromFilters != null && !countryCodesFromFilters.isEmpty()) {
            filteredCountryCodes = countryCodesFromFilters.stream()
                    .filter(countryCode -> {
                        try {
                            Country country = readCountriesUseCase.execute(countryCode);
                            CountryCode countryCodeFromCountry = CountryCode.valueOf(country.getCode());
                            log.info("Country found for : {}", countryCode);
                            return countriesWithCanViewPermission.contains(countryCodeFromCountry);
                        } catch (NotFoundException e) {
                            return false;
                        }
                    }).collect(Collectors.toList());

        } else {
            filteredCountryCodes = countriesWithCanViewPermission.stream().map(countryCode -> {
                try {
                    Country country = readCountriesUseCase.execute(String.valueOf(countryCode));
                    log.info("Country found for : {}", countryCode);
                    return country.getCode();
                } catch (NotFoundException e) {
                    log.debug("Country not found for ID: {}", countryCode, e);
                    return null;
                }
            }).filter(Objects::nonNull).collect(Collectors.toList());

        }
        exportLogFilters.setCountryCodes(filteredCountryCodes);
        return exportLogFilters;
    }
}
