package pt.jumia.services.brad.api.controllers;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import pt.jumia.services.brad.api.payloads.request.PageFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.bale.BaleFilterListApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.bale.BaleFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.bale.BaleGroupFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.bale.BaleSortFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.baleviewentity.BaleViewEntityFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.response.BaleApiResponsePayload;
import pt.jumia.services.brad.api.payloads.response.PageApiResponsePayload;
import pt.jumia.services.brad.api.payloads.response.group.BaleGroupingsApiResponsePayload;
import pt.jumia.services.brad.api.payloads.response.group.GroupInfoResponsePayload;
import pt.jumia.services.brad.api.payloads.response.views.BaleRawResponsePayload;
import pt.jumia.services.brad.domain.RequestContext;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.bale.BaleFilters;
import pt.jumia.services.brad.domain.entities.filter.bale.BaleGroupFilters;
import pt.jumia.services.brad.domain.entities.filter.bale.BaleSortFilters;
import pt.jumia.services.brad.domain.exceptions.DatabaseErrorsException;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.exceptions.UserForbiddenException;
import pt.jumia.services.brad.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.brad.domain.usecases.bale.ReadBaleUseCase;
import pt.jumia.services.brad.domain.usecases.bale.brad.ReadBradBaleUseCase;
import pt.jumia.services.brad.domain.usecases.bale.brad.SyncBradBaleUseCase;

import java.text.ParseException;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping(value = "/api/bales")
public class BaleController {

    private final ValidateUserAccessUseCase validateUserAccessUseCase;

    private final ReadBaleUseCase readBaleUseCase;
    private final ReadBradBaleUseCase readBradBaleUseCase;
    private final SyncBradBaleUseCase syncBradBaleUseCase;

    @GetMapping
    public PageApiResponsePayload<BaleApiResponsePayload> fetch(HttpServletRequest httpServletRequest,
                                                                @Valid BaleFiltersApiRequestPayload baleFiltersApiRequestPayload,
                                                                @Valid BaleSortFiltersApiRequestPayload baleSortFiltersApiRequestPayload,
                                                                @Valid PageFiltersApiRequestPayload pageFiltersApiRequestPayload)
            throws UserForbiddenException, EntityErrorsException, ParseException {

        validateUserAccessUseCase.checkCanAccessReconciliation(RequestContext.getUser());
        log.info("Fetching all bale for user with identifier {}",
                RequestContext.getUsername());
        BaleFilters baleFilters = baleFiltersApiRequestPayload.toEntity();
        BaleSortFilters baleSortFilters = baleSortFiltersApiRequestPayload.toEntity();
        PageFilters pageFilters = pageFiltersApiRequestPayload.toEntity();


        List<BaleApiResponsePayload> baleList = readBradBaleUseCase.execute(baleFilters,
                        baleSortFilters, pageFilters)
                .stream().map(BaleApiResponsePayload::new).collect(Collectors.toList());


        long total = readBradBaleUseCase.executeCount(baleFilters);


        return PageApiResponsePayload.buildPageResponsePayload(
                httpServletRequest,
                pageFilters,
                baleList,
                total
        );

    }

    @GetMapping(value = "/{entryNo}")
    public BaleApiResponsePayload fetchByEntryNo(@PathVariable(value = "entryNo") Long entryNo)
            throws NotFoundException, UserForbiddenException {

        validateUserAccessUseCase.checkCanAccessReconciliation(RequestContext.getUser());

        log.info("Fetching bale {} for user with identifier {}",
                entryNo, RequestContext.getUsername());

        return new BaleApiResponsePayload(readBradBaleUseCase.execute(entryNo));

    }

    @GetMapping(value = "/grouped")
    public BaleGroupingsApiResponsePayload fetchGrouped(@Valid BaleFiltersApiRequestPayload baleFiltersApiRequestPayload,
                                                        @Valid BaleGroupFiltersApiRequestPayload baleGroupFiltersApiRequestPayload)
            throws UserForbiddenException, ParseException {

        validateUserAccessUseCase.checkCanAccessReconciliation(RequestContext.getUser());

        log.info("Fetching all bale grouped for user with identifier {}",
                RequestContext.getUsername());

        BaleFilters baleFilters = baleFiltersApiRequestPayload.toEntity();
        BaleGroupFilters baleGroupFilters = baleGroupFiltersApiRequestPayload.toEntity();

        return new BaleGroupingsApiResponsePayload(readBradBaleUseCase.executeGroup(baleFilters, baleGroupFilters));

    }

    @PostMapping(value = "/group-info")
    public List<GroupInfoResponsePayload> fetchGroupInfo(@RequestBody @Valid BaleFilterListApiRequestPayload baleFilterListApiRequestPayload)
            throws UserForbiddenException, ParseException {

        validateUserAccessUseCase.checkCanAccessReconciliation(RequestContext.getUser());

        log.info("Fetching bale group info for user with identifier {}",
                RequestContext.getUsername());

        List<BaleFilters> baleFiltersList = baleFilterListApiRequestPayload.toEntities();

        return readBradBaleUseCase.executeGroupInfoList(baleFiltersList)
                .stream().map(GroupInfoResponsePayload::new).collect(Collectors.toList());
    }

    @GetMapping(value = "/groupable-fields")
    public List<String> fetchGroupableFields() throws UserForbiddenException {

        validateUserAccessUseCase.checkCanAccessReconciliation(RequestContext.getUser());

        log.info("Fetching all groupable bale fields for user with identifier {}",
                RequestContext.getUsername());
        return readBradBaleUseCase.executeGroupableFields();
    }

    @GetMapping(value = "/sync")
    public void syncByBaleViewIds(@Valid BaleViewEntityFiltersApiRequestPayload baleViewEntityFiltersApiRequestPayload)
            throws UserForbiddenException {

        validateUserAccessUseCase.checkCanAccessScheduler(RequestContext.getUser());

        List<Integer> baleViewIds = baleViewEntityFiltersApiRequestPayload.getBaleViewIds();

        log.info("Syncing bale by bale view ids {} for user with identifier {}",
                baleViewIds, RequestContext.getUsername());

        try {
            // Use Spring Batch for consistent processing
            syncBradBaleUseCase.triggerBaleSyncForViews(baleViewIds);
            log.info("Successfully triggered Spring Batch job for bale view IDs: {}", baleViewIds);
        } catch (Exception e) {
            log.error("Failed to trigger Spring Batch job for bale view IDs: {}", baleViewIds, e);
            throw new RuntimeException("Bale sync job launch failed", e);
        }
    }

    @GetMapping(value = "/sync/{entryNo}")
    public void syncByEntryNo(@PathVariable(value = "entryNo") Integer entryNo)
            throws UserForbiddenException {

        validateUserAccessUseCase.checkCanAccessScheduler(RequestContext.getUser());

        log.info("Syncing bale by entryNo {} for user with identifier {}",
                entryNo, RequestContext.getUsername());

        try {
            // Use Spring Batch for consistent processing
            syncBradBaleUseCase.triggerBaleSync(entryNo);
            log.info("Successfully triggered Spring Batch job for entry number: {}", entryNo);
        } catch (Exception e) {
            log.error("Failed to trigger Spring Batch job for entry number: {}", entryNo, e);
            throw new RuntimeException("Bale sync job launch failed", e);
        }
    }

    @GetMapping(value = "/sync/{entryNo}/{baleViewEntityId}")
    public void syncByEntryNo(@PathVariable(value = "entryNo") Integer entryNo,
                              @PathVariable(value = "baleViewEntityId") Long baleViewEntityId)
            throws UserForbiddenException {

        validateUserAccessUseCase.checkCanAccessScheduler(RequestContext.getUser());

        log.info("Syncing bale by entryNo {} in bale view with id {} for user with identifier {}",
                entryNo, baleViewEntityId, RequestContext.getUsername());

        try {
            // Use Spring Batch for consistent processing
            syncBradBaleUseCase.triggerBaleSyncForView(entryNo, baleViewEntityId);
            log.info("Successfully triggered Spring Batch job for entry number: {} in view: {}", entryNo, baleViewEntityId);
        } catch (Exception e) {
            log.error("Failed to trigger Spring Batch job for entry number: {} in view: {}", entryNo, baleViewEntityId, e);
            throw new RuntimeException("Bale sync job launch failed", e);
        }
    }

    @GetMapping(value = "/last/{baleViewEntityId}/{offset}")
    public Integer fetchLastBaleWithOffset(@PathVariable(value = "baleViewEntityId") Long baleViewEntityId,
                                        @PathVariable(value = "offset") Integer offset)
            throws UserForbiddenException, EntityErrorsException, NotFoundException {

        validateUserAccessUseCase.checkCanManageViewEntities(RequestContext.getUser());

        log.info("Fetching last bale with an offset of {} in bale view with id {} for user with identifier {}",
                offset, baleViewEntityId, RequestContext.getUsername());

        return readBaleUseCase.executeLastBaleOfOffset(baleViewEntityId, offset);
    }

    @GetMapping(value = "/all-last/{baleViewEntityId}/{offset}")
    public List<BaleRawResponsePayload> fetchAllLastBaleWithOffset(@PathVariable(value = "baleViewEntityId") Long baleViewEntityId,
                                                                   @PathVariable(value = "offset") Integer offset)
            throws UserForbiddenException, EntityErrorsException, NotFoundException {

        validateUserAccessUseCase.checkCanManageViewEntities(RequestContext.getUser());

        log.info("Fetching all last bale with an offset of {} in bale view with id {} for user with identifier {}",
                offset, baleViewEntityId, RequestContext.getUsername());

        return readBaleUseCase.executeAllLastBaleOfOffset(baleViewEntityId, offset)
                .stream().map(BaleRawResponsePayload::new).collect(Collectors.toList());

    }
}
