package pt.jumia.services.brad.api.controllers;

import java.util.List;
import java.util.stream.Collectors;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import pt.jumia.services.brad.api.payloads.request.setting.SettingApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.setting.SettingFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.setting.SettingSortFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.response.SettingApiResponsePayload;
import pt.jumia.services.brad.domain.RequestContext;
import pt.jumia.services.brad.domain.entities.Setting;
import pt.jumia.services.brad.domain.entities.filter.setting.SettingFilters;
import pt.jumia.services.brad.domain.entities.filter.setting.SettingSortFilters;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.UserForbiddenException;
import pt.jumia.services.brad.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.brad.domain.usecases.setting.CreateSettingUseCase;
import pt.jumia.services.brad.domain.usecases.setting.DeleteSettingUseCase;
import pt.jumia.services.brad.domain.usecases.setting.ReadSettingUseCase;
import pt.jumia.services.brad.domain.usecases.setting.UpdateSettingUseCase;
import pt.jumia.services.brad.domain.utils.JsonUtils;

@RestController
@RequestMapping(value = "/api/settings")
@Slf4j
@RequiredArgsConstructor
public class SettingController {

    private final DeleteSettingUseCase deleteSettingUseCase;
    private final UpdateSettingUseCase updateSettingUseCase;
    private final CreateSettingUseCase createSettingUseCase;
    private final ReadSettingUseCase readSettingUseCase;
    private final ValidateUserAccessUseCase validateUserAccessUseCase;

    @GetMapping
    public List<SettingApiResponsePayload> fetch(@Valid SettingFiltersApiRequestPayload settingFiltersApiRequestPayload,
        @Valid SettingSortFiltersApiRequestPayload sortFiltersApiRequestPayload) throws UserForbiddenException, EntityErrorsException {

        validateUserAccessUseCase.checkCanAccessSettings(RequestContext.getUser());

        log.info("Fetching all settings for user with identifier {}",
            RequestContext.getUsername());

        SettingFilters settingFilters = settingFiltersApiRequestPayload.toEntity();
        SettingSortFilters settingSortFilters = sortFiltersApiRequestPayload.toEntity();

        return readSettingUseCase.execute(settingFilters, settingSortFilters)
            .stream()
            .map(SettingApiResponsePayload::new)
            .collect(Collectors.toList());
    }

    @GetMapping("/{id}")
    public SettingApiResponsePayload fetchById(@PathVariable("id") long id) throws UserForbiddenException {

        validateUserAccessUseCase.checkCanAccessSettings(RequestContext.getUser());

        log.info("Fetching setting {} for user with identifier {}",
            id, RequestContext.getUsername());

        Setting setting = readSettingUseCase.findById(id);
        return new SettingApiResponsePayload(setting);
    }

    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public SettingApiResponsePayload create(@RequestBody @Valid SettingApiRequestPayload payload)
        throws UserForbiddenException, EntityErrorsException {

        validateUserAccessUseCase.checkCanEditSettings(RequestContext.getUser());

        log.info("Creating new setting: {} for user with identifier {}",
            JsonUtils.toJsonOrNull(payload), RequestContext.getUsername());

        Setting setting = createSettingUseCase.execute(payload.toEntity());
        return new SettingApiResponsePayload(setting);
    }

    @PutMapping("/{id}")
    public SettingApiResponsePayload update(@PathVariable("id") long id,
        @Valid @RequestBody SettingApiRequestPayload payload) throws UserForbiddenException {

        validateUserAccessUseCase.checkCanEditSettings(RequestContext.getUser());

        log.info("Updating setting with id {} : {} for user with identifier {}",
            id, JsonUtils.toJsonOrNull(payload), RequestContext.getUsername());

        Setting setting = updateSettingUseCase.execute(id, payload.toEntity());
        return new SettingApiResponsePayload(setting);
    }

    @DeleteMapping("/{id}")
    public SettingApiResponsePayload delete(@PathVariable("id") long id) throws UserForbiddenException {

        validateUserAccessUseCase.checkCanEditSettings(RequestContext.getUser());

        log.info("Delete setting with id {} for user with identifier {}",
            id, RequestContext.getUsername());

        Setting setting = deleteSettingUseCase.execute(id);
        return new SettingApiResponsePayload(setting);
    }

}
