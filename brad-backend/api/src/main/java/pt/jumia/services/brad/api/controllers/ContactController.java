package pt.jumia.services.brad.api.controllers;

import com.neovisionaries.i18n.CountryCode;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import pt.jumia.services.brad.api.csvs.exports.ContactExportCSV;
import pt.jumia.services.brad.api.payloads.request.PageFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.account.AccountFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.contact.ContactApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.contact.ContactFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.contact.ContactSortFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.response.ContactApiResponsePayload;
import pt.jumia.services.brad.api.payloads.response.PageApiResponsePayload;
import pt.jumia.services.brad.domain.RequestContext;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.account.Contact;
import pt.jumia.services.brad.domain.entities.account.Country;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.account.AccountFilters;
import pt.jumia.services.brad.domain.entities.filter.contact.ContactFilters;
import pt.jumia.services.brad.domain.entities.filter.contact.ContactSortFilters;
import pt.jumia.services.brad.domain.exceptions.AlreadyExistsException;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.exceptions.UserForbiddenException;
import pt.jumia.services.brad.domain.usecases.accounts.ReadAccountsUseCase;
import pt.jumia.services.brad.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.brad.domain.usecases.contact.CreateContactUseCase;
import pt.jumia.services.brad.domain.usecases.contact.DeleteContactUseCase;
import pt.jumia.services.brad.domain.usecases.contact.ReadContactUseCase;
import pt.jumia.services.brad.domain.usecases.contact.UpdateContactUseCase;
import pt.jumia.services.brad.domain.usecases.countries.ReadCountriesUseCase;
import pt.jumia.services.brad.domain.utils.JsonUtils;

import java.text.ParseException;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * Controller responsible for handling CRUD operations for contacts.
 */
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping(value = "/api/contacts")
public class ContactController {

    private final CreateContactUseCase createContactUseCase;
    private final ReadContactUseCase readContactUseCase;
    private final UpdateContactUseCase updateContactUseCase;
    private final DeleteContactUseCase deleteContactUseCase;
    private final ValidateUserAccessUseCase validateUserAccessUseCase;
    private final ReadAccountsUseCase readAccountsUseCase;
    private final ReadCountriesUseCase readCountriesUseCase;


    @GetMapping
    public PageApiResponsePayload<ContactApiResponsePayload> fetch(HttpServletRequest httpServletRequest,
                  @Valid ContactFiltersApiRequestPayload contactFiltersApiRequestPayload,
                  @Valid ContactSortFiltersApiRequestPayload contactSortFiltersApiRequestPayload,
                  @Valid PageFiltersApiRequestPayload pageFiltersApiRequestPayload) throws UserForbiddenException,
                    EntityErrorsException, NotFoundException {

        String country = readAccountsUseCase.execute(Math.toIntExact(contactFiltersApiRequestPayload.getAccountID()))
                .getCountry().getCode();
        validateUserAccessUseCase.checkCanAccessAccounts(RequestContext.getUser(), CountryCode.valueOf(country));

        log.info("Fetching all contacts for user with identifier {}",
                RequestContext.getUsername());
        ContactFilters contactFilters = contactFiltersApiRequestPayload.toEntity();
        ContactSortFilters contactSortFilters = contactSortFiltersApiRequestPayload.toEntity();
        PageFilters pageFilters = pageFiltersApiRequestPayload.toEntity();


        List<ContactApiResponsePayload> contacts = readContactUseCase.execute(contactFilters,
                        contactSortFilters, pageFilters)
                .stream().map(ContactApiResponsePayload::new)
                .collect(Collectors.toList());


        long total = readContactUseCase.executeCount(contactFilters);


        return PageApiResponsePayload.buildPageResponsePayload(
                httpServletRequest,
                pageFilters,
                contacts,
                total
        );
    }

    @GetMapping(value = "/account/{id}")
    public List<ContactApiResponsePayload> fetchByAccount(@PathVariable(value = "id") Long accountId)
            throws UserForbiddenException, EntityErrorsException, NotFoundException {

        String country = readAccountsUseCase.execute(Math.toIntExact(accountId))
                .getCountry().getCode();

        validateUserAccessUseCase.checkCanAccessAccounts(RequestContext.getUser(), CountryCode.valueOf(country));

        log.info("Fetching contacts for account {} for user with identifier {}",
                accountId, RequestContext.getUsername());

        ContactFilters contactFilters = ContactFilters.builder()
                .accountID(accountId)
                .build();
        ContactSortFilters contactSortFilters = ContactSortFilters.builder()
                .field(Contact.SortingFields.NAME)
                .build();
        PageFilters pageFilters = null;

        return readContactUseCase.execute(contactFilters,
                        contactSortFilters, pageFilters)
                .stream().map(ContactApiResponsePayload::new)
                .collect(Collectors.toList());
    }

    @GetMapping(value = "/{id}")
    public ContactApiResponsePayload fetchById(@PathVariable(value = "id") Integer id)
            throws NotFoundException, UserForbiddenException {

        Contact contact = readContactUseCase.execute(id);
        String country = contact.getAccount().getCountry().getCode();

        validateUserAccessUseCase.checkCanAccessAccounts(RequestContext.getUser(), CountryCode.valueOf(country));

        log.info("Fetching contact {} for user with identifier {}",
                id, RequestContext.getUsername());

        return new ContactApiResponsePayload(contact);

    }

    @GetMapping(value = "/download")
    @ResponseStatus(HttpStatus.OK)
    public Future<ResponseEntity<byte[]>> download(@Valid AccountFiltersApiRequestPayload accountFiltersApiRequestPayload)
            throws UserForbiddenException, NotFoundException, ParseException, EntityErrorsException {

        log.info("Downloading Contacts for user with identifier {}", RequestContext.getUsername());

        AccountFilters accountFilters = accountFiltersApiRequestPayload.toEntity();
        List<Long> accountIds = readAccountsUseCase.execute(accountFilters, null, null).stream()
                .map(Account::getId).toList();

        ContactFilters contactFilters = ContactFilters.builder().accountIds(accountIds).build();

        contactFilters = getContactFiltersWithUpdatedCountryCodesBasedOnUserAccessToDownload(contactFilters);

        CompletableFuture<ResponseEntity<byte[]>> future = new CompletableFuture<>();
        ContactFilters finalContactFilters = contactFilters;
        CompletableFuture.runAsync(() -> {
            try {
                final
                List<Contact> contacts = readContactUseCase.execute(finalContactFilters, null, null);

                future.complete(ContactExportCSV.buildContactCSV(contacts));

            } catch (Exception e) {
                log.error("Error downloading Contacts", e);
                future.completeExceptionally(e);
            }
        });
        return future;
    }


    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public ContactApiResponsePayload create(@RequestBody @Valid ContactApiRequestPayload payload)
            throws UserForbiddenException, AlreadyExistsException, NotFoundException, EntityErrorsException {

       String country = readAccountsUseCase.execute(Math.toIntExact(payload.getAccountID())).getCountry().getCode();

        validateUserAccessUseCase.checkCanManageAccounts(RequestContext.getUser(), CountryCode.valueOf(country));

        log.info("Creating new contact: {} for user with identifier {}",
                JsonUtils.toJson(payload), RequestContext.getUsername());


        return new ContactApiResponsePayload(
                createContactUseCase.execute(payload.toEntity(), payload.getAccountID())
        );


    }

    @PutMapping(value = "/{id}")
    public void update(@PathVariable(value = "id") Long id, @RequestBody @Valid ContactApiRequestPayload payload)
            throws NotFoundException, UserForbiddenException, EntityErrorsException {

        String country = readAccountsUseCase.execute(Math.toIntExact(payload.getAccountID())).getCountry().getCode();

        validateUserAccessUseCase.checkCanManageAccounts(RequestContext.getUser(), CountryCode.valueOf(country));

        log.info("Updating contact with id {} : {} for user with identifier {}",
                id, JsonUtils.toJson(payload), RequestContext.getUsername());

        updateContactUseCase.execute(payload.toEntity());

    }

    @DeleteMapping(value = "/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void delete(@PathVariable(value = "id") Integer id)
            throws NotFoundException, UserForbiddenException {

        String country = readContactUseCase.execute(id).getAccount().getCountry().getCode();
        validateUserAccessUseCase.checkCanManageAccounts(RequestContext.getUser(), CountryCode.valueOf(country));

        log.info("Deleting contact with id {} for user with identifier {}",
                id, RequestContext.getUsername());

        deleteContactUseCase.execute(id);
    }

    @GetMapping(value = "/types")
    public List<String> fetchContactTypes() throws UserForbiddenException {

        validateUserAccessUseCase.checkCanAccess(RequestContext.getUser());

        log.info("Fetching all contact types for user with identifier {}",
                RequestContext.getUsername());
        return readContactUseCase.executeContactTypes();
    }

    private ContactFilters getContactFiltersWithUpdatedCountryCodesBasedOnUserAccessToDownload
            (ContactFilters contactFilters)
            throws UserForbiddenException {
        List<CountryCode> countriesWithCanViewPermission =
                validateUserAccessUseCase.getCountriesCanDownloadContactsOrThrow(RequestContext.getUser());
        return getContactFiltersAccordingToCountryPermissions(contactFilters, countriesWithCanViewPermission);
    }

    private ContactFilters getContactFiltersAccordingToCountryPermissions
            (ContactFilters contactFilters, List<CountryCode> countriesWithCanViewPermission) {
        List<Long> filteredCountryCodes = countriesWithCanViewPermission.stream().map(countryCode -> {
                try {
                    Country country = readCountriesUseCase.execute(String.valueOf(countryCode));
                    log.info("Country found for : {}", countryCode);
                    return country.getId();
                } catch (NotFoundException e) {
                    log.error("Country not found for ID: {}", countryCode, e);
                    return null;
                }
            }).filter(Objects::nonNull).collect(Collectors.toList());

        contactFilters.setCountryCodes(filteredCountryCodes);
        return contactFilters;
    }

}
