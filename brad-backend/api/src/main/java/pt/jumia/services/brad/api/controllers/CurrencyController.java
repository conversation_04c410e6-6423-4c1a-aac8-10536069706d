package pt.jumia.services.brad.api.controllers;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import pt.jumia.services.brad.api.payloads.request.currency.CurrencyApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.currency.CurrencyFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.response.CurrencyApiResponsePayload;
import pt.jumia.services.brad.domain.RequestContext;
import pt.jumia.services.brad.domain.exceptions.AlreadyExistsException;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.exceptions.UserForbiddenException;
import pt.jumia.services.brad.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.brad.domain.usecases.currencies.CreateCurrenciesUseCase;
import pt.jumia.services.brad.domain.usecases.currencies.DeleteCurrenciesUseCase;
import pt.jumia.services.brad.domain.usecases.currencies.ReadCurrenciesUseCase;
import pt.jumia.services.brad.domain.usecases.currencies.UpdateCurrenciesUseCase;
import pt.jumia.services.brad.domain.utils.JsonUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Controller responsible for handling CRUD operations for currencies.
 */
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping(value = "/api/currencies")
public class CurrencyController {

    private final CreateCurrenciesUseCase createCurrenciesUseCase;
    private final ReadCurrenciesUseCase readCurrenciesUseCase;
    private final UpdateCurrenciesUseCase updateCurrenciesUseCase;
    private final DeleteCurrenciesUseCase deleteCurrenciesUseCase;

    private final ValidateUserAccessUseCase validateUserAccessUseCase;

    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public CurrencyApiResponsePayload create(@RequestBody @Valid CurrencyApiRequestPayload currencyApiRequestPayload)
            throws UserForbiddenException, AlreadyExistsException, EntityErrorsException {

        validateUserAccessUseCase.checkCanAccessCurrencies(RequestContext.getUser());
        validateUserAccessUseCase.checkCanManageCurrencies(RequestContext.getUser());

        log.info("Creating currency {} for user with identifier {}",
                JsonUtils.toJson(currencyApiRequestPayload), RequestContext.getUsername());

        return new CurrencyApiResponsePayload(createCurrenciesUseCase.execute(currencyApiRequestPayload.toEntity()));

    }

    @GetMapping(value = "/{id}")
    public CurrencyApiResponsePayload fetchById(@PathVariable(value = "id") Long id)
            throws NotFoundException, UserForbiddenException {

        validateUserAccessUseCase.checkCanAccessCurrencies(RequestContext.getUser());

        log.info("Fetching currency {} for user with identifier {}",
                id, RequestContext.getUsername());

        return new CurrencyApiResponsePayload(readCurrenciesUseCase.execute(id));

    }

    @GetMapping
    public List<CurrencyApiResponsePayload> fetchCurrencies(
            @Valid CurrencyFiltersApiRequestPayload currencyFiltersApiRequestPayload
    ) throws UserForbiddenException {

        validateUserAccessUseCase.checkCanAccessCurrencies(RequestContext.getUser());

        log.info("Fetching all currencies for user with identifier {}",
                RequestContext.getUsername());
        return readCurrenciesUseCase.executeCurrencies(currencyFiltersApiRequestPayload.toEntity())
                .stream().map(CurrencyApiResponsePayload::new).collect(Collectors.toList());
    }

    @PutMapping(value = "/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void updateCurrency(@PathVariable(value = "id") Long id, @RequestBody @Valid CurrencyApiRequestPayload payload)
            throws UserForbiddenException, NotFoundException, EntityErrorsException {

        validateUserAccessUseCase.checkCanAccessCurrencies(RequestContext.getUser());
        validateUserAccessUseCase.checkCanManageCurrencies(RequestContext.getUser());

        log.info("Updating currency with id {} : {} for user with identifier {}",
                id, JsonUtils.toJson(payload), RequestContext.getUsername());

        payload.setId(id);
        updateCurrenciesUseCase.execute(payload.toEntity());
    }

    @DeleteMapping(value = "/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void delete(@PathVariable(value = "id") Integer id)
            throws NotFoundException, UserForbiddenException {

        validateUserAccessUseCase.checkCanAccessCurrencies(RequestContext.getUser());
        validateUserAccessUseCase.checkCanManageCurrencies(RequestContext.getUser());

        log.info("Deleting currency with id {} for user with identifier {}",
                id, RequestContext.getUsername());

        deleteCurrenciesUseCase.execute(id);
    }
}
