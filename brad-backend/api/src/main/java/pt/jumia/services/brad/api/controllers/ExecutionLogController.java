package pt.jumia.services.brad.api.controllers;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import pt.jumia.services.brad.api.payloads.request.PageFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.executionlog.ExecutionLogFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.executionlog.ExecutionLogSortFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.response.ExecutionLogApiResponsePayload;
import pt.jumia.services.brad.api.payloads.response.PageApiResponsePayload;
import pt.jumia.services.brad.domain.RequestContext;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.executionlog.ExecutionLogFilters;
import pt.jumia.services.brad.domain.entities.filter.executionlog.ExecutionLogSortFilters;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.exceptions.UserForbiddenException;
import pt.jumia.services.brad.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.brad.domain.usecases.executionlogs.ReadExecutionLogsUseCase;

import java.text.ParseException;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping(value = "/api/execution-logs")
public class ExecutionLogController {

    public final ValidateUserAccessUseCase validateUserAccessUseCase;
    public final ReadExecutionLogsUseCase readExecutionLogsUseCase;



    @GetMapping
    public PageApiResponsePayload<ExecutionLogApiResponsePayload> fetch(HttpServletRequest httpServletRequest,
                    @Valid ExecutionLogFiltersApiRequestPayload executionLogFiltersApiRequestPayload,
                    @Valid ExecutionLogSortFiltersApiRequestPayload executionLogSortFiltersApiRequestPayload,
                    @Valid PageFiltersApiRequestPayload pageFiltersApiRequestPayload)
            throws UserForbiddenException, EntityErrorsException, ParseException {


        validateUserAccessUseCase.checkCanAccessExecutionLog(RequestContext.getUser());

        log.info("Fetching all execution logs for user with identifier {}", RequestContext.getUsername());

        ExecutionLogFilters executionLogFilters = executionLogFiltersApiRequestPayload.toEntity();
        ExecutionLogSortFilters executionLogSortFilters = executionLogSortFiltersApiRequestPayload.toEntity();
        PageFilters pageFilters = pageFiltersApiRequestPayload.toEntity();

        List<ExecutionLogApiResponsePayload> executionLogs =
                readExecutionLogsUseCase.execute(executionLogFilters, executionLogSortFilters, pageFilters)
                        .stream()
                        .map(ExecutionLogApiResponsePayload::new)
                        .collect(Collectors.toList());

        long total = readExecutionLogsUseCase.executeCount(executionLogFilters);

        return PageApiResponsePayload.buildPageResponsePayload(
                httpServletRequest,
                pageFilters,
                executionLogs,
                total
        );
    }

    @GetMapping(value = "/{id}")
    public ExecutionLogApiResponsePayload fetchById(@PathVariable(value = "id") Integer id)
            throws NotFoundException, UserForbiddenException {

        validateUserAccessUseCase.checkCanAccessExecutionLog(RequestContext.getUser());

        log.info("Fetching execution log {} for user with identifier {}",
                id, RequestContext.getUsername());

        return new ExecutionLogApiResponsePayload(readExecutionLogsUseCase.execute(id));

    }

    @GetMapping(value = "/statuses")
    public List<String> fetchStatuses() throws UserForbiddenException {

        validateUserAccessUseCase.checkCanAccessExecutionLog(RequestContext.getUser());

        log.info("Fetching all executionLog statuses for user with identifier {}",
                RequestContext.getUsername());
        return readExecutionLogsUseCase.executeStatuses();
    }

    @GetMapping(value = "/types")
    public List<String> fetchTypes() throws UserForbiddenException {

        validateUserAccessUseCase.checkCanAccessExecutionLog(RequestContext.getUser());

        log.info("Fetching all executionLog types for user with identifier {}",
                RequestContext.getUsername());
        return readExecutionLogsUseCase.executeTypes();
    }

}
