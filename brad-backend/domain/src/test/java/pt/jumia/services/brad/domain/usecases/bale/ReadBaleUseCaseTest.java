package pt.jumia.services.brad.domain.usecases.bale;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.entities.ViewEntity;
import pt.jumia.services.brad.domain.entities.fake.FakeBale;
import pt.jumia.services.brad.domain.entities.fake.FakeViewEntity;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.repository.BaleRepository;
import pt.jumia.services.brad.domain.usecases.viewentity.ReadViewEntityUseCase;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ReadBaleUseCaseTest {

    private static final List<Bale> BALE_LIST = new ArrayList<>(FakeBale.getFakeBale(10));
    @Mock
    private BaleRepository baleRepository;



    @Mock
    private ReadViewEntityUseCase readViewEntityUseCase;



    @InjectMocks
    private ReadBaleUseCase readBaleUseCase;

    @BeforeEach
    void setUp() {
        // Setup simplified for Spring Batch migration
        // BatchProcessingConfig removed - now handled by Spring Batch configuration
    }

    @Test
    public void executeLastBaleOfOffset_shouldReturnLastBaleOffset() throws NotFoundException, EntityErrorsException {
        // Given
        Long baleViewEntityId = 1L;
        Integer offset = 100;
        Integer expectedLastBale = 12345;
        ViewEntity mockViewEntity = FakeViewEntity.getFakeViewEntity(1).get(0);

        when(readViewEntityUseCase.execute(baleViewEntityId, ViewEntity.EntityType.BALE)).thenReturn(mockViewEntity);
        when(baleRepository.findLastBaleWithOffset(mockViewEntity, offset)).thenReturn(expectedLastBale);

        // When
        Integer result = readBaleUseCase.executeLastBaleOfOffset(baleViewEntityId, offset);

        // Then
        assertEquals(expectedLastBale, result);
        verify(readViewEntityUseCase).execute(baleViewEntityId, ViewEntity.EntityType.BALE);
        verify(baleRepository).findLastBaleWithOffset(mockViewEntity, offset);
    }

    @Test
    public void executeAllLastBaleOfOffset_shouldReturnAllLastBales() throws NotFoundException, EntityErrorsException {
        // Given
        Long baleViewEntityId = 1L;
        Integer offset = 100;
        List<Bale> expectedBales = BALE_LIST;
        ViewEntity mockViewEntity = FakeViewEntity.getFakeViewEntity(1).get(0);

        when(readViewEntityUseCase.execute(baleViewEntityId, ViewEntity.EntityType.BALE)).thenReturn(mockViewEntity);
        when(baleRepository.findAllLastBaleWithOffset(mockViewEntity, offset)).thenReturn(expectedBales);

        // When
        List<Bale> result = readBaleUseCase.executeAllLastBaleOfOffset(baleViewEntityId, offset);

        // Then
        assertEquals(expectedBales, result);
        verify(readViewEntityUseCase).execute(baleViewEntityId, ViewEntity.EntityType.BALE);
        verify(baleRepository).findAllLastBaleWithOffset(mockViewEntity, offset);
    }

}
